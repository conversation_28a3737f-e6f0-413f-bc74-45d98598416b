import { API_BASE_URL, API_TARGET_URL } from '../constant';
import { ProxyOptions } from 'vite';

type ProxyTargetList = Record<string, ProxyOptions>;

const init: ProxyTargetList = {
  // test
  [API_BASE_URL]: {
    target: API_TARGET_URL,
    rewrite: (path) => path.replace(new RegExp(`^${API_BASE_URL}`), '/'),
    changeOrigin: true,
    xfwd: true,
  },
  // // mock
  // [MOCK_API_BASE_URL]: {
  //   target: MOCK_API_TARGET_URL,
  //   changeOrigin: true,
  //   rewrite: (path) => path.replace(new RegExp(`^${MOCK_API_BASE_URL}`), '/api'),
  // },
};

console.log(init);
export default init;
