{"name": "vue3-vite-ts-template", "version": "0.1.1", "author": "", "scripts": {"dev": "vite --mode development", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "build:dev": "vite build --mode development", "build:pro": "vite build --mode production", "serve": "vite preview", "plop": "plop", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "prepare": "husky install", "deps": "yarn upgrade-interactive --latest"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@dataview/datav-vue3": "0.0.0-test.1672506674342", "@element-plus/icons": "^0.0.11", "@element-plus/icons-vue": "^2.3.1", "@vueuse/components": "^10.11.0", "@vueuse/core": "^10.11.0", "aieditor": "^1.0.10", "ant-design-vue": "^3.2.20", "axios": "^1.7.2", "dayjs": "^1.11.11", "echarts": "^5.5.0", "element-plus": "^2.7.6", "html2canvas": "^1.4.1", "mitt": "^3.0.1", "naive-ui": "^2.38.2", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qs": "^6.12.1", "tdesign-vue-next": "^1.9.6", "vue": "^3.4.30", "vue-router": "^4.4.0", "vue3-print-nb": "^0.1.4", "vue3-scale-box": "^0.1.9", "vuedraggable": "^2.24.3"}, "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@types/node": "^18.19.39", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.15", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-vue": "^4.6.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.19", "cz-git": "^1.9.3", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-define-config": "^1.24.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-vue": "^9.26.0", "husky": "^8.0.3", "import": "^0.0.6", "less": "^4.2.0", "less-loader": "^11.1.4", "lint-staged": "^13.3.0", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "plop": "^3.1.2", "postcss": "^8.4.38", "postcss-html": "^1.7.0", "postcss-less": "^6.0.0", "postcss-px-to-viewport": "^1.1.1", "postcss-scss": "^4.0.9", "prettier": "^2.8.8", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.6", "stylelint": "^15.11.0", "stylelint-config-recommended": "^12.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^33.0.0", "stylelint-config-standard-scss": "^10.0.0", "stylelint-order": "^6.0.4", "stylelint-prettier": "^3.0.0", "typescript": "5.0.4", "unocss": "^0.53.6", "unplugin-auto-import": "^0.16.7", "unplugin-icons": "^0.16.6", "unplugin-vue-components": "^0.25.2", "vite": "^4.5.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-mkcert": "^1.17.5", "vite-plugin-mock": "^3.0.2", "vite-plugin-pages": "^0.31.0", "vite-plugin-progress": "^0.0.7", "vite-plugin-restart": "^0.3.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-images": "^0.6.1", "vite-plugin-vue-layouts": "^0.8.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^1.8.27"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{scss}": ["stylelint --fix", "git add"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}