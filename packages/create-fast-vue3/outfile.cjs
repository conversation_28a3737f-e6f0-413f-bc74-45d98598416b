#!/usr/bin/env node
var __create = Object.create
var __defProp = Object.defineProperty
var __getOwnPropDesc = Object.getOwnPropertyDescriptor
var __getOwnPropNames = Object.getOwnPropertyNames
var __getProtoOf = Object.getPrototypeOf
var __hasOwnProp = Object.prototype.hasOwnProperty
var __markAsModule = (target) => __defProp(target, '__esModule', { value: true })
var __commonJS = (cb, mod) =>
  function __require() {
    return (
      mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports
    )
  }
var __reExport = (target, module2, copyDefault, desc) => {
  if ((module2 && typeof module2 === 'object') || typeof module2 === 'function') {
    for (let key of __getOwnPropNames(module2))
      if (!__hasOwnProp.call(target, key) && (copyDefault || key !== 'default'))
        __defProp(target, key, {
          get: () => module2[key],
          enumerable: !(desc = __getOwnPropDesc(module2, key)) || desc.enumerable
        })
  }
  return target
}
var __toESM = (module2, isNodeMode) => {
  return __reExport(
    __markAsModule(
      __defProp(
        module2 != null ? __create(__getProtoOf(module2)) : {},
        'default',
        !isNodeMode && module2 && module2.__esModule
          ? { get: () => module2.default, enumerable: true }
          : { value: module2, enumerable: true }
      )
    ),
    module2
  )
}

// ../../node_modules/.pnpm/minimist@1.2.5/node_modules/minimist/index.js
var require_minimist = __commonJS({
  '../../node_modules/.pnpm/minimist@1.2.5/node_modules/minimist/index.js'(exports, module2) {
    module2.exports = function (args, opts) {
      if (!opts) opts = {}
      var flags = { bools: {}, strings: {}, unknownFn: null }
      if (typeof opts['unknown'] === 'function') {
        flags.unknownFn = opts['unknown']
      }
      if (typeof opts['boolean'] === 'boolean' && opts['boolean']) {
        flags.allBools = true
      } else {
        ;[]
          .concat(opts['boolean'])
          .filter(Boolean)
          .forEach(function (key2) {
            flags.bools[key2] = true
          })
      }
      var aliases = {}
      Object.keys(opts.alias || {}).forEach(function (key2) {
        aliases[key2] = [].concat(opts.alias[key2])
        aliases[key2].forEach(function (x) {
          aliases[x] = [key2].concat(
            aliases[key2].filter(function (y) {
              return x !== y
            })
          )
        })
      })
      ;[]
        .concat(opts.string)
        .filter(Boolean)
        .forEach(function (key2) {
          flags.strings[key2] = true
          if (aliases[key2]) {
            flags.strings[aliases[key2]] = true
          }
        })
      var defaults = opts['default'] || {}
      var argv = { _: [] }
      Object.keys(flags.bools).forEach(function (key2) {
        setArg(key2, defaults[key2] === void 0 ? false : defaults[key2])
      })
      var notFlags = []
      if (args.indexOf('--') !== -1) {
        notFlags = args.slice(args.indexOf('--') + 1)
        args = args.slice(0, args.indexOf('--'))
      }
      function argDefined(key2, arg2) {
        return (
          (flags.allBools && /^--[^=]+$/.test(arg2)) ||
          flags.strings[key2] ||
          flags.bools[key2] ||
          aliases[key2]
        )
      }
      function setArg(key2, val, arg2) {
        if (arg2 && flags.unknownFn && !argDefined(key2, arg2)) {
          if (flags.unknownFn(arg2) === false) return
        }
        var value2 = !flags.strings[key2] && isNumber(val) ? Number(val) : val
        setKey(argv, key2.split('.'), value2)
        ;(aliases[key2] || []).forEach(function (x) {
          setKey(argv, x.split('.'), value2)
        })
      }
      function setKey(obj, keys, value2) {
        var o = obj
        for (var i2 = 0; i2 < keys.length - 1; i2++) {
          var key2 = keys[i2]
          if (key2 === '__proto__') return
          if (o[key2] === void 0) o[key2] = {}
          if (
            o[key2] === Object.prototype ||
            o[key2] === Number.prototype ||
            o[key2] === String.prototype
          )
            o[key2] = {}
          if (o[key2] === Array.prototype) o[key2] = []
          o = o[key2]
        }
        var key2 = keys[keys.length - 1]
        if (key2 === '__proto__') return
        if (o === Object.prototype || o === Number.prototype || o === String.prototype) o = {}
        if (o === Array.prototype) o = []
        if (o[key2] === void 0 || flags.bools[key2] || typeof o[key2] === 'boolean') {
          o[key2] = value2
        } else if (Array.isArray(o[key2])) {
          o[key2].push(value2)
        } else {
          o[key2] = [o[key2], value2]
        }
      }
      function aliasIsBoolean(key2) {
        return aliases[key2].some(function (x) {
          return flags.bools[x]
        })
      }
      for (var i = 0; i < args.length; i++) {
        var arg = args[i]
        if (/^--.+=/.test(arg)) {
          var m = arg.match(/^--([^=]+)=([\s\S]*)$/)
          var key = m[1]
          var value = m[2]
          if (flags.bools[key]) {
            value = value !== 'false'
          }
          setArg(key, value, arg)
        } else if (/^--no-.+/.test(arg)) {
          var key = arg.match(/^--no-(.+)/)[1]
          setArg(key, false, arg)
        } else if (/^--.+/.test(arg)) {
          var key = arg.match(/^--(.+)/)[1]
          var next = args[i + 1]
          if (
            next !== void 0 &&
            !/^-/.test(next) &&
            !flags.bools[key] &&
            !flags.allBools &&
            (aliases[key] ? !aliasIsBoolean(key) : true)
          ) {
            setArg(key, next, arg)
            i++
          } else if (/^(true|false)$/.test(next)) {
            setArg(key, next === 'true', arg)
            i++
          } else {
            setArg(key, flags.strings[key] ? '' : true, arg)
          }
        } else if (/^-[^-]+/.test(arg)) {
          var letters = arg.slice(1, -1).split('')
          var broken = false
          for (var j = 0; j < letters.length; j++) {
            var next = arg.slice(j + 2)
            if (next === '-') {
              setArg(letters[j], next, arg)
              continue
            }
            if (/[A-Za-z]/.test(letters[j]) && /=/.test(next)) {
              setArg(letters[j], next.split('=')[1], arg)
              broken = true
              break
            }
            if (/[A-Za-z]/.test(letters[j]) && /-?\d+(\.\d*)?(e-?\d+)?$/.test(next)) {
              setArg(letters[j], next, arg)
              broken = true
              break
            }
            if (letters[j + 1] && letters[j + 1].match(/\W/)) {
              setArg(letters[j], arg.slice(j + 2), arg)
              broken = true
              break
            } else {
              setArg(letters[j], flags.strings[letters[j]] ? '' : true, arg)
            }
          }
          var key = arg.slice(-1)[0]
          if (!broken && key !== '-') {
            if (
              args[i + 1] &&
              !/^(-|--)[^-]/.test(args[i + 1]) &&
              !flags.bools[key] &&
              (aliases[key] ? !aliasIsBoolean(key) : true)
            ) {
              setArg(key, args[i + 1], arg)
              i++
            } else if (args[i + 1] && /^(true|false)$/.test(args[i + 1])) {
              setArg(key, args[i + 1] === 'true', arg)
              i++
            } else {
              setArg(key, flags.strings[key] ? '' : true, arg)
            }
          }
        } else {
          if (!flags.unknownFn || flags.unknownFn(arg) !== false) {
            argv._.push(flags.strings['_'] || !isNumber(arg) ? arg : Number(arg))
          }
          if (opts.stopEarly) {
            argv._.push.apply(argv._, args.slice(i + 1))
            break
          }
        }
      }
      Object.keys(defaults).forEach(function (key2) {
        if (!hasKey(argv, key2.split('.'))) {
          setKey(argv, key2.split('.'), defaults[key2])
          ;(aliases[key2] || []).forEach(function (x) {
            setKey(argv, x.split('.'), defaults[key2])
          })
        }
      })
      if (opts['--']) {
        argv['--'] = []
        notFlags.forEach(function (key2) {
          argv['--'].push(key2)
        })
      } else {
        notFlags.forEach(function (key2) {
          argv._.push(key2)
        })
      }
      return argv
    }
    function hasKey(obj, keys) {
      var o = obj
      keys.slice(0, -1).forEach(function (key2) {
        o = o[key2] || {}
      })
      var key = keys[keys.length - 1]
      return key in o
    }
    function isNumber(x) {
      if (typeof x === 'number') return true
      if (/^0x[0-9a-f]+$/i.test(x)) return true
      return /^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(e[-+]?\d+)?$/.test(x)
    }
  }
})

// ../../node_modules/.pnpm/kleur@3.0.3/node_modules/kleur/index.js
var require_kleur = __commonJS({
  '../../node_modules/.pnpm/kleur@3.0.3/node_modules/kleur/index.js'(exports, module2) {
    'use strict'
    var { FORCE_COLOR, NODE_DISABLE_COLORS, TERM } = process.env
    var $ = {
      enabled: !NODE_DISABLE_COLORS && TERM !== 'dumb' && FORCE_COLOR !== '0',
      reset: init2(0, 0),
      bold: init2(1, 22),
      dim: init2(2, 22),
      italic: init2(3, 23),
      underline: init2(4, 24),
      inverse: init2(7, 27),
      hidden: init2(8, 28),
      strikethrough: init2(9, 29),
      black: init2(30, 39),
      red: init2(31, 39),
      green: init2(32, 39),
      yellow: init2(33, 39),
      blue: init2(34, 39),
      magenta: init2(35, 39),
      cyan: init2(36, 39),
      white: init2(37, 39),
      gray: init2(90, 39),
      grey: init2(90, 39),
      bgBlack: init2(40, 49),
      bgRed: init2(41, 49),
      bgGreen: init2(42, 49),
      bgYellow: init2(43, 49),
      bgBlue: init2(44, 49),
      bgMagenta: init2(45, 49),
      bgCyan: init2(46, 49),
      bgWhite: init2(47, 49)
    }
    function run(arr, str) {
      let i = 0,
        tmp,
        beg = '',
        end = ''
      for (; i < arr.length; i++) {
        tmp = arr[i]
        beg += tmp.open
        end += tmp.close
        if (str.includes(tmp.close)) {
          str = str.replace(tmp.rgx, tmp.close + tmp.open)
        }
      }
      return beg + str + end
    }
    function chain(has, keys) {
      let ctx = { has, keys }
      ctx.reset = $.reset.bind(ctx)
      ctx.bold = $.bold.bind(ctx)
      ctx.dim = $.dim.bind(ctx)
      ctx.italic = $.italic.bind(ctx)
      ctx.underline = $.underline.bind(ctx)
      ctx.inverse = $.inverse.bind(ctx)
      ctx.hidden = $.hidden.bind(ctx)
      ctx.strikethrough = $.strikethrough.bind(ctx)
      ctx.black = $.black.bind(ctx)
      ctx.red = $.red.bind(ctx)
      ctx.green = $.green.bind(ctx)
      ctx.yellow = $.yellow.bind(ctx)
      ctx.blue = $.blue.bind(ctx)
      ctx.magenta = $.magenta.bind(ctx)
      ctx.cyan = $.cyan.bind(ctx)
      ctx.white = $.white.bind(ctx)
      ctx.gray = $.gray.bind(ctx)
      ctx.grey = $.grey.bind(ctx)
      ctx.bgBlack = $.bgBlack.bind(ctx)
      ctx.bgRed = $.bgRed.bind(ctx)
      ctx.bgGreen = $.bgGreen.bind(ctx)
      ctx.bgYellow = $.bgYellow.bind(ctx)
      ctx.bgBlue = $.bgBlue.bind(ctx)
      ctx.bgMagenta = $.bgMagenta.bind(ctx)
      ctx.bgCyan = $.bgCyan.bind(ctx)
      ctx.bgWhite = $.bgWhite.bind(ctx)
      return ctx
    }
    function init2(open, close) {
      let blk = {
        open: `\x1B[${open}m`,
        close: `\x1B[${close}m`,
        rgx: new RegExp(`\\x1b\\[${close}m`, 'g')
      }
      return function (txt) {
        if (this !== void 0 && this.has !== void 0) {
          this.has.includes(open) || (this.has.push(open), this.keys.push(blk))
          return txt === void 0 ? this : $.enabled ? run(this.keys, txt + '') : txt + ''
        }
        return txt === void 0 ? chain([open], [blk]) : $.enabled ? run([blk], txt + '') : txt + ''
      }
    }
    module2.exports = $
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/action.js
var require_action = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/action.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = (key, isSelect) => {
      if (key.meta && key.name !== 'escape') return
      if (key.ctrl) {
        if (key.name === 'a') return 'first'
        if (key.name === 'c') return 'abort'
        if (key.name === 'd') return 'abort'
        if (key.name === 'e') return 'last'
        if (key.name === 'g') return 'reset'
      }
      if (isSelect) {
        if (key.name === 'j') return 'down'
        if (key.name === 'k') return 'up'
      }
      if (key.name === 'return') return 'submit'
      if (key.name === 'enter') return 'submit'
      if (key.name === 'backspace') return 'delete'
      if (key.name === 'delete') return 'deleteForward'
      if (key.name === 'abort') return 'abort'
      if (key.name === 'escape') return 'exit'
      if (key.name === 'tab') return 'next'
      if (key.name === 'pagedown') return 'nextPage'
      if (key.name === 'pageup') return 'prevPage'
      if (key.name === 'home') return 'home'
      if (key.name === 'end') return 'end'
      if (key.name === 'up') return 'up'
      if (key.name === 'down') return 'down'
      if (key.name === 'right') return 'right'
      if (key.name === 'left') return 'left'
      return false
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/strip.js
var require_strip = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/strip.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = (str) => {
      const pattern = [
        '[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)',
        '(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PRZcf-ntqry=><~]))'
      ].join('|')
      const RGX = new RegExp(pattern, 'g')
      return typeof str === 'string' ? str.replace(RGX, '') : str
    }
  }
})

// ../../node_modules/.pnpm/sisteransi@1.0.5/node_modules/sisteransi/src/index.js
var require_src = __commonJS({
  '../../node_modules/.pnpm/sisteransi@1.0.5/node_modules/sisteransi/src/index.js'(
    exports,
    module2
  ) {
    'use strict'
    var ESC = '\x1B'
    var CSI = `${ESC}[`
    var beep = '\x07'
    var cursor = {
      to(x, y) {
        if (!y) return `${CSI}${x + 1}G`
        return `${CSI}${y + 1};${x + 1}H`
      },
      move(x, y) {
        let ret = ''
        if (x < 0) ret += `${CSI}${-x}D`
        else if (x > 0) ret += `${CSI}${x}C`
        if (y < 0) ret += `${CSI}${-y}A`
        else if (y > 0) ret += `${CSI}${y}B`
        return ret
      },
      up: (count = 1) => `${CSI}${count}A`,
      down: (count = 1) => `${CSI}${count}B`,
      forward: (count = 1) => `${CSI}${count}C`,
      backward: (count = 1) => `${CSI}${count}D`,
      nextLine: (count = 1) => `${CSI}E`.repeat(count),
      prevLine: (count = 1) => `${CSI}F`.repeat(count),
      left: `${CSI}G`,
      hide: `${CSI}?25l`,
      show: `${CSI}?25h`,
      save: `${ESC}7`,
      restore: `${ESC}8`
    }
    var scroll = {
      up: (count = 1) => `${CSI}S`.repeat(count),
      down: (count = 1) => `${CSI}T`.repeat(count)
    }
    var erase = {
      screen: `${CSI}2J`,
      up: (count = 1) => `${CSI}1J`.repeat(count),
      down: (count = 1) => `${CSI}J`.repeat(count),
      line: `${CSI}2K`,
      lineEnd: `${CSI}K`,
      lineStart: `${CSI}1K`,
      lines(count) {
        let clear = ''
        for (let i = 0; i < count; i++) clear += this.line + (i < count - 1 ? cursor.up() : '')
        if (count) clear += cursor.left
        return clear
      }
    }
    module2.exports = { cursor, scroll, erase, beep }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/clear.js
var require_clear = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/clear.js'(
    exports,
    module2
  ) {
    'use strict'
    function _createForOfIteratorHelper(o, allowArrayLike) {
      var it = (typeof Symbol !== 'undefined' && o[Symbol.iterator]) || o['@@iterator']
      if (!it) {
        if (
          Array.isArray(o) ||
          (it = _unsupportedIterableToArray(o)) ||
          (allowArrayLike && o && typeof o.length === 'number')
        ) {
          if (it) o = it
          var i = 0
          var F = function F2() {}
          return {
            s: F,
            n: function n() {
              if (i >= o.length) return { done: true }
              return { done: false, value: o[i++] }
            },
            e: function e(_e) {
              throw _e
            },
            f: F
          }
        }
        throw new TypeError(
          'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
        )
      }
      var normalCompletion = true,
        didErr = false,
        err
      return {
        s: function s() {
          it = it.call(o)
        },
        n: function n() {
          var step = it.next()
          normalCompletion = step.done
          return step
        },
        e: function e(_e2) {
          didErr = true
          err = _e2
        },
        f: function f() {
          try {
            if (!normalCompletion && it.return != null) it.return()
          } finally {
            if (didErr) throw err
          }
        }
      }
    }
    function _unsupportedIterableToArray(o, minLen) {
      if (!o) return
      if (typeof o === 'string') return _arrayLikeToArray(o, minLen)
      var n = Object.prototype.toString.call(o).slice(8, -1)
      if (n === 'Object' && o.constructor) n = o.constructor.name
      if (n === 'Map' || n === 'Set') return Array.from(o)
      if (n === 'Arguments' || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))
        return _arrayLikeToArray(o, minLen)
    }
    function _arrayLikeToArray(arr, len) {
      if (len == null || len > arr.length) len = arr.length
      for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]
      return arr2
    }
    var strip = require_strip()
    var _require = require_src()
    var erase = _require.erase
    var cursor = _require.cursor
    var width = (str) => [...strip(str)].length
    module2.exports = function (prompt, perLine) {
      if (!perLine) return erase.line + cursor.to(0)
      let rows = 0
      const lines = prompt.split(/\r?\n/)
      var _iterator = _createForOfIteratorHelper(lines),
        _step
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done; ) {
          let line = _step.value
          rows += 1 + Math.floor(Math.max(width(line) - 1, 0) / perLine)
        }
      } catch (err) {
        _iterator.e(err)
      } finally {
        _iterator.f()
      }
      return erase.lines(rows)
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/figures.js
var require_figures = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/figures.js'(
    exports,
    module2
  ) {
    'use strict'
    var main = {
      arrowUp: '\u2191',
      arrowDown: '\u2193',
      arrowLeft: '\u2190',
      arrowRight: '\u2192',
      radioOn: '\u25C9',
      radioOff: '\u25EF',
      tick: '\u2714',
      cross: '\u2716',
      ellipsis: '\u2026',
      pointerSmall: '\u203A',
      line: '\u2500',
      pointer: '\u276F'
    }
    var win = {
      arrowUp: main.arrowUp,
      arrowDown: main.arrowDown,
      arrowLeft: main.arrowLeft,
      arrowRight: main.arrowRight,
      radioOn: '(*)',
      radioOff: '( )',
      tick: '\u221A',
      cross: '\xD7',
      ellipsis: '...',
      pointerSmall: '\xBB',
      line: '\u2500',
      pointer: '>'
    }
    var figures = process.platform === 'win32' ? win : main
    module2.exports = figures
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/style.js
var require_style = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/style.js'(
    exports,
    module2
  ) {
    'use strict'
    var c = require_kleur()
    var figures = require_figures()
    var styles = Object.freeze({
      password: {
        scale: 1,
        render: (input) => '*'.repeat(input.length)
      },
      emoji: {
        scale: 2,
        render: (input) => '\u{1F603}'.repeat(input.length)
      },
      invisible: {
        scale: 0,
        render: (input) => ''
      },
      default: {
        scale: 1,
        render: (input) => `${input}`
      }
    })
    var render = (type) => styles[type] || styles.default
    var symbols = Object.freeze({
      aborted: c.red(figures.cross),
      done: c.green(figures.tick),
      exited: c.yellow(figures.cross),
      default: c.cyan('?')
    })
    var symbol = (done, aborted, exited) =>
      aborted ? symbols.aborted : exited ? symbols.exited : done ? symbols.done : symbols.default
    var delimiter = (completing) => c.gray(completing ? figures.ellipsis : figures.pointerSmall)
    var item = (expandable, expanded) =>
      c.gray(expandable ? (expanded ? figures.pointerSmall : '+') : figures.line)
    module2.exports = {
      styles,
      render,
      symbols,
      symbol,
      delimiter,
      item
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/lines.js
var require_lines = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/lines.js'(
    exports,
    module2
  ) {
    'use strict'
    var strip = require_strip()
    module2.exports = function (msg, perLine) {
      let lines = String(strip(msg) || '').split(/\r?\n/)
      if (!perLine) return lines.length
      return lines.map((l) => Math.ceil(l.length / perLine)).reduce((a, b) => a + b)
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/wrap.js
var require_wrap = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/wrap.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = (msg, opts = {}) => {
      const tab = Number.isSafeInteger(parseInt(opts.margin))
        ? new Array(parseInt(opts.margin)).fill(' ').join('')
        : opts.margin || ''
      const width = opts.width
      return (msg || '')
        .split(/\r?\n/g)
        .map((line) =>
          line
            .split(/\s+/g)
            .reduce(
              (arr, w) => {
                if (
                  w.length + tab.length >= width ||
                  arr[arr.length - 1].length + w.length + 1 < width
                )
                  arr[arr.length - 1] += ` ${w}`
                else arr.push(`${tab}${w}`)
                return arr
              },
              [tab]
            )
            .join('\n')
        )
        .join('\n')
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/entriesToDisplay.js
var require_entriesToDisplay = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/entriesToDisplay.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = (cursor, total, maxVisible) => {
      maxVisible = maxVisible || total
      let startIndex = Math.min(total - maxVisible, cursor - Math.floor(maxVisible / 2))
      if (startIndex < 0) startIndex = 0
      let endIndex = Math.min(startIndex + maxVisible, total)
      return {
        startIndex,
        endIndex
      }
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/index.js
var require_util = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/util/index.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = {
      action: require_action(),
      clear: require_clear(),
      style: require_style(),
      strip: require_strip(),
      figures: require_figures(),
      lines: require_lines(),
      wrap: require_wrap(),
      entriesToDisplay: require_entriesToDisplay()
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/prompt.js
var require_prompt = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/prompt.js'(
    exports,
    module2
  ) {
    'use strict'
    var readline = require('readline')
    var _require = require_util()
    var action = _require.action
    var EventEmitter = require('events')
    var _require2 = require_src()
    var beep = _require2.beep
    var cursor = _require2.cursor
    var color = require_kleur()
    var Prompt = class extends EventEmitter {
      constructor(opts = {}) {
        super()
        this.firstRender = true
        this.in = opts.stdin || process.stdin
        this.out = opts.stdout || process.stdout
        this.onRender = (opts.onRender || (() => void 0)).bind(this)
        const rl = readline.createInterface({
          input: this.in,
          escapeCodeTimeout: 50
        })
        readline.emitKeypressEvents(this.in, rl)
        if (this.in.isTTY) this.in.setRawMode(true)
        const isSelect = ['SelectPrompt', 'MultiselectPrompt'].indexOf(this.constructor.name) > -1
        const keypress = (str, key) => {
          let a = action(key, isSelect)
          if (a === false) {
            this._ && this._(str, key)
          } else if (typeof this[a] === 'function') {
            this[a](key)
          } else {
            this.bell()
          }
        }
        this.close = () => {
          this.out.write(cursor.show)
          this.in.removeListener('keypress', keypress)
          if (this.in.isTTY) this.in.setRawMode(false)
          rl.close()
          this.emit(this.aborted ? 'abort' : this.exited ? 'exit' : 'submit', this.value)
          this.closed = true
        }
        this.in.on('keypress', keypress)
      }
      fire() {
        this.emit('state', {
          value: this.value,
          aborted: !!this.aborted,
          exited: !!this.exited
        })
      }
      bell() {
        this.out.write(beep)
      }
      render() {
        this.onRender(color)
        if (this.firstRender) this.firstRender = false
      }
    }
    module2.exports = Prompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/text.js
var require_text = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/text.js'(
    exports,
    module2
  ) {
    'use strict'
    function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg)
        var value = info.value
      } catch (error) {
        reject(error)
        return
      }
      if (info.done) {
        resolve(value)
      } else {
        Promise.resolve(value).then(_next, _throw)
      }
    }
    function _asyncToGenerator(fn) {
      return function () {
        var self2 = this,
          args = arguments
        return new Promise(function (resolve, reject) {
          var gen = fn.apply(self2, args)
          function _next(value) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'next', value)
          }
          function _throw(err) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'throw', err)
          }
          _next(void 0)
        })
      }
    }
    var color = require_kleur()
    var Prompt = require_prompt()
    var _require = require_src()
    var erase = _require.erase
    var cursor = _require.cursor
    var _require2 = require_util()
    var style = _require2.style
    var clear = _require2.clear
    var lines = _require2.lines
    var figures = _require2.figures
    var TextPrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.transform = style.render(opts.style)
        this.scale = this.transform.scale
        this.msg = opts.message
        this.initial = opts.initial || ``
        this.validator = opts.validate || (() => true)
        this.value = ``
        this.errorMsg = opts.error || `Please Enter A Valid Value`
        this.cursor = Number(!!this.initial)
        this.cursorOffset = 0
        this.clear = clear(``, this.out.columns)
        this.render()
      }
      set value(v) {
        if (!v && this.initial) {
          this.placeholder = true
          this.rendered = color.gray(this.transform.render(this.initial))
        } else {
          this.placeholder = false
          this.rendered = this.transform.render(v)
        }
        this._value = v
        this.fire()
      }
      get value() {
        return this._value
      }
      reset() {
        this.value = ``
        this.cursor = Number(!!this.initial)
        this.cursorOffset = 0
        this.fire()
        this.render()
      }
      exit() {
        this.abort()
      }
      abort() {
        this.value = this.value || this.initial
        this.done = this.aborted = true
        this.error = false
        this.red = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      validate() {
        var _this = this
        return _asyncToGenerator(function* () {
          let valid = yield _this.validator(_this.value)
          if (typeof valid === `string`) {
            _this.errorMsg = valid
            valid = false
          }
          _this.error = !valid
        })()
      }
      submit() {
        var _this2 = this
        return _asyncToGenerator(function* () {
          _this2.value = _this2.value || _this2.initial
          _this2.cursorOffset = 0
          _this2.cursor = _this2.rendered.length
          yield _this2.validate()
          if (_this2.error) {
            _this2.red = true
            _this2.fire()
            _this2.render()
            return
          }
          _this2.done = true
          _this2.aborted = false
          _this2.fire()
          _this2.render()
          _this2.out.write('\n')
          _this2.close()
        })()
      }
      next() {
        if (!this.placeholder) return this.bell()
        this.value = this.initial
        this.cursor = this.rendered.length
        this.fire()
        this.render()
      }
      moveCursor(n) {
        if (this.placeholder) return
        this.cursor = this.cursor + n
        this.cursorOffset += n
      }
      _(c, key) {
        let s1 = this.value.slice(0, this.cursor)
        let s2 = this.value.slice(this.cursor)
        this.value = `${s1}${c}${s2}`
        this.red = false
        this.cursor = this.placeholder ? 0 : s1.length + 1
        this.render()
      }
      delete() {
        if (this.isCursorAtStart()) return this.bell()
        let s1 = this.value.slice(0, this.cursor - 1)
        let s2 = this.value.slice(this.cursor)
        this.value = `${s1}${s2}`
        this.red = false
        if (this.isCursorAtStart()) {
          this.cursorOffset = 0
        } else {
          this.cursorOffset++
          this.moveCursor(-1)
        }
        this.render()
      }
      deleteForward() {
        if (this.cursor * this.scale >= this.rendered.length || this.placeholder) return this.bell()
        let s1 = this.value.slice(0, this.cursor)
        let s2 = this.value.slice(this.cursor + 1)
        this.value = `${s1}${s2}`
        this.red = false
        if (this.isCursorAtEnd()) {
          this.cursorOffset = 0
        } else {
          this.cursorOffset++
        }
        this.render()
      }
      first() {
        this.cursor = 0
        this.render()
      }
      last() {
        this.cursor = this.value.length
        this.render()
      }
      left() {
        if (this.cursor <= 0 || this.placeholder) return this.bell()
        this.moveCursor(-1)
        this.render()
      }
      right() {
        if (this.cursor * this.scale >= this.rendered.length || this.placeholder) return this.bell()
        this.moveCursor(1)
        this.render()
      }
      isCursorAtStart() {
        return this.cursor === 0 || (this.placeholder && this.cursor === 1)
      }
      isCursorAtEnd() {
        return (
          this.cursor === this.rendered.length ||
          (this.placeholder && this.cursor === this.rendered.length + 1)
        )
      }
      render() {
        if (this.closed) return
        if (!this.firstRender) {
          if (this.outputError)
            this.out.write(
              cursor.down(lines(this.outputError, this.out.columns) - 1) +
                clear(this.outputError, this.out.columns)
            )
          this.out.write(clear(this.outputText, this.out.columns))
        }
        super.render()
        this.outputError = ''
        this.outputText = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(this.done),
          this.red ? color.red(this.rendered) : this.rendered
        ].join(` `)
        if (this.error) {
          this.outputError += this.errorMsg
            .split(
              `
`
            )
            .reduce(
              (a, l, i) =>
                a +
                `
${i ? ' ' : figures.pointerSmall} ${color.red().italic(l)}`,
              ``
            )
        }
        this.out.write(
          erase.line +
            cursor.to(0) +
            this.outputText +
            cursor.save +
            this.outputError +
            cursor.restore +
            cursor.move(this.cursorOffset, 0)
        )
      }
    }
    module2.exports = TextPrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/select.js
var require_select = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/select.js'(
    exports,
    module2
  ) {
    'use strict'
    var color = require_kleur()
    var Prompt = require_prompt()
    var _require = require_util()
    var style = _require.style
    var clear = _require.clear
    var figures = _require.figures
    var wrap = _require.wrap
    var entriesToDisplay = _require.entriesToDisplay
    var _require2 = require_src()
    var cursor = _require2.cursor
    var SelectPrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.msg = opts.message
        this.hint = opts.hint || '- Use arrow-keys. Return to submit.'
        this.warn = opts.warn || '- This option is disabled'
        this.cursor = opts.initial || 0
        this.choices = opts.choices.map((ch, idx) => {
          if (typeof ch === 'string')
            ch = {
              title: ch,
              value: idx
            }
          return {
            title: ch && (ch.title || ch.value || ch),
            value: ch && (ch.value === void 0 ? idx : ch.value),
            description: ch && ch.description,
            selected: ch && ch.selected,
            disabled: ch && ch.disabled
          }
        })
        this.optionsPerPage = opts.optionsPerPage || 10
        this.value = (this.choices[this.cursor] || {}).value
        this.clear = clear('', this.out.columns)
        this.render()
      }
      moveCursor(n) {
        this.cursor = n
        this.value = this.choices[n].value
        this.fire()
      }
      reset() {
        this.moveCursor(0)
        this.fire()
        this.render()
      }
      exit() {
        this.abort()
      }
      abort() {
        this.done = this.aborted = true
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      submit() {
        if (!this.selection.disabled) {
          this.done = true
          this.aborted = false
          this.fire()
          this.render()
          this.out.write('\n')
          this.close()
        } else this.bell()
      }
      first() {
        this.moveCursor(0)
        this.render()
      }
      last() {
        this.moveCursor(this.choices.length - 1)
        this.render()
      }
      up() {
        if (this.cursor === 0) {
          this.moveCursor(this.choices.length - 1)
        } else {
          this.moveCursor(this.cursor - 1)
        }
        this.render()
      }
      down() {
        if (this.cursor === this.choices.length - 1) {
          this.moveCursor(0)
        } else {
          this.moveCursor(this.cursor + 1)
        }
        this.render()
      }
      next() {
        this.moveCursor((this.cursor + 1) % this.choices.length)
        this.render()
      }
      _(c, key) {
        if (c === ' ') return this.submit()
      }
      get selection() {
        return this.choices[this.cursor]
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        else this.out.write(clear(this.outputText, this.out.columns))
        super.render()
        let _entriesToDisplay = entriesToDisplay(
            this.cursor,
            this.choices.length,
            this.optionsPerPage
          ),
          startIndex = _entriesToDisplay.startIndex,
          endIndex = _entriesToDisplay.endIndex
        this.outputText = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(false),
          this.done
            ? this.selection.title
            : this.selection.disabled
            ? color.yellow(this.warn)
            : color.gray(this.hint)
        ].join(' ')
        if (!this.done) {
          this.outputText += '\n'
          for (let i = startIndex; i < endIndex; i++) {
            let title,
              prefix,
              desc = '',
              v = this.choices[i]
            if (i === startIndex && startIndex > 0) {
              prefix = figures.arrowUp
            } else if (i === endIndex - 1 && endIndex < this.choices.length) {
              prefix = figures.arrowDown
            } else {
              prefix = ' '
            }
            if (v.disabled) {
              title =
                this.cursor === i
                  ? color.gray().underline(v.title)
                  : color.strikethrough().gray(v.title)
              prefix =
                (this.cursor === i ? color.bold().gray(figures.pointer) + ' ' : '  ') + prefix
            } else {
              title = this.cursor === i ? color.cyan().underline(v.title) : v.title
              prefix = (this.cursor === i ? color.cyan(figures.pointer) + ' ' : '  ') + prefix
              if (v.description && this.cursor === i) {
                desc = ` - ${v.description}`
                if (
                  prefix.length + title.length + desc.length >= this.out.columns ||
                  v.description.split(/\r?\n/).length > 1
                ) {
                  desc =
                    '\n' +
                    wrap(v.description, {
                      margin: 3,
                      width: this.out.columns
                    })
                }
              }
            }
            this.outputText += `${prefix} ${title}${color.gray(desc)}
`
          }
        }
        this.out.write(this.outputText)
      }
    }
    module2.exports = SelectPrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/toggle.js
var require_toggle = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/toggle.js'(
    exports,
    module2
  ) {
    'use strict'
    var color = require_kleur()
    var Prompt = require_prompt()
    var _require = require_util()
    var style = _require.style
    var clear = _require.clear
    var _require2 = require_src()
    var cursor = _require2.cursor
    var erase = _require2.erase
    var TogglePrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.msg = opts.message
        this.value = !!opts.initial
        this.active = opts.active || 'on'
        this.inactive = opts.inactive || 'off'
        this.initialValue = this.value
        this.render()
      }
      reset() {
        this.value = this.initialValue
        this.fire()
        this.render()
      }
      exit() {
        this.abort()
      }
      abort() {
        this.done = this.aborted = true
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      submit() {
        this.done = true
        this.aborted = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      deactivate() {
        if (this.value === false) return this.bell()
        this.value = false
        this.render()
      }
      activate() {
        if (this.value === true) return this.bell()
        this.value = true
        this.render()
      }
      delete() {
        this.deactivate()
      }
      left() {
        this.deactivate()
      }
      right() {
        this.activate()
      }
      down() {
        this.deactivate()
      }
      up() {
        this.activate()
      }
      next() {
        this.value = !this.value
        this.fire()
        this.render()
      }
      _(c, key) {
        if (c === ' ') {
          this.value = !this.value
        } else if (c === '1') {
          this.value = true
        } else if (c === '0') {
          this.value = false
        } else return this.bell()
        this.render()
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        else this.out.write(clear(this.outputText, this.out.columns))
        super.render()
        this.outputText = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(this.done),
          this.value ? this.inactive : color.cyan().underline(this.inactive),
          color.gray('/'),
          this.value ? color.cyan().underline(this.active) : this.active
        ].join(' ')
        this.out.write(erase.line + cursor.to(0) + this.outputText)
      }
    }
    module2.exports = TogglePrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/datepart.js
var require_datepart = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/datepart.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = class {
      constructor({ token, date, parts, locales }) {
        this.token = token
        this.date = date || new Date()
        this.parts = parts || [this]
        this.locales = locales || {}
      }
      up() {}
      down() {}
      next() {
        const currentIdx = this.parts.indexOf(this)
        return this.parts.find((part, idx) => idx > currentIdx && part instanceof DatePart)
      }
      setTo(val) {}
      prev() {
        let parts = [].concat(this.parts).reverse()
        const currentIdx = parts.indexOf(this)
        return parts.find((part, idx) => idx > currentIdx && part instanceof DatePart)
      }
      toString() {
        return String(this.date)
      }
    }
    module2.exports = DatePart
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/meridiem.js
var require_meridiem = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/meridiem.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart()
    var Meridiem = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setHours((this.date.getHours() + 12) % 24)
      }
      down() {
        this.up()
      }
      toString() {
        let meridiem = this.date.getHours() > 12 ? 'pm' : 'am'
        return /\A/.test(this.token) ? meridiem.toUpperCase() : meridiem
      }
    }
    module2.exports = Meridiem
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/day.js
var require_day = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/day.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart()
    var pos = (n) => {
      n = n % 10
      return n === 1 ? 'st' : n === 2 ? 'nd' : n === 3 ? 'rd' : 'th'
    }
    var Day = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setDate(this.date.getDate() + 1)
      }
      down() {
        this.date.setDate(this.date.getDate() - 1)
      }
      setTo(val) {
        this.date.setDate(parseInt(val.substr(-2)))
      }
      toString() {
        let date = this.date.getDate()
        let day = this.date.getDay()
        return this.token === 'DD'
          ? String(date).padStart(2, '0')
          : this.token === 'Do'
          ? date + pos(date)
          : this.token === 'd'
          ? day + 1
          : this.token === 'ddd'
          ? this.locales.weekdaysShort[day]
          : this.token === 'dddd'
          ? this.locales.weekdays[day]
          : date
      }
    }
    module2.exports = Day
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/hours.js
var require_hours = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/hours.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart()
    var Hours = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setHours(this.date.getHours() + 1)
      }
      down() {
        this.date.setHours(this.date.getHours() - 1)
      }
      setTo(val) {
        this.date.setHours(parseInt(val.substr(-2)))
      }
      toString() {
        let hours = this.date.getHours()
        if (/h/.test(this.token)) hours = hours % 12 || 12
        return this.token.length > 1 ? String(hours).padStart(2, '0') : hours
      }
    }
    module2.exports = Hours
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/milliseconds.js
var require_milliseconds = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/milliseconds.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart()
    var Milliseconds = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setMilliseconds(this.date.getMilliseconds() + 1)
      }
      down() {
        this.date.setMilliseconds(this.date.getMilliseconds() - 1)
      }
      setTo(val) {
        this.date.setMilliseconds(parseInt(val.substr(-this.token.length)))
      }
      toString() {
        return String(this.date.getMilliseconds()).padStart(4, '0').substr(0, this.token.length)
      }
    }
    module2.exports = Milliseconds
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/minutes.js
var require_minutes = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/minutes.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart()
    var Minutes = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setMinutes(this.date.getMinutes() + 1)
      }
      down() {
        this.date.setMinutes(this.date.getMinutes() - 1)
      }
      setTo(val) {
        this.date.setMinutes(parseInt(val.substr(-2)))
      }
      toString() {
        let m = this.date.getMinutes()
        return this.token.length > 1 ? String(m).padStart(2, '0') : m
      }
    }
    module2.exports = Minutes
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/month.js
var require_month = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/month.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart()
    var Month = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setMonth(this.date.getMonth() + 1)
      }
      down() {
        this.date.setMonth(this.date.getMonth() - 1)
      }
      setTo(val) {
        val = parseInt(val.substr(-2)) - 1
        this.date.setMonth(val < 0 ? 0 : val)
      }
      toString() {
        let month = this.date.getMonth()
        let tl = this.token.length
        return tl === 2
          ? String(month + 1).padStart(2, '0')
          : tl === 3
          ? this.locales.monthsShort[month]
          : tl === 4
          ? this.locales.months[month]
          : String(month + 1)
      }
    }
    module2.exports = Month
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/seconds.js
var require_seconds = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/seconds.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart()
    var Seconds = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setSeconds(this.date.getSeconds() + 1)
      }
      down() {
        this.date.setSeconds(this.date.getSeconds() - 1)
      }
      setTo(val) {
        this.date.setSeconds(parseInt(val.substr(-2)))
      }
      toString() {
        let s = this.date.getSeconds()
        return this.token.length > 1 ? String(s).padStart(2, '0') : s
      }
    }
    module2.exports = Seconds
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/year.js
var require_year = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/year.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart()
    var Year = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setFullYear(this.date.getFullYear() + 1)
      }
      down() {
        this.date.setFullYear(this.date.getFullYear() - 1)
      }
      setTo(val) {
        this.date.setFullYear(val.substr(-4))
      }
      toString() {
        let year = String(this.date.getFullYear()).padStart(4, '0')
        return this.token.length === 2 ? year.substr(-2) : year
      }
    }
    module2.exports = Year
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/index.js
var require_dateparts = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/dateparts/index.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = {
      DatePart: require_datepart(),
      Meridiem: require_meridiem(),
      Day: require_day(),
      Hours: require_hours(),
      Milliseconds: require_milliseconds(),
      Minutes: require_minutes(),
      Month: require_month(),
      Seconds: require_seconds(),
      Year: require_year()
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/date.js
var require_date = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/date.js'(
    exports,
    module2
  ) {
    'use strict'
    function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg)
        var value = info.value
      } catch (error) {
        reject(error)
        return
      }
      if (info.done) {
        resolve(value)
      } else {
        Promise.resolve(value).then(_next, _throw)
      }
    }
    function _asyncToGenerator(fn) {
      return function () {
        var self2 = this,
          args = arguments
        return new Promise(function (resolve, reject) {
          var gen = fn.apply(self2, args)
          function _next(value) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'next', value)
          }
          function _throw(err) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'throw', err)
          }
          _next(void 0)
        })
      }
    }
    var color = require_kleur()
    var Prompt = require_prompt()
    var _require = require_util()
    var style = _require.style
    var clear = _require.clear
    var figures = _require.figures
    var _require2 = require_src()
    var erase = _require2.erase
    var cursor = _require2.cursor
    var _require3 = require_dateparts()
    var DatePart = _require3.DatePart
    var Meridiem = _require3.Meridiem
    var Day = _require3.Day
    var Hours = _require3.Hours
    var Milliseconds = _require3.Milliseconds
    var Minutes = _require3.Minutes
    var Month = _require3.Month
    var Seconds = _require3.Seconds
    var Year = _require3.Year
    var regex =
      /\\(.)|"((?:\\["\\]|[^"])+)"|(D[Do]?|d{3,4}|d)|(M{1,4})|(YY(?:YY)?)|([aA])|([Hh]{1,2})|(m{1,2})|(s{1,2})|(S{1,4})|./g
    var regexGroups = {
      1: ({ token }) => token.replace(/\\(.)/g, '$1'),
      2: (opts) => new Day(opts),
      3: (opts) => new Month(opts),
      4: (opts) => new Year(opts),
      5: (opts) => new Meridiem(opts),
      6: (opts) => new Hours(opts),
      7: (opts) => new Minutes(opts),
      8: (opts) => new Seconds(opts),
      9: (opts) => new Milliseconds(opts)
    }
    var dfltLocales = {
      months:
        'January,February,March,April,May,June,July,August,September,October,November,December'.split(
          ','
        ),
      monthsShort: 'Jan,Feb,Mar,Apr,May,Jun,Jul,Aug,Sep,Oct,Nov,Dec'.split(','),
      weekdays: 'Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday'.split(','),
      weekdaysShort: 'Sun,Mon,Tue,Wed,Thu,Fri,Sat'.split(',')
    }
    var DatePrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.msg = opts.message
        this.cursor = 0
        this.typed = ''
        this.locales = Object.assign(dfltLocales, opts.locales)
        this._date = opts.initial || new Date()
        this.errorMsg = opts.error || 'Please Enter A Valid Value'
        this.validator = opts.validate || (() => true)
        this.mask = opts.mask || 'YYYY-MM-DD HH:mm:ss'
        this.clear = clear('', this.out.columns)
        this.render()
      }
      get value() {
        return this.date
      }
      get date() {
        return this._date
      }
      set date(date) {
        if (date) this._date.setTime(date.getTime())
      }
      set mask(mask) {
        let result
        this.parts = []
        while ((result = regex.exec(mask))) {
          let match = result.shift()
          let idx = result.findIndex((gr) => gr != null)
          this.parts.push(
            idx in regexGroups
              ? regexGroups[idx]({
                  token: result[idx] || match,
                  date: this.date,
                  parts: this.parts,
                  locales: this.locales
                })
              : result[idx] || match
          )
        }
        let parts = this.parts.reduce((arr, i) => {
          if (typeof i === 'string' && typeof arr[arr.length - 1] === 'string')
            arr[arr.length - 1] += i
          else arr.push(i)
          return arr
        }, [])
        this.parts.splice(0)
        this.parts.push(...parts)
        this.reset()
      }
      moveCursor(n) {
        this.typed = ''
        this.cursor = n
        this.fire()
      }
      reset() {
        this.moveCursor(this.parts.findIndex((p) => p instanceof DatePart))
        this.fire()
        this.render()
      }
      exit() {
        this.abort()
      }
      abort() {
        this.done = this.aborted = true
        this.error = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      validate() {
        var _this = this
        return _asyncToGenerator(function* () {
          let valid = yield _this.validator(_this.value)
          if (typeof valid === 'string') {
            _this.errorMsg = valid
            valid = false
          }
          _this.error = !valid
        })()
      }
      submit() {
        var _this2 = this
        return _asyncToGenerator(function* () {
          yield _this2.validate()
          if (_this2.error) {
            _this2.color = 'red'
            _this2.fire()
            _this2.render()
            return
          }
          _this2.done = true
          _this2.aborted = false
          _this2.fire()
          _this2.render()
          _this2.out.write('\n')
          _this2.close()
        })()
      }
      up() {
        this.typed = ''
        this.parts[this.cursor].up()
        this.render()
      }
      down() {
        this.typed = ''
        this.parts[this.cursor].down()
        this.render()
      }
      left() {
        let prev = this.parts[this.cursor].prev()
        if (prev == null) return this.bell()
        this.moveCursor(this.parts.indexOf(prev))
        this.render()
      }
      right() {
        let next = this.parts[this.cursor].next()
        if (next == null) return this.bell()
        this.moveCursor(this.parts.indexOf(next))
        this.render()
      }
      next() {
        let next = this.parts[this.cursor].next()
        this.moveCursor(
          next ? this.parts.indexOf(next) : this.parts.findIndex((part) => part instanceof DatePart)
        )
        this.render()
      }
      _(c) {
        if (/\d/.test(c)) {
          this.typed += c
          this.parts[this.cursor].setTo(this.typed)
          this.render()
        }
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        else this.out.write(clear(this.outputText, this.out.columns))
        super.render()
        this.outputText = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(false),
          this.parts
            .reduce(
              (arr, p, idx) =>
                arr.concat(
                  idx === this.cursor && !this.done ? color.cyan().underline(p.toString()) : p
                ),
              []
            )
            .join('')
        ].join(' ')
        if (this.error) {
          this.outputText += this.errorMsg.split('\n').reduce(
            (a, l, i) =>
              a +
              `
${i ? ` ` : figures.pointerSmall} ${color.red().italic(l)}`,
            ``
          )
        }
        this.out.write(erase.line + cursor.to(0) + this.outputText)
      }
    }
    module2.exports = DatePrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/number.js
var require_number = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/number.js'(
    exports,
    module2
  ) {
    'use strict'
    function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg)
        var value = info.value
      } catch (error) {
        reject(error)
        return
      }
      if (info.done) {
        resolve(value)
      } else {
        Promise.resolve(value).then(_next, _throw)
      }
    }
    function _asyncToGenerator(fn) {
      return function () {
        var self2 = this,
          args = arguments
        return new Promise(function (resolve, reject) {
          var gen = fn.apply(self2, args)
          function _next(value) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'next', value)
          }
          function _throw(err) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'throw', err)
          }
          _next(void 0)
        })
      }
    }
    var color = require_kleur()
    var Prompt = require_prompt()
    var _require = require_src()
    var cursor = _require.cursor
    var erase = _require.erase
    var _require2 = require_util()
    var style = _require2.style
    var figures = _require2.figures
    var clear = _require2.clear
    var lines = _require2.lines
    var isNumber = /[0-9]/
    var isDef = (any) => any !== void 0
    var round = (number, precision) => {
      let factor = Math.pow(10, precision)
      return Math.round(number * factor) / factor
    }
    var NumberPrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.transform = style.render(opts.style)
        this.msg = opts.message
        this.initial = isDef(opts.initial) ? opts.initial : ''
        this.float = !!opts.float
        this.round = opts.round || 2
        this.inc = opts.increment || 1
        this.min = isDef(opts.min) ? opts.min : -Infinity
        this.max = isDef(opts.max) ? opts.max : Infinity
        this.errorMsg = opts.error || `Please Enter A Valid Value`
        this.validator = opts.validate || (() => true)
        this.color = `cyan`
        this.value = ``
        this.typed = ``
        this.lastHit = 0
        this.render()
      }
      set value(v) {
        if (!v && v !== 0) {
          this.placeholder = true
          this.rendered = color.gray(this.transform.render(`${this.initial}`))
          this._value = ``
        } else {
          this.placeholder = false
          this.rendered = this.transform.render(`${round(v, this.round)}`)
          this._value = round(v, this.round)
        }
        this.fire()
      }
      get value() {
        return this._value
      }
      parse(x) {
        return this.float ? parseFloat(x) : parseInt(x)
      }
      valid(c) {
        return c === `-` || (c === `.` && this.float) || isNumber.test(c)
      }
      reset() {
        this.typed = ``
        this.value = ``
        this.fire()
        this.render()
      }
      exit() {
        this.abort()
      }
      abort() {
        let x = this.value
        this.value = x !== `` ? x : this.initial
        this.done = this.aborted = true
        this.error = false
        this.fire()
        this.render()
        this.out.write(`
`)
        this.close()
      }
      validate() {
        var _this = this
        return _asyncToGenerator(function* () {
          let valid = yield _this.validator(_this.value)
          if (typeof valid === `string`) {
            _this.errorMsg = valid
            valid = false
          }
          _this.error = !valid
        })()
      }
      submit() {
        var _this2 = this
        return _asyncToGenerator(function* () {
          yield _this2.validate()
          if (_this2.error) {
            _this2.color = `red`
            _this2.fire()
            _this2.render()
            return
          }
          let x = _this2.value
          _this2.value = x !== `` ? x : _this2.initial
          _this2.done = true
          _this2.aborted = false
          _this2.error = false
          _this2.fire()
          _this2.render()
          _this2.out.write(`
`)
          _this2.close()
        })()
      }
      up() {
        this.typed = ``
        if (this.value === '') {
          this.value = this.min - this.inc
        }
        if (this.value >= this.max) return this.bell()
        this.value += this.inc
        this.color = `cyan`
        this.fire()
        this.render()
      }
      down() {
        this.typed = ``
        if (this.value === '') {
          this.value = this.min + this.inc
        }
        if (this.value <= this.min) return this.bell()
        this.value -= this.inc
        this.color = `cyan`
        this.fire()
        this.render()
      }
      delete() {
        let val = this.value.toString()
        if (val.length === 0) return this.bell()
        this.value = this.parse((val = val.slice(0, -1))) || ``
        if (this.value !== '' && this.value < this.min) {
          this.value = this.min
        }
        this.color = `cyan`
        this.fire()
        this.render()
      }
      next() {
        this.value = this.initial
        this.fire()
        this.render()
      }
      _(c, key) {
        if (!this.valid(c)) return this.bell()
        const now = Date.now()
        if (now - this.lastHit > 1e3) this.typed = ``
        this.typed += c
        this.lastHit = now
        this.color = `cyan`
        if (c === `.`) return this.fire()
        this.value = Math.min(this.parse(this.typed), this.max)
        if (this.value > this.max) this.value = this.max
        if (this.value < this.min) this.value = this.min
        this.fire()
        this.render()
      }
      render() {
        if (this.closed) return
        if (!this.firstRender) {
          if (this.outputError)
            this.out.write(
              cursor.down(lines(this.outputError, this.out.columns) - 1) +
                clear(this.outputError, this.out.columns)
            )
          this.out.write(clear(this.outputText, this.out.columns))
        }
        super.render()
        this.outputError = ''
        this.outputText = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(this.done),
          !this.done || (!this.done && !this.placeholder)
            ? color[this.color]().underline(this.rendered)
            : this.rendered
        ].join(` `)
        if (this.error) {
          this.outputError += this.errorMsg
            .split(
              `
`
            )
            .reduce(
              (a, l, i) =>
                a +
                `
${i ? ` ` : figures.pointerSmall} ${color.red().italic(l)}`,
              ``
            )
        }
        this.out.write(
          erase.line +
            cursor.to(0) +
            this.outputText +
            cursor.save +
            this.outputError +
            cursor.restore
        )
      }
    }
    module2.exports = NumberPrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/multiselect.js
var require_multiselect = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/multiselect.js'(
    exports,
    module2
  ) {
    'use strict'
    var color = require_kleur()
    var _require = require_src()
    var cursor = _require.cursor
    var Prompt = require_prompt()
    var _require2 = require_util()
    var clear = _require2.clear
    var figures = _require2.figures
    var style = _require2.style
    var wrap = _require2.wrap
    var entriesToDisplay = _require2.entriesToDisplay
    var MultiselectPrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.msg = opts.message
        this.cursor = opts.cursor || 0
        this.scrollIndex = opts.cursor || 0
        this.hint = opts.hint || ''
        this.warn = opts.warn || '- This option is disabled -'
        this.minSelected = opts.min
        this.showMinError = false
        this.maxChoices = opts.max
        this.instructions = opts.instructions
        this.optionsPerPage = opts.optionsPerPage || 10
        this.value = opts.choices.map((ch, idx) => {
          if (typeof ch === 'string')
            ch = {
              title: ch,
              value: idx
            }
          return {
            title: ch && (ch.title || ch.value || ch),
            description: ch && ch.description,
            value: ch && (ch.value === void 0 ? idx : ch.value),
            selected: ch && ch.selected,
            disabled: ch && ch.disabled
          }
        })
        this.clear = clear('', this.out.columns)
        if (!opts.overrideRender) {
          this.render()
        }
      }
      reset() {
        this.value.map((v) => !v.selected)
        this.cursor = 0
        this.fire()
        this.render()
      }
      selected() {
        return this.value.filter((v) => v.selected)
      }
      exit() {
        this.abort()
      }
      abort() {
        this.done = this.aborted = true
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      submit() {
        const selected = this.value.filter((e) => e.selected)
        if (this.minSelected && selected.length < this.minSelected) {
          this.showMinError = true
          this.render()
        } else {
          this.done = true
          this.aborted = false
          this.fire()
          this.render()
          this.out.write('\n')
          this.close()
        }
      }
      first() {
        this.cursor = 0
        this.render()
      }
      last() {
        this.cursor = this.value.length - 1
        this.render()
      }
      next() {
        this.cursor = (this.cursor + 1) % this.value.length
        this.render()
      }
      up() {
        if (this.cursor === 0) {
          this.cursor = this.value.length - 1
        } else {
          this.cursor--
        }
        this.render()
      }
      down() {
        if (this.cursor === this.value.length - 1) {
          this.cursor = 0
        } else {
          this.cursor++
        }
        this.render()
      }
      left() {
        this.value[this.cursor].selected = false
        this.render()
      }
      right() {
        if (this.value.filter((e) => e.selected).length >= this.maxChoices) return this.bell()
        this.value[this.cursor].selected = true
        this.render()
      }
      handleSpaceToggle() {
        const v = this.value[this.cursor]
        if (v.selected) {
          v.selected = false
          this.render()
        } else if (v.disabled || this.value.filter((e) => e.selected).length >= this.maxChoices) {
          return this.bell()
        } else {
          v.selected = true
          this.render()
        }
      }
      toggleAll() {
        if (this.maxChoices !== void 0 || this.value[this.cursor].disabled) {
          return this.bell()
        }
        const newSelected = !this.value[this.cursor].selected
        this.value.filter((v) => !v.disabled).forEach((v) => (v.selected = newSelected))
        this.render()
      }
      _(c, key) {
        if (c === ' ') {
          this.handleSpaceToggle()
        } else if (c === 'a') {
          this.toggleAll()
        } else {
          return this.bell()
        }
      }
      renderInstructions() {
        if (this.instructions === void 0 || this.instructions) {
          if (typeof this.instructions === 'string') {
            return this.instructions
          }
          return (
            `
Instructions:
    ${figures.arrowUp}/${figures.arrowDown}: Highlight option
    ${figures.arrowLeft}/${figures.arrowRight}/[space]: Toggle selection
` +
            (this.maxChoices === void 0
              ? `    a: Toggle all
`
              : '') +
            `    enter/return: Complete answer`
          )
        }
        return ''
      }
      renderOption(cursor2, v, i, arrowIndicator) {
        const prefix =
          (v.selected ? color.green(figures.radioOn) : figures.radioOff) +
          ' ' +
          arrowIndicator +
          ' '
        let title, desc
        if (v.disabled) {
          title =
            cursor2 === i ? color.gray().underline(v.title) : color.strikethrough().gray(v.title)
        } else {
          title = cursor2 === i ? color.cyan().underline(v.title) : v.title
          if (cursor2 === i && v.description) {
            desc = ` - ${v.description}`
            if (
              prefix.length + title.length + desc.length >= this.out.columns ||
              v.description.split(/\r?\n/).length > 1
            ) {
              desc =
                '\n' +
                wrap(v.description, {
                  margin: prefix.length,
                  width: this.out.columns
                })
            }
          }
        }
        return prefix + title + color.gray(desc || '')
      }
      paginateOptions(options2) {
        if (options2.length === 0) {
          return color.red('No matches for this query.')
        }
        let _entriesToDisplay = entriesToDisplay(this.cursor, options2.length, this.optionsPerPage),
          startIndex = _entriesToDisplay.startIndex,
          endIndex = _entriesToDisplay.endIndex
        let prefix,
          styledOptions = []
        for (let i = startIndex; i < endIndex; i++) {
          if (i === startIndex && startIndex > 0) {
            prefix = figures.arrowUp
          } else if (i === endIndex - 1 && endIndex < options2.length) {
            prefix = figures.arrowDown
          } else {
            prefix = ' '
          }
          styledOptions.push(this.renderOption(this.cursor, options2[i], i, prefix))
        }
        return '\n' + styledOptions.join('\n')
      }
      renderOptions(options2) {
        if (!this.done) {
          return this.paginateOptions(options2)
        }
        return ''
      }
      renderDoneOrInstructions() {
        if (this.done) {
          return this.value
            .filter((e) => e.selected)
            .map((v) => v.title)
            .join(', ')
        }
        const output = [color.gray(this.hint), this.renderInstructions()]
        if (this.value[this.cursor].disabled) {
          output.push(color.yellow(this.warn))
        }
        return output.join(' ')
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        super.render()
        let prompt = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(false),
          this.renderDoneOrInstructions()
        ].join(' ')
        if (this.showMinError) {
          prompt += color.red(`You must select a minimum of ${this.minSelected} choices.`)
          this.showMinError = false
        }
        prompt += this.renderOptions(this.value)
        this.out.write(this.clear + prompt)
        this.clear = clear(prompt, this.out.columns)
      }
    }
    module2.exports = MultiselectPrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/autocomplete.js
var require_autocomplete = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/autocomplete.js'(
    exports,
    module2
  ) {
    'use strict'
    function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg)
        var value = info.value
      } catch (error) {
        reject(error)
        return
      }
      if (info.done) {
        resolve(value)
      } else {
        Promise.resolve(value).then(_next, _throw)
      }
    }
    function _asyncToGenerator(fn) {
      return function () {
        var self2 = this,
          args = arguments
        return new Promise(function (resolve, reject) {
          var gen = fn.apply(self2, args)
          function _next(value) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'next', value)
          }
          function _throw(err) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'throw', err)
          }
          _next(void 0)
        })
      }
    }
    var color = require_kleur()
    var Prompt = require_prompt()
    var _require = require_src()
    var erase = _require.erase
    var cursor = _require.cursor
    var _require2 = require_util()
    var style = _require2.style
    var clear = _require2.clear
    var figures = _require2.figures
    var wrap = _require2.wrap
    var entriesToDisplay = _require2.entriesToDisplay
    var getVal = (arr, i) => arr[i] && (arr[i].value || arr[i].title || arr[i])
    var getTitle = (arr, i) => arr[i] && (arr[i].title || arr[i].value || arr[i])
    var getIndex = (arr, valOrTitle) => {
      const index = arr.findIndex((el) => el.value === valOrTitle || el.title === valOrTitle)
      return index > -1 ? index : void 0
    }
    var AutocompletePrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.msg = opts.message
        this.suggest = opts.suggest
        this.choices = opts.choices
        this.initial =
          typeof opts.initial === 'number' ? opts.initial : getIndex(opts.choices, opts.initial)
        this.select = this.initial || opts.cursor || 0
        this.i18n = {
          noMatches: opts.noMatches || 'no matches found'
        }
        this.fallback = opts.fallback || this.initial
        this.clearFirst = opts.clearFirst || false
        this.suggestions = []
        this.input = ''
        this.limit = opts.limit || 10
        this.cursor = 0
        this.transform = style.render(opts.style)
        this.scale = this.transform.scale
        this.render = this.render.bind(this)
        this.complete = this.complete.bind(this)
        this.clear = clear('', this.out.columns)
        this.complete(this.render)
        this.render()
      }
      set fallback(fb) {
        this._fb = Number.isSafeInteger(parseInt(fb)) ? parseInt(fb) : fb
      }
      get fallback() {
        let choice
        if (typeof this._fb === 'number') choice = this.choices[this._fb]
        else if (typeof this._fb === 'string')
          choice = {
            title: this._fb
          }
        return (
          choice ||
          this._fb || {
            title: this.i18n.noMatches
          }
        )
      }
      moveSelect(i) {
        this.select = i
        if (this.suggestions.length > 0) this.value = getVal(this.suggestions, i)
        else this.value = this.fallback.value
        this.fire()
      }
      complete(cb) {
        var _this = this
        return _asyncToGenerator(function* () {
          const p = (_this.completing = _this.suggest(_this.input, _this.choices))
          const suggestions = yield p
          if (_this.completing !== p) return
          _this.suggestions = suggestions.map((s, i, arr) => ({
            title: getTitle(arr, i),
            value: getVal(arr, i),
            description: s.description
          }))
          _this.completing = false
          const l = Math.max(suggestions.length - 1, 0)
          _this.moveSelect(Math.min(l, _this.select))
          cb && cb()
        })()
      }
      reset() {
        this.input = ''
        this.complete(() => {
          this.moveSelect(this.initial !== void 0 ? this.initial : 0)
          this.render()
        })
        this.render()
      }
      exit() {
        if (this.clearFirst && this.input.length > 0) {
          this.reset()
        } else {
          this.done = this.exited = true
          this.aborted = false
          this.fire()
          this.render()
          this.out.write('\n')
          this.close()
        }
      }
      abort() {
        this.done = this.aborted = true
        this.exited = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      submit() {
        this.done = true
        this.aborted = this.exited = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      _(c, key) {
        let s1 = this.input.slice(0, this.cursor)
        let s2 = this.input.slice(this.cursor)
        this.input = `${s1}${c}${s2}`
        this.cursor = s1.length + 1
        this.complete(this.render)
        this.render()
      }
      delete() {
        if (this.cursor === 0) return this.bell()
        let s1 = this.input.slice(0, this.cursor - 1)
        let s2 = this.input.slice(this.cursor)
        this.input = `${s1}${s2}`
        this.complete(this.render)
        this.cursor = this.cursor - 1
        this.render()
      }
      deleteForward() {
        if (this.cursor * this.scale >= this.rendered.length) return this.bell()
        let s1 = this.input.slice(0, this.cursor)
        let s2 = this.input.slice(this.cursor + 1)
        this.input = `${s1}${s2}`
        this.complete(this.render)
        this.render()
      }
      first() {
        this.moveSelect(0)
        this.render()
      }
      last() {
        this.moveSelect(this.suggestions.length - 1)
        this.render()
      }
      up() {
        if (this.select === 0) {
          this.moveSelect(this.suggestions.length - 1)
        } else {
          this.moveSelect(this.select - 1)
        }
        this.render()
      }
      down() {
        if (this.select === this.suggestions.length - 1) {
          this.moveSelect(0)
        } else {
          this.moveSelect(this.select + 1)
        }
        this.render()
      }
      next() {
        if (this.select === this.suggestions.length - 1) {
          this.moveSelect(0)
        } else this.moveSelect(this.select + 1)
        this.render()
      }
      nextPage() {
        this.moveSelect(Math.min(this.select + this.limit, this.suggestions.length - 1))
        this.render()
      }
      prevPage() {
        this.moveSelect(Math.max(this.select - this.limit, 0))
        this.render()
      }
      left() {
        if (this.cursor <= 0) return this.bell()
        this.cursor = this.cursor - 1
        this.render()
      }
      right() {
        if (this.cursor * this.scale >= this.rendered.length) return this.bell()
        this.cursor = this.cursor + 1
        this.render()
      }
      renderOption(v, hovered, isStart, isEnd) {
        let desc
        let prefix = isStart ? figures.arrowUp : isEnd ? figures.arrowDown : ' '
        let title = hovered ? color.cyan().underline(v.title) : v.title
        prefix = (hovered ? color.cyan(figures.pointer) + ' ' : '  ') + prefix
        if (v.description) {
          desc = ` - ${v.description}`
          if (
            prefix.length + title.length + desc.length >= this.out.columns ||
            v.description.split(/\r?\n/).length > 1
          ) {
            desc =
              '\n' +
              wrap(v.description, {
                margin: 3,
                width: this.out.columns
              })
          }
        }
        return prefix + ' ' + title + color.gray(desc || '')
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        else this.out.write(clear(this.outputText, this.out.columns))
        super.render()
        let _entriesToDisplay = entriesToDisplay(this.select, this.choices.length, this.limit),
          startIndex = _entriesToDisplay.startIndex,
          endIndex = _entriesToDisplay.endIndex
        this.outputText = [
          style.symbol(this.done, this.aborted, this.exited),
          color.bold(this.msg),
          style.delimiter(this.completing),
          this.done && this.suggestions[this.select]
            ? this.suggestions[this.select].title
            : (this.rendered = this.transform.render(this.input))
        ].join(' ')
        if (!this.done) {
          const suggestions = this.suggestions
            .slice(startIndex, endIndex)
            .map((item, i) =>
              this.renderOption(
                item,
                this.select === i + startIndex,
                i === 0 && startIndex > 0,
                i + startIndex === endIndex - 1 && endIndex < this.choices.length
              )
            )
            .join('\n')
          this.outputText +=
            `
` + (suggestions || color.gray(this.fallback.title))
        }
        this.out.write(erase.line + cursor.to(0) + this.outputText)
      }
    }
    module2.exports = AutocompletePrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/autocompleteMultiselect.js
var require_autocompleteMultiselect = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/autocompleteMultiselect.js'(
    exports,
    module2
  ) {
    'use strict'
    var color = require_kleur()
    var _require = require_src()
    var cursor = _require.cursor
    var MultiselectPrompt = require_multiselect()
    var _require2 = require_util()
    var clear = _require2.clear
    var style = _require2.style
    var figures = _require2.figures
    var AutocompleteMultiselectPrompt = class extends MultiselectPrompt {
      constructor(opts = {}) {
        opts.overrideRender = true
        super(opts)
        this.inputValue = ''
        this.clear = clear('', this.out.columns)
        this.filteredOptions = this.value
        this.render()
      }
      last() {
        this.cursor = this.filteredOptions.length - 1
        this.render()
      }
      next() {
        this.cursor = (this.cursor + 1) % this.filteredOptions.length
        this.render()
      }
      up() {
        if (this.cursor === 0) {
          this.cursor = this.filteredOptions.length - 1
        } else {
          this.cursor--
        }
        this.render()
      }
      down() {
        if (this.cursor === this.filteredOptions.length - 1) {
          this.cursor = 0
        } else {
          this.cursor++
        }
        this.render()
      }
      left() {
        this.filteredOptions[this.cursor].selected = false
        this.render()
      }
      right() {
        if (this.value.filter((e) => e.selected).length >= this.maxChoices) return this.bell()
        this.filteredOptions[this.cursor].selected = true
        this.render()
      }
      delete() {
        if (this.inputValue.length) {
          this.inputValue = this.inputValue.substr(0, this.inputValue.length - 1)
          this.updateFilteredOptions()
        }
      }
      updateFilteredOptions() {
        const currentHighlight = this.filteredOptions[this.cursor]
        this.filteredOptions = this.value.filter((v) => {
          if (this.inputValue) {
            if (typeof v.title === 'string') {
              if (v.title.toLowerCase().includes(this.inputValue.toLowerCase())) {
                return true
              }
            }
            if (typeof v.value === 'string') {
              if (v.value.toLowerCase().includes(this.inputValue.toLowerCase())) {
                return true
              }
            }
            return false
          }
          return true
        })
        const newHighlightIndex = this.filteredOptions.findIndex((v) => v === currentHighlight)
        this.cursor = newHighlightIndex < 0 ? 0 : newHighlightIndex
        this.render()
      }
      handleSpaceToggle() {
        const v = this.filteredOptions[this.cursor]
        if (v.selected) {
          v.selected = false
          this.render()
        } else if (v.disabled || this.value.filter((e) => e.selected).length >= this.maxChoices) {
          return this.bell()
        } else {
          v.selected = true
          this.render()
        }
      }
      handleInputChange(c) {
        this.inputValue = this.inputValue + c
        this.updateFilteredOptions()
      }
      _(c, key) {
        if (c === ' ') {
          this.handleSpaceToggle()
        } else {
          this.handleInputChange(c)
        }
      }
      renderInstructions() {
        if (this.instructions === void 0 || this.instructions) {
          if (typeof this.instructions === 'string') {
            return this.instructions
          }
          return `
Instructions:
    ${figures.arrowUp}/${figures.arrowDown}: Highlight option
    ${figures.arrowLeft}/${figures.arrowRight}/[space]: Toggle selection
    [a,b,c]/delete: Filter choices
    enter/return: Complete answer
`
        }
        return ''
      }
      renderCurrentInput() {
        return `
Filtered results for: ${this.inputValue ? this.inputValue : color.gray('Enter something to filter')}
`
      }
      renderOption(cursor2, v, i) {
        let title
        if (v.disabled)
          title =
            cursor2 === i ? color.gray().underline(v.title) : color.strikethrough().gray(v.title)
        else title = cursor2 === i ? color.cyan().underline(v.title) : v.title
        return (v.selected ? color.green(figures.radioOn) : figures.radioOff) + '  ' + title
      }
      renderDoneOrInstructions() {
        if (this.done) {
          return this.value
            .filter((e) => e.selected)
            .map((v) => v.title)
            .join(', ')
        }
        const output = [color.gray(this.hint), this.renderInstructions(), this.renderCurrentInput()]
        if (this.filteredOptions.length && this.filteredOptions[this.cursor].disabled) {
          output.push(color.yellow(this.warn))
        }
        return output.join(' ')
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        super.render()
        let prompt = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(false),
          this.renderDoneOrInstructions()
        ].join(' ')
        if (this.showMinError) {
          prompt += color.red(`You must select a minimum of ${this.minSelected} choices.`)
          this.showMinError = false
        }
        prompt += this.renderOptions(this.filteredOptions)
        this.out.write(this.clear + prompt)
        this.clear = clear(prompt, this.out.columns)
      }
    }
    module2.exports = AutocompleteMultiselectPrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/confirm.js
var require_confirm = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/confirm.js'(
    exports,
    module2
  ) {
    'use strict'
    var color = require_kleur()
    var Prompt = require_prompt()
    var _require = require_util()
    var style = _require.style
    var clear = _require.clear
    var _require2 = require_src()
    var erase = _require2.erase
    var cursor = _require2.cursor
    var ConfirmPrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.msg = opts.message
        this.value = opts.initial
        this.initialValue = !!opts.initial
        this.yesMsg = opts.yes || 'yes'
        this.yesOption = opts.yesOption || '(Y/n)'
        this.noMsg = opts.no || 'no'
        this.noOption = opts.noOption || '(y/N)'
        this.render()
      }
      reset() {
        this.value = this.initialValue
        this.fire()
        this.render()
      }
      exit() {
        this.abort()
      }
      abort() {
        this.done = this.aborted = true
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      submit() {
        this.value = this.value || false
        this.done = true
        this.aborted = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      _(c, key) {
        if (c.toLowerCase() === 'y') {
          this.value = true
          return this.submit()
        }
        if (c.toLowerCase() === 'n') {
          this.value = false
          return this.submit()
        }
        return this.bell()
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        else this.out.write(clear(this.outputText, this.out.columns))
        super.render()
        this.outputText = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(this.done),
          this.done
            ? this.value
              ? this.yesMsg
              : this.noMsg
            : color.gray(this.initialValue ? this.yesOption : this.noOption)
        ].join(' ')
        this.out.write(erase.line + cursor.to(0) + this.outputText)
      }
    }
    module2.exports = ConfirmPrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/index.js
var require_elements = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/elements/index.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = {
      TextPrompt: require_text(),
      SelectPrompt: require_select(),
      TogglePrompt: require_toggle(),
      DatePrompt: require_date(),
      NumberPrompt: require_number(),
      MultiselectPrompt: require_multiselect(),
      AutocompletePrompt: require_autocomplete(),
      AutocompleteMultiselectPrompt: require_autocompleteMultiselect(),
      ConfirmPrompt: require_confirm()
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/prompts.js
var require_prompts = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/prompts.js'(exports) {
    'use strict'
    var $ = exports
    var el = require_elements()
    var noop = (v) => v
    function toPrompt(type, args, opts = {}) {
      return new Promise((res, rej) => {
        const p = new el[type](args)
        const onAbort = opts.onAbort || noop
        const onSubmit = opts.onSubmit || noop
        const onExit = opts.onExit || noop
        p.on('state', args.onState || noop)
        p.on('submit', (x) => res(onSubmit(x)))
        p.on('exit', (x) => res(onExit(x)))
        p.on('abort', (x) => rej(onAbort(x)))
      })
    }
    $.text = (args) => toPrompt('TextPrompt', args)
    $.password = (args) => {
      args.style = 'password'
      return $.text(args)
    }
    $.invisible = (args) => {
      args.style = 'invisible'
      return $.text(args)
    }
    $.number = (args) => toPrompt('NumberPrompt', args)
    $.date = (args) => toPrompt('DatePrompt', args)
    $.confirm = (args) => toPrompt('ConfirmPrompt', args)
    $.list = (args) => {
      const sep = args.separator || ','
      return toPrompt('TextPrompt', args, {
        onSubmit: (str) => str.split(sep).map((s) => s.trim())
      })
    }
    $.toggle = (args) => toPrompt('TogglePrompt', args)
    $.select = (args) => toPrompt('SelectPrompt', args)
    $.multiselect = (args) => {
      args.choices = [].concat(args.choices || [])
      const toSelected = (items) => items.filter((item) => item.selected).map((item) => item.value)
      return toPrompt('MultiselectPrompt', args, {
        onAbort: toSelected,
        onSubmit: toSelected
      })
    }
    $.autocompleteMultiselect = (args) => {
      args.choices = [].concat(args.choices || [])
      const toSelected = (items) => items.filter((item) => item.selected).map((item) => item.value)
      return toPrompt('AutocompleteMultiselectPrompt', args, {
        onAbort: toSelected,
        onSubmit: toSelected
      })
    }
    var byTitle = (input, choices) =>
      Promise.resolve(
        choices.filter(
          (item) => item.title.slice(0, input.length).toLowerCase() === input.toLowerCase()
        )
      )
    $.autocomplete = (args) => {
      args.suggest = args.suggest || byTitle
      args.choices = [].concat(args.choices || [])
      return toPrompt('AutocompletePrompt', args)
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/index.js
var require_dist = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/dist/index.js'(exports, module2) {
    'use strict'
    function ownKeys(object, enumerableOnly) {
      var keys = Object.keys(object)
      if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object)
        if (enumerableOnly) {
          symbols = symbols.filter(function (sym) {
            return Object.getOwnPropertyDescriptor(object, sym).enumerable
          })
        }
        keys.push.apply(keys, symbols)
      }
      return keys
    }
    function _objectSpread(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i] != null ? arguments[i] : {}
        if (i % 2) {
          ownKeys(Object(source), true).forEach(function (key) {
            _defineProperty(target, key, source[key])
          })
        } else if (Object.getOwnPropertyDescriptors) {
          Object.defineProperties(target, Object.getOwnPropertyDescriptors(source))
        } else {
          ownKeys(Object(source)).forEach(function (key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key))
          })
        }
      }
      return target
    }
    function _defineProperty(obj, key, value) {
      if (key in obj) {
        Object.defineProperty(obj, key, {
          value,
          enumerable: true,
          configurable: true,
          writable: true
        })
      } else {
        obj[key] = value
      }
      return obj
    }
    function _createForOfIteratorHelper(o, allowArrayLike) {
      var it = (typeof Symbol !== 'undefined' && o[Symbol.iterator]) || o['@@iterator']
      if (!it) {
        if (
          Array.isArray(o) ||
          (it = _unsupportedIterableToArray(o)) ||
          (allowArrayLike && o && typeof o.length === 'number')
        ) {
          if (it) o = it
          var i = 0
          var F = function F2() {}
          return {
            s: F,
            n: function n() {
              if (i >= o.length) return { done: true }
              return { done: false, value: o[i++] }
            },
            e: function e(_e) {
              throw _e
            },
            f: F
          }
        }
        throw new TypeError(
          'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
        )
      }
      var normalCompletion = true,
        didErr = false,
        err
      return {
        s: function s() {
          it = it.call(o)
        },
        n: function n() {
          var step = it.next()
          normalCompletion = step.done
          return step
        },
        e: function e(_e2) {
          didErr = true
          err = _e2
        },
        f: function f() {
          try {
            if (!normalCompletion && it.return != null) it.return()
          } finally {
            if (didErr) throw err
          }
        }
      }
    }
    function _unsupportedIterableToArray(o, minLen) {
      if (!o) return
      if (typeof o === 'string') return _arrayLikeToArray(o, minLen)
      var n = Object.prototype.toString.call(o).slice(8, -1)
      if (n === 'Object' && o.constructor) n = o.constructor.name
      if (n === 'Map' || n === 'Set') return Array.from(o)
      if (n === 'Arguments' || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))
        return _arrayLikeToArray(o, minLen)
    }
    function _arrayLikeToArray(arr, len) {
      if (len == null || len > arr.length) len = arr.length
      for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]
      return arr2
    }
    function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg)
        var value = info.value
      } catch (error) {
        reject(error)
        return
      }
      if (info.done) {
        resolve(value)
      } else {
        Promise.resolve(value).then(_next, _throw)
      }
    }
    function _asyncToGenerator(fn) {
      return function () {
        var self2 = this,
          args = arguments
        return new Promise(function (resolve, reject) {
          var gen = fn.apply(self2, args)
          function _next(value) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'next', value)
          }
          function _throw(err) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'throw', err)
          }
          _next(void 0)
        })
      }
    }
    var prompts2 = require_prompts()
    var passOn = ['suggest', 'format', 'onState', 'validate', 'onRender', 'type']
    var noop = () => {}
    function prompt() {
      return _prompt.apply(this, arguments)
    }
    function _prompt() {
      _prompt = _asyncToGenerator(function* (
        questions = [],
        { onSubmit = noop, onCancel = noop } = {}
      ) {
        const answers = {}
        const override2 = prompt._override || {}
        questions = [].concat(questions)
        let answer, question, quit, name, type, lastPrompt
        const getFormattedAnswer = /* @__PURE__ */ (function () {
          var _ref = _asyncToGenerator(function* (question2, answer2, skipValidation = false) {
            if (!skipValidation && question2.validate && question2.validate(answer2) !== true) {
              return
            }
            return question2.format ? yield question2.format(answer2, answers) : answer2
          })
          return function getFormattedAnswer2(_x, _x2) {
            return _ref.apply(this, arguments)
          }
        })()
        var _iterator = _createForOfIteratorHelper(questions),
          _step
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done; ) {
            question = _step.value
            var _question = question
            name = _question.name
            type = _question.type
            if (typeof type === 'function') {
              type = yield type(answer, _objectSpread({}, answers), question)
              question['type'] = type
            }
            if (!type) continue
            for (let key in question) {
              if (passOn.includes(key)) continue
              let value = question[key]
              question[key] =
                typeof value === 'function'
                  ? yield value(answer, _objectSpread({}, answers), lastPrompt)
                  : value
            }
            lastPrompt = question
            if (typeof question.message !== 'string') {
              throw new Error('prompt message is required')
            }
            var _question2 = question
            name = _question2.name
            type = _question2.type
            if (prompts2[type] === void 0) {
              throw new Error(`prompt type (${type}) is not defined`)
            }
            if (override2[question.name] !== void 0) {
              answer = yield getFormattedAnswer(question, override2[question.name])
              if (answer !== void 0) {
                answers[name] = answer
                continue
              }
            }
            try {
              answer = prompt._injected
                ? getInjectedAnswer(prompt._injected, question.initial)
                : yield prompts2[type](question)
              answers[name] = answer = yield getFormattedAnswer(question, answer, true)
              quit = yield onSubmit(question, answer, answers)
            } catch (err) {
              quit = !(yield onCancel(question, answers))
            }
            if (quit) return answers
          }
        } catch (err) {
          _iterator.e(err)
        } finally {
          _iterator.f()
        }
        return answers
      })
      return _prompt.apply(this, arguments)
    }
    function getInjectedAnswer(injected, deafultValue) {
      const answer = injected.shift()
      if (answer instanceof Error) {
        throw answer
      }
      return answer === void 0 ? deafultValue : answer
    }
    function inject(answers) {
      prompt._injected = (prompt._injected || []).concat(answers)
    }
    function override(answers) {
      prompt._override = Object.assign({}, answers)
    }
    module2.exports = Object.assign(prompt, {
      prompt,
      prompts: prompts2,
      inject,
      override
    })
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/action.js
var require_action2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/action.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = (key, isSelect) => {
      if (key.meta && key.name !== 'escape') return
      if (key.ctrl) {
        if (key.name === 'a') return 'first'
        if (key.name === 'c') return 'abort'
        if (key.name === 'd') return 'abort'
        if (key.name === 'e') return 'last'
        if (key.name === 'g') return 'reset'
      }
      if (isSelect) {
        if (key.name === 'j') return 'down'
        if (key.name === 'k') return 'up'
      }
      if (key.name === 'return') return 'submit'
      if (key.name === 'enter') return 'submit'
      if (key.name === 'backspace') return 'delete'
      if (key.name === 'delete') return 'deleteForward'
      if (key.name === 'abort') return 'abort'
      if (key.name === 'escape') return 'exit'
      if (key.name === 'tab') return 'next'
      if (key.name === 'pagedown') return 'nextPage'
      if (key.name === 'pageup') return 'prevPage'
      if (key.name === 'home') return 'home'
      if (key.name === 'end') return 'end'
      if (key.name === 'up') return 'up'
      if (key.name === 'down') return 'down'
      if (key.name === 'right') return 'right'
      if (key.name === 'left') return 'left'
      return false
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/strip.js
var require_strip2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/strip.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = (str) => {
      const pattern = [
        '[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)',
        '(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PRZcf-ntqry=><~]))'
      ].join('|')
      const RGX = new RegExp(pattern, 'g')
      return typeof str === 'string' ? str.replace(RGX, '') : str
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/clear.js
var require_clear2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/clear.js'(
    exports,
    module2
  ) {
    'use strict'
    var strip = require_strip2()
    var { erase, cursor } = require_src()
    var width = (str) => [...strip(str)].length
    module2.exports = function (prompt, perLine) {
      if (!perLine) return erase.line + cursor.to(0)
      let rows = 0
      const lines = prompt.split(/\r?\n/)
      for (let line of lines) {
        rows += 1 + Math.floor(Math.max(width(line) - 1, 0) / perLine)
      }
      return erase.lines(rows)
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/figures.js
var require_figures2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/figures.js'(
    exports,
    module2
  ) {
    'use strict'
    var main = {
      arrowUp: '\u2191',
      arrowDown: '\u2193',
      arrowLeft: '\u2190',
      arrowRight: '\u2192',
      radioOn: '\u25C9',
      radioOff: '\u25EF',
      tick: '\u2714',
      cross: '\u2716',
      ellipsis: '\u2026',
      pointerSmall: '\u203A',
      line: '\u2500',
      pointer: '\u276F'
    }
    var win = {
      arrowUp: main.arrowUp,
      arrowDown: main.arrowDown,
      arrowLeft: main.arrowLeft,
      arrowRight: main.arrowRight,
      radioOn: '(*)',
      radioOff: '( )',
      tick: '\u221A',
      cross: '\xD7',
      ellipsis: '...',
      pointerSmall: '\xBB',
      line: '\u2500',
      pointer: '>'
    }
    var figures = process.platform === 'win32' ? win : main
    module2.exports = figures
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/style.js
var require_style2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/style.js'(
    exports,
    module2
  ) {
    'use strict'
    var c = require_kleur()
    var figures = require_figures2()
    var styles = Object.freeze({
      password: { scale: 1, render: (input) => '*'.repeat(input.length) },
      emoji: { scale: 2, render: (input) => '\u{1F603}'.repeat(input.length) },
      invisible: { scale: 0, render: (input) => '' },
      default: { scale: 1, render: (input) => `${input}` }
    })
    var render = (type) => styles[type] || styles.default
    var symbols = Object.freeze({
      aborted: c.red(figures.cross),
      done: c.green(figures.tick),
      exited: c.yellow(figures.cross),
      default: c.cyan('?')
    })
    var symbol = (done, aborted, exited) =>
      aborted ? symbols.aborted : exited ? symbols.exited : done ? symbols.done : symbols.default
    var delimiter = (completing) => c.gray(completing ? figures.ellipsis : figures.pointerSmall)
    var item = (expandable, expanded) =>
      c.gray(expandable ? (expanded ? figures.pointerSmall : '+') : figures.line)
    module2.exports = {
      styles,
      render,
      symbols,
      symbol,
      delimiter,
      item
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/lines.js
var require_lines2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/lines.js'(
    exports,
    module2
  ) {
    'use strict'
    var strip = require_strip2()
    module2.exports = function (msg, perLine) {
      let lines = String(strip(msg) || '').split(/\r?\n/)
      if (!perLine) return lines.length
      return lines.map((l) => Math.ceil(l.length / perLine)).reduce((a, b) => a + b)
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/wrap.js
var require_wrap2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/wrap.js'(exports, module2) {
    'use strict'
    module2.exports = (msg, opts = {}) => {
      const tab = Number.isSafeInteger(parseInt(opts.margin))
        ? new Array(parseInt(opts.margin)).fill(' ').join('')
        : opts.margin || ''
      const width = opts.width
      return (msg || '')
        .split(/\r?\n/g)
        .map((line) =>
          line
            .split(/\s+/g)
            .reduce(
              (arr, w) => {
                if (
                  w.length + tab.length >= width ||
                  arr[arr.length - 1].length + w.length + 1 < width
                )
                  arr[arr.length - 1] += ` ${w}`
                else arr.push(`${tab}${w}`)
                return arr
              },
              [tab]
            )
            .join('\n')
        )
        .join('\n')
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/entriesToDisplay.js
var require_entriesToDisplay2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/entriesToDisplay.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = (cursor, total, maxVisible) => {
      maxVisible = maxVisible || total
      let startIndex = Math.min(total - maxVisible, cursor - Math.floor(maxVisible / 2))
      if (startIndex < 0) startIndex = 0
      let endIndex = Math.min(startIndex + maxVisible, total)
      return { startIndex, endIndex }
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/index.js
var require_util2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/util/index.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = {
      action: require_action2(),
      clear: require_clear2(),
      style: require_style2(),
      strip: require_strip2(),
      figures: require_figures2(),
      lines: require_lines2(),
      wrap: require_wrap2(),
      entriesToDisplay: require_entriesToDisplay2()
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/prompt.js
var require_prompt2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/prompt.js'(
    exports,
    module2
  ) {
    'use strict'
    var readline = require('readline')
    var { action } = require_util2()
    var EventEmitter = require('events')
    var { beep, cursor } = require_src()
    var color = require_kleur()
    var Prompt = class extends EventEmitter {
      constructor(opts = {}) {
        super()
        this.firstRender = true
        this.in = opts.stdin || process.stdin
        this.out = opts.stdout || process.stdout
        this.onRender = (opts.onRender || (() => void 0)).bind(this)
        const rl = readline.createInterface({ input: this.in, escapeCodeTimeout: 50 })
        readline.emitKeypressEvents(this.in, rl)
        if (this.in.isTTY) this.in.setRawMode(true)
        const isSelect = ['SelectPrompt', 'MultiselectPrompt'].indexOf(this.constructor.name) > -1
        const keypress = (str, key) => {
          let a = action(key, isSelect)
          if (a === false) {
            this._ && this._(str, key)
          } else if (typeof this[a] === 'function') {
            this[a](key)
          } else {
            this.bell()
          }
        }
        this.close = () => {
          this.out.write(cursor.show)
          this.in.removeListener('keypress', keypress)
          if (this.in.isTTY) this.in.setRawMode(false)
          rl.close()
          this.emit(this.aborted ? 'abort' : this.exited ? 'exit' : 'submit', this.value)
          this.closed = true
        }
        this.in.on('keypress', keypress)
      }
      fire() {
        this.emit('state', {
          value: this.value,
          aborted: !!this.aborted,
          exited: !!this.exited
        })
      }
      bell() {
        this.out.write(beep)
      }
      render() {
        this.onRender(color)
        if (this.firstRender) this.firstRender = false
      }
    }
    module2.exports = Prompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/text.js
var require_text2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/text.js'(
    exports,
    module2
  ) {
    var color = require_kleur()
    var Prompt = require_prompt2()
    var { erase, cursor } = require_src()
    var { style, clear, lines, figures } = require_util2()
    var TextPrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.transform = style.render(opts.style)
        this.scale = this.transform.scale
        this.msg = opts.message
        this.initial = opts.initial || ``
        this.validator = opts.validate || (() => true)
        this.value = ``
        this.errorMsg = opts.error || `Please Enter A Valid Value`
        this.cursor = Number(!!this.initial)
        this.cursorOffset = 0
        this.clear = clear(``, this.out.columns)
        this.render()
      }
      set value(v) {
        if (!v && this.initial) {
          this.placeholder = true
          this.rendered = color.gray(this.transform.render(this.initial))
        } else {
          this.placeholder = false
          this.rendered = this.transform.render(v)
        }
        this._value = v
        this.fire()
      }
      get value() {
        return this._value
      }
      reset() {
        this.value = ``
        this.cursor = Number(!!this.initial)
        this.cursorOffset = 0
        this.fire()
        this.render()
      }
      exit() {
        this.abort()
      }
      abort() {
        this.value = this.value || this.initial
        this.done = this.aborted = true
        this.error = false
        this.red = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      async validate() {
        let valid = await this.validator(this.value)
        if (typeof valid === `string`) {
          this.errorMsg = valid
          valid = false
        }
        this.error = !valid
      }
      async submit() {
        this.value = this.value || this.initial
        this.cursorOffset = 0
        this.cursor = this.rendered.length
        await this.validate()
        if (this.error) {
          this.red = true
          this.fire()
          this.render()
          return
        }
        this.done = true
        this.aborted = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      next() {
        if (!this.placeholder) return this.bell()
        this.value = this.initial
        this.cursor = this.rendered.length
        this.fire()
        this.render()
      }
      moveCursor(n) {
        if (this.placeholder) return
        this.cursor = this.cursor + n
        this.cursorOffset += n
      }
      _(c, key) {
        let s1 = this.value.slice(0, this.cursor)
        let s2 = this.value.slice(this.cursor)
        this.value = `${s1}${c}${s2}`
        this.red = false
        this.cursor = this.placeholder ? 0 : s1.length + 1
        this.render()
      }
      delete() {
        if (this.isCursorAtStart()) return this.bell()
        let s1 = this.value.slice(0, this.cursor - 1)
        let s2 = this.value.slice(this.cursor)
        this.value = `${s1}${s2}`
        this.red = false
        if (this.isCursorAtStart()) {
          this.cursorOffset = 0
        } else {
          this.cursorOffset++
          this.moveCursor(-1)
        }
        this.render()
      }
      deleteForward() {
        if (this.cursor * this.scale >= this.rendered.length || this.placeholder) return this.bell()
        let s1 = this.value.slice(0, this.cursor)
        let s2 = this.value.slice(this.cursor + 1)
        this.value = `${s1}${s2}`
        this.red = false
        if (this.isCursorAtEnd()) {
          this.cursorOffset = 0
        } else {
          this.cursorOffset++
        }
        this.render()
      }
      first() {
        this.cursor = 0
        this.render()
      }
      last() {
        this.cursor = this.value.length
        this.render()
      }
      left() {
        if (this.cursor <= 0 || this.placeholder) return this.bell()
        this.moveCursor(-1)
        this.render()
      }
      right() {
        if (this.cursor * this.scale >= this.rendered.length || this.placeholder) return this.bell()
        this.moveCursor(1)
        this.render()
      }
      isCursorAtStart() {
        return this.cursor === 0 || (this.placeholder && this.cursor === 1)
      }
      isCursorAtEnd() {
        return (
          this.cursor === this.rendered.length ||
          (this.placeholder && this.cursor === this.rendered.length + 1)
        )
      }
      render() {
        if (this.closed) return
        if (!this.firstRender) {
          if (this.outputError)
            this.out.write(
              cursor.down(lines(this.outputError, this.out.columns) - 1) +
                clear(this.outputError, this.out.columns)
            )
          this.out.write(clear(this.outputText, this.out.columns))
        }
        super.render()
        this.outputError = ''
        this.outputText = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(this.done),
          this.red ? color.red(this.rendered) : this.rendered
        ].join(` `)
        if (this.error) {
          this.outputError += this.errorMsg
            .split(
              `
`
            )
            .reduce(
              (a, l, i) =>
                a +
                `
${i ? ' ' : figures.pointerSmall} ${color.red().italic(l)}`,
              ``
            )
        }
        this.out.write(
          erase.line +
            cursor.to(0) +
            this.outputText +
            cursor.save +
            this.outputError +
            cursor.restore +
            cursor.move(this.cursorOffset, 0)
        )
      }
    }
    module2.exports = TextPrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/select.js
var require_select2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/select.js'(
    exports,
    module2
  ) {
    'use strict'
    var color = require_kleur()
    var Prompt = require_prompt2()
    var { style, clear, figures, wrap, entriesToDisplay } = require_util2()
    var { cursor } = require_src()
    var SelectPrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.msg = opts.message
        this.hint = opts.hint || '- Use arrow-keys. Return to submit.'
        this.warn = opts.warn || '- This option is disabled'
        this.cursor = opts.initial || 0
        this.choices = opts.choices.map((ch, idx) => {
          if (typeof ch === 'string') ch = { title: ch, value: idx }
          return {
            title: ch && (ch.title || ch.value || ch),
            value: ch && (ch.value === void 0 ? idx : ch.value),
            description: ch && ch.description,
            selected: ch && ch.selected,
            disabled: ch && ch.disabled
          }
        })
        this.optionsPerPage = opts.optionsPerPage || 10
        this.value = (this.choices[this.cursor] || {}).value
        this.clear = clear('', this.out.columns)
        this.render()
      }
      moveCursor(n) {
        this.cursor = n
        this.value = this.choices[n].value
        this.fire()
      }
      reset() {
        this.moveCursor(0)
        this.fire()
        this.render()
      }
      exit() {
        this.abort()
      }
      abort() {
        this.done = this.aborted = true
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      submit() {
        if (!this.selection.disabled) {
          this.done = true
          this.aborted = false
          this.fire()
          this.render()
          this.out.write('\n')
          this.close()
        } else this.bell()
      }
      first() {
        this.moveCursor(0)
        this.render()
      }
      last() {
        this.moveCursor(this.choices.length - 1)
        this.render()
      }
      up() {
        if (this.cursor === 0) {
          this.moveCursor(this.choices.length - 1)
        } else {
          this.moveCursor(this.cursor - 1)
        }
        this.render()
      }
      down() {
        if (this.cursor === this.choices.length - 1) {
          this.moveCursor(0)
        } else {
          this.moveCursor(this.cursor + 1)
        }
        this.render()
      }
      next() {
        this.moveCursor((this.cursor + 1) % this.choices.length)
        this.render()
      }
      _(c, key) {
        if (c === ' ') return this.submit()
      }
      get selection() {
        return this.choices[this.cursor]
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        else this.out.write(clear(this.outputText, this.out.columns))
        super.render()
        let { startIndex, endIndex } = entriesToDisplay(
          this.cursor,
          this.choices.length,
          this.optionsPerPage
        )
        this.outputText = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(false),
          this.done
            ? this.selection.title
            : this.selection.disabled
            ? color.yellow(this.warn)
            : color.gray(this.hint)
        ].join(' ')
        if (!this.done) {
          this.outputText += '\n'
          for (let i = startIndex; i < endIndex; i++) {
            let title,
              prefix,
              desc = '',
              v = this.choices[i]
            if (i === startIndex && startIndex > 0) {
              prefix = figures.arrowUp
            } else if (i === endIndex - 1 && endIndex < this.choices.length) {
              prefix = figures.arrowDown
            } else {
              prefix = ' '
            }
            if (v.disabled) {
              title =
                this.cursor === i
                  ? color.gray().underline(v.title)
                  : color.strikethrough().gray(v.title)
              prefix =
                (this.cursor === i ? color.bold().gray(figures.pointer) + ' ' : '  ') + prefix
            } else {
              title = this.cursor === i ? color.cyan().underline(v.title) : v.title
              prefix = (this.cursor === i ? color.cyan(figures.pointer) + ' ' : '  ') + prefix
              if (v.description && this.cursor === i) {
                desc = ` - ${v.description}`
                if (
                  prefix.length + title.length + desc.length >= this.out.columns ||
                  v.description.split(/\r?\n/).length > 1
                ) {
                  desc = '\n' + wrap(v.description, { margin: 3, width: this.out.columns })
                }
              }
            }
            this.outputText += `${prefix} ${title}${color.gray(desc)}
`
          }
        }
        this.out.write(this.outputText)
      }
    }
    module2.exports = SelectPrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/toggle.js
var require_toggle2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/toggle.js'(
    exports,
    module2
  ) {
    var color = require_kleur()
    var Prompt = require_prompt2()
    var { style, clear } = require_util2()
    var { cursor, erase } = require_src()
    var TogglePrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.msg = opts.message
        this.value = !!opts.initial
        this.active = opts.active || 'on'
        this.inactive = opts.inactive || 'off'
        this.initialValue = this.value
        this.render()
      }
      reset() {
        this.value = this.initialValue
        this.fire()
        this.render()
      }
      exit() {
        this.abort()
      }
      abort() {
        this.done = this.aborted = true
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      submit() {
        this.done = true
        this.aborted = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      deactivate() {
        if (this.value === false) return this.bell()
        this.value = false
        this.render()
      }
      activate() {
        if (this.value === true) return this.bell()
        this.value = true
        this.render()
      }
      delete() {
        this.deactivate()
      }
      left() {
        this.deactivate()
      }
      right() {
        this.activate()
      }
      down() {
        this.deactivate()
      }
      up() {
        this.activate()
      }
      next() {
        this.value = !this.value
        this.fire()
        this.render()
      }
      _(c, key) {
        if (c === ' ') {
          this.value = !this.value
        } else if (c === '1') {
          this.value = true
        } else if (c === '0') {
          this.value = false
        } else return this.bell()
        this.render()
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        else this.out.write(clear(this.outputText, this.out.columns))
        super.render()
        this.outputText = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(this.done),
          this.value ? this.inactive : color.cyan().underline(this.inactive),
          color.gray('/'),
          this.value ? color.cyan().underline(this.active) : this.active
        ].join(' ')
        this.out.write(erase.line + cursor.to(0) + this.outputText)
      }
    }
    module2.exports = TogglePrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/datepart.js
var require_datepart2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/datepart.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = class {
      constructor({ token, date, parts, locales }) {
        this.token = token
        this.date = date || new Date()
        this.parts = parts || [this]
        this.locales = locales || {}
      }
      up() {}
      down() {}
      next() {
        const currentIdx = this.parts.indexOf(this)
        return this.parts.find((part, idx) => idx > currentIdx && part instanceof DatePart)
      }
      setTo(val) {}
      prev() {
        let parts = [].concat(this.parts).reverse()
        const currentIdx = parts.indexOf(this)
        return parts.find((part, idx) => idx > currentIdx && part instanceof DatePart)
      }
      toString() {
        return String(this.date)
      }
    }
    module2.exports = DatePart
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/meridiem.js
var require_meridiem2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/meridiem.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart2()
    var Meridiem = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setHours((this.date.getHours() + 12) % 24)
      }
      down() {
        this.up()
      }
      toString() {
        let meridiem = this.date.getHours() > 12 ? 'pm' : 'am'
        return /\A/.test(this.token) ? meridiem.toUpperCase() : meridiem
      }
    }
    module2.exports = Meridiem
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/day.js
var require_day2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/day.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart2()
    var pos = (n) => {
      n = n % 10
      return n === 1 ? 'st' : n === 2 ? 'nd' : n === 3 ? 'rd' : 'th'
    }
    var Day = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setDate(this.date.getDate() + 1)
      }
      down() {
        this.date.setDate(this.date.getDate() - 1)
      }
      setTo(val) {
        this.date.setDate(parseInt(val.substr(-2)))
      }
      toString() {
        let date = this.date.getDate()
        let day = this.date.getDay()
        return this.token === 'DD'
          ? String(date).padStart(2, '0')
          : this.token === 'Do'
          ? date + pos(date)
          : this.token === 'd'
          ? day + 1
          : this.token === 'ddd'
          ? this.locales.weekdaysShort[day]
          : this.token === 'dddd'
          ? this.locales.weekdays[day]
          : date
      }
    }
    module2.exports = Day
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/hours.js
var require_hours2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/hours.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart2()
    var Hours = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setHours(this.date.getHours() + 1)
      }
      down() {
        this.date.setHours(this.date.getHours() - 1)
      }
      setTo(val) {
        this.date.setHours(parseInt(val.substr(-2)))
      }
      toString() {
        let hours = this.date.getHours()
        if (/h/.test(this.token)) hours = hours % 12 || 12
        return this.token.length > 1 ? String(hours).padStart(2, '0') : hours
      }
    }
    module2.exports = Hours
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/milliseconds.js
var require_milliseconds2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/milliseconds.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart2()
    var Milliseconds = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setMilliseconds(this.date.getMilliseconds() + 1)
      }
      down() {
        this.date.setMilliseconds(this.date.getMilliseconds() - 1)
      }
      setTo(val) {
        this.date.setMilliseconds(parseInt(val.substr(-this.token.length)))
      }
      toString() {
        return String(this.date.getMilliseconds()).padStart(4, '0').substr(0, this.token.length)
      }
    }
    module2.exports = Milliseconds
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/minutes.js
var require_minutes2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/minutes.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart2()
    var Minutes = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setMinutes(this.date.getMinutes() + 1)
      }
      down() {
        this.date.setMinutes(this.date.getMinutes() - 1)
      }
      setTo(val) {
        this.date.setMinutes(parseInt(val.substr(-2)))
      }
      toString() {
        let m = this.date.getMinutes()
        return this.token.length > 1 ? String(m).padStart(2, '0') : m
      }
    }
    module2.exports = Minutes
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/month.js
var require_month2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/month.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart2()
    var Month = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setMonth(this.date.getMonth() + 1)
      }
      down() {
        this.date.setMonth(this.date.getMonth() - 1)
      }
      setTo(val) {
        val = parseInt(val.substr(-2)) - 1
        this.date.setMonth(val < 0 ? 0 : val)
      }
      toString() {
        let month = this.date.getMonth()
        let tl = this.token.length
        return tl === 2
          ? String(month + 1).padStart(2, '0')
          : tl === 3
          ? this.locales.monthsShort[month]
          : tl === 4
          ? this.locales.months[month]
          : String(month + 1)
      }
    }
    module2.exports = Month
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/seconds.js
var require_seconds2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/seconds.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart2()
    var Seconds = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setSeconds(this.date.getSeconds() + 1)
      }
      down() {
        this.date.setSeconds(this.date.getSeconds() - 1)
      }
      setTo(val) {
        this.date.setSeconds(parseInt(val.substr(-2)))
      }
      toString() {
        let s = this.date.getSeconds()
        return this.token.length > 1 ? String(s).padStart(2, '0') : s
      }
    }
    module2.exports = Seconds
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/year.js
var require_year2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/year.js'(
    exports,
    module2
  ) {
    'use strict'
    var DatePart = require_datepart2()
    var Year = class extends DatePart {
      constructor(opts = {}) {
        super(opts)
      }
      up() {
        this.date.setFullYear(this.date.getFullYear() + 1)
      }
      down() {
        this.date.setFullYear(this.date.getFullYear() - 1)
      }
      setTo(val) {
        this.date.setFullYear(val.substr(-4))
      }
      toString() {
        let year = String(this.date.getFullYear()).padStart(4, '0')
        return this.token.length === 2 ? year.substr(-2) : year
      }
    }
    module2.exports = Year
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/index.js
var require_dateparts2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/dateparts/index.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = {
      DatePart: require_datepart2(),
      Meridiem: require_meridiem2(),
      Day: require_day2(),
      Hours: require_hours2(),
      Milliseconds: require_milliseconds2(),
      Minutes: require_minutes2(),
      Month: require_month2(),
      Seconds: require_seconds2(),
      Year: require_year2()
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/date.js
var require_date2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/date.js'(
    exports,
    module2
  ) {
    'use strict'
    var color = require_kleur()
    var Prompt = require_prompt2()
    var { style, clear, figures } = require_util2()
    var { erase, cursor } = require_src()
    var { DatePart, Meridiem, Day, Hours, Milliseconds, Minutes, Month, Seconds, Year } =
      require_dateparts2()
    var regex =
      /\\(.)|"((?:\\["\\]|[^"])+)"|(D[Do]?|d{3,4}|d)|(M{1,4})|(YY(?:YY)?)|([aA])|([Hh]{1,2})|(m{1,2})|(s{1,2})|(S{1,4})|./g
    var regexGroups = {
      1: ({ token }) => token.replace(/\\(.)/g, '$1'),
      2: (opts) => new Day(opts),
      3: (opts) => new Month(opts),
      4: (opts) => new Year(opts),
      5: (opts) => new Meridiem(opts),
      6: (opts) => new Hours(opts),
      7: (opts) => new Minutes(opts),
      8: (opts) => new Seconds(opts),
      9: (opts) => new Milliseconds(opts)
    }
    var dfltLocales = {
      months:
        'January,February,March,April,May,June,July,August,September,October,November,December'.split(
          ','
        ),
      monthsShort: 'Jan,Feb,Mar,Apr,May,Jun,Jul,Aug,Sep,Oct,Nov,Dec'.split(','),
      weekdays: 'Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday'.split(','),
      weekdaysShort: 'Sun,Mon,Tue,Wed,Thu,Fri,Sat'.split(',')
    }
    var DatePrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.msg = opts.message
        this.cursor = 0
        this.typed = ''
        this.locales = Object.assign(dfltLocales, opts.locales)
        this._date = opts.initial || new Date()
        this.errorMsg = opts.error || 'Please Enter A Valid Value'
        this.validator = opts.validate || (() => true)
        this.mask = opts.mask || 'YYYY-MM-DD HH:mm:ss'
        this.clear = clear('', this.out.columns)
        this.render()
      }
      get value() {
        return this.date
      }
      get date() {
        return this._date
      }
      set date(date) {
        if (date) this._date.setTime(date.getTime())
      }
      set mask(mask) {
        let result
        this.parts = []
        while ((result = regex.exec(mask))) {
          let match = result.shift()
          let idx = result.findIndex((gr) => gr != null)
          this.parts.push(
            idx in regexGroups
              ? regexGroups[idx]({
                  token: result[idx] || match,
                  date: this.date,
                  parts: this.parts,
                  locales: this.locales
                })
              : result[idx] || match
          )
        }
        let parts = this.parts.reduce((arr, i) => {
          if (typeof i === 'string' && typeof arr[arr.length - 1] === 'string')
            arr[arr.length - 1] += i
          else arr.push(i)
          return arr
        }, [])
        this.parts.splice(0)
        this.parts.push(...parts)
        this.reset()
      }
      moveCursor(n) {
        this.typed = ''
        this.cursor = n
        this.fire()
      }
      reset() {
        this.moveCursor(this.parts.findIndex((p) => p instanceof DatePart))
        this.fire()
        this.render()
      }
      exit() {
        this.abort()
      }
      abort() {
        this.done = this.aborted = true
        this.error = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      async validate() {
        let valid = await this.validator(this.value)
        if (typeof valid === 'string') {
          this.errorMsg = valid
          valid = false
        }
        this.error = !valid
      }
      async submit() {
        await this.validate()
        if (this.error) {
          this.color = 'red'
          this.fire()
          this.render()
          return
        }
        this.done = true
        this.aborted = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      up() {
        this.typed = ''
        this.parts[this.cursor].up()
        this.render()
      }
      down() {
        this.typed = ''
        this.parts[this.cursor].down()
        this.render()
      }
      left() {
        let prev = this.parts[this.cursor].prev()
        if (prev == null) return this.bell()
        this.moveCursor(this.parts.indexOf(prev))
        this.render()
      }
      right() {
        let next = this.parts[this.cursor].next()
        if (next == null) return this.bell()
        this.moveCursor(this.parts.indexOf(next))
        this.render()
      }
      next() {
        let next = this.parts[this.cursor].next()
        this.moveCursor(
          next ? this.parts.indexOf(next) : this.parts.findIndex((part) => part instanceof DatePart)
        )
        this.render()
      }
      _(c) {
        if (/\d/.test(c)) {
          this.typed += c
          this.parts[this.cursor].setTo(this.typed)
          this.render()
        }
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        else this.out.write(clear(this.outputText, this.out.columns))
        super.render()
        this.outputText = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(false),
          this.parts
            .reduce(
              (arr, p, idx) =>
                arr.concat(
                  idx === this.cursor && !this.done ? color.cyan().underline(p.toString()) : p
                ),
              []
            )
            .join('')
        ].join(' ')
        if (this.error) {
          this.outputText += this.errorMsg.split('\n').reduce(
            (a, l, i) =>
              a +
              `
${i ? ` ` : figures.pointerSmall} ${color.red().italic(l)}`,
            ``
          )
        }
        this.out.write(erase.line + cursor.to(0) + this.outputText)
      }
    }
    module2.exports = DatePrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/number.js
var require_number2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/number.js'(
    exports,
    module2
  ) {
    var color = require_kleur()
    var Prompt = require_prompt2()
    var { cursor, erase } = require_src()
    var { style, figures, clear, lines } = require_util2()
    var isNumber = /[0-9]/
    var isDef = (any) => any !== void 0
    var round = (number, precision) => {
      let factor = Math.pow(10, precision)
      return Math.round(number * factor) / factor
    }
    var NumberPrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.transform = style.render(opts.style)
        this.msg = opts.message
        this.initial = isDef(opts.initial) ? opts.initial : ''
        this.float = !!opts.float
        this.round = opts.round || 2
        this.inc = opts.increment || 1
        this.min = isDef(opts.min) ? opts.min : -Infinity
        this.max = isDef(opts.max) ? opts.max : Infinity
        this.errorMsg = opts.error || `Please Enter A Valid Value`
        this.validator = opts.validate || (() => true)
        this.color = `cyan`
        this.value = ``
        this.typed = ``
        this.lastHit = 0
        this.render()
      }
      set value(v) {
        if (!v && v !== 0) {
          this.placeholder = true
          this.rendered = color.gray(this.transform.render(`${this.initial}`))
          this._value = ``
        } else {
          this.placeholder = false
          this.rendered = this.transform.render(`${round(v, this.round)}`)
          this._value = round(v, this.round)
        }
        this.fire()
      }
      get value() {
        return this._value
      }
      parse(x) {
        return this.float ? parseFloat(x) : parseInt(x)
      }
      valid(c) {
        return c === `-` || (c === `.` && this.float) || isNumber.test(c)
      }
      reset() {
        this.typed = ``
        this.value = ``
        this.fire()
        this.render()
      }
      exit() {
        this.abort()
      }
      abort() {
        let x = this.value
        this.value = x !== `` ? x : this.initial
        this.done = this.aborted = true
        this.error = false
        this.fire()
        this.render()
        this.out.write(`
`)
        this.close()
      }
      async validate() {
        let valid = await this.validator(this.value)
        if (typeof valid === `string`) {
          this.errorMsg = valid
          valid = false
        }
        this.error = !valid
      }
      async submit() {
        await this.validate()
        if (this.error) {
          this.color = `red`
          this.fire()
          this.render()
          return
        }
        let x = this.value
        this.value = x !== `` ? x : this.initial
        this.done = true
        this.aborted = false
        this.error = false
        this.fire()
        this.render()
        this.out.write(`
`)
        this.close()
      }
      up() {
        this.typed = ``
        if (this.value === '') {
          this.value = this.min - this.inc
        }
        if (this.value >= this.max) return this.bell()
        this.value += this.inc
        this.color = `cyan`
        this.fire()
        this.render()
      }
      down() {
        this.typed = ``
        if (this.value === '') {
          this.value = this.min + this.inc
        }
        if (this.value <= this.min) return this.bell()
        this.value -= this.inc
        this.color = `cyan`
        this.fire()
        this.render()
      }
      delete() {
        let val = this.value.toString()
        if (val.length === 0) return this.bell()
        this.value = this.parse((val = val.slice(0, -1))) || ``
        if (this.value !== '' && this.value < this.min) {
          this.value = this.min
        }
        this.color = `cyan`
        this.fire()
        this.render()
      }
      next() {
        this.value = this.initial
        this.fire()
        this.render()
      }
      _(c, key) {
        if (!this.valid(c)) return this.bell()
        const now = Date.now()
        if (now - this.lastHit > 1e3) this.typed = ``
        this.typed += c
        this.lastHit = now
        this.color = `cyan`
        if (c === `.`) return this.fire()
        this.value = Math.min(this.parse(this.typed), this.max)
        if (this.value > this.max) this.value = this.max
        if (this.value < this.min) this.value = this.min
        this.fire()
        this.render()
      }
      render() {
        if (this.closed) return
        if (!this.firstRender) {
          if (this.outputError)
            this.out.write(
              cursor.down(lines(this.outputError, this.out.columns) - 1) +
                clear(this.outputError, this.out.columns)
            )
          this.out.write(clear(this.outputText, this.out.columns))
        }
        super.render()
        this.outputError = ''
        this.outputText = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(this.done),
          !this.done || (!this.done && !this.placeholder)
            ? color[this.color]().underline(this.rendered)
            : this.rendered
        ].join(` `)
        if (this.error) {
          this.outputError += this.errorMsg
            .split(
              `
`
            )
            .reduce(
              (a, l, i) =>
                a +
                `
${i ? ` ` : figures.pointerSmall} ${color.red().italic(l)}`,
              ``
            )
        }
        this.out.write(
          erase.line +
            cursor.to(0) +
            this.outputText +
            cursor.save +
            this.outputError +
            cursor.restore
        )
      }
    }
    module2.exports = NumberPrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/multiselect.js
var require_multiselect2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/multiselect.js'(
    exports,
    module2
  ) {
    'use strict'
    var color = require_kleur()
    var { cursor } = require_src()
    var Prompt = require_prompt2()
    var { clear, figures, style, wrap, entriesToDisplay } = require_util2()
    var MultiselectPrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.msg = opts.message
        this.cursor = opts.cursor || 0
        this.scrollIndex = opts.cursor || 0
        this.hint = opts.hint || ''
        this.warn = opts.warn || '- This option is disabled -'
        this.minSelected = opts.min
        this.showMinError = false
        this.maxChoices = opts.max
        this.instructions = opts.instructions
        this.optionsPerPage = opts.optionsPerPage || 10
        this.value = opts.choices.map((ch, idx) => {
          if (typeof ch === 'string') ch = { title: ch, value: idx }
          return {
            title: ch && (ch.title || ch.value || ch),
            description: ch && ch.description,
            value: ch && (ch.value === void 0 ? idx : ch.value),
            selected: ch && ch.selected,
            disabled: ch && ch.disabled
          }
        })
        this.clear = clear('', this.out.columns)
        if (!opts.overrideRender) {
          this.render()
        }
      }
      reset() {
        this.value.map((v) => !v.selected)
        this.cursor = 0
        this.fire()
        this.render()
      }
      selected() {
        return this.value.filter((v) => v.selected)
      }
      exit() {
        this.abort()
      }
      abort() {
        this.done = this.aborted = true
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      submit() {
        const selected = this.value.filter((e) => e.selected)
        if (this.minSelected && selected.length < this.minSelected) {
          this.showMinError = true
          this.render()
        } else {
          this.done = true
          this.aborted = false
          this.fire()
          this.render()
          this.out.write('\n')
          this.close()
        }
      }
      first() {
        this.cursor = 0
        this.render()
      }
      last() {
        this.cursor = this.value.length - 1
        this.render()
      }
      next() {
        this.cursor = (this.cursor + 1) % this.value.length
        this.render()
      }
      up() {
        if (this.cursor === 0) {
          this.cursor = this.value.length - 1
        } else {
          this.cursor--
        }
        this.render()
      }
      down() {
        if (this.cursor === this.value.length - 1) {
          this.cursor = 0
        } else {
          this.cursor++
        }
        this.render()
      }
      left() {
        this.value[this.cursor].selected = false
        this.render()
      }
      right() {
        if (this.value.filter((e) => e.selected).length >= this.maxChoices) return this.bell()
        this.value[this.cursor].selected = true
        this.render()
      }
      handleSpaceToggle() {
        const v = this.value[this.cursor]
        if (v.selected) {
          v.selected = false
          this.render()
        } else if (v.disabled || this.value.filter((e) => e.selected).length >= this.maxChoices) {
          return this.bell()
        } else {
          v.selected = true
          this.render()
        }
      }
      toggleAll() {
        if (this.maxChoices !== void 0 || this.value[this.cursor].disabled) {
          return this.bell()
        }
        const newSelected = !this.value[this.cursor].selected
        this.value.filter((v) => !v.disabled).forEach((v) => (v.selected = newSelected))
        this.render()
      }
      _(c, key) {
        if (c === ' ') {
          this.handleSpaceToggle()
        } else if (c === 'a') {
          this.toggleAll()
        } else {
          return this.bell()
        }
      }
      renderInstructions() {
        if (this.instructions === void 0 || this.instructions) {
          if (typeof this.instructions === 'string') {
            return this.instructions
          }
          return (
            `
Instructions:
    ${figures.arrowUp}/${figures.arrowDown}: Highlight option
    ${figures.arrowLeft}/${figures.arrowRight}/[space]: Toggle selection
` +
            (this.maxChoices === void 0
              ? `    a: Toggle all
`
              : '') +
            `    enter/return: Complete answer`
          )
        }
        return ''
      }
      renderOption(cursor2, v, i, arrowIndicator) {
        const prefix =
          (v.selected ? color.green(figures.radioOn) : figures.radioOff) +
          ' ' +
          arrowIndicator +
          ' '
        let title, desc
        if (v.disabled) {
          title =
            cursor2 === i ? color.gray().underline(v.title) : color.strikethrough().gray(v.title)
        } else {
          title = cursor2 === i ? color.cyan().underline(v.title) : v.title
          if (cursor2 === i && v.description) {
            desc = ` - ${v.description}`
            if (
              prefix.length + title.length + desc.length >= this.out.columns ||
              v.description.split(/\r?\n/).length > 1
            ) {
              desc = '\n' + wrap(v.description, { margin: prefix.length, width: this.out.columns })
            }
          }
        }
        return prefix + title + color.gray(desc || '')
      }
      paginateOptions(options2) {
        if (options2.length === 0) {
          return color.red('No matches for this query.')
        }
        let { startIndex, endIndex } = entriesToDisplay(
          this.cursor,
          options2.length,
          this.optionsPerPage
        )
        let prefix,
          styledOptions = []
        for (let i = startIndex; i < endIndex; i++) {
          if (i === startIndex && startIndex > 0) {
            prefix = figures.arrowUp
          } else if (i === endIndex - 1 && endIndex < options2.length) {
            prefix = figures.arrowDown
          } else {
            prefix = ' '
          }
          styledOptions.push(this.renderOption(this.cursor, options2[i], i, prefix))
        }
        return '\n' + styledOptions.join('\n')
      }
      renderOptions(options2) {
        if (!this.done) {
          return this.paginateOptions(options2)
        }
        return ''
      }
      renderDoneOrInstructions() {
        if (this.done) {
          return this.value
            .filter((e) => e.selected)
            .map((v) => v.title)
            .join(', ')
        }
        const output = [color.gray(this.hint), this.renderInstructions()]
        if (this.value[this.cursor].disabled) {
          output.push(color.yellow(this.warn))
        }
        return output.join(' ')
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        super.render()
        let prompt = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(false),
          this.renderDoneOrInstructions()
        ].join(' ')
        if (this.showMinError) {
          prompt += color.red(`You must select a minimum of ${this.minSelected} choices.`)
          this.showMinError = false
        }
        prompt += this.renderOptions(this.value)
        this.out.write(this.clear + prompt)
        this.clear = clear(prompt, this.out.columns)
      }
    }
    module2.exports = MultiselectPrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/autocomplete.js
var require_autocomplete2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/autocomplete.js'(
    exports,
    module2
  ) {
    'use strict'
    var color = require_kleur()
    var Prompt = require_prompt2()
    var { erase, cursor } = require_src()
    var { style, clear, figures, wrap, entriesToDisplay } = require_util2()
    var getVal = (arr, i) => arr[i] && (arr[i].value || arr[i].title || arr[i])
    var getTitle = (arr, i) => arr[i] && (arr[i].title || arr[i].value || arr[i])
    var getIndex = (arr, valOrTitle) => {
      const index = arr.findIndex((el) => el.value === valOrTitle || el.title === valOrTitle)
      return index > -1 ? index : void 0
    }
    var AutocompletePrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.msg = opts.message
        this.suggest = opts.suggest
        this.choices = opts.choices
        this.initial =
          typeof opts.initial === 'number' ? opts.initial : getIndex(opts.choices, opts.initial)
        this.select = this.initial || opts.cursor || 0
        this.i18n = { noMatches: opts.noMatches || 'no matches found' }
        this.fallback = opts.fallback || this.initial
        this.clearFirst = opts.clearFirst || false
        this.suggestions = []
        this.input = ''
        this.limit = opts.limit || 10
        this.cursor = 0
        this.transform = style.render(opts.style)
        this.scale = this.transform.scale
        this.render = this.render.bind(this)
        this.complete = this.complete.bind(this)
        this.clear = clear('', this.out.columns)
        this.complete(this.render)
        this.render()
      }
      set fallback(fb) {
        this._fb = Number.isSafeInteger(parseInt(fb)) ? parseInt(fb) : fb
      }
      get fallback() {
        let choice
        if (typeof this._fb === 'number') choice = this.choices[this._fb]
        else if (typeof this._fb === 'string') choice = { title: this._fb }
        return choice || this._fb || { title: this.i18n.noMatches }
      }
      moveSelect(i) {
        this.select = i
        if (this.suggestions.length > 0) this.value = getVal(this.suggestions, i)
        else this.value = this.fallback.value
        this.fire()
      }
      async complete(cb) {
        const p = (this.completing = this.suggest(this.input, this.choices))
        const suggestions = await p
        if (this.completing !== p) return
        this.suggestions = suggestions.map((s, i, arr) => ({
          title: getTitle(arr, i),
          value: getVal(arr, i),
          description: s.description
        }))
        this.completing = false
        const l = Math.max(suggestions.length - 1, 0)
        this.moveSelect(Math.min(l, this.select))
        cb && cb()
      }
      reset() {
        this.input = ''
        this.complete(() => {
          this.moveSelect(this.initial !== void 0 ? this.initial : 0)
          this.render()
        })
        this.render()
      }
      exit() {
        if (this.clearFirst && this.input.length > 0) {
          this.reset()
        } else {
          this.done = this.exited = true
          this.aborted = false
          this.fire()
          this.render()
          this.out.write('\n')
          this.close()
        }
      }
      abort() {
        this.done = this.aborted = true
        this.exited = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      submit() {
        this.done = true
        this.aborted = this.exited = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      _(c, key) {
        let s1 = this.input.slice(0, this.cursor)
        let s2 = this.input.slice(this.cursor)
        this.input = `${s1}${c}${s2}`
        this.cursor = s1.length + 1
        this.complete(this.render)
        this.render()
      }
      delete() {
        if (this.cursor === 0) return this.bell()
        let s1 = this.input.slice(0, this.cursor - 1)
        let s2 = this.input.slice(this.cursor)
        this.input = `${s1}${s2}`
        this.complete(this.render)
        this.cursor = this.cursor - 1
        this.render()
      }
      deleteForward() {
        if (this.cursor * this.scale >= this.rendered.length) return this.bell()
        let s1 = this.input.slice(0, this.cursor)
        let s2 = this.input.slice(this.cursor + 1)
        this.input = `${s1}${s2}`
        this.complete(this.render)
        this.render()
      }
      first() {
        this.moveSelect(0)
        this.render()
      }
      last() {
        this.moveSelect(this.suggestions.length - 1)
        this.render()
      }
      up() {
        if (this.select === 0) {
          this.moveSelect(this.suggestions.length - 1)
        } else {
          this.moveSelect(this.select - 1)
        }
        this.render()
      }
      down() {
        if (this.select === this.suggestions.length - 1) {
          this.moveSelect(0)
        } else {
          this.moveSelect(this.select + 1)
        }
        this.render()
      }
      next() {
        if (this.select === this.suggestions.length - 1) {
          this.moveSelect(0)
        } else this.moveSelect(this.select + 1)
        this.render()
      }
      nextPage() {
        this.moveSelect(Math.min(this.select + this.limit, this.suggestions.length - 1))
        this.render()
      }
      prevPage() {
        this.moveSelect(Math.max(this.select - this.limit, 0))
        this.render()
      }
      left() {
        if (this.cursor <= 0) return this.bell()
        this.cursor = this.cursor - 1
        this.render()
      }
      right() {
        if (this.cursor * this.scale >= this.rendered.length) return this.bell()
        this.cursor = this.cursor + 1
        this.render()
      }
      renderOption(v, hovered, isStart, isEnd) {
        let desc
        let prefix = isStart ? figures.arrowUp : isEnd ? figures.arrowDown : ' '
        let title = hovered ? color.cyan().underline(v.title) : v.title
        prefix = (hovered ? color.cyan(figures.pointer) + ' ' : '  ') + prefix
        if (v.description) {
          desc = ` - ${v.description}`
          if (
            prefix.length + title.length + desc.length >= this.out.columns ||
            v.description.split(/\r?\n/).length > 1
          ) {
            desc = '\n' + wrap(v.description, { margin: 3, width: this.out.columns })
          }
        }
        return prefix + ' ' + title + color.gray(desc || '')
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        else this.out.write(clear(this.outputText, this.out.columns))
        super.render()
        let { startIndex, endIndex } = entriesToDisplay(
          this.select,
          this.choices.length,
          this.limit
        )
        this.outputText = [
          style.symbol(this.done, this.aborted, this.exited),
          color.bold(this.msg),
          style.delimiter(this.completing),
          this.done && this.suggestions[this.select]
            ? this.suggestions[this.select].title
            : (this.rendered = this.transform.render(this.input))
        ].join(' ')
        if (!this.done) {
          const suggestions = this.suggestions
            .slice(startIndex, endIndex)
            .map((item, i) =>
              this.renderOption(
                item,
                this.select === i + startIndex,
                i === 0 && startIndex > 0,
                i + startIndex === endIndex - 1 && endIndex < this.choices.length
              )
            )
            .join('\n')
          this.outputText +=
            `
` + (suggestions || color.gray(this.fallback.title))
        }
        this.out.write(erase.line + cursor.to(0) + this.outputText)
      }
    }
    module2.exports = AutocompletePrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/autocompleteMultiselect.js
var require_autocompleteMultiselect2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/autocompleteMultiselect.js'(
    exports,
    module2
  ) {
    'use strict'
    var color = require_kleur()
    var { cursor } = require_src()
    var MultiselectPrompt = require_multiselect2()
    var { clear, style, figures } = require_util2()
    var AutocompleteMultiselectPrompt = class extends MultiselectPrompt {
      constructor(opts = {}) {
        opts.overrideRender = true
        super(opts)
        this.inputValue = ''
        this.clear = clear('', this.out.columns)
        this.filteredOptions = this.value
        this.render()
      }
      last() {
        this.cursor = this.filteredOptions.length - 1
        this.render()
      }
      next() {
        this.cursor = (this.cursor + 1) % this.filteredOptions.length
        this.render()
      }
      up() {
        if (this.cursor === 0) {
          this.cursor = this.filteredOptions.length - 1
        } else {
          this.cursor--
        }
        this.render()
      }
      down() {
        if (this.cursor === this.filteredOptions.length - 1) {
          this.cursor = 0
        } else {
          this.cursor++
        }
        this.render()
      }
      left() {
        this.filteredOptions[this.cursor].selected = false
        this.render()
      }
      right() {
        if (this.value.filter((e) => e.selected).length >= this.maxChoices) return this.bell()
        this.filteredOptions[this.cursor].selected = true
        this.render()
      }
      delete() {
        if (this.inputValue.length) {
          this.inputValue = this.inputValue.substr(0, this.inputValue.length - 1)
          this.updateFilteredOptions()
        }
      }
      updateFilteredOptions() {
        const currentHighlight = this.filteredOptions[this.cursor]
        this.filteredOptions = this.value.filter((v) => {
          if (this.inputValue) {
            if (typeof v.title === 'string') {
              if (v.title.toLowerCase().includes(this.inputValue.toLowerCase())) {
                return true
              }
            }
            if (typeof v.value === 'string') {
              if (v.value.toLowerCase().includes(this.inputValue.toLowerCase())) {
                return true
              }
            }
            return false
          }
          return true
        })
        const newHighlightIndex = this.filteredOptions.findIndex((v) => v === currentHighlight)
        this.cursor = newHighlightIndex < 0 ? 0 : newHighlightIndex
        this.render()
      }
      handleSpaceToggle() {
        const v = this.filteredOptions[this.cursor]
        if (v.selected) {
          v.selected = false
          this.render()
        } else if (v.disabled || this.value.filter((e) => e.selected).length >= this.maxChoices) {
          return this.bell()
        } else {
          v.selected = true
          this.render()
        }
      }
      handleInputChange(c) {
        this.inputValue = this.inputValue + c
        this.updateFilteredOptions()
      }
      _(c, key) {
        if (c === ' ') {
          this.handleSpaceToggle()
        } else {
          this.handleInputChange(c)
        }
      }
      renderInstructions() {
        if (this.instructions === void 0 || this.instructions) {
          if (typeof this.instructions === 'string') {
            return this.instructions
          }
          return `
Instructions:
    ${figures.arrowUp}/${figures.arrowDown}: Highlight option
    ${figures.arrowLeft}/${figures.arrowRight}/[space]: Toggle selection
    [a,b,c]/delete: Filter choices
    enter/return: Complete answer
`
        }
        return ''
      }
      renderCurrentInput() {
        return `
Filtered results for: ${this.inputValue ? this.inputValue : color.gray('Enter something to filter')}
`
      }
      renderOption(cursor2, v, i) {
        let title
        if (v.disabled)
          title =
            cursor2 === i ? color.gray().underline(v.title) : color.strikethrough().gray(v.title)
        else title = cursor2 === i ? color.cyan().underline(v.title) : v.title
        return (v.selected ? color.green(figures.radioOn) : figures.radioOff) + '  ' + title
      }
      renderDoneOrInstructions() {
        if (this.done) {
          return this.value
            .filter((e) => e.selected)
            .map((v) => v.title)
            .join(', ')
        }
        const output = [color.gray(this.hint), this.renderInstructions(), this.renderCurrentInput()]
        if (this.filteredOptions.length && this.filteredOptions[this.cursor].disabled) {
          output.push(color.yellow(this.warn))
        }
        return output.join(' ')
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        super.render()
        let prompt = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(false),
          this.renderDoneOrInstructions()
        ].join(' ')
        if (this.showMinError) {
          prompt += color.red(`You must select a minimum of ${this.minSelected} choices.`)
          this.showMinError = false
        }
        prompt += this.renderOptions(this.filteredOptions)
        this.out.write(this.clear + prompt)
        this.clear = clear(prompt, this.out.columns)
      }
    }
    module2.exports = AutocompleteMultiselectPrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/confirm.js
var require_confirm2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/confirm.js'(
    exports,
    module2
  ) {
    var color = require_kleur()
    var Prompt = require_prompt2()
    var { style, clear } = require_util2()
    var { erase, cursor } = require_src()
    var ConfirmPrompt = class extends Prompt {
      constructor(opts = {}) {
        super(opts)
        this.msg = opts.message
        this.value = opts.initial
        this.initialValue = !!opts.initial
        this.yesMsg = opts.yes || 'yes'
        this.yesOption = opts.yesOption || '(Y/n)'
        this.noMsg = opts.no || 'no'
        this.noOption = opts.noOption || '(y/N)'
        this.render()
      }
      reset() {
        this.value = this.initialValue
        this.fire()
        this.render()
      }
      exit() {
        this.abort()
      }
      abort() {
        this.done = this.aborted = true
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      submit() {
        this.value = this.value || false
        this.done = true
        this.aborted = false
        this.fire()
        this.render()
        this.out.write('\n')
        this.close()
      }
      _(c, key) {
        if (c.toLowerCase() === 'y') {
          this.value = true
          return this.submit()
        }
        if (c.toLowerCase() === 'n') {
          this.value = false
          return this.submit()
        }
        return this.bell()
      }
      render() {
        if (this.closed) return
        if (this.firstRender) this.out.write(cursor.hide)
        else this.out.write(clear(this.outputText, this.out.columns))
        super.render()
        this.outputText = [
          style.symbol(this.done, this.aborted),
          color.bold(this.msg),
          style.delimiter(this.done),
          this.done
            ? this.value
              ? this.yesMsg
              : this.noMsg
            : color.gray(this.initialValue ? this.yesOption : this.noOption)
        ].join(' ')
        this.out.write(erase.line + cursor.to(0) + this.outputText)
      }
    }
    module2.exports = ConfirmPrompt
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/index.js
var require_elements2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/elements/index.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = {
      TextPrompt: require_text2(),
      SelectPrompt: require_select2(),
      TogglePrompt: require_toggle2(),
      DatePrompt: require_date2(),
      NumberPrompt: require_number2(),
      MultiselectPrompt: require_multiselect2(),
      AutocompletePrompt: require_autocomplete2(),
      AutocompleteMultiselectPrompt: require_autocompleteMultiselect2(),
      ConfirmPrompt: require_confirm2()
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/prompts.js
var require_prompts2 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/prompts.js'(exports) {
    'use strict'
    var $ = exports
    var el = require_elements2()
    var noop = (v) => v
    function toPrompt(type, args, opts = {}) {
      return new Promise((res, rej) => {
        const p = new el[type](args)
        const onAbort = opts.onAbort || noop
        const onSubmit = opts.onSubmit || noop
        const onExit = opts.onExit || noop
        p.on('state', args.onState || noop)
        p.on('submit', (x) => res(onSubmit(x)))
        p.on('exit', (x) => res(onExit(x)))
        p.on('abort', (x) => rej(onAbort(x)))
      })
    }
    $.text = (args) => toPrompt('TextPrompt', args)
    $.password = (args) => {
      args.style = 'password'
      return $.text(args)
    }
    $.invisible = (args) => {
      args.style = 'invisible'
      return $.text(args)
    }
    $.number = (args) => toPrompt('NumberPrompt', args)
    $.date = (args) => toPrompt('DatePrompt', args)
    $.confirm = (args) => toPrompt('ConfirmPrompt', args)
    $.list = (args) => {
      const sep = args.separator || ','
      return toPrompt('TextPrompt', args, {
        onSubmit: (str) => str.split(sep).map((s) => s.trim())
      })
    }
    $.toggle = (args) => toPrompt('TogglePrompt', args)
    $.select = (args) => toPrompt('SelectPrompt', args)
    $.multiselect = (args) => {
      args.choices = [].concat(args.choices || [])
      const toSelected = (items) => items.filter((item) => item.selected).map((item) => item.value)
      return toPrompt('MultiselectPrompt', args, {
        onAbort: toSelected,
        onSubmit: toSelected
      })
    }
    $.autocompleteMultiselect = (args) => {
      args.choices = [].concat(args.choices || [])
      const toSelected = (items) => items.filter((item) => item.selected).map((item) => item.value)
      return toPrompt('AutocompleteMultiselectPrompt', args, {
        onAbort: toSelected,
        onSubmit: toSelected
      })
    }
    var byTitle = (input, choices) =>
      Promise.resolve(
        choices.filter(
          (item) => item.title.slice(0, input.length).toLowerCase() === input.toLowerCase()
        )
      )
    $.autocomplete = (args) => {
      args.suggest = args.suggest || byTitle
      args.choices = [].concat(args.choices || [])
      return toPrompt('AutocompletePrompt', args)
    }
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/index.js
var require_lib = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/lib/index.js'(exports, module2) {
    'use strict'
    var prompts2 = require_prompts2()
    var passOn = ['suggest', 'format', 'onState', 'validate', 'onRender', 'type']
    var noop = () => {}
    async function prompt(questions = [], { onSubmit = noop, onCancel = noop } = {}) {
      const answers = {}
      const override2 = prompt._override || {}
      questions = [].concat(questions)
      let answer, question, quit, name, type, lastPrompt
      const getFormattedAnswer = async (question2, answer2, skipValidation = false) => {
        if (!skipValidation && question2.validate && question2.validate(answer2) !== true) {
          return
        }
        return question2.format ? await question2.format(answer2, answers) : answer2
      }
      for (question of questions) {
        ;({ name, type } = question)
        if (typeof type === 'function') {
          type = await type(answer, { ...answers }, question)
          question['type'] = type
        }
        if (!type) continue
        for (let key in question) {
          if (passOn.includes(key)) continue
          let value = question[key]
          question[key] =
            typeof value === 'function' ? await value(answer, { ...answers }, lastPrompt) : value
        }
        lastPrompt = question
        if (typeof question.message !== 'string') {
          throw new Error('prompt message is required')
        }
        ;({ name, type } = question)
        if (prompts2[type] === void 0) {
          throw new Error(`prompt type (${type}) is not defined`)
        }
        if (override2[question.name] !== void 0) {
          answer = await getFormattedAnswer(question, override2[question.name])
          if (answer !== void 0) {
            answers[name] = answer
            continue
          }
        }
        try {
          answer = prompt._injected
            ? getInjectedAnswer(prompt._injected, question.initial)
            : await prompts2[type](question)
          answers[name] = answer = await getFormattedAnswer(question, answer, true)
          quit = await onSubmit(question, answer, answers)
        } catch (err) {
          quit = !(await onCancel(question, answers))
        }
        if (quit) return answers
      }
      return answers
    }
    function getInjectedAnswer(injected, deafultValue) {
      const answer = injected.shift()
      if (answer instanceof Error) {
        throw answer
      }
      return answer === void 0 ? deafultValue : answer
    }
    function inject(answers) {
      prompt._injected = (prompt._injected || []).concat(answers)
    }
    function override(answers) {
      prompt._override = Object.assign({}, answers)
    }
    module2.exports = Object.assign(prompt, { prompt, prompts: prompts2, inject, override })
  }
})

// ../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/index.js
var require_prompts3 = __commonJS({
  '../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts/index.js'(exports, module2) {
    function isNodeLT(tar) {
      tar = (Array.isArray(tar) ? tar : tar.split('.')).map(Number)
      let i = 0,
        src = process.versions.node.split('.').map(Number)
      for (; i < tar.length; i++) {
        if (src[i] > tar[i]) return false
        if (tar[i] > src[i]) return true
      }
      return false
    }
    module2.exports = isNodeLT('8.6.0') ? require_dist() : require_lib()
  }
})

// ../../node_modules/.pnpm/git-clone@0.2.0/node_modules/git-clone/private/util.js
var require_util3 = __commonJS({
  '../../node_modules/.pnpm/git-clone@0.2.0/node_modules/git-clone/private/util.js'(exports) {
    function git(opts) {
      return opts.git || 'git'
    }
    exports.buildCloneCommand = function (repo, targetPath, opts) {
      let args = ['clone']
      const userArgs = opts.args || []
      if (opts.shallow) {
        if (userArgs.indexOf('--depth') >= 0) {
          throw new Error("'--depth' cannot be specified when shallow is set to 'true'")
        }
        args.push('--depth', '1')
      }
      args = args.concat(userArgs)
      args.push('--', repo, targetPath)
      return [git(opts), args]
    }
    exports.buildCheckoutCommand = function (ref, opts) {
      return [git(opts), ['checkout', ref]]
    }
  }
})

// ../../node_modules/.pnpm/git-clone@0.2.0/node_modules/git-clone/private/impl.js
var require_impl = __commonJS({
  '../../node_modules/.pnpm/git-clone@0.2.0/node_modules/git-clone/private/impl.js'(
    exports,
    module2
  ) {
    var { buildCloneCommand, buildCheckoutCommand } = require_util3()
    var spawn = require('child_process').spawn
    module2.exports = function clone2(repo, targetPath, opts, onSuccess, onError) {
      const [cmd, args] = buildCloneCommand(repo, targetPath, opts)
      const proc = spawn(cmd, args)
      proc.on('close', (status) => {
        if (status == 0) {
          if (opts.checkout) {
            _checkout()
          } else {
            onSuccess()
          }
        } else {
          onError(new Error("'git clone' failed with status " + status))
        }
      })
      function _checkout() {
        const [cmd2, args2] = buildCheckoutCommand(opts.checkout, opts)
        const proc2 = spawn(cmd2, args2, { cwd: targetPath })
        proc2.on('close', function (status) {
          if (status == 0) {
            onSuccess()
          } else {
            onError(new Error("'git checkout' failed with status " + status))
          }
        })
      }
    }
  }
})

// ../../node_modules/.pnpm/git-clone@0.2.0/node_modules/git-clone/promise.js
var require_promise = __commonJS({
  '../../node_modules/.pnpm/git-clone@0.2.0/node_modules/git-clone/promise.js'(exports, module2) {
    var impl = require_impl()
    module2.exports = function (repo, targetPath, opts) {
      return new Promise((yes, no) => {
        impl(repo, targetPath, opts || {}, yes, no)
      })
    }
  }
})

// ../../node_modules/.pnpm/color-name@1.1.4/node_modules/color-name/index.js
var require_color_name = __commonJS({
  '../../node_modules/.pnpm/color-name@1.1.4/node_modules/color-name/index.js'(exports, module2) {
    'use strict'
    module2.exports = {
      aliceblue: [240, 248, 255],
      antiquewhite: [250, 235, 215],
      aqua: [0, 255, 255],
      aquamarine: [127, 255, 212],
      azure: [240, 255, 255],
      beige: [245, 245, 220],
      bisque: [255, 228, 196],
      black: [0, 0, 0],
      blanchedalmond: [255, 235, 205],
      blue: [0, 0, 255],
      blueviolet: [138, 43, 226],
      brown: [165, 42, 42],
      burlywood: [222, 184, 135],
      cadetblue: [95, 158, 160],
      chartreuse: [127, 255, 0],
      chocolate: [210, 105, 30],
      coral: [255, 127, 80],
      cornflowerblue: [100, 149, 237],
      cornsilk: [255, 248, 220],
      crimson: [220, 20, 60],
      cyan: [0, 255, 255],
      darkblue: [0, 0, 139],
      darkcyan: [0, 139, 139],
      darkgoldenrod: [184, 134, 11],
      darkgray: [169, 169, 169],
      darkgreen: [0, 100, 0],
      darkgrey: [169, 169, 169],
      darkkhaki: [189, 183, 107],
      darkmagenta: [139, 0, 139],
      darkolivegreen: [85, 107, 47],
      darkorange: [255, 140, 0],
      darkorchid: [153, 50, 204],
      darkred: [139, 0, 0],
      darksalmon: [233, 150, 122],
      darkseagreen: [143, 188, 143],
      darkslateblue: [72, 61, 139],
      darkslategray: [47, 79, 79],
      darkslategrey: [47, 79, 79],
      darkturquoise: [0, 206, 209],
      darkviolet: [148, 0, 211],
      deeppink: [255, 20, 147],
      deepskyblue: [0, 191, 255],
      dimgray: [105, 105, 105],
      dimgrey: [105, 105, 105],
      dodgerblue: [30, 144, 255],
      firebrick: [178, 34, 34],
      floralwhite: [255, 250, 240],
      forestgreen: [34, 139, 34],
      fuchsia: [255, 0, 255],
      gainsboro: [220, 220, 220],
      ghostwhite: [248, 248, 255],
      gold: [255, 215, 0],
      goldenrod: [218, 165, 32],
      gray: [128, 128, 128],
      green: [0, 128, 0],
      greenyellow: [173, 255, 47],
      grey: [128, 128, 128],
      honeydew: [240, 255, 240],
      hotpink: [255, 105, 180],
      indianred: [205, 92, 92],
      indigo: [75, 0, 130],
      ivory: [255, 255, 240],
      khaki: [240, 230, 140],
      lavender: [230, 230, 250],
      lavenderblush: [255, 240, 245],
      lawngreen: [124, 252, 0],
      lemonchiffon: [255, 250, 205],
      lightblue: [173, 216, 230],
      lightcoral: [240, 128, 128],
      lightcyan: [224, 255, 255],
      lightgoldenrodyellow: [250, 250, 210],
      lightgray: [211, 211, 211],
      lightgreen: [144, 238, 144],
      lightgrey: [211, 211, 211],
      lightpink: [255, 182, 193],
      lightsalmon: [255, 160, 122],
      lightseagreen: [32, 178, 170],
      lightskyblue: [135, 206, 250],
      lightslategray: [119, 136, 153],
      lightslategrey: [119, 136, 153],
      lightsteelblue: [176, 196, 222],
      lightyellow: [255, 255, 224],
      lime: [0, 255, 0],
      limegreen: [50, 205, 50],
      linen: [250, 240, 230],
      magenta: [255, 0, 255],
      maroon: [128, 0, 0],
      mediumaquamarine: [102, 205, 170],
      mediumblue: [0, 0, 205],
      mediumorchid: [186, 85, 211],
      mediumpurple: [147, 112, 219],
      mediumseagreen: [60, 179, 113],
      mediumslateblue: [123, 104, 238],
      mediumspringgreen: [0, 250, 154],
      mediumturquoise: [72, 209, 204],
      mediumvioletred: [199, 21, 133],
      midnightblue: [25, 25, 112],
      mintcream: [245, 255, 250],
      mistyrose: [255, 228, 225],
      moccasin: [255, 228, 181],
      navajowhite: [255, 222, 173],
      navy: [0, 0, 128],
      oldlace: [253, 245, 230],
      olive: [128, 128, 0],
      olivedrab: [107, 142, 35],
      orange: [255, 165, 0],
      orangered: [255, 69, 0],
      orchid: [218, 112, 214],
      palegoldenrod: [238, 232, 170],
      palegreen: [152, 251, 152],
      paleturquoise: [175, 238, 238],
      palevioletred: [219, 112, 147],
      papayawhip: [255, 239, 213],
      peachpuff: [255, 218, 185],
      peru: [205, 133, 63],
      pink: [255, 192, 203],
      plum: [221, 160, 221],
      powderblue: [176, 224, 230],
      purple: [128, 0, 128],
      rebeccapurple: [102, 51, 153],
      red: [255, 0, 0],
      rosybrown: [188, 143, 143],
      royalblue: [65, 105, 225],
      saddlebrown: [139, 69, 19],
      salmon: [250, 128, 114],
      sandybrown: [244, 164, 96],
      seagreen: [46, 139, 87],
      seashell: [255, 245, 238],
      sienna: [160, 82, 45],
      silver: [192, 192, 192],
      skyblue: [135, 206, 235],
      slateblue: [106, 90, 205],
      slategray: [112, 128, 144],
      slategrey: [112, 128, 144],
      snow: [255, 250, 250],
      springgreen: [0, 255, 127],
      steelblue: [70, 130, 180],
      tan: [210, 180, 140],
      teal: [0, 128, 128],
      thistle: [216, 191, 216],
      tomato: [255, 99, 71],
      turquoise: [64, 224, 208],
      violet: [238, 130, 238],
      wheat: [245, 222, 179],
      white: [255, 255, 255],
      whitesmoke: [245, 245, 245],
      yellow: [255, 255, 0],
      yellowgreen: [154, 205, 50]
    }
  }
})

// ../../node_modules/.pnpm/color-convert@2.0.1/node_modules/color-convert/conversions.js
var require_conversions = __commonJS({
  '../../node_modules/.pnpm/color-convert@2.0.1/node_modules/color-convert/conversions.js'(
    exports,
    module2
  ) {
    var cssKeywords = require_color_name()
    var reverseKeywords = {}
    for (const key of Object.keys(cssKeywords)) {
      reverseKeywords[cssKeywords[key]] = key
    }
    var convert = {
      rgb: { channels: 3, labels: 'rgb' },
      hsl: { channels: 3, labels: 'hsl' },
      hsv: { channels: 3, labels: 'hsv' },
      hwb: { channels: 3, labels: 'hwb' },
      cmyk: { channels: 4, labels: 'cmyk' },
      xyz: { channels: 3, labels: 'xyz' },
      lab: { channels: 3, labels: 'lab' },
      lch: { channels: 3, labels: 'lch' },
      hex: { channels: 1, labels: ['hex'] },
      keyword: { channels: 1, labels: ['keyword'] },
      ansi16: { channels: 1, labels: ['ansi16'] },
      ansi256: { channels: 1, labels: ['ansi256'] },
      hcg: { channels: 3, labels: ['h', 'c', 'g'] },
      apple: { channels: 3, labels: ['r16', 'g16', 'b16'] },
      gray: { channels: 1, labels: ['gray'] }
    }
    module2.exports = convert
    for (const model of Object.keys(convert)) {
      if (!('channels' in convert[model])) {
        throw new Error('missing channels property: ' + model)
      }
      if (!('labels' in convert[model])) {
        throw new Error('missing channel labels property: ' + model)
      }
      if (convert[model].labels.length !== convert[model].channels) {
        throw new Error('channel and label counts mismatch: ' + model)
      }
      const { channels, labels } = convert[model]
      delete convert[model].channels
      delete convert[model].labels
      Object.defineProperty(convert[model], 'channels', { value: channels })
      Object.defineProperty(convert[model], 'labels', { value: labels })
    }
    convert.rgb.hsl = function (rgb) {
      const r = rgb[0] / 255
      const g = rgb[1] / 255
      const b = rgb[2] / 255
      const min = Math.min(r, g, b)
      const max = Math.max(r, g, b)
      const delta = max - min
      let h
      let s
      if (max === min) {
        h = 0
      } else if (r === max) {
        h = (g - b) / delta
      } else if (g === max) {
        h = 2 + (b - r) / delta
      } else if (b === max) {
        h = 4 + (r - g) / delta
      }
      h = Math.min(h * 60, 360)
      if (h < 0) {
        h += 360
      }
      const l = (min + max) / 2
      if (max === min) {
        s = 0
      } else if (l <= 0.5) {
        s = delta / (max + min)
      } else {
        s = delta / (2 - max - min)
      }
      return [h, s * 100, l * 100]
    }
    convert.rgb.hsv = function (rgb) {
      let rdif
      let gdif
      let bdif
      let h
      let s
      const r = rgb[0] / 255
      const g = rgb[1] / 255
      const b = rgb[2] / 255
      const v = Math.max(r, g, b)
      const diff = v - Math.min(r, g, b)
      const diffc = function (c) {
        return (v - c) / 6 / diff + 1 / 2
      }
      if (diff === 0) {
        h = 0
        s = 0
      } else {
        s = diff / v
        rdif = diffc(r)
        gdif = diffc(g)
        bdif = diffc(b)
        if (r === v) {
          h = bdif - gdif
        } else if (g === v) {
          h = 1 / 3 + rdif - bdif
        } else if (b === v) {
          h = 2 / 3 + gdif - rdif
        }
        if (h < 0) {
          h += 1
        } else if (h > 1) {
          h -= 1
        }
      }
      return [h * 360, s * 100, v * 100]
    }
    convert.rgb.hwb = function (rgb) {
      const r = rgb[0]
      const g = rgb[1]
      let b = rgb[2]
      const h = convert.rgb.hsl(rgb)[0]
      const w = (1 / 255) * Math.min(r, Math.min(g, b))
      b = 1 - (1 / 255) * Math.max(r, Math.max(g, b))
      return [h, w * 100, b * 100]
    }
    convert.rgb.cmyk = function (rgb) {
      const r = rgb[0] / 255
      const g = rgb[1] / 255
      const b = rgb[2] / 255
      const k = Math.min(1 - r, 1 - g, 1 - b)
      const c = (1 - r - k) / (1 - k) || 0
      const m = (1 - g - k) / (1 - k) || 0
      const y = (1 - b - k) / (1 - k) || 0
      return [c * 100, m * 100, y * 100, k * 100]
    }
    function comparativeDistance(x, y) {
      return (x[0] - y[0]) ** 2 + (x[1] - y[1]) ** 2 + (x[2] - y[2]) ** 2
    }
    convert.rgb.keyword = function (rgb) {
      const reversed = reverseKeywords[rgb]
      if (reversed) {
        return reversed
      }
      let currentClosestDistance = Infinity
      let currentClosestKeyword
      for (const keyword of Object.keys(cssKeywords)) {
        const value = cssKeywords[keyword]
        const distance = comparativeDistance(rgb, value)
        if (distance < currentClosestDistance) {
          currentClosestDistance = distance
          currentClosestKeyword = keyword
        }
      }
      return currentClosestKeyword
    }
    convert.keyword.rgb = function (keyword) {
      return cssKeywords[keyword]
    }
    convert.rgb.xyz = function (rgb) {
      let r = rgb[0] / 255
      let g = rgb[1] / 255
      let b = rgb[2] / 255
      r = r > 0.04045 ? ((r + 0.055) / 1.055) ** 2.4 : r / 12.92
      g = g > 0.04045 ? ((g + 0.055) / 1.055) ** 2.4 : g / 12.92
      b = b > 0.04045 ? ((b + 0.055) / 1.055) ** 2.4 : b / 12.92
      const x = r * 0.4124 + g * 0.3576 + b * 0.1805
      const y = r * 0.2126 + g * 0.7152 + b * 0.0722
      const z = r * 0.0193 + g * 0.1192 + b * 0.9505
      return [x * 100, y * 100, z * 100]
    }
    convert.rgb.lab = function (rgb) {
      const xyz = convert.rgb.xyz(rgb)
      let x = xyz[0]
      let y = xyz[1]
      let z = xyz[2]
      x /= 95.047
      y /= 100
      z /= 108.883
      x = x > 8856e-6 ? x ** (1 / 3) : 7.787 * x + 16 / 116
      y = y > 8856e-6 ? y ** (1 / 3) : 7.787 * y + 16 / 116
      z = z > 8856e-6 ? z ** (1 / 3) : 7.787 * z + 16 / 116
      const l = 116 * y - 16
      const a = 500 * (x - y)
      const b = 200 * (y - z)
      return [l, a, b]
    }
    convert.hsl.rgb = function (hsl) {
      const h = hsl[0] / 360
      const s = hsl[1] / 100
      const l = hsl[2] / 100
      let t2
      let t3
      let val
      if (s === 0) {
        val = l * 255
        return [val, val, val]
      }
      if (l < 0.5) {
        t2 = l * (1 + s)
      } else {
        t2 = l + s - l * s
      }
      const t1 = 2 * l - t2
      const rgb = [0, 0, 0]
      for (let i = 0; i < 3; i++) {
        t3 = h + (1 / 3) * -(i - 1)
        if (t3 < 0) {
          t3++
        }
        if (t3 > 1) {
          t3--
        }
        if (6 * t3 < 1) {
          val = t1 + (t2 - t1) * 6 * t3
        } else if (2 * t3 < 1) {
          val = t2
        } else if (3 * t3 < 2) {
          val = t1 + (t2 - t1) * (2 / 3 - t3) * 6
        } else {
          val = t1
        }
        rgb[i] = val * 255
      }
      return rgb
    }
    convert.hsl.hsv = function (hsl) {
      const h = hsl[0]
      let s = hsl[1] / 100
      let l = hsl[2] / 100
      let smin = s
      const lmin = Math.max(l, 0.01)
      l *= 2
      s *= l <= 1 ? l : 2 - l
      smin *= lmin <= 1 ? lmin : 2 - lmin
      const v = (l + s) / 2
      const sv = l === 0 ? (2 * smin) / (lmin + smin) : (2 * s) / (l + s)
      return [h, sv * 100, v * 100]
    }
    convert.hsv.rgb = function (hsv) {
      const h = hsv[0] / 60
      const s = hsv[1] / 100
      let v = hsv[2] / 100
      const hi = Math.floor(h) % 6
      const f = h - Math.floor(h)
      const p = 255 * v * (1 - s)
      const q = 255 * v * (1 - s * f)
      const t = 255 * v * (1 - s * (1 - f))
      v *= 255
      switch (hi) {
        case 0:
          return [v, t, p]
        case 1:
          return [q, v, p]
        case 2:
          return [p, v, t]
        case 3:
          return [p, q, v]
        case 4:
          return [t, p, v]
        case 5:
          return [v, p, q]
      }
    }
    convert.hsv.hsl = function (hsv) {
      const h = hsv[0]
      const s = hsv[1] / 100
      const v = hsv[2] / 100
      const vmin = Math.max(v, 0.01)
      let sl
      let l
      l = (2 - s) * v
      const lmin = (2 - s) * vmin
      sl = s * vmin
      sl /= lmin <= 1 ? lmin : 2 - lmin
      sl = sl || 0
      l /= 2
      return [h, sl * 100, l * 100]
    }
    convert.hwb.rgb = function (hwb) {
      const h = hwb[0] / 360
      let wh = hwb[1] / 100
      let bl = hwb[2] / 100
      const ratio = wh + bl
      let f
      if (ratio > 1) {
        wh /= ratio
        bl /= ratio
      }
      const i = Math.floor(6 * h)
      const v = 1 - bl
      f = 6 * h - i
      if ((i & 1) !== 0) {
        f = 1 - f
      }
      const n = wh + f * (v - wh)
      let r
      let g
      let b
      switch (i) {
        default:
        case 6:
        case 0:
          r = v
          g = n
          b = wh
          break
        case 1:
          r = n
          g = v
          b = wh
          break
        case 2:
          r = wh
          g = v
          b = n
          break
        case 3:
          r = wh
          g = n
          b = v
          break
        case 4:
          r = n
          g = wh
          b = v
          break
        case 5:
          r = v
          g = wh
          b = n
          break
      }
      return [r * 255, g * 255, b * 255]
    }
    convert.cmyk.rgb = function (cmyk) {
      const c = cmyk[0] / 100
      const m = cmyk[1] / 100
      const y = cmyk[2] / 100
      const k = cmyk[3] / 100
      const r = 1 - Math.min(1, c * (1 - k) + k)
      const g = 1 - Math.min(1, m * (1 - k) + k)
      const b = 1 - Math.min(1, y * (1 - k) + k)
      return [r * 255, g * 255, b * 255]
    }
    convert.xyz.rgb = function (xyz) {
      const x = xyz[0] / 100
      const y = xyz[1] / 100
      const z = xyz[2] / 100
      let r
      let g
      let b
      r = x * 3.2406 + y * -1.5372 + z * -0.4986
      g = x * -0.9689 + y * 1.8758 + z * 0.0415
      b = x * 0.0557 + y * -0.204 + z * 1.057
      r = r > 31308e-7 ? 1.055 * r ** (1 / 2.4) - 0.055 : r * 12.92
      g = g > 31308e-7 ? 1.055 * g ** (1 / 2.4) - 0.055 : g * 12.92
      b = b > 31308e-7 ? 1.055 * b ** (1 / 2.4) - 0.055 : b * 12.92
      r = Math.min(Math.max(0, r), 1)
      g = Math.min(Math.max(0, g), 1)
      b = Math.min(Math.max(0, b), 1)
      return [r * 255, g * 255, b * 255]
    }
    convert.xyz.lab = function (xyz) {
      let x = xyz[0]
      let y = xyz[1]
      let z = xyz[2]
      x /= 95.047
      y /= 100
      z /= 108.883
      x = x > 8856e-6 ? x ** (1 / 3) : 7.787 * x + 16 / 116
      y = y > 8856e-6 ? y ** (1 / 3) : 7.787 * y + 16 / 116
      z = z > 8856e-6 ? z ** (1 / 3) : 7.787 * z + 16 / 116
      const l = 116 * y - 16
      const a = 500 * (x - y)
      const b = 200 * (y - z)
      return [l, a, b]
    }
    convert.lab.xyz = function (lab) {
      const l = lab[0]
      const a = lab[1]
      const b = lab[2]
      let x
      let y
      let z
      y = (l + 16) / 116
      x = a / 500 + y
      z = y - b / 200
      const y2 = y ** 3
      const x2 = x ** 3
      const z2 = z ** 3
      y = y2 > 8856e-6 ? y2 : (y - 16 / 116) / 7.787
      x = x2 > 8856e-6 ? x2 : (x - 16 / 116) / 7.787
      z = z2 > 8856e-6 ? z2 : (z - 16 / 116) / 7.787
      x *= 95.047
      y *= 100
      z *= 108.883
      return [x, y, z]
    }
    convert.lab.lch = function (lab) {
      const l = lab[0]
      const a = lab[1]
      const b = lab[2]
      let h
      const hr = Math.atan2(b, a)
      h = (hr * 360) / 2 / Math.PI
      if (h < 0) {
        h += 360
      }
      const c = Math.sqrt(a * a + b * b)
      return [l, c, h]
    }
    convert.lch.lab = function (lch) {
      const l = lch[0]
      const c = lch[1]
      const h = lch[2]
      const hr = (h / 360) * 2 * Math.PI
      const a = c * Math.cos(hr)
      const b = c * Math.sin(hr)
      return [l, a, b]
    }
    convert.rgb.ansi16 = function (args, saturation = null) {
      const [r, g, b] = args
      let value = saturation === null ? convert.rgb.hsv(args)[2] : saturation
      value = Math.round(value / 50)
      if (value === 0) {
        return 30
      }
      let ansi =
        30 + ((Math.round(b / 255) << 2) | (Math.round(g / 255) << 1) | Math.round(r / 255))
      if (value === 2) {
        ansi += 60
      }
      return ansi
    }
    convert.hsv.ansi16 = function (args) {
      return convert.rgb.ansi16(convert.hsv.rgb(args), args[2])
    }
    convert.rgb.ansi256 = function (args) {
      const r = args[0]
      const g = args[1]
      const b = args[2]
      if (r === g && g === b) {
        if (r < 8) {
          return 16
        }
        if (r > 248) {
          return 231
        }
        return Math.round(((r - 8) / 247) * 24) + 232
      }
      const ansi =
        16 +
        36 * Math.round((r / 255) * 5) +
        6 * Math.round((g / 255) * 5) +
        Math.round((b / 255) * 5)
      return ansi
    }
    convert.ansi16.rgb = function (args) {
      let color = args % 10
      if (color === 0 || color === 7) {
        if (args > 50) {
          color += 3.5
        }
        color = (color / 10.5) * 255
        return [color, color, color]
      }
      const mult = (~~(args > 50) + 1) * 0.5
      const r = (color & 1) * mult * 255
      const g = ((color >> 1) & 1) * mult * 255
      const b = ((color >> 2) & 1) * mult * 255
      return [r, g, b]
    }
    convert.ansi256.rgb = function (args) {
      if (args >= 232) {
        const c = (args - 232) * 10 + 8
        return [c, c, c]
      }
      args -= 16
      let rem
      const r = (Math.floor(args / 36) / 5) * 255
      const g = (Math.floor((rem = args % 36) / 6) / 5) * 255
      const b = ((rem % 6) / 5) * 255
      return [r, g, b]
    }
    convert.rgb.hex = function (args) {
      const integer =
        ((Math.round(args[0]) & 255) << 16) +
        ((Math.round(args[1]) & 255) << 8) +
        (Math.round(args[2]) & 255)
      const string = integer.toString(16).toUpperCase()
      return '000000'.substring(string.length) + string
    }
    convert.hex.rgb = function (args) {
      const match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i)
      if (!match) {
        return [0, 0, 0]
      }
      let colorString = match[0]
      if (match[0].length === 3) {
        colorString = colorString
          .split('')
          .map((char) => {
            return char + char
          })
          .join('')
      }
      const integer = parseInt(colorString, 16)
      const r = (integer >> 16) & 255
      const g = (integer >> 8) & 255
      const b = integer & 255
      return [r, g, b]
    }
    convert.rgb.hcg = function (rgb) {
      const r = rgb[0] / 255
      const g = rgb[1] / 255
      const b = rgb[2] / 255
      const max = Math.max(Math.max(r, g), b)
      const min = Math.min(Math.min(r, g), b)
      const chroma = max - min
      let grayscale
      let hue
      if (chroma < 1) {
        grayscale = min / (1 - chroma)
      } else {
        grayscale = 0
      }
      if (chroma <= 0) {
        hue = 0
      } else if (max === r) {
        hue = ((g - b) / chroma) % 6
      } else if (max === g) {
        hue = 2 + (b - r) / chroma
      } else {
        hue = 4 + (r - g) / chroma
      }
      hue /= 6
      hue %= 1
      return [hue * 360, chroma * 100, grayscale * 100]
    }
    convert.hsl.hcg = function (hsl) {
      const s = hsl[1] / 100
      const l = hsl[2] / 100
      const c = l < 0.5 ? 2 * s * l : 2 * s * (1 - l)
      let f = 0
      if (c < 1) {
        f = (l - 0.5 * c) / (1 - c)
      }
      return [hsl[0], c * 100, f * 100]
    }
    convert.hsv.hcg = function (hsv) {
      const s = hsv[1] / 100
      const v = hsv[2] / 100
      const c = s * v
      let f = 0
      if (c < 1) {
        f = (v - c) / (1 - c)
      }
      return [hsv[0], c * 100, f * 100]
    }
    convert.hcg.rgb = function (hcg) {
      const h = hcg[0] / 360
      const c = hcg[1] / 100
      const g = hcg[2] / 100
      if (c === 0) {
        return [g * 255, g * 255, g * 255]
      }
      const pure = [0, 0, 0]
      const hi = (h % 1) * 6
      const v = hi % 1
      const w = 1 - v
      let mg = 0
      switch (Math.floor(hi)) {
        case 0:
          pure[0] = 1
          pure[1] = v
          pure[2] = 0
          break
        case 1:
          pure[0] = w
          pure[1] = 1
          pure[2] = 0
          break
        case 2:
          pure[0] = 0
          pure[1] = 1
          pure[2] = v
          break
        case 3:
          pure[0] = 0
          pure[1] = w
          pure[2] = 1
          break
        case 4:
          pure[0] = v
          pure[1] = 0
          pure[2] = 1
          break
        default:
          pure[0] = 1
          pure[1] = 0
          pure[2] = w
      }
      mg = (1 - c) * g
      return [(c * pure[0] + mg) * 255, (c * pure[1] + mg) * 255, (c * pure[2] + mg) * 255]
    }
    convert.hcg.hsv = function (hcg) {
      const c = hcg[1] / 100
      const g = hcg[2] / 100
      const v = c + g * (1 - c)
      let f = 0
      if (v > 0) {
        f = c / v
      }
      return [hcg[0], f * 100, v * 100]
    }
    convert.hcg.hsl = function (hcg) {
      const c = hcg[1] / 100
      const g = hcg[2] / 100
      const l = g * (1 - c) + 0.5 * c
      let s = 0
      if (l > 0 && l < 0.5) {
        s = c / (2 * l)
      } else if (l >= 0.5 && l < 1) {
        s = c / (2 * (1 - l))
      }
      return [hcg[0], s * 100, l * 100]
    }
    convert.hcg.hwb = function (hcg) {
      const c = hcg[1] / 100
      const g = hcg[2] / 100
      const v = c + g * (1 - c)
      return [hcg[0], (v - c) * 100, (1 - v) * 100]
    }
    convert.hwb.hcg = function (hwb) {
      const w = hwb[1] / 100
      const b = hwb[2] / 100
      const v = 1 - b
      const c = v - w
      let g = 0
      if (c < 1) {
        g = (v - c) / (1 - c)
      }
      return [hwb[0], c * 100, g * 100]
    }
    convert.apple.rgb = function (apple) {
      return [(apple[0] / 65535) * 255, (apple[1] / 65535) * 255, (apple[2] / 65535) * 255]
    }
    convert.rgb.apple = function (rgb) {
      return [(rgb[0] / 255) * 65535, (rgb[1] / 255) * 65535, (rgb[2] / 255) * 65535]
    }
    convert.gray.rgb = function (args) {
      return [(args[0] / 100) * 255, (args[0] / 100) * 255, (args[0] / 100) * 255]
    }
    convert.gray.hsl = function (args) {
      return [0, 0, args[0]]
    }
    convert.gray.hsv = convert.gray.hsl
    convert.gray.hwb = function (gray2) {
      return [0, 100, gray2[0]]
    }
    convert.gray.cmyk = function (gray2) {
      return [0, 0, 0, gray2[0]]
    }
    convert.gray.lab = function (gray2) {
      return [gray2[0], 0, 0]
    }
    convert.gray.hex = function (gray2) {
      const val = Math.round((gray2[0] / 100) * 255) & 255
      const integer = (val << 16) + (val << 8) + val
      const string = integer.toString(16).toUpperCase()
      return '000000'.substring(string.length) + string
    }
    convert.rgb.gray = function (rgb) {
      const val = (rgb[0] + rgb[1] + rgb[2]) / 3
      return [(val / 255) * 100]
    }
  }
})

// ../../node_modules/.pnpm/color-convert@2.0.1/node_modules/color-convert/route.js
var require_route = __commonJS({
  '../../node_modules/.pnpm/color-convert@2.0.1/node_modules/color-convert/route.js'(
    exports,
    module2
  ) {
    var conversions = require_conversions()
    function buildGraph() {
      const graph = {}
      const models = Object.keys(conversions)
      for (let len = models.length, i = 0; i < len; i++) {
        graph[models[i]] = {
          distance: -1,
          parent: null
        }
      }
      return graph
    }
    function deriveBFS(fromModel) {
      const graph = buildGraph()
      const queue = [fromModel]
      graph[fromModel].distance = 0
      while (queue.length) {
        const current = queue.pop()
        const adjacents = Object.keys(conversions[current])
        for (let len = adjacents.length, i = 0; i < len; i++) {
          const adjacent = adjacents[i]
          const node = graph[adjacent]
          if (node.distance === -1) {
            node.distance = graph[current].distance + 1
            node.parent = current
            queue.unshift(adjacent)
          }
        }
      }
      return graph
    }
    function link(from, to) {
      return function (args) {
        return to(from(args))
      }
    }
    function wrapConversion(toModel, graph) {
      const path3 = [graph[toModel].parent, toModel]
      let fn = conversions[graph[toModel].parent][toModel]
      let cur = graph[toModel].parent
      while (graph[cur].parent) {
        path3.unshift(graph[cur].parent)
        fn = link(conversions[graph[cur].parent][cur], fn)
        cur = graph[cur].parent
      }
      fn.conversion = path3
      return fn
    }
    module2.exports = function (fromModel) {
      const graph = deriveBFS(fromModel)
      const conversion = {}
      const models = Object.keys(graph)
      for (let len = models.length, i = 0; i < len; i++) {
        const toModel = models[i]
        const node = graph[toModel]
        if (node.parent === null) {
          continue
        }
        conversion[toModel] = wrapConversion(toModel, graph)
      }
      return conversion
    }
  }
})

// ../../node_modules/.pnpm/color-convert@2.0.1/node_modules/color-convert/index.js
var require_color_convert = __commonJS({
  '../../node_modules/.pnpm/color-convert@2.0.1/node_modules/color-convert/index.js'(
    exports,
    module2
  ) {
    var conversions = require_conversions()
    var route = require_route()
    var convert = {}
    var models = Object.keys(conversions)
    function wrapRaw(fn) {
      const wrappedFn = function (...args) {
        const arg0 = args[0]
        if (arg0 === void 0 || arg0 === null) {
          return arg0
        }
        if (arg0.length > 1) {
          args = arg0
        }
        return fn(args)
      }
      if ('conversion' in fn) {
        wrappedFn.conversion = fn.conversion
      }
      return wrappedFn
    }
    function wrapRounded(fn) {
      const wrappedFn = function (...args) {
        const arg0 = args[0]
        if (arg0 === void 0 || arg0 === null) {
          return arg0
        }
        if (arg0.length > 1) {
          args = arg0
        }
        const result = fn(args)
        if (typeof result === 'object') {
          for (let len = result.length, i = 0; i < len; i++) {
            result[i] = Math.round(result[i])
          }
        }
        return result
      }
      if ('conversion' in fn) {
        wrappedFn.conversion = fn.conversion
      }
      return wrappedFn
    }
    models.forEach((fromModel) => {
      convert[fromModel] = {}
      Object.defineProperty(convert[fromModel], 'channels', {
        value: conversions[fromModel].channels
      })
      Object.defineProperty(convert[fromModel], 'labels', { value: conversions[fromModel].labels })
      const routes = route(fromModel)
      const routeModels = Object.keys(routes)
      routeModels.forEach((toModel) => {
        const fn = routes[toModel]
        convert[fromModel][toModel] = wrapRounded(fn)
        convert[fromModel][toModel].raw = wrapRaw(fn)
      })
    })
    module2.exports = convert
  }
})

// ../../node_modules/.pnpm/ansi-styles@4.3.0/node_modules/ansi-styles/index.js
var require_ansi_styles = __commonJS({
  '../../node_modules/.pnpm/ansi-styles@4.3.0/node_modules/ansi-styles/index.js'(exports, module2) {
    'use strict'
    var wrapAnsi16 =
      (fn, offset) =>
      (...args) => {
        const code = fn(...args)
        return `\x1B[${code + offset}m`
      }
    var wrapAnsi256 =
      (fn, offset) =>
      (...args) => {
        const code = fn(...args)
        return `\x1B[${38 + offset};5;${code}m`
      }
    var wrapAnsi16m =
      (fn, offset) =>
      (...args) => {
        const rgb = fn(...args)
        return `\x1B[${38 + offset};2;${rgb[0]};${rgb[1]};${rgb[2]}m`
      }
    var ansi2ansi = (n) => n
    var rgb2rgb = (r, g, b) => [r, g, b]
    var setLazyProperty = (object, property, get) => {
      Object.defineProperty(object, property, {
        get: () => {
          const value = get()
          Object.defineProperty(object, property, {
            value,
            enumerable: true,
            configurable: true
          })
          return value
        },
        enumerable: true,
        configurable: true
      })
    }
    var colorConvert
    var makeDynamicStyles = (wrap, targetSpace, identity, isBackground) => {
      if (colorConvert === void 0) {
        colorConvert = require_color_convert()
      }
      const offset = isBackground ? 10 : 0
      const styles = {}
      for (const [sourceSpace, suite] of Object.entries(colorConvert)) {
        const name = sourceSpace === 'ansi16' ? 'ansi' : sourceSpace
        if (sourceSpace === targetSpace) {
          styles[name] = wrap(identity, offset)
        } else if (typeof suite === 'object') {
          styles[name] = wrap(suite[targetSpace], offset)
        }
      }
      return styles
    }
    function assembleStyles() {
      const codes = /* @__PURE__ */ new Map()
      const styles = {
        modifier: {
          reset: [0, 0],
          bold: [1, 22],
          dim: [2, 22],
          italic: [3, 23],
          underline: [4, 24],
          inverse: [7, 27],
          hidden: [8, 28],
          strikethrough: [9, 29]
        },
        color: {
          black: [30, 39],
          red: [31, 39],
          green: [32, 39],
          yellow: [33, 39],
          blue: [34, 39],
          magenta: [35, 39],
          cyan: [36, 39],
          white: [37, 39],
          blackBright: [90, 39],
          redBright: [91, 39],
          greenBright: [92, 39],
          yellowBright: [93, 39],
          blueBright: [94, 39],
          magentaBright: [95, 39],
          cyanBright: [96, 39],
          whiteBright: [97, 39]
        },
        bgColor: {
          bgBlack: [40, 49],
          bgRed: [41, 49],
          bgGreen: [42, 49],
          bgYellow: [43, 49],
          bgBlue: [44, 49],
          bgMagenta: [45, 49],
          bgCyan: [46, 49],
          bgWhite: [47, 49],
          bgBlackBright: [100, 49],
          bgRedBright: [101, 49],
          bgGreenBright: [102, 49],
          bgYellowBright: [103, 49],
          bgBlueBright: [104, 49],
          bgMagentaBright: [105, 49],
          bgCyanBright: [106, 49],
          bgWhiteBright: [107, 49]
        }
      }
      styles.color.gray = styles.color.blackBright
      styles.bgColor.bgGray = styles.bgColor.bgBlackBright
      styles.color.grey = styles.color.blackBright
      styles.bgColor.bgGrey = styles.bgColor.bgBlackBright
      for (const [groupName, group] of Object.entries(styles)) {
        for (const [styleName, style] of Object.entries(group)) {
          styles[styleName] = {
            open: `\x1B[${style[0]}m`,
            close: `\x1B[${style[1]}m`
          }
          group[styleName] = styles[styleName]
          codes.set(style[0], style[1])
        }
        Object.defineProperty(styles, groupName, {
          value: group,
          enumerable: false
        })
      }
      Object.defineProperty(styles, 'codes', {
        value: codes,
        enumerable: false
      })
      styles.color.close = '\x1B[39m'
      styles.bgColor.close = '\x1B[49m'
      setLazyProperty(styles.color, 'ansi', () =>
        makeDynamicStyles(wrapAnsi16, 'ansi16', ansi2ansi, false)
      )
      setLazyProperty(styles.color, 'ansi256', () =>
        makeDynamicStyles(wrapAnsi256, 'ansi256', ansi2ansi, false)
      )
      setLazyProperty(styles.color, 'ansi16m', () =>
        makeDynamicStyles(wrapAnsi16m, 'rgb', rgb2rgb, false)
      )
      setLazyProperty(styles.bgColor, 'ansi', () =>
        makeDynamicStyles(wrapAnsi16, 'ansi16', ansi2ansi, true)
      )
      setLazyProperty(styles.bgColor, 'ansi256', () =>
        makeDynamicStyles(wrapAnsi256, 'ansi256', ansi2ansi, true)
      )
      setLazyProperty(styles.bgColor, 'ansi16m', () =>
        makeDynamicStyles(wrapAnsi16m, 'rgb', rgb2rgb, true)
      )
      return styles
    }
    Object.defineProperty(module2, 'exports', {
      enumerable: true,
      get: assembleStyles
    })
  }
})

// ../../node_modules/.pnpm/has-flag@4.0.0/node_modules/has-flag/index.js
var require_has_flag = __commonJS({
  '../../node_modules/.pnpm/has-flag@4.0.0/node_modules/has-flag/index.js'(exports, module2) {
    'use strict'
    module2.exports = (flag, argv = process.argv) => {
      const prefix = flag.startsWith('-') ? '' : flag.length === 1 ? '-' : '--'
      const position = argv.indexOf(prefix + flag)
      const terminatorPosition = argv.indexOf('--')
      return position !== -1 && (terminatorPosition === -1 || position < terminatorPosition)
    }
  }
})

// ../../node_modules/.pnpm/supports-color@7.2.0/node_modules/supports-color/index.js
var require_supports_color = __commonJS({
  '../../node_modules/.pnpm/supports-color@7.2.0/node_modules/supports-color/index.js'(
    exports,
    module2
  ) {
    'use strict'
    var os = require('os')
    var tty = require('tty')
    var hasFlag = require_has_flag()
    var { env } = process
    var forceColor
    if (
      hasFlag('no-color') ||
      hasFlag('no-colors') ||
      hasFlag('color=false') ||
      hasFlag('color=never')
    ) {
      forceColor = 0
    } else if (
      hasFlag('color') ||
      hasFlag('colors') ||
      hasFlag('color=true') ||
      hasFlag('color=always')
    ) {
      forceColor = 1
    }
    if ('FORCE_COLOR' in env) {
      if (env.FORCE_COLOR === 'true') {
        forceColor = 1
      } else if (env.FORCE_COLOR === 'false') {
        forceColor = 0
      } else {
        forceColor = env.FORCE_COLOR.length === 0 ? 1 : Math.min(parseInt(env.FORCE_COLOR, 10), 3)
      }
    }
    function translateLevel(level) {
      if (level === 0) {
        return false
      }
      return {
        level,
        hasBasic: true,
        has256: level >= 2,
        has16m: level >= 3
      }
    }
    function supportsColor(haveStream, streamIsTTY) {
      if (forceColor === 0) {
        return 0
      }
      if (hasFlag('color=16m') || hasFlag('color=full') || hasFlag('color=truecolor')) {
        return 3
      }
      if (hasFlag('color=256')) {
        return 2
      }
      if (haveStream && !streamIsTTY && forceColor === void 0) {
        return 0
      }
      const min = forceColor || 0
      if (env.TERM === 'dumb') {
        return min
      }
      if (process.platform === 'win32') {
        const osRelease = os.release().split('.')
        if (Number(osRelease[0]) >= 10 && Number(osRelease[2]) >= 10586) {
          return Number(osRelease[2]) >= 14931 ? 3 : 2
        }
        return 1
      }
      if ('CI' in env) {
        if (
          ['TRAVIS', 'CIRCLECI', 'APPVEYOR', 'GITLAB_CI', 'GITHUB_ACTIONS', 'BUILDKITE'].some(
            (sign) => sign in env
          ) ||
          env.CI_NAME === 'codeship'
        ) {
          return 1
        }
        return min
      }
      if ('TEAMCITY_VERSION' in env) {
        return /^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(env.TEAMCITY_VERSION) ? 1 : 0
      }
      if (env.COLORTERM === 'truecolor') {
        return 3
      }
      if ('TERM_PROGRAM' in env) {
        const version = parseInt((env.TERM_PROGRAM_VERSION || '').split('.')[0], 10)
        switch (env.TERM_PROGRAM) {
          case 'iTerm.app':
            return version >= 3 ? 3 : 2
          case 'Apple_Terminal':
            return 2
        }
      }
      if (/-256(color)?$/i.test(env.TERM)) {
        return 2
      }
      if (/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(env.TERM)) {
        return 1
      }
      if ('COLORTERM' in env) {
        return 1
      }
      return min
    }
    function getSupportLevel(stream) {
      const level = supportsColor(stream, stream && stream.isTTY)
      return translateLevel(level)
    }
    module2.exports = {
      supportsColor: getSupportLevel,
      stdout: translateLevel(supportsColor(true, tty.isatty(1))),
      stderr: translateLevel(supportsColor(true, tty.isatty(2)))
    }
  }
})

// ../../node_modules/.pnpm/chalk@4.1.2/node_modules/chalk/source/util.js
var require_util4 = __commonJS({
  '../../node_modules/.pnpm/chalk@4.1.2/node_modules/chalk/source/util.js'(exports, module2) {
    'use strict'
    var stringReplaceAll = (string, substring, replacer) => {
      let index = string.indexOf(substring)
      if (index === -1) {
        return string
      }
      const substringLength = substring.length
      let endIndex = 0
      let returnValue = ''
      do {
        returnValue += string.substr(endIndex, index - endIndex) + substring + replacer
        endIndex = index + substringLength
        index = string.indexOf(substring, endIndex)
      } while (index !== -1)
      returnValue += string.substr(endIndex)
      return returnValue
    }
    var stringEncaseCRLFWithFirstIndex = (string, prefix, postfix, index) => {
      let endIndex = 0
      let returnValue = ''
      do {
        const gotCR = string[index - 1] === '\r'
        returnValue +=
          string.substr(endIndex, (gotCR ? index - 1 : index) - endIndex) +
          prefix +
          (gotCR ? '\r\n' : '\n') +
          postfix
        endIndex = index + 1
        index = string.indexOf('\n', endIndex)
      } while (index !== -1)
      returnValue += string.substr(endIndex)
      return returnValue
    }
    module2.exports = {
      stringReplaceAll,
      stringEncaseCRLFWithFirstIndex
    }
  }
})

// ../../node_modules/.pnpm/chalk@4.1.2/node_modules/chalk/source/templates.js
var require_templates = __commonJS({
  '../../node_modules/.pnpm/chalk@4.1.2/node_modules/chalk/source/templates.js'(exports, module2) {
    'use strict'
    var TEMPLATE_REGEX =
      /(?:\\(u(?:[a-f\d]{4}|\{[a-f\d]{1,6}\})|x[a-f\d]{2}|.))|(?:\{(~)?(\w+(?:\([^)]*\))?(?:\.\w+(?:\([^)]*\))?)*)(?:[ \t]|(?=\r?\n)))|(\})|((?:.|[\r\n\f])+?)/gi
    var STYLE_REGEX = /(?:^|\.)(\w+)(?:\(([^)]*)\))?/g
    var STRING_REGEX = /^(['"])((?:\\.|(?!\1)[^\\])*)\1$/
    var ESCAPE_REGEX = /\\(u(?:[a-f\d]{4}|{[a-f\d]{1,6}})|x[a-f\d]{2}|.)|([^\\])/gi
    var ESCAPES = /* @__PURE__ */ new Map([
      ['n', '\n'],
      ['r', '\r'],
      ['t', '	'],
      ['b', '\b'],
      ['f', '\f'],
      ['v', '\v'],
      ['0', '\0'],
      ['\\', '\\'],
      ['e', '\x1B'],
      ['a', '\x07']
    ])
    function unescape(c) {
      const u = c[0] === 'u'
      const bracket = c[1] === '{'
      if ((u && !bracket && c.length === 5) || (c[0] === 'x' && c.length === 3)) {
        return String.fromCharCode(parseInt(c.slice(1), 16))
      }
      if (u && bracket) {
        return String.fromCodePoint(parseInt(c.slice(2, -1), 16))
      }
      return ESCAPES.get(c) || c
    }
    function parseArguments(name, arguments_) {
      const results = []
      const chunks = arguments_.trim().split(/\s*,\s*/g)
      let matches
      for (const chunk of chunks) {
        const number = Number(chunk)
        if (!Number.isNaN(number)) {
          results.push(number)
        } else if ((matches = chunk.match(STRING_REGEX))) {
          results.push(
            matches[2].replace(ESCAPE_REGEX, (m, escape, character) =>
              escape ? unescape(escape) : character
            )
          )
        } else {
          throw new Error(`Invalid Chalk template style argument: ${chunk} (in style '${name}')`)
        }
      }
      return results
    }
    function parseStyle(style) {
      STYLE_REGEX.lastIndex = 0
      const results = []
      let matches
      while ((matches = STYLE_REGEX.exec(style)) !== null) {
        const name = matches[1]
        if (matches[2]) {
          const args = parseArguments(name, matches[2])
          results.push([name].concat(args))
        } else {
          results.push([name])
        }
      }
      return results
    }
    function buildStyle(chalk, styles) {
      const enabled2 = {}
      for (const layer of styles) {
        for (const style of layer.styles) {
          enabled2[style[0]] = layer.inverse ? null : style.slice(1)
        }
      }
      let current = chalk
      for (const [styleName, styles2] of Object.entries(enabled2)) {
        if (!Array.isArray(styles2)) {
          continue
        }
        if (!(styleName in current)) {
          throw new Error(`Unknown Chalk style: ${styleName}`)
        }
        current = styles2.length > 0 ? current[styleName](...styles2) : current[styleName]
      }
      return current
    }
    module2.exports = (chalk, temporary) => {
      const styles = []
      const chunks = []
      let chunk = []
      temporary.replace(TEMPLATE_REGEX, (m, escapeCharacter, inverse2, style, close, character) => {
        if (escapeCharacter) {
          chunk.push(unescape(escapeCharacter))
        } else if (style) {
          const string = chunk.join('')
          chunk = []
          chunks.push(styles.length === 0 ? string : buildStyle(chalk, styles)(string))
          styles.push({ inverse: inverse2, styles: parseStyle(style) })
        } else if (close) {
          if (styles.length === 0) {
            throw new Error('Found extraneous } in Chalk template literal')
          }
          chunks.push(buildStyle(chalk, styles)(chunk.join('')))
          chunk = []
          styles.pop()
        } else {
          chunk.push(character)
        }
      })
      chunks.push(chunk.join(''))
      if (styles.length > 0) {
        const errMessage = `Chalk template literal is missing ${styles.length} closing bracket${
          styles.length === 1 ? '' : 's'
        } (\`}\`)`
        throw new Error(errMessage)
      }
      return chunks.join('')
    }
  }
})

// ../../node_modules/.pnpm/chalk@4.1.2/node_modules/chalk/source/index.js
var require_source = __commonJS({
  '../../node_modules/.pnpm/chalk@4.1.2/node_modules/chalk/source/index.js'(exports, module2) {
    'use strict'
    var ansiStyles = require_ansi_styles()
    var { stdout: stdoutColor, stderr: stderrColor } = require_supports_color()
    var { stringReplaceAll, stringEncaseCRLFWithFirstIndex } = require_util4()
    var { isArray } = Array
    var levelMapping = ['ansi', 'ansi', 'ansi256', 'ansi16m']
    var styles = /* @__PURE__ */ Object.create(null)
    var applyOptions = (object, options2 = {}) => {
      if (
        options2.level &&
        !(Number.isInteger(options2.level) && options2.level >= 0 && options2.level <= 3)
      ) {
        throw new Error('The `level` option should be an integer from 0 to 3')
      }
      const colorLevel = stdoutColor ? stdoutColor.level : 0
      object.level = options2.level === void 0 ? colorLevel : options2.level
    }
    var ChalkClass = class {
      constructor(options2) {
        return chalkFactory(options2)
      }
    }
    var chalkFactory = (options2) => {
      const chalk2 = {}
      applyOptions(chalk2, options2)
      chalk2.template = (...arguments_) => chalkTag(chalk2.template, ...arguments_)
      Object.setPrototypeOf(chalk2, Chalk.prototype)
      Object.setPrototypeOf(chalk2.template, chalk2)
      chalk2.template.constructor = () => {
        throw new Error('`chalk.constructor()` is deprecated. Use `new chalk.Instance()` instead.')
      }
      chalk2.template.Instance = ChalkClass
      return chalk2.template
    }
    function Chalk(options2) {
      return chalkFactory(options2)
    }
    for (const [styleName, style] of Object.entries(ansiStyles)) {
      styles[styleName] = {
        get() {
          const builder = createBuilder(
            this,
            createStyler(style.open, style.close, this._styler),
            this._isEmpty
          )
          Object.defineProperty(this, styleName, { value: builder })
          return builder
        }
      }
    }
    styles.visible = {
      get() {
        const builder = createBuilder(this, this._styler, true)
        Object.defineProperty(this, 'visible', { value: builder })
        return builder
      }
    }
    var usedModels = ['rgb', 'hex', 'keyword', 'hsl', 'hsv', 'hwb', 'ansi', 'ansi256']
    for (const model of usedModels) {
      styles[model] = {
        get() {
          const { level } = this
          return function (...arguments_) {
            const styler = createStyler(
              ansiStyles.color[levelMapping[level]][model](...arguments_),
              ansiStyles.color.close,
              this._styler
            )
            return createBuilder(this, styler, this._isEmpty)
          }
        }
      }
    }
    for (const model of usedModels) {
      const bgModel = 'bg' + model[0].toUpperCase() + model.slice(1)
      styles[bgModel] = {
        get() {
          const { level } = this
          return function (...arguments_) {
            const styler = createStyler(
              ansiStyles.bgColor[levelMapping[level]][model](...arguments_),
              ansiStyles.bgColor.close,
              this._styler
            )
            return createBuilder(this, styler, this._isEmpty)
          }
        }
      }
    }
    var proto = Object.defineProperties(() => {}, {
      ...styles,
      level: {
        enumerable: true,
        get() {
          return this._generator.level
        },
        set(level) {
          this._generator.level = level
        }
      }
    })
    var createStyler = (open, close, parent) => {
      let openAll
      let closeAll
      if (parent === void 0) {
        openAll = open
        closeAll = close
      } else {
        openAll = parent.openAll + open
        closeAll = close + parent.closeAll
      }
      return {
        open,
        close,
        openAll,
        closeAll,
        parent
      }
    }
    var createBuilder = (self2, _styler, _isEmpty) => {
      const builder = (...arguments_) => {
        if (isArray(arguments_[0]) && isArray(arguments_[0].raw)) {
          return applyStyle(builder, chalkTag(builder, ...arguments_))
        }
        return applyStyle(
          builder,
          arguments_.length === 1 ? '' + arguments_[0] : arguments_.join(' ')
        )
      }
      Object.setPrototypeOf(builder, proto)
      builder._generator = self2
      builder._styler = _styler
      builder._isEmpty = _isEmpty
      return builder
    }
    var applyStyle = (self2, string) => {
      if (self2.level <= 0 || !string) {
        return self2._isEmpty ? '' : string
      }
      let styler = self2._styler
      if (styler === void 0) {
        return string
      }
      const { openAll, closeAll } = styler
      if (string.indexOf('\x1B') !== -1) {
        while (styler !== void 0) {
          string = stringReplaceAll(string, styler.close, styler.open)
          styler = styler.parent
        }
      }
      const lfIndex = string.indexOf('\n')
      if (lfIndex !== -1) {
        string = stringEncaseCRLFWithFirstIndex(string, closeAll, openAll, lfIndex)
      }
      return openAll + string + closeAll
    }
    var template
    var chalkTag = (chalk2, ...strings) => {
      const [firstString] = strings
      if (!isArray(firstString) || !isArray(firstString.raw)) {
        return strings.join(' ')
      }
      const arguments_ = strings.slice(1)
      const parts = [firstString.raw[0]]
      for (let i = 1; i < firstString.length; i++) {
        parts.push(String(arguments_[i - 1]).replace(/[{}\\]/g, '\\$&'), String(firstString.raw[i]))
      }
      if (template === void 0) {
        template = require_templates()
      }
      return template(chalk2, parts.join(''))
    }
    Object.defineProperties(Chalk.prototype, styles)
    var chalk = Chalk()
    chalk.supportsColor = stdoutColor
    chalk.stderr = Chalk({ level: stderrColor ? stderrColor.level : 0 })
    chalk.stderr.supportsColor = stderrColor
    module2.exports = chalk
  }
})

// ../../node_modules/.pnpm/mimic-fn@2.1.0/node_modules/mimic-fn/index.js
var require_mimic_fn = __commonJS({
  '../../node_modules/.pnpm/mimic-fn@2.1.0/node_modules/mimic-fn/index.js'(exports, module2) {
    'use strict'
    var mimicFn = (to, from) => {
      for (const prop of Reflect.ownKeys(from)) {
        Object.defineProperty(to, prop, Object.getOwnPropertyDescriptor(from, prop))
      }
      return to
    }
    module2.exports = mimicFn
    module2.exports.default = mimicFn
  }
})

// ../../node_modules/.pnpm/onetime@5.1.2/node_modules/onetime/index.js
var require_onetime = __commonJS({
  '../../node_modules/.pnpm/onetime@5.1.2/node_modules/onetime/index.js'(exports, module2) {
    'use strict'
    var mimicFn = require_mimic_fn()
    var calledFunctions = /* @__PURE__ */ new WeakMap()
    var onetime = (function_, options2 = {}) => {
      if (typeof function_ !== 'function') {
        throw new TypeError('Expected a function')
      }
      let returnValue
      let callCount = 0
      const functionName = function_.displayName || function_.name || '<anonymous>'
      const onetime2 = function (...arguments_) {
        calledFunctions.set(onetime2, ++callCount)
        if (callCount === 1) {
          returnValue = function_.apply(this, arguments_)
          function_ = null
        } else if (options2.throw === true) {
          throw new Error(`Function \`${functionName}\` can only be called once`)
        }
        return returnValue
      }
      mimicFn(onetime2, function_)
      calledFunctions.set(onetime2, callCount)
      return onetime2
    }
    module2.exports = onetime
    module2.exports.default = onetime
    module2.exports.callCount = (function_) => {
      if (!calledFunctions.has(function_)) {
        throw new Error(
          `The given function \`${function_.name}\` is not wrapped by the \`onetime\` package`
        )
      }
      return calledFunctions.get(function_)
    }
  }
})

// ../../node_modules/.pnpm/signal-exit@3.0.6/node_modules/signal-exit/signals.js
var require_signals = __commonJS({
  '../../node_modules/.pnpm/signal-exit@3.0.6/node_modules/signal-exit/signals.js'(
    exports,
    module2
  ) {
    module2.exports = ['SIGABRT', 'SIGALRM', 'SIGHUP', 'SIGINT', 'SIGTERM']
    if (process.platform !== 'win32') {
      module2.exports.push(
        'SIGVTALRM',
        'SIGXCPU',
        'SIGXFSZ',
        'SIGUSR2',
        'SIGTRAP',
        'SIGSYS',
        'SIGQUIT',
        'SIGIOT'
      )
    }
    if (process.platform === 'linux') {
      module2.exports.push('SIGIO', 'SIGPOLL', 'SIGPWR', 'SIGSTKFLT', 'SIGUNUSED')
    }
  }
})

// ../../node_modules/.pnpm/signal-exit@3.0.6/node_modules/signal-exit/index.js
var require_signal_exit = __commonJS({
  '../../node_modules/.pnpm/signal-exit@3.0.6/node_modules/signal-exit/index.js'(exports, module2) {
    var process2 = global.process
    var processOk = function (process3) {
      return (
        process3 &&
        typeof process3 === 'object' &&
        typeof process3.removeListener === 'function' &&
        typeof process3.emit === 'function' &&
        typeof process3.reallyExit === 'function' &&
        typeof process3.listeners === 'function' &&
        typeof process3.kill === 'function' &&
        typeof process3.pid === 'number' &&
        typeof process3.on === 'function'
      )
    }
    if (!processOk(process2)) {
      module2.exports = function () {}
    } else {
      assert = require('assert')
      signals = require_signals()
      isWin = /^win/i.test(process2.platform)
      EE = require('events')
      if (typeof EE !== 'function') {
        EE = EE.EventEmitter
      }
      if (process2.__signal_exit_emitter__) {
        emitter = process2.__signal_exit_emitter__
      } else {
        emitter = process2.__signal_exit_emitter__ = new EE()
        emitter.count = 0
        emitter.emitted = {}
      }
      if (!emitter.infinite) {
        emitter.setMaxListeners(Infinity)
        emitter.infinite = true
      }
      module2.exports = function (cb, opts) {
        if (!processOk(global.process)) {
          return
        }
        assert.equal(typeof cb, 'function', 'a callback must be provided for exit handler')
        if (loaded === false) {
          load()
        }
        var ev = 'exit'
        if (opts && opts.alwaysLast) {
          ev = 'afterexit'
        }
        var remove = function () {
          emitter.removeListener(ev, cb)
          if (
            emitter.listeners('exit').length === 0 &&
            emitter.listeners('afterexit').length === 0
          ) {
            unload()
          }
        }
        emitter.on(ev, cb)
        return remove
      }
      unload = function unload2() {
        if (!loaded || !processOk(global.process)) {
          return
        }
        loaded = false
        signals.forEach(function (sig) {
          try {
            process2.removeListener(sig, sigListeners[sig])
          } catch (er) {}
        })
        process2.emit = originalProcessEmit
        process2.reallyExit = originalProcessReallyExit
        emitter.count -= 1
      }
      module2.exports.unload = unload
      emit = function emit2(event, code, signal) {
        if (emitter.emitted[event]) {
          return
        }
        emitter.emitted[event] = true
        emitter.emit(event, code, signal)
      }
      sigListeners = {}
      signals.forEach(function (sig) {
        sigListeners[sig] = function listener() {
          if (!processOk(global.process)) {
            return
          }
          var listeners = process2.listeners(sig)
          if (listeners.length === emitter.count) {
            unload()
            emit('exit', null, sig)
            emit('afterexit', null, sig)
            if (isWin && sig === 'SIGHUP') {
              sig = 'SIGINT'
            }
            process2.kill(process2.pid, sig)
          }
        }
      })
      module2.exports.signals = function () {
        return signals
      }
      loaded = false
      load = function load2() {
        if (loaded || !processOk(global.process)) {
          return
        }
        loaded = true
        emitter.count += 1
        signals = signals.filter(function (sig) {
          try {
            process2.on(sig, sigListeners[sig])
            return true
          } catch (er) {
            return false
          }
        })
        process2.emit = processEmit
        process2.reallyExit = processReallyExit
      }
      module2.exports.load = load
      originalProcessReallyExit = process2.reallyExit
      processReallyExit = function processReallyExit2(code) {
        if (!processOk(global.process)) {
          return
        }
        process2.exitCode = code || 0
        emit('exit', process2.exitCode, null)
        emit('afterexit', process2.exitCode, null)
        originalProcessReallyExit.call(process2, process2.exitCode)
      }
      originalProcessEmit = process2.emit
      processEmit = function processEmit2(ev, arg) {
        if (ev === 'exit' && processOk(global.process)) {
          if (arg !== void 0) {
            process2.exitCode = arg
          }
          var ret = originalProcessEmit.apply(this, arguments)
          emit('exit', process2.exitCode, null)
          emit('afterexit', process2.exitCode, null)
          return ret
        } else {
          return originalProcessEmit.apply(this, arguments)
        }
      }
    }
    var assert
    var signals
    var isWin
    var EE
    var emitter
    var unload
    var emit
    var sigListeners
    var loaded
    var load
    var originalProcessReallyExit
    var processReallyExit
    var originalProcessEmit
    var processEmit
  }
})

// ../../node_modules/.pnpm/restore-cursor@3.1.0/node_modules/restore-cursor/index.js
var require_restore_cursor = __commonJS({
  '../../node_modules/.pnpm/restore-cursor@3.1.0/node_modules/restore-cursor/index.js'(
    exports,
    module2
  ) {
    'use strict'
    var onetime = require_onetime()
    var signalExit = require_signal_exit()
    module2.exports = onetime(() => {
      signalExit(
        () => {
          process.stderr.write('\x1B[?25h')
        },
        { alwaysLast: true }
      )
    })
  }
})

// ../../node_modules/.pnpm/cli-cursor@3.1.0/node_modules/cli-cursor/index.js
var require_cli_cursor = __commonJS({
  '../../node_modules/.pnpm/cli-cursor@3.1.0/node_modules/cli-cursor/index.js'(exports) {
    'use strict'
    var restoreCursor = require_restore_cursor()
    var isHidden = false
    exports.show = (writableStream = process.stderr) => {
      if (!writableStream.isTTY) {
        return
      }
      isHidden = false
      writableStream.write('\x1B[?25h')
    }
    exports.hide = (writableStream = process.stderr) => {
      if (!writableStream.isTTY) {
        return
      }
      restoreCursor()
      isHidden = true
      writableStream.write('\x1B[?25l')
    }
    exports.toggle = (force, writableStream) => {
      if (force !== void 0) {
        isHidden = force
      }
      if (isHidden) {
        exports.show(writableStream)
      } else {
        exports.hide(writableStream)
      }
    }
  }
})

// ../../node_modules/.pnpm/cli-spinners@2.6.1/node_modules/cli-spinners/spinners.json
var require_spinners = __commonJS({
  '../../node_modules/.pnpm/cli-spinners@2.6.1/node_modules/cli-spinners/spinners.json'(
    exports,
    module2
  ) {
    module2.exports = {
      dots: {
        interval: 80,
        frames: [
          '\u280B',
          '\u2819',
          '\u2839',
          '\u2838',
          '\u283C',
          '\u2834',
          '\u2826',
          '\u2827',
          '\u2807',
          '\u280F'
        ]
      },
      dots2: {
        interval: 80,
        frames: ['\u28FE', '\u28FD', '\u28FB', '\u28BF', '\u287F', '\u28DF', '\u28EF', '\u28F7']
      },
      dots3: {
        interval: 80,
        frames: [
          '\u280B',
          '\u2819',
          '\u281A',
          '\u281E',
          '\u2816',
          '\u2826',
          '\u2834',
          '\u2832',
          '\u2833',
          '\u2813'
        ]
      },
      dots4: {
        interval: 80,
        frames: [
          '\u2804',
          '\u2806',
          '\u2807',
          '\u280B',
          '\u2819',
          '\u2838',
          '\u2830',
          '\u2820',
          '\u2830',
          '\u2838',
          '\u2819',
          '\u280B',
          '\u2807',
          '\u2806'
        ]
      },
      dots5: {
        interval: 80,
        frames: [
          '\u280B',
          '\u2819',
          '\u281A',
          '\u2812',
          '\u2802',
          '\u2802',
          '\u2812',
          '\u2832',
          '\u2834',
          '\u2826',
          '\u2816',
          '\u2812',
          '\u2810',
          '\u2810',
          '\u2812',
          '\u2813',
          '\u280B'
        ]
      },
      dots6: {
        interval: 80,
        frames: [
          '\u2801',
          '\u2809',
          '\u2819',
          '\u281A',
          '\u2812',
          '\u2802',
          '\u2802',
          '\u2812',
          '\u2832',
          '\u2834',
          '\u2824',
          '\u2804',
          '\u2804',
          '\u2824',
          '\u2834',
          '\u2832',
          '\u2812',
          '\u2802',
          '\u2802',
          '\u2812',
          '\u281A',
          '\u2819',
          '\u2809',
          '\u2801'
        ]
      },
      dots7: {
        interval: 80,
        frames: [
          '\u2808',
          '\u2809',
          '\u280B',
          '\u2813',
          '\u2812',
          '\u2810',
          '\u2810',
          '\u2812',
          '\u2816',
          '\u2826',
          '\u2824',
          '\u2820',
          '\u2820',
          '\u2824',
          '\u2826',
          '\u2816',
          '\u2812',
          '\u2810',
          '\u2810',
          '\u2812',
          '\u2813',
          '\u280B',
          '\u2809',
          '\u2808'
        ]
      },
      dots8: {
        interval: 80,
        frames: [
          '\u2801',
          '\u2801',
          '\u2809',
          '\u2819',
          '\u281A',
          '\u2812',
          '\u2802',
          '\u2802',
          '\u2812',
          '\u2832',
          '\u2834',
          '\u2824',
          '\u2804',
          '\u2804',
          '\u2824',
          '\u2820',
          '\u2820',
          '\u2824',
          '\u2826',
          '\u2816',
          '\u2812',
          '\u2810',
          '\u2810',
          '\u2812',
          '\u2813',
          '\u280B',
          '\u2809',
          '\u2808',
          '\u2808'
        ]
      },
      dots9: {
        interval: 80,
        frames: ['\u28B9', '\u28BA', '\u28BC', '\u28F8', '\u28C7', '\u2867', '\u2857', '\u284F']
      },
      dots10: {
        interval: 80,
        frames: ['\u2884', '\u2882', '\u2881', '\u2841', '\u2848', '\u2850', '\u2860']
      },
      dots11: {
        interval: 100,
        frames: ['\u2801', '\u2802', '\u2804', '\u2840', '\u2880', '\u2820', '\u2810', '\u2808']
      },
      dots12: {
        interval: 80,
        frames: [
          '\u2880\u2800',
          '\u2840\u2800',
          '\u2804\u2800',
          '\u2882\u2800',
          '\u2842\u2800',
          '\u2805\u2800',
          '\u2883\u2800',
          '\u2843\u2800',
          '\u280D\u2800',
          '\u288B\u2800',
          '\u284B\u2800',
          '\u280D\u2801',
          '\u288B\u2801',
          '\u284B\u2801',
          '\u280D\u2809',
          '\u280B\u2809',
          '\u280B\u2809',
          '\u2809\u2819',
          '\u2809\u2819',
          '\u2809\u2829',
          '\u2808\u2899',
          '\u2808\u2859',
          '\u2888\u2829',
          '\u2840\u2899',
          '\u2804\u2859',
          '\u2882\u2829',
          '\u2842\u2898',
          '\u2805\u2858',
          '\u2883\u2828',
          '\u2843\u2890',
          '\u280D\u2850',
          '\u288B\u2820',
          '\u284B\u2880',
          '\u280D\u2841',
          '\u288B\u2801',
          '\u284B\u2801',
          '\u280D\u2809',
          '\u280B\u2809',
          '\u280B\u2809',
          '\u2809\u2819',
          '\u2809\u2819',
          '\u2809\u2829',
          '\u2808\u2899',
          '\u2808\u2859',
          '\u2808\u2829',
          '\u2800\u2899',
          '\u2800\u2859',
          '\u2800\u2829',
          '\u2800\u2898',
          '\u2800\u2858',
          '\u2800\u2828',
          '\u2800\u2890',
          '\u2800\u2850',
          '\u2800\u2820',
          '\u2800\u2880',
          '\u2800\u2840'
        ]
      },
      dots8Bit: {
        interval: 80,
        frames: [
          '\u2800',
          '\u2801',
          '\u2802',
          '\u2803',
          '\u2804',
          '\u2805',
          '\u2806',
          '\u2807',
          '\u2840',
          '\u2841',
          '\u2842',
          '\u2843',
          '\u2844',
          '\u2845',
          '\u2846',
          '\u2847',
          '\u2808',
          '\u2809',
          '\u280A',
          '\u280B',
          '\u280C',
          '\u280D',
          '\u280E',
          '\u280F',
          '\u2848',
          '\u2849',
          '\u284A',
          '\u284B',
          '\u284C',
          '\u284D',
          '\u284E',
          '\u284F',
          '\u2810',
          '\u2811',
          '\u2812',
          '\u2813',
          '\u2814',
          '\u2815',
          '\u2816',
          '\u2817',
          '\u2850',
          '\u2851',
          '\u2852',
          '\u2853',
          '\u2854',
          '\u2855',
          '\u2856',
          '\u2857',
          '\u2818',
          '\u2819',
          '\u281A',
          '\u281B',
          '\u281C',
          '\u281D',
          '\u281E',
          '\u281F',
          '\u2858',
          '\u2859',
          '\u285A',
          '\u285B',
          '\u285C',
          '\u285D',
          '\u285E',
          '\u285F',
          '\u2820',
          '\u2821',
          '\u2822',
          '\u2823',
          '\u2824',
          '\u2825',
          '\u2826',
          '\u2827',
          '\u2860',
          '\u2861',
          '\u2862',
          '\u2863',
          '\u2864',
          '\u2865',
          '\u2866',
          '\u2867',
          '\u2828',
          '\u2829',
          '\u282A',
          '\u282B',
          '\u282C',
          '\u282D',
          '\u282E',
          '\u282F',
          '\u2868',
          '\u2869',
          '\u286A',
          '\u286B',
          '\u286C',
          '\u286D',
          '\u286E',
          '\u286F',
          '\u2830',
          '\u2831',
          '\u2832',
          '\u2833',
          '\u2834',
          '\u2835',
          '\u2836',
          '\u2837',
          '\u2870',
          '\u2871',
          '\u2872',
          '\u2873',
          '\u2874',
          '\u2875',
          '\u2876',
          '\u2877',
          '\u2838',
          '\u2839',
          '\u283A',
          '\u283B',
          '\u283C',
          '\u283D',
          '\u283E',
          '\u283F',
          '\u2878',
          '\u2879',
          '\u287A',
          '\u287B',
          '\u287C',
          '\u287D',
          '\u287E',
          '\u287F',
          '\u2880',
          '\u2881',
          '\u2882',
          '\u2883',
          '\u2884',
          '\u2885',
          '\u2886',
          '\u2887',
          '\u28C0',
          '\u28C1',
          '\u28C2',
          '\u28C3',
          '\u28C4',
          '\u28C5',
          '\u28C6',
          '\u28C7',
          '\u2888',
          '\u2889',
          '\u288A',
          '\u288B',
          '\u288C',
          '\u288D',
          '\u288E',
          '\u288F',
          '\u28C8',
          '\u28C9',
          '\u28CA',
          '\u28CB',
          '\u28CC',
          '\u28CD',
          '\u28CE',
          '\u28CF',
          '\u2890',
          '\u2891',
          '\u2892',
          '\u2893',
          '\u2894',
          '\u2895',
          '\u2896',
          '\u2897',
          '\u28D0',
          '\u28D1',
          '\u28D2',
          '\u28D3',
          '\u28D4',
          '\u28D5',
          '\u28D6',
          '\u28D7',
          '\u2898',
          '\u2899',
          '\u289A',
          '\u289B',
          '\u289C',
          '\u289D',
          '\u289E',
          '\u289F',
          '\u28D8',
          '\u28D9',
          '\u28DA',
          '\u28DB',
          '\u28DC',
          '\u28DD',
          '\u28DE',
          '\u28DF',
          '\u28A0',
          '\u28A1',
          '\u28A2',
          '\u28A3',
          '\u28A4',
          '\u28A5',
          '\u28A6',
          '\u28A7',
          '\u28E0',
          '\u28E1',
          '\u28E2',
          '\u28E3',
          '\u28E4',
          '\u28E5',
          '\u28E6',
          '\u28E7',
          '\u28A8',
          '\u28A9',
          '\u28AA',
          '\u28AB',
          '\u28AC',
          '\u28AD',
          '\u28AE',
          '\u28AF',
          '\u28E8',
          '\u28E9',
          '\u28EA',
          '\u28EB',
          '\u28EC',
          '\u28ED',
          '\u28EE',
          '\u28EF',
          '\u28B0',
          '\u28B1',
          '\u28B2',
          '\u28B3',
          '\u28B4',
          '\u28B5',
          '\u28B6',
          '\u28B7',
          '\u28F0',
          '\u28F1',
          '\u28F2',
          '\u28F3',
          '\u28F4',
          '\u28F5',
          '\u28F6',
          '\u28F7',
          '\u28B8',
          '\u28B9',
          '\u28BA',
          '\u28BB',
          '\u28BC',
          '\u28BD',
          '\u28BE',
          '\u28BF',
          '\u28F8',
          '\u28F9',
          '\u28FA',
          '\u28FB',
          '\u28FC',
          '\u28FD',
          '\u28FE',
          '\u28FF'
        ]
      },
      line: {
        interval: 130,
        frames: ['-', '\\', '|', '/']
      },
      line2: {
        interval: 100,
        frames: ['\u2802', '-', '\u2013', '\u2014', '\u2013', '-']
      },
      pipe: {
        interval: 100,
        frames: ['\u2524', '\u2518', '\u2534', '\u2514', '\u251C', '\u250C', '\u252C', '\u2510']
      },
      simpleDots: {
        interval: 400,
        frames: ['.  ', '.. ', '...', '   ']
      },
      simpleDotsScrolling: {
        interval: 200,
        frames: ['.  ', '.. ', '...', ' ..', '  .', '   ']
      },
      star: {
        interval: 70,
        frames: ['\u2736', '\u2738', '\u2739', '\u273A', '\u2739', '\u2737']
      },
      star2: {
        interval: 80,
        frames: ['+', 'x', '*']
      },
      flip: {
        interval: 70,
        frames: ['_', '_', '_', '-', '`', '`', "'", '\xB4', '-', '_', '_', '_']
      },
      hamburger: {
        interval: 100,
        frames: ['\u2631', '\u2632', '\u2634']
      },
      growVertical: {
        interval: 120,
        frames: [
          '\u2581',
          '\u2583',
          '\u2584',
          '\u2585',
          '\u2586',
          '\u2587',
          '\u2586',
          '\u2585',
          '\u2584',
          '\u2583'
        ]
      },
      growHorizontal: {
        interval: 120,
        frames: [
          '\u258F',
          '\u258E',
          '\u258D',
          '\u258C',
          '\u258B',
          '\u258A',
          '\u2589',
          '\u258A',
          '\u258B',
          '\u258C',
          '\u258D',
          '\u258E'
        ]
      },
      balloon: {
        interval: 140,
        frames: [' ', '.', 'o', 'O', '@', '*', ' ']
      },
      balloon2: {
        interval: 120,
        frames: ['.', 'o', 'O', '\xB0', 'O', 'o', '.']
      },
      noise: {
        interval: 100,
        frames: ['\u2593', '\u2592', '\u2591']
      },
      bounce: {
        interval: 120,
        frames: ['\u2801', '\u2802', '\u2804', '\u2802']
      },
      boxBounce: {
        interval: 120,
        frames: ['\u2596', '\u2598', '\u259D', '\u2597']
      },
      boxBounce2: {
        interval: 100,
        frames: ['\u258C', '\u2580', '\u2590', '\u2584']
      },
      triangle: {
        interval: 50,
        frames: ['\u25E2', '\u25E3', '\u25E4', '\u25E5']
      },
      arc: {
        interval: 100,
        frames: ['\u25DC', '\u25E0', '\u25DD', '\u25DE', '\u25E1', '\u25DF']
      },
      circle: {
        interval: 120,
        frames: ['\u25E1', '\u2299', '\u25E0']
      },
      squareCorners: {
        interval: 180,
        frames: ['\u25F0', '\u25F3', '\u25F2', '\u25F1']
      },
      circleQuarters: {
        interval: 120,
        frames: ['\u25F4', '\u25F7', '\u25F6', '\u25F5']
      },
      circleHalves: {
        interval: 50,
        frames: ['\u25D0', '\u25D3', '\u25D1', '\u25D2']
      },
      squish: {
        interval: 100,
        frames: ['\u256B', '\u256A']
      },
      toggle: {
        interval: 250,
        frames: ['\u22B6', '\u22B7']
      },
      toggle2: {
        interval: 80,
        frames: ['\u25AB', '\u25AA']
      },
      toggle3: {
        interval: 120,
        frames: ['\u25A1', '\u25A0']
      },
      toggle4: {
        interval: 100,
        frames: ['\u25A0', '\u25A1', '\u25AA', '\u25AB']
      },
      toggle5: {
        interval: 100,
        frames: ['\u25AE', '\u25AF']
      },
      toggle6: {
        interval: 300,
        frames: ['\u101D', '\u1040']
      },
      toggle7: {
        interval: 80,
        frames: ['\u29BE', '\u29BF']
      },
      toggle8: {
        interval: 100,
        frames: ['\u25CD', '\u25CC']
      },
      toggle9: {
        interval: 100,
        frames: ['\u25C9', '\u25CE']
      },
      toggle10: {
        interval: 100,
        frames: ['\u3282', '\u3280', '\u3281']
      },
      toggle11: {
        interval: 50,
        frames: ['\u29C7', '\u29C6']
      },
      toggle12: {
        interval: 120,
        frames: ['\u2617', '\u2616']
      },
      toggle13: {
        interval: 80,
        frames: ['=', '*', '-']
      },
      arrow: {
        interval: 100,
        frames: ['\u2190', '\u2196', '\u2191', '\u2197', '\u2192', '\u2198', '\u2193', '\u2199']
      },
      arrow2: {
        interval: 80,
        frames: [
          '\u2B06\uFE0F ',
          '\u2197\uFE0F ',
          '\u27A1\uFE0F ',
          '\u2198\uFE0F ',
          '\u2B07\uFE0F ',
          '\u2199\uFE0F ',
          '\u2B05\uFE0F ',
          '\u2196\uFE0F '
        ]
      },
      arrow3: {
        interval: 120,
        frames: [
          '\u25B9\u25B9\u25B9\u25B9\u25B9',
          '\u25B8\u25B9\u25B9\u25B9\u25B9',
          '\u25B9\u25B8\u25B9\u25B9\u25B9',
          '\u25B9\u25B9\u25B8\u25B9\u25B9',
          '\u25B9\u25B9\u25B9\u25B8\u25B9',
          '\u25B9\u25B9\u25B9\u25B9\u25B8'
        ]
      },
      bouncingBar: {
        interval: 80,
        frames: [
          '[    ]',
          '[=   ]',
          '[==  ]',
          '[=== ]',
          '[ ===]',
          '[  ==]',
          '[   =]',
          '[    ]',
          '[   =]',
          '[  ==]',
          '[ ===]',
          '[====]',
          '[=== ]',
          '[==  ]',
          '[=   ]'
        ]
      },
      bouncingBall: {
        interval: 80,
        frames: [
          '( \u25CF    )',
          '(  \u25CF   )',
          '(   \u25CF  )',
          '(    \u25CF )',
          '(     \u25CF)',
          '(    \u25CF )',
          '(   \u25CF  )',
          '(  \u25CF   )',
          '( \u25CF    )',
          '(\u25CF     )'
        ]
      },
      smiley: {
        interval: 200,
        frames: ['\u{1F604} ', '\u{1F61D} ']
      },
      monkey: {
        interval: 300,
        frames: ['\u{1F648} ', '\u{1F648} ', '\u{1F649} ', '\u{1F64A} ']
      },
      hearts: {
        interval: 100,
        frames: ['\u{1F49B} ', '\u{1F499} ', '\u{1F49C} ', '\u{1F49A} ', '\u2764\uFE0F ']
      },
      clock: {
        interval: 100,
        frames: [
          '\u{1F55B} ',
          '\u{1F550} ',
          '\u{1F551} ',
          '\u{1F552} ',
          '\u{1F553} ',
          '\u{1F554} ',
          '\u{1F555} ',
          '\u{1F556} ',
          '\u{1F557} ',
          '\u{1F558} ',
          '\u{1F559} ',
          '\u{1F55A} '
        ]
      },
      earth: {
        interval: 180,
        frames: ['\u{1F30D} ', '\u{1F30E} ', '\u{1F30F} ']
      },
      material: {
        interval: 17,
        frames: [
          '\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581',
          '\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581',
          '\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581',
          '\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581',
          '\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588',
          '\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588',
          '\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588',
          '\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588',
          '\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588',
          '\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588',
          '\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588',
          '\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581',
          '\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581',
          '\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581',
          '\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581'
        ]
      },
      moon: {
        interval: 80,
        frames: [
          '\u{1F311} ',
          '\u{1F312} ',
          '\u{1F313} ',
          '\u{1F314} ',
          '\u{1F315} ',
          '\u{1F316} ',
          '\u{1F317} ',
          '\u{1F318} '
        ]
      },
      runner: {
        interval: 140,
        frames: ['\u{1F6B6} ', '\u{1F3C3} ']
      },
      pong: {
        interval: 80,
        frames: [
          '\u2590\u2802       \u258C',
          '\u2590\u2808       \u258C',
          '\u2590 \u2802      \u258C',
          '\u2590 \u2820      \u258C',
          '\u2590  \u2840     \u258C',
          '\u2590  \u2820     \u258C',
          '\u2590   \u2802    \u258C',
          '\u2590   \u2808    \u258C',
          '\u2590    \u2802   \u258C',
          '\u2590    \u2820   \u258C',
          '\u2590     \u2840  \u258C',
          '\u2590     \u2820  \u258C',
          '\u2590      \u2802 \u258C',
          '\u2590      \u2808 \u258C',
          '\u2590       \u2802\u258C',
          '\u2590       \u2820\u258C',
          '\u2590       \u2840\u258C',
          '\u2590      \u2820 \u258C',
          '\u2590      \u2802 \u258C',
          '\u2590     \u2808  \u258C',
          '\u2590     \u2802  \u258C',
          '\u2590    \u2820   \u258C',
          '\u2590    \u2840   \u258C',
          '\u2590   \u2820    \u258C',
          '\u2590   \u2802    \u258C',
          '\u2590  \u2808     \u258C',
          '\u2590  \u2802     \u258C',
          '\u2590 \u2820      \u258C',
          '\u2590 \u2840      \u258C',
          '\u2590\u2820       \u258C'
        ]
      },
      shark: {
        interval: 120,
        frames: [
          '\u2590|\\____________\u258C',
          '\u2590_|\\___________\u258C',
          '\u2590__|\\__________\u258C',
          '\u2590___|\\_________\u258C',
          '\u2590____|\\________\u258C',
          '\u2590_____|\\_______\u258C',
          '\u2590______|\\______\u258C',
          '\u2590_______|\\_____\u258C',
          '\u2590________|\\____\u258C',
          '\u2590_________|\\___\u258C',
          '\u2590__________|\\__\u258C',
          '\u2590___________|\\_\u258C',
          '\u2590____________|\\\u258C',
          '\u2590____________/|\u258C',
          '\u2590___________/|_\u258C',
          '\u2590__________/|__\u258C',
          '\u2590_________/|___\u258C',
          '\u2590________/|____\u258C',
          '\u2590_______/|_____\u258C',
          '\u2590______/|______\u258C',
          '\u2590_____/|_______\u258C',
          '\u2590____/|________\u258C',
          '\u2590___/|_________\u258C',
          '\u2590__/|__________\u258C',
          '\u2590_/|___________\u258C',
          '\u2590/|____________\u258C'
        ]
      },
      dqpb: {
        interval: 100,
        frames: ['d', 'q', 'p', 'b']
      },
      weather: {
        interval: 100,
        frames: [
          '\u2600\uFE0F ',
          '\u2600\uFE0F ',
          '\u2600\uFE0F ',
          '\u{1F324} ',
          '\u26C5\uFE0F ',
          '\u{1F325} ',
          '\u2601\uFE0F ',
          '\u{1F327} ',
          '\u{1F328} ',
          '\u{1F327} ',
          '\u{1F328} ',
          '\u{1F327} ',
          '\u{1F328} ',
          '\u26C8 ',
          '\u{1F328} ',
          '\u{1F327} ',
          '\u{1F328} ',
          '\u2601\uFE0F ',
          '\u{1F325} ',
          '\u26C5\uFE0F ',
          '\u{1F324} ',
          '\u2600\uFE0F ',
          '\u2600\uFE0F '
        ]
      },
      christmas: {
        interval: 400,
        frames: ['\u{1F332}', '\u{1F384}']
      },
      grenade: {
        interval: 80,
        frames: [
          '\u060C  ',
          '\u2032  ',
          ' \xB4 ',
          ' \u203E ',
          '  \u2E0C',
          '  \u2E0A',
          '  |',
          '  \u204E',
          '  \u2055',
          ' \u0DF4 ',
          '  \u2053',
          '   ',
          '   ',
          '   '
        ]
      },
      point: {
        interval: 125,
        frames: [
          '\u2219\u2219\u2219',
          '\u25CF\u2219\u2219',
          '\u2219\u25CF\u2219',
          '\u2219\u2219\u25CF',
          '\u2219\u2219\u2219'
        ]
      },
      layer: {
        interval: 150,
        frames: ['-', '=', '\u2261']
      },
      betaWave: {
        interval: 80,
        frames: [
          '\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2',
          '\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2',
          '\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2',
          '\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2',
          '\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2',
          '\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2',
          '\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1'
        ]
      },
      fingerDance: {
        interval: 160,
        frames: ['\u{1F918} ', '\u{1F91F} ', '\u{1F596} ', '\u270B ', '\u{1F91A} ', '\u{1F446} ']
      },
      fistBump: {
        interval: 80,
        frames: [
          '\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ',
          '\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ',
          '\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ',
          '\u3000\u{1F91C}\u3000\u3000\u{1F91B}\u3000 ',
          '\u3000\u3000\u{1F91C}\u{1F91B}\u3000\u3000 ',
          '\u3000\u{1F91C}\u2728\u{1F91B}\u3000\u3000 ',
          '\u{1F91C}\u3000\u2728\u3000\u{1F91B}\u3000 '
        ]
      },
      soccerHeader: {
        interval: 80,
        frames: [
          ' \u{1F9D1}\u26BD\uFE0F       \u{1F9D1} ',
          '\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} ',
          '\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ',
          '\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ',
          '\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ',
          '\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ',
          '\u{1F9D1}       \u26BD\uFE0F\u{1F9D1}  ',
          '\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ',
          '\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ',
          '\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ',
          '\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ',
          '\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} '
        ]
      },
      mindblown: {
        interval: 160,
        frames: [
          '\u{1F610} ',
          '\u{1F610} ',
          '\u{1F62E} ',
          '\u{1F62E} ',
          '\u{1F626} ',
          '\u{1F626} ',
          '\u{1F627} ',
          '\u{1F627} ',
          '\u{1F92F} ',
          '\u{1F4A5} ',
          '\u2728 ',
          '\u3000 ',
          '\u3000 ',
          '\u3000 '
        ]
      },
      speaker: {
        interval: 160,
        frames: ['\u{1F508} ', '\u{1F509} ', '\u{1F50A} ', '\u{1F509} ']
      },
      orangePulse: {
        interval: 100,
        frames: ['\u{1F538} ', '\u{1F536} ', '\u{1F7E0} ', '\u{1F7E0} ', '\u{1F536} ']
      },
      bluePulse: {
        interval: 100,
        frames: ['\u{1F539} ', '\u{1F537} ', '\u{1F535} ', '\u{1F535} ', '\u{1F537} ']
      },
      orangeBluePulse: {
        interval: 100,
        frames: [
          '\u{1F538} ',
          '\u{1F536} ',
          '\u{1F7E0} ',
          '\u{1F7E0} ',
          '\u{1F536} ',
          '\u{1F539} ',
          '\u{1F537} ',
          '\u{1F535} ',
          '\u{1F535} ',
          '\u{1F537} '
        ]
      },
      timeTravel: {
        interval: 100,
        frames: [
          '\u{1F55B} ',
          '\u{1F55A} ',
          '\u{1F559} ',
          '\u{1F558} ',
          '\u{1F557} ',
          '\u{1F556} ',
          '\u{1F555} ',
          '\u{1F554} ',
          '\u{1F553} ',
          '\u{1F552} ',
          '\u{1F551} ',
          '\u{1F550} '
        ]
      },
      aesthetic: {
        interval: 80,
        frames: [
          '\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1',
          '\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1',
          '\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1',
          '\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1',
          '\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1',
          '\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1',
          '\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0',
          '\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1'
        ]
      }
    }
  }
})

// ../../node_modules/.pnpm/cli-spinners@2.6.1/node_modules/cli-spinners/index.js
var require_cli_spinners = __commonJS({
  '../../node_modules/.pnpm/cli-spinners@2.6.1/node_modules/cli-spinners/index.js'(
    exports,
    module2
  ) {
    'use strict'
    var spinners = Object.assign({}, require_spinners())
    var spinnersList = Object.keys(spinners)
    Object.defineProperty(spinners, 'random', {
      get() {
        const randomIndex = Math.floor(Math.random() * spinnersList.length)
        const spinnerName = spinnersList[randomIndex]
        return spinners[spinnerName]
      }
    })
    module2.exports = spinners
  }
})

// ../../node_modules/.pnpm/is-unicode-supported@0.1.0/node_modules/is-unicode-supported/index.js
var require_is_unicode_supported = __commonJS({
  '../../node_modules/.pnpm/is-unicode-supported@0.1.0/node_modules/is-unicode-supported/index.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = () => {
      if (process.platform !== 'win32') {
        return true
      }
      return (
        Boolean(process.env.CI) ||
        Boolean(process.env.WT_SESSION) ||
        process.env.TERM_PROGRAM === 'vscode' ||
        process.env.TERM === 'xterm-256color' ||
        process.env.TERM === 'alacritty'
      )
    }
  }
})

// ../../node_modules/.pnpm/log-symbols@4.1.0/node_modules/log-symbols/index.js
var require_log_symbols = __commonJS({
  '../../node_modules/.pnpm/log-symbols@4.1.0/node_modules/log-symbols/index.js'(exports, module2) {
    'use strict'
    var chalk = require_source()
    var isUnicodeSupported = require_is_unicode_supported()
    var main = {
      info: chalk.blue('\u2139'),
      success: chalk.green('\u2714'),
      warning: chalk.yellow('\u26A0'),
      error: chalk.red('\u2716')
    }
    var fallback = {
      info: chalk.blue('i'),
      success: chalk.green('\u221A'),
      warning: chalk.yellow('\u203C'),
      error: chalk.red('\xD7')
    }
    module2.exports = isUnicodeSupported() ? main : fallback
  }
})

// ../../node_modules/.pnpm/ansi-regex@5.0.1/node_modules/ansi-regex/index.js
var require_ansi_regex = __commonJS({
  '../../node_modules/.pnpm/ansi-regex@5.0.1/node_modules/ansi-regex/index.js'(exports, module2) {
    'use strict'
    module2.exports = ({ onlyFirst = false } = {}) => {
      const pattern = [
        '[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)',
        '(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))'
      ].join('|')
      return new RegExp(pattern, onlyFirst ? void 0 : 'g')
    }
  }
})

// ../../node_modules/.pnpm/strip-ansi@6.0.1/node_modules/strip-ansi/index.js
var require_strip_ansi = __commonJS({
  '../../node_modules/.pnpm/strip-ansi@6.0.1/node_modules/strip-ansi/index.js'(exports, module2) {
    'use strict'
    var ansiRegex = require_ansi_regex()
    module2.exports = (string) =>
      typeof string === 'string' ? string.replace(ansiRegex(), '') : string
  }
})

// ../../node_modules/.pnpm/clone@1.0.4/node_modules/clone/clone.js
var require_clone = __commonJS({
  '../../node_modules/.pnpm/clone@1.0.4/node_modules/clone/clone.js'(exports, module2) {
    var clone2 = (function () {
      'use strict'
      function clone3(parent, circular, depth, prototype) {
        var filter
        if (typeof circular === 'object') {
          depth = circular.depth
          prototype = circular.prototype
          filter = circular.filter
          circular = circular.circular
        }
        var allParents = []
        var allChildren = []
        var useBuffer = typeof Buffer != 'undefined'
        if (typeof circular == 'undefined') circular = true
        if (typeof depth == 'undefined') depth = Infinity
        function _clone(parent2, depth2) {
          if (parent2 === null) return null
          if (depth2 == 0) return parent2
          var child
          var proto
          if (typeof parent2 != 'object') {
            return parent2
          }
          if (clone3.__isArray(parent2)) {
            child = []
          } else if (clone3.__isRegExp(parent2)) {
            child = new RegExp(parent2.source, __getRegExpFlags(parent2))
            if (parent2.lastIndex) child.lastIndex = parent2.lastIndex
          } else if (clone3.__isDate(parent2)) {
            child = new Date(parent2.getTime())
          } else if (useBuffer && Buffer.isBuffer(parent2)) {
            if (Buffer.allocUnsafe) {
              child = Buffer.allocUnsafe(parent2.length)
            } else {
              child = new Buffer(parent2.length)
            }
            parent2.copy(child)
            return child
          } else {
            if (typeof prototype == 'undefined') {
              proto = Object.getPrototypeOf(parent2)
              child = Object.create(proto)
            } else {
              child = Object.create(prototype)
              proto = prototype
            }
          }
          if (circular) {
            var index = allParents.indexOf(parent2)
            if (index != -1) {
              return allChildren[index]
            }
            allParents.push(parent2)
            allChildren.push(child)
          }
          for (var i in parent2) {
            var attrs
            if (proto) {
              attrs = Object.getOwnPropertyDescriptor(proto, i)
            }
            if (attrs && attrs.set == null) {
              continue
            }
            child[i] = _clone(parent2[i], depth2 - 1)
          }
          return child
        }
        return _clone(parent, depth)
      }
      clone3.clonePrototype = function clonePrototype(parent) {
        if (parent === null) return null
        var c = function () {}
        c.prototype = parent
        return new c()
      }
      function __objToStr(o) {
        return Object.prototype.toString.call(o)
      }
      clone3.__objToStr = __objToStr
      function __isDate(o) {
        return typeof o === 'object' && __objToStr(o) === '[object Date]'
      }
      clone3.__isDate = __isDate
      function __isArray(o) {
        return typeof o === 'object' && __objToStr(o) === '[object Array]'
      }
      clone3.__isArray = __isArray
      function __isRegExp(o) {
        return typeof o === 'object' && __objToStr(o) === '[object RegExp]'
      }
      clone3.__isRegExp = __isRegExp
      function __getRegExpFlags(re) {
        var flags = ''
        if (re.global) flags += 'g'
        if (re.ignoreCase) flags += 'i'
        if (re.multiline) flags += 'm'
        return flags
      }
      clone3.__getRegExpFlags = __getRegExpFlags
      return clone3
    })()
    if (typeof module2 === 'object' && module2.exports) {
      module2.exports = clone2
    }
  }
})

// ../../node_modules/.pnpm/defaults@1.0.3/node_modules/defaults/index.js
var require_defaults = __commonJS({
  '../../node_modules/.pnpm/defaults@1.0.3/node_modules/defaults/index.js'(exports, module2) {
    var clone2 = require_clone()
    module2.exports = function (options2, defaults) {
      options2 = options2 || {}
      Object.keys(defaults).forEach(function (key) {
        if (typeof options2[key] === 'undefined') {
          options2[key] = clone2(defaults[key])
        }
      })
      return options2
    }
  }
})

// ../../node_modules/.pnpm/wcwidth@1.0.1/node_modules/wcwidth/combining.js
var require_combining = __commonJS({
  '../../node_modules/.pnpm/wcwidth@1.0.1/node_modules/wcwidth/combining.js'(exports, module2) {
    module2.exports = [
      [768, 879],
      [1155, 1158],
      [1160, 1161],
      [1425, 1469],
      [1471, 1471],
      [1473, 1474],
      [1476, 1477],
      [1479, 1479],
      [1536, 1539],
      [1552, 1557],
      [1611, 1630],
      [1648, 1648],
      [1750, 1764],
      [1767, 1768],
      [1770, 1773],
      [1807, 1807],
      [1809, 1809],
      [1840, 1866],
      [1958, 1968],
      [2027, 2035],
      [2305, 2306],
      [2364, 2364],
      [2369, 2376],
      [2381, 2381],
      [2385, 2388],
      [2402, 2403],
      [2433, 2433],
      [2492, 2492],
      [2497, 2500],
      [2509, 2509],
      [2530, 2531],
      [2561, 2562],
      [2620, 2620],
      [2625, 2626],
      [2631, 2632],
      [2635, 2637],
      [2672, 2673],
      [2689, 2690],
      [2748, 2748],
      [2753, 2757],
      [2759, 2760],
      [2765, 2765],
      [2786, 2787],
      [2817, 2817],
      [2876, 2876],
      [2879, 2879],
      [2881, 2883],
      [2893, 2893],
      [2902, 2902],
      [2946, 2946],
      [3008, 3008],
      [3021, 3021],
      [3134, 3136],
      [3142, 3144],
      [3146, 3149],
      [3157, 3158],
      [3260, 3260],
      [3263, 3263],
      [3270, 3270],
      [3276, 3277],
      [3298, 3299],
      [3393, 3395],
      [3405, 3405],
      [3530, 3530],
      [3538, 3540],
      [3542, 3542],
      [3633, 3633],
      [3636, 3642],
      [3655, 3662],
      [3761, 3761],
      [3764, 3769],
      [3771, 3772],
      [3784, 3789],
      [3864, 3865],
      [3893, 3893],
      [3895, 3895],
      [3897, 3897],
      [3953, 3966],
      [3968, 3972],
      [3974, 3975],
      [3984, 3991],
      [3993, 4028],
      [4038, 4038],
      [4141, 4144],
      [4146, 4146],
      [4150, 4151],
      [4153, 4153],
      [4184, 4185],
      [4448, 4607],
      [4959, 4959],
      [5906, 5908],
      [5938, 5940],
      [5970, 5971],
      [6002, 6003],
      [6068, 6069],
      [6071, 6077],
      [6086, 6086],
      [6089, 6099],
      [6109, 6109],
      [6155, 6157],
      [6313, 6313],
      [6432, 6434],
      [6439, 6440],
      [6450, 6450],
      [6457, 6459],
      [6679, 6680],
      [6912, 6915],
      [6964, 6964],
      [6966, 6970],
      [6972, 6972],
      [6978, 6978],
      [7019, 7027],
      [7616, 7626],
      [7678, 7679],
      [8203, 8207],
      [8234, 8238],
      [8288, 8291],
      [8298, 8303],
      [8400, 8431],
      [12330, 12335],
      [12441, 12442],
      [43014, 43014],
      [43019, 43019],
      [43045, 43046],
      [64286, 64286],
      [65024, 65039],
      [65056, 65059],
      [65279, 65279],
      [65529, 65531],
      [68097, 68099],
      [68101, 68102],
      [68108, 68111],
      [68152, 68154],
      [68159, 68159],
      [119143, 119145],
      [119155, 119170],
      [119173, 119179],
      [119210, 119213],
      [119362, 119364],
      [917505, 917505],
      [917536, 917631],
      [917760, 917999]
    ]
  }
})

// ../../node_modules/.pnpm/wcwidth@1.0.1/node_modules/wcwidth/index.js
var require_wcwidth = __commonJS({
  '../../node_modules/.pnpm/wcwidth@1.0.1/node_modules/wcwidth/index.js'(exports, module2) {
    'use strict'
    var defaults = require_defaults()
    var combining = require_combining()
    var DEFAULTS = {
      nul: 0,
      control: 0
    }
    module2.exports = function wcwidth2(str) {
      return wcswidth(str, DEFAULTS)
    }
    module2.exports.config = function (opts) {
      opts = defaults(opts || {}, DEFAULTS)
      return function wcwidth2(str) {
        return wcswidth(str, opts)
      }
    }
    function wcswidth(str, opts) {
      if (typeof str !== 'string') return wcwidth(str, opts)
      var s = 0
      for (var i = 0; i < str.length; i++) {
        var n = wcwidth(str.charCodeAt(i), opts)
        if (n < 0) return -1
        s += n
      }
      return s
    }
    function wcwidth(ucs, opts) {
      if (ucs === 0) return opts.nul
      if (ucs < 32 || (ucs >= 127 && ucs < 160)) return opts.control
      if (bisearch(ucs)) return 0
      return (
        1 +
        (ucs >= 4352 &&
          (ucs <= 4447 ||
            ucs == 9001 ||
            ucs == 9002 ||
            (ucs >= 11904 && ucs <= 42191 && ucs != 12351) ||
            (ucs >= 44032 && ucs <= 55203) ||
            (ucs >= 63744 && ucs <= 64255) ||
            (ucs >= 65040 && ucs <= 65049) ||
            (ucs >= 65072 && ucs <= 65135) ||
            (ucs >= 65280 && ucs <= 65376) ||
            (ucs >= 65504 && ucs <= 65510) ||
            (ucs >= 131072 && ucs <= 196605) ||
            (ucs >= 196608 && ucs <= 262141)))
      )
    }
    function bisearch(ucs) {
      var min = 0
      var max = combining.length - 1
      var mid
      if (ucs < combining[0][0] || ucs > combining[max][1]) return false
      while (max >= min) {
        mid = Math.floor((min + max) / 2)
        if (ucs > combining[mid][1]) min = mid + 1
        else if (ucs < combining[mid][0]) max = mid - 1
        else return true
      }
      return false
    }
  }
})

// ../../node_modules/.pnpm/is-interactive@1.0.0/node_modules/is-interactive/index.js
var require_is_interactive = __commonJS({
  '../../node_modules/.pnpm/is-interactive@1.0.0/node_modules/is-interactive/index.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = ({ stream = process.stdout } = {}) => {
      return Boolean(
        stream && stream.isTTY && process.env.TERM !== 'dumb' && !('CI' in process.env)
      )
    }
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/stream.js
var require_stream = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/stream.js'(
    exports,
    module2
  ) {
    module2.exports = require('stream')
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/buffer_list.js
var require_buffer_list = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/buffer_list.js'(
    exports,
    module2
  ) {
    'use strict'
    function ownKeys(object, enumerableOnly) {
      var keys = Object.keys(object)
      if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object)
        if (enumerableOnly)
          symbols = symbols.filter(function (sym) {
            return Object.getOwnPropertyDescriptor(object, sym).enumerable
          })
        keys.push.apply(keys, symbols)
      }
      return keys
    }
    function _objectSpread(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i] != null ? arguments[i] : {}
        if (i % 2) {
          ownKeys(Object(source), true).forEach(function (key) {
            _defineProperty(target, key, source[key])
          })
        } else if (Object.getOwnPropertyDescriptors) {
          Object.defineProperties(target, Object.getOwnPropertyDescriptors(source))
        } else {
          ownKeys(Object(source)).forEach(function (key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key))
          })
        }
      }
      return target
    }
    function _defineProperty(obj, key, value) {
      if (key in obj) {
        Object.defineProperty(obj, key, {
          value,
          enumerable: true,
          configurable: true,
          writable: true
        })
      } else {
        obj[key] = value
      }
      return obj
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError('Cannot call a class as a function')
      }
    }
    function _defineProperties(target, props) {
      for (var i = 0; i < props.length; i++) {
        var descriptor = props[i]
        descriptor.enumerable = descriptor.enumerable || false
        descriptor.configurable = true
        if ('value' in descriptor) descriptor.writable = true
        Object.defineProperty(target, descriptor.key, descriptor)
      }
    }
    function _createClass(Constructor, protoProps, staticProps) {
      if (protoProps) _defineProperties(Constructor.prototype, protoProps)
      if (staticProps) _defineProperties(Constructor, staticProps)
      return Constructor
    }
    var _require = require('buffer')
    var Buffer2 = _require.Buffer
    var _require2 = require('util')
    var inspect = _require2.inspect
    var custom = (inspect && inspect.custom) || 'inspect'
    function copyBuffer(src, target, offset) {
      Buffer2.prototype.copy.call(src, target, offset)
    }
    module2.exports = /* @__PURE__ */ (function () {
      function BufferList() {
        _classCallCheck(this, BufferList)
        this.head = null
        this.tail = null
        this.length = 0
      }
      _createClass(BufferList, [
        {
          key: 'push',
          value: function push(v) {
            var entry = {
              data: v,
              next: null
            }
            if (this.length > 0) this.tail.next = entry
            else this.head = entry
            this.tail = entry
            ++this.length
          }
        },
        {
          key: 'unshift',
          value: function unshift(v) {
            var entry = {
              data: v,
              next: this.head
            }
            if (this.length === 0) this.tail = entry
            this.head = entry
            ++this.length
          }
        },
        {
          key: 'shift',
          value: function shift() {
            if (this.length === 0) return
            var ret = this.head.data
            if (this.length === 1) this.head = this.tail = null
            else this.head = this.head.next
            --this.length
            return ret
          }
        },
        {
          key: 'clear',
          value: function clear() {
            this.head = this.tail = null
            this.length = 0
          }
        },
        {
          key: 'join',
          value: function join(s) {
            if (this.length === 0) return ''
            var p = this.head
            var ret = '' + p.data
            while ((p = p.next)) {
              ret += s + p.data
            }
            return ret
          }
        },
        {
          key: 'concat',
          value: function concat(n) {
            if (this.length === 0) return Buffer2.alloc(0)
            var ret = Buffer2.allocUnsafe(n >>> 0)
            var p = this.head
            var i = 0
            while (p) {
              copyBuffer(p.data, ret, i)
              i += p.data.length
              p = p.next
            }
            return ret
          }
        },
        {
          key: 'consume',
          value: function consume(n, hasStrings) {
            var ret
            if (n < this.head.data.length) {
              ret = this.head.data.slice(0, n)
              this.head.data = this.head.data.slice(n)
            } else if (n === this.head.data.length) {
              ret = this.shift()
            } else {
              ret = hasStrings ? this._getString(n) : this._getBuffer(n)
            }
            return ret
          }
        },
        {
          key: 'first',
          value: function first() {
            return this.head.data
          }
        },
        {
          key: '_getString',
          value: function _getString(n) {
            var p = this.head
            var c = 1
            var ret = p.data
            n -= ret.length
            while ((p = p.next)) {
              var str = p.data
              var nb = n > str.length ? str.length : n
              if (nb === str.length) ret += str
              else ret += str.slice(0, n)
              n -= nb
              if (n === 0) {
                if (nb === str.length) {
                  ++c
                  if (p.next) this.head = p.next
                  else this.head = this.tail = null
                } else {
                  this.head = p
                  p.data = str.slice(nb)
                }
                break
              }
              ++c
            }
            this.length -= c
            return ret
          }
        },
        {
          key: '_getBuffer',
          value: function _getBuffer(n) {
            var ret = Buffer2.allocUnsafe(n)
            var p = this.head
            var c = 1
            p.data.copy(ret)
            n -= p.data.length
            while ((p = p.next)) {
              var buf = p.data
              var nb = n > buf.length ? buf.length : n
              buf.copy(ret, ret.length - n, 0, nb)
              n -= nb
              if (n === 0) {
                if (nb === buf.length) {
                  ++c
                  if (p.next) this.head = p.next
                  else this.head = this.tail = null
                } else {
                  this.head = p
                  p.data = buf.slice(nb)
                }
                break
              }
              ++c
            }
            this.length -= c
            return ret
          }
        },
        {
          key: custom,
          value: function value(_, options2) {
            return inspect(
              this,
              _objectSpread({}, options2, {
                depth: 0,
                customInspect: false
              })
            )
          }
        }
      ])
      return BufferList
    })()
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/destroy.js
var require_destroy = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/destroy.js'(
    exports,
    module2
  ) {
    'use strict'
    function destroy(err, cb) {
      var _this = this
      var readableDestroyed = this._readableState && this._readableState.destroyed
      var writableDestroyed = this._writableState && this._writableState.destroyed
      if (readableDestroyed || writableDestroyed) {
        if (cb) {
          cb(err)
        } else if (err) {
          if (!this._writableState) {
            process.nextTick(emitErrorNT, this, err)
          } else if (!this._writableState.errorEmitted) {
            this._writableState.errorEmitted = true
            process.nextTick(emitErrorNT, this, err)
          }
        }
        return this
      }
      if (this._readableState) {
        this._readableState.destroyed = true
      }
      if (this._writableState) {
        this._writableState.destroyed = true
      }
      this._destroy(err || null, function (err2) {
        if (!cb && err2) {
          if (!_this._writableState) {
            process.nextTick(emitErrorAndCloseNT, _this, err2)
          } else if (!_this._writableState.errorEmitted) {
            _this._writableState.errorEmitted = true
            process.nextTick(emitErrorAndCloseNT, _this, err2)
          } else {
            process.nextTick(emitCloseNT, _this)
          }
        } else if (cb) {
          process.nextTick(emitCloseNT, _this)
          cb(err2)
        } else {
          process.nextTick(emitCloseNT, _this)
        }
      })
      return this
    }
    function emitErrorAndCloseNT(self2, err) {
      emitErrorNT(self2, err)
      emitCloseNT(self2)
    }
    function emitCloseNT(self2) {
      if (self2._writableState && !self2._writableState.emitClose) return
      if (self2._readableState && !self2._readableState.emitClose) return
      self2.emit('close')
    }
    function undestroy() {
      if (this._readableState) {
        this._readableState.destroyed = false
        this._readableState.reading = false
        this._readableState.ended = false
        this._readableState.endEmitted = false
      }
      if (this._writableState) {
        this._writableState.destroyed = false
        this._writableState.ended = false
        this._writableState.ending = false
        this._writableState.finalCalled = false
        this._writableState.prefinished = false
        this._writableState.finished = false
        this._writableState.errorEmitted = false
      }
    }
    function emitErrorNT(self2, err) {
      self2.emit('error', err)
    }
    function errorOrDestroy(stream, err) {
      var rState = stream._readableState
      var wState = stream._writableState
      if ((rState && rState.autoDestroy) || (wState && wState.autoDestroy)) stream.destroy(err)
      else stream.emit('error', err)
    }
    module2.exports = {
      destroy,
      undestroy,
      errorOrDestroy
    }
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/errors.js
var require_errors = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/errors.js'(
    exports,
    module2
  ) {
    'use strict'
    var codes = {}
    function createErrorType(code, message, Base) {
      if (!Base) {
        Base = Error
      }
      function getMessage(arg1, arg2, arg3) {
        if (typeof message === 'string') {
          return message
        } else {
          return message(arg1, arg2, arg3)
        }
      }
      class NodeError extends Base {
        constructor(arg1, arg2, arg3) {
          super(getMessage(arg1, arg2, arg3))
        }
      }
      NodeError.prototype.name = Base.name
      NodeError.prototype.code = code
      codes[code] = NodeError
    }
    function oneOf(expected, thing) {
      if (Array.isArray(expected)) {
        const len = expected.length
        expected = expected.map((i) => String(i))
        if (len > 2) {
          return `one of ${thing} ${expected.slice(0, len - 1).join(', ')}, or ` + expected[len - 1]
        } else if (len === 2) {
          return `one of ${thing} ${expected[0]} or ${expected[1]}`
        } else {
          return `of ${thing} ${expected[0]}`
        }
      } else {
        return `of ${thing} ${String(expected)}`
      }
    }
    function startsWith(str, search, pos) {
      return str.substr(!pos || pos < 0 ? 0 : +pos, search.length) === search
    }
    function endsWith(str, search, this_len) {
      if (this_len === void 0 || this_len > str.length) {
        this_len = str.length
      }
      return str.substring(this_len - search.length, this_len) === search
    }
    function includes(str, search, start) {
      if (typeof start !== 'number') {
        start = 0
      }
      if (start + search.length > str.length) {
        return false
      } else {
        return str.indexOf(search, start) !== -1
      }
    }
    createErrorType(
      'ERR_INVALID_OPT_VALUE',
      function (name, value) {
        return 'The value "' + value + '" is invalid for option "' + name + '"'
      },
      TypeError
    )
    createErrorType(
      'ERR_INVALID_ARG_TYPE',
      function (name, expected, actual) {
        let determiner
        if (typeof expected === 'string' && startsWith(expected, 'not ')) {
          determiner = 'must not be'
          expected = expected.replace(/^not /, '')
        } else {
          determiner = 'must be'
        }
        let msg
        if (endsWith(name, ' argument')) {
          msg = `The ${name} ${determiner} ${oneOf(expected, 'type')}`
        } else {
          const type = includes(name, '.') ? 'property' : 'argument'
          msg = `The "${name}" ${type} ${determiner} ${oneOf(expected, 'type')}`
        }
        msg += `. Received type ${typeof actual}`
        return msg
      },
      TypeError
    )
    createErrorType('ERR_STREAM_PUSH_AFTER_EOF', 'stream.push() after EOF')
    createErrorType('ERR_METHOD_NOT_IMPLEMENTED', function (name) {
      return 'The ' + name + ' method is not implemented'
    })
    createErrorType('ERR_STREAM_PREMATURE_CLOSE', 'Premature close')
    createErrorType('ERR_STREAM_DESTROYED', function (name) {
      return 'Cannot call ' + name + ' after a stream was destroyed'
    })
    createErrorType('ERR_MULTIPLE_CALLBACK', 'Callback called multiple times')
    createErrorType('ERR_STREAM_CANNOT_PIPE', 'Cannot pipe, not readable')
    createErrorType('ERR_STREAM_WRITE_AFTER_END', 'write after end')
    createErrorType('ERR_STREAM_NULL_VALUES', 'May not write null values to stream', TypeError)
    createErrorType(
      'ERR_UNKNOWN_ENCODING',
      function (arg) {
        return 'Unknown encoding: ' + arg
      },
      TypeError
    )
    createErrorType('ERR_STREAM_UNSHIFT_AFTER_END_EVENT', 'stream.unshift() after end event')
    module2.exports.codes = codes
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/state.js
var require_state = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/state.js'(
    exports,
    module2
  ) {
    'use strict'
    var ERR_INVALID_OPT_VALUE = require_errors().codes.ERR_INVALID_OPT_VALUE
    function highWaterMarkFrom(options2, isDuplex, duplexKey) {
      return options2.highWaterMark != null
        ? options2.highWaterMark
        : isDuplex
        ? options2[duplexKey]
        : null
    }
    function getHighWaterMark(state, options2, duplexKey, isDuplex) {
      var hwm = highWaterMarkFrom(options2, isDuplex, duplexKey)
      if (hwm != null) {
        if (!(isFinite(hwm) && Math.floor(hwm) === hwm) || hwm < 0) {
          var name = isDuplex ? duplexKey : 'highWaterMark'
          throw new ERR_INVALID_OPT_VALUE(name, hwm)
        }
        return Math.floor(hwm)
      }
      return state.objectMode ? 16 : 16 * 1024
    }
    module2.exports = {
      getHighWaterMark
    }
  }
})

// ../../node_modules/.pnpm/inherits@2.0.4/node_modules/inherits/inherits_browser.js
var require_inherits_browser = __commonJS({
  '../../node_modules/.pnpm/inherits@2.0.4/node_modules/inherits/inherits_browser.js'(
    exports,
    module2
  ) {
    if (typeof Object.create === 'function') {
      module2.exports = function inherits(ctor, superCtor) {
        if (superCtor) {
          ctor.super_ = superCtor
          ctor.prototype = Object.create(superCtor.prototype, {
            constructor: {
              value: ctor,
              enumerable: false,
              writable: true,
              configurable: true
            }
          })
        }
      }
    } else {
      module2.exports = function inherits(ctor, superCtor) {
        if (superCtor) {
          ctor.super_ = superCtor
          var TempCtor = function () {}
          TempCtor.prototype = superCtor.prototype
          ctor.prototype = new TempCtor()
          ctor.prototype.constructor = ctor
        }
      }
    }
  }
})

// ../../node_modules/.pnpm/inherits@2.0.4/node_modules/inherits/inherits.js
var require_inherits = __commonJS({
  '../../node_modules/.pnpm/inherits@2.0.4/node_modules/inherits/inherits.js'(exports, module2) {
    try {
      util = require('util')
      if (typeof util.inherits !== 'function') throw ''
      module2.exports = util.inherits
    } catch (e) {
      module2.exports = require_inherits_browser()
    }
    var util
  }
})

// ../../node_modules/.pnpm/util-deprecate@1.0.2/node_modules/util-deprecate/node.js
var require_node = __commonJS({
  '../../node_modules/.pnpm/util-deprecate@1.0.2/node_modules/util-deprecate/node.js'(
    exports,
    module2
  ) {
    module2.exports = require('util').deprecate
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/_stream_writable.js
var require_stream_writable = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/_stream_writable.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = Writable
    function CorkedRequest(state) {
      var _this = this
      this.next = null
      this.entry = null
      this.finish = function () {
        onCorkedFinish(_this, state)
      }
    }
    var Duplex
    Writable.WritableState = WritableState
    var internalUtil = {
      deprecate: require_node()
    }
    var Stream = require_stream()
    var Buffer2 = require('buffer').Buffer
    var OurUint8Array = global.Uint8Array || function () {}
    function _uint8ArrayToBuffer(chunk) {
      return Buffer2.from(chunk)
    }
    function _isUint8Array(obj) {
      return Buffer2.isBuffer(obj) || obj instanceof OurUint8Array
    }
    var destroyImpl = require_destroy()
    var _require = require_state()
    var getHighWaterMark = _require.getHighWaterMark
    var _require$codes = require_errors().codes
    var ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE
    var ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED
    var ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK
    var ERR_STREAM_CANNOT_PIPE = _require$codes.ERR_STREAM_CANNOT_PIPE
    var ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED
    var ERR_STREAM_NULL_VALUES = _require$codes.ERR_STREAM_NULL_VALUES
    var ERR_STREAM_WRITE_AFTER_END = _require$codes.ERR_STREAM_WRITE_AFTER_END
    var ERR_UNKNOWN_ENCODING = _require$codes.ERR_UNKNOWN_ENCODING
    var errorOrDestroy = destroyImpl.errorOrDestroy
    require_inherits()(Writable, Stream)
    function nop() {}
    function WritableState(options2, stream, isDuplex) {
      Duplex = Duplex || require_stream_duplex()
      options2 = options2 || {}
      if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex
      this.objectMode = !!options2.objectMode
      if (isDuplex) this.objectMode = this.objectMode || !!options2.writableObjectMode
      this.highWaterMark = getHighWaterMark(this, options2, 'writableHighWaterMark', isDuplex)
      this.finalCalled = false
      this.needDrain = false
      this.ending = false
      this.ended = false
      this.finished = false
      this.destroyed = false
      var noDecode = options2.decodeStrings === false
      this.decodeStrings = !noDecode
      this.defaultEncoding = options2.defaultEncoding || 'utf8'
      this.length = 0
      this.writing = false
      this.corked = 0
      this.sync = true
      this.bufferProcessing = false
      this.onwrite = function (er) {
        onwrite(stream, er)
      }
      this.writecb = null
      this.writelen = 0
      this.bufferedRequest = null
      this.lastBufferedRequest = null
      this.pendingcb = 0
      this.prefinished = false
      this.errorEmitted = false
      this.emitClose = options2.emitClose !== false
      this.autoDestroy = !!options2.autoDestroy
      this.bufferedRequestCount = 0
      this.corkedRequestsFree = new CorkedRequest(this)
    }
    WritableState.prototype.getBuffer = function getBuffer() {
      var current = this.bufferedRequest
      var out = []
      while (current) {
        out.push(current)
        current = current.next
      }
      return out
    }
    ;(function () {
      try {
        Object.defineProperty(WritableState.prototype, 'buffer', {
          get: internalUtil.deprecate(
            function writableStateBufferGetter() {
              return this.getBuffer()
            },
            '_writableState.buffer is deprecated. Use _writableState.getBuffer instead.',
            'DEP0003'
          )
        })
      } catch (_) {}
    })()
    var realHasInstance
    if (
      typeof Symbol === 'function' &&
      Symbol.hasInstance &&
      typeof Function.prototype[Symbol.hasInstance] === 'function'
    ) {
      realHasInstance = Function.prototype[Symbol.hasInstance]
      Object.defineProperty(Writable, Symbol.hasInstance, {
        value: function value(object) {
          if (realHasInstance.call(this, object)) return true
          if (this !== Writable) return false
          return object && object._writableState instanceof WritableState
        }
      })
    } else {
      realHasInstance = function realHasInstance2(object) {
        return object instanceof this
      }
    }
    function Writable(options2) {
      Duplex = Duplex || require_stream_duplex()
      var isDuplex = this instanceof Duplex
      if (!isDuplex && !realHasInstance.call(Writable, this)) return new Writable(options2)
      this._writableState = new WritableState(options2, this, isDuplex)
      this.writable = true
      if (options2) {
        if (typeof options2.write === 'function') this._write = options2.write
        if (typeof options2.writev === 'function') this._writev = options2.writev
        if (typeof options2.destroy === 'function') this._destroy = options2.destroy
        if (typeof options2.final === 'function') this._final = options2.final
      }
      Stream.call(this)
    }
    Writable.prototype.pipe = function () {
      errorOrDestroy(this, new ERR_STREAM_CANNOT_PIPE())
    }
    function writeAfterEnd(stream, cb) {
      var er = new ERR_STREAM_WRITE_AFTER_END()
      errorOrDestroy(stream, er)
      process.nextTick(cb, er)
    }
    function validChunk(stream, state, chunk, cb) {
      var er
      if (chunk === null) {
        er = new ERR_STREAM_NULL_VALUES()
      } else if (typeof chunk !== 'string' && !state.objectMode) {
        er = new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer'], chunk)
      }
      if (er) {
        errorOrDestroy(stream, er)
        process.nextTick(cb, er)
        return false
      }
      return true
    }
    Writable.prototype.write = function (chunk, encoding, cb) {
      var state = this._writableState
      var ret = false
      var isBuf = !state.objectMode && _isUint8Array(chunk)
      if (isBuf && !Buffer2.isBuffer(chunk)) {
        chunk = _uint8ArrayToBuffer(chunk)
      }
      if (typeof encoding === 'function') {
        cb = encoding
        encoding = null
      }
      if (isBuf) encoding = 'buffer'
      else if (!encoding) encoding = state.defaultEncoding
      if (typeof cb !== 'function') cb = nop
      if (state.ending) writeAfterEnd(this, cb)
      else if (isBuf || validChunk(this, state, chunk, cb)) {
        state.pendingcb++
        ret = writeOrBuffer(this, state, isBuf, chunk, encoding, cb)
      }
      return ret
    }
    Writable.prototype.cork = function () {
      this._writableState.corked++
    }
    Writable.prototype.uncork = function () {
      var state = this._writableState
      if (state.corked) {
        state.corked--
        if (!state.writing && !state.corked && !state.bufferProcessing && state.bufferedRequest)
          clearBuffer(this, state)
      }
    }
    Writable.prototype.setDefaultEncoding = function setDefaultEncoding(encoding) {
      if (typeof encoding === 'string') encoding = encoding.toLowerCase()
      if (
        !(
          [
            'hex',
            'utf8',
            'utf-8',
            'ascii',
            'binary',
            'base64',
            'ucs2',
            'ucs-2',
            'utf16le',
            'utf-16le',
            'raw'
          ].indexOf((encoding + '').toLowerCase()) > -1
        )
      )
        throw new ERR_UNKNOWN_ENCODING(encoding)
      this._writableState.defaultEncoding = encoding
      return this
    }
    Object.defineProperty(Writable.prototype, 'writableBuffer', {
      enumerable: false,
      get: function get() {
        return this._writableState && this._writableState.getBuffer()
      }
    })
    function decodeChunk(state, chunk, encoding) {
      if (!state.objectMode && state.decodeStrings !== false && typeof chunk === 'string') {
        chunk = Buffer2.from(chunk, encoding)
      }
      return chunk
    }
    Object.defineProperty(Writable.prototype, 'writableHighWaterMark', {
      enumerable: false,
      get: function get() {
        return this._writableState.highWaterMark
      }
    })
    function writeOrBuffer(stream, state, isBuf, chunk, encoding, cb) {
      if (!isBuf) {
        var newChunk = decodeChunk(state, chunk, encoding)
        if (chunk !== newChunk) {
          isBuf = true
          encoding = 'buffer'
          chunk = newChunk
        }
      }
      var len = state.objectMode ? 1 : chunk.length
      state.length += len
      var ret = state.length < state.highWaterMark
      if (!ret) state.needDrain = true
      if (state.writing || state.corked) {
        var last = state.lastBufferedRequest
        state.lastBufferedRequest = {
          chunk,
          encoding,
          isBuf,
          callback: cb,
          next: null
        }
        if (last) {
          last.next = state.lastBufferedRequest
        } else {
          state.bufferedRequest = state.lastBufferedRequest
        }
        state.bufferedRequestCount += 1
      } else {
        doWrite(stream, state, false, len, chunk, encoding, cb)
      }
      return ret
    }
    function doWrite(stream, state, writev, len, chunk, encoding, cb) {
      state.writelen = len
      state.writecb = cb
      state.writing = true
      state.sync = true
      if (state.destroyed) state.onwrite(new ERR_STREAM_DESTROYED('write'))
      else if (writev) stream._writev(chunk, state.onwrite)
      else stream._write(chunk, encoding, state.onwrite)
      state.sync = false
    }
    function onwriteError(stream, state, sync, er, cb) {
      --state.pendingcb
      if (sync) {
        process.nextTick(cb, er)
        process.nextTick(finishMaybe, stream, state)
        stream._writableState.errorEmitted = true
        errorOrDestroy(stream, er)
      } else {
        cb(er)
        stream._writableState.errorEmitted = true
        errorOrDestroy(stream, er)
        finishMaybe(stream, state)
      }
    }
    function onwriteStateUpdate(state) {
      state.writing = false
      state.writecb = null
      state.length -= state.writelen
      state.writelen = 0
    }
    function onwrite(stream, er) {
      var state = stream._writableState
      var sync = state.sync
      var cb = state.writecb
      if (typeof cb !== 'function') throw new ERR_MULTIPLE_CALLBACK()
      onwriteStateUpdate(state)
      if (er) onwriteError(stream, state, sync, er, cb)
      else {
        var finished = needFinish(state) || stream.destroyed
        if (!finished && !state.corked && !state.bufferProcessing && state.bufferedRequest) {
          clearBuffer(stream, state)
        }
        if (sync) {
          process.nextTick(afterWrite, stream, state, finished, cb)
        } else {
          afterWrite(stream, state, finished, cb)
        }
      }
    }
    function afterWrite(stream, state, finished, cb) {
      if (!finished) onwriteDrain(stream, state)
      state.pendingcb--
      cb()
      finishMaybe(stream, state)
    }
    function onwriteDrain(stream, state) {
      if (state.length === 0 && state.needDrain) {
        state.needDrain = false
        stream.emit('drain')
      }
    }
    function clearBuffer(stream, state) {
      state.bufferProcessing = true
      var entry = state.bufferedRequest
      if (stream._writev && entry && entry.next) {
        var l = state.bufferedRequestCount
        var buffer = new Array(l)
        var holder = state.corkedRequestsFree
        holder.entry = entry
        var count = 0
        var allBuffers = true
        while (entry) {
          buffer[count] = entry
          if (!entry.isBuf) allBuffers = false
          entry = entry.next
          count += 1
        }
        buffer.allBuffers = allBuffers
        doWrite(stream, state, true, state.length, buffer, '', holder.finish)
        state.pendingcb++
        state.lastBufferedRequest = null
        if (holder.next) {
          state.corkedRequestsFree = holder.next
          holder.next = null
        } else {
          state.corkedRequestsFree = new CorkedRequest(state)
        }
        state.bufferedRequestCount = 0
      } else {
        while (entry) {
          var chunk = entry.chunk
          var encoding = entry.encoding
          var cb = entry.callback
          var len = state.objectMode ? 1 : chunk.length
          doWrite(stream, state, false, len, chunk, encoding, cb)
          entry = entry.next
          state.bufferedRequestCount--
          if (state.writing) {
            break
          }
        }
        if (entry === null) state.lastBufferedRequest = null
      }
      state.bufferedRequest = entry
      state.bufferProcessing = false
    }
    Writable.prototype._write = function (chunk, encoding, cb) {
      cb(new ERR_METHOD_NOT_IMPLEMENTED('_write()'))
    }
    Writable.prototype._writev = null
    Writable.prototype.end = function (chunk, encoding, cb) {
      var state = this._writableState
      if (typeof chunk === 'function') {
        cb = chunk
        chunk = null
        encoding = null
      } else if (typeof encoding === 'function') {
        cb = encoding
        encoding = null
      }
      if (chunk !== null && chunk !== void 0) this.write(chunk, encoding)
      if (state.corked) {
        state.corked = 1
        this.uncork()
      }
      if (!state.ending) endWritable(this, state, cb)
      return this
    }
    Object.defineProperty(Writable.prototype, 'writableLength', {
      enumerable: false,
      get: function get() {
        return this._writableState.length
      }
    })
    function needFinish(state) {
      return (
        state.ending &&
        state.length === 0 &&
        state.bufferedRequest === null &&
        !state.finished &&
        !state.writing
      )
    }
    function callFinal(stream, state) {
      stream._final(function (err) {
        state.pendingcb--
        if (err) {
          errorOrDestroy(stream, err)
        }
        state.prefinished = true
        stream.emit('prefinish')
        finishMaybe(stream, state)
      })
    }
    function prefinish(stream, state) {
      if (!state.prefinished && !state.finalCalled) {
        if (typeof stream._final === 'function' && !state.destroyed) {
          state.pendingcb++
          state.finalCalled = true
          process.nextTick(callFinal, stream, state)
        } else {
          state.prefinished = true
          stream.emit('prefinish')
        }
      }
    }
    function finishMaybe(stream, state) {
      var need = needFinish(state)
      if (need) {
        prefinish(stream, state)
        if (state.pendingcb === 0) {
          state.finished = true
          stream.emit('finish')
          if (state.autoDestroy) {
            var rState = stream._readableState
            if (!rState || (rState.autoDestroy && rState.endEmitted)) {
              stream.destroy()
            }
          }
        }
      }
      return need
    }
    function endWritable(stream, state, cb) {
      state.ending = true
      finishMaybe(stream, state)
      if (cb) {
        if (state.finished) process.nextTick(cb)
        else stream.once('finish', cb)
      }
      state.ended = true
      stream.writable = false
    }
    function onCorkedFinish(corkReq, state, err) {
      var entry = corkReq.entry
      corkReq.entry = null
      while (entry) {
        var cb = entry.callback
        state.pendingcb--
        cb(err)
        entry = entry.next
      }
      state.corkedRequestsFree.next = corkReq
    }
    Object.defineProperty(Writable.prototype, 'destroyed', {
      enumerable: false,
      get: function get() {
        if (this._writableState === void 0) {
          return false
        }
        return this._writableState.destroyed
      },
      set: function set(value) {
        if (!this._writableState) {
          return
        }
        this._writableState.destroyed = value
      }
    })
    Writable.prototype.destroy = destroyImpl.destroy
    Writable.prototype._undestroy = destroyImpl.undestroy
    Writable.prototype._destroy = function (err, cb) {
      cb(err)
    }
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/_stream_duplex.js
var require_stream_duplex = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/_stream_duplex.js'(
    exports,
    module2
  ) {
    'use strict'
    var objectKeys =
      Object.keys ||
      function (obj) {
        var keys2 = []
        for (var key in obj) {
          keys2.push(key)
        }
        return keys2
      }
    module2.exports = Duplex
    var Readable = require_stream_readable()
    var Writable = require_stream_writable()
    require_inherits()(Duplex, Readable)
    {
      keys = objectKeys(Writable.prototype)
      for (v = 0; v < keys.length; v++) {
        method = keys[v]
        if (!Duplex.prototype[method]) Duplex.prototype[method] = Writable.prototype[method]
      }
    }
    var keys
    var method
    var v
    function Duplex(options2) {
      if (!(this instanceof Duplex)) return new Duplex(options2)
      Readable.call(this, options2)
      Writable.call(this, options2)
      this.allowHalfOpen = true
      if (options2) {
        if (options2.readable === false) this.readable = false
        if (options2.writable === false) this.writable = false
        if (options2.allowHalfOpen === false) {
          this.allowHalfOpen = false
          this.once('end', onend)
        }
      }
    }
    Object.defineProperty(Duplex.prototype, 'writableHighWaterMark', {
      enumerable: false,
      get: function get() {
        return this._writableState.highWaterMark
      }
    })
    Object.defineProperty(Duplex.prototype, 'writableBuffer', {
      enumerable: false,
      get: function get() {
        return this._writableState && this._writableState.getBuffer()
      }
    })
    Object.defineProperty(Duplex.prototype, 'writableLength', {
      enumerable: false,
      get: function get() {
        return this._writableState.length
      }
    })
    function onend() {
      if (this._writableState.ended) return
      process.nextTick(onEndNT, this)
    }
    function onEndNT(self2) {
      self2.end()
    }
    Object.defineProperty(Duplex.prototype, 'destroyed', {
      enumerable: false,
      get: function get() {
        if (this._readableState === void 0 || this._writableState === void 0) {
          return false
        }
        return this._readableState.destroyed && this._writableState.destroyed
      },
      set: function set(value) {
        if (this._readableState === void 0 || this._writableState === void 0) {
          return
        }
        this._readableState.destroyed = value
        this._writableState.destroyed = value
      }
    })
  }
})

// ../../node_modules/.pnpm/safe-buffer@5.2.1/node_modules/safe-buffer/index.js
var require_safe_buffer = __commonJS({
  '../../node_modules/.pnpm/safe-buffer@5.2.1/node_modules/safe-buffer/index.js'(exports, module2) {
    var buffer = require('buffer')
    var Buffer2 = buffer.Buffer
    function copyProps(src, dst) {
      for (var key in src) {
        dst[key] = src[key]
      }
    }
    if (Buffer2.from && Buffer2.alloc && Buffer2.allocUnsafe && Buffer2.allocUnsafeSlow) {
      module2.exports = buffer
    } else {
      copyProps(buffer, exports)
      exports.Buffer = SafeBuffer
    }
    function SafeBuffer(arg, encodingOrOffset, length) {
      return Buffer2(arg, encodingOrOffset, length)
    }
    SafeBuffer.prototype = Object.create(Buffer2.prototype)
    copyProps(Buffer2, SafeBuffer)
    SafeBuffer.from = function (arg, encodingOrOffset, length) {
      if (typeof arg === 'number') {
        throw new TypeError('Argument must not be a number')
      }
      return Buffer2(arg, encodingOrOffset, length)
    }
    SafeBuffer.alloc = function (size, fill, encoding) {
      if (typeof size !== 'number') {
        throw new TypeError('Argument must be a number')
      }
      var buf = Buffer2(size)
      if (fill !== void 0) {
        if (typeof encoding === 'string') {
          buf.fill(fill, encoding)
        } else {
          buf.fill(fill)
        }
      } else {
        buf.fill(0)
      }
      return buf
    }
    SafeBuffer.allocUnsafe = function (size) {
      if (typeof size !== 'number') {
        throw new TypeError('Argument must be a number')
      }
      return Buffer2(size)
    }
    SafeBuffer.allocUnsafeSlow = function (size) {
      if (typeof size !== 'number') {
        throw new TypeError('Argument must be a number')
      }
      return buffer.SlowBuffer(size)
    }
  }
})

// ../../node_modules/.pnpm/string_decoder@1.3.0/node_modules/string_decoder/lib/string_decoder.js
var require_string_decoder = __commonJS({
  '../../node_modules/.pnpm/string_decoder@1.3.0/node_modules/string_decoder/lib/string_decoder.js'(
    exports
  ) {
    'use strict'
    var Buffer2 = require_safe_buffer().Buffer
    var isEncoding =
      Buffer2.isEncoding ||
      function (encoding) {
        encoding = '' + encoding
        switch (encoding && encoding.toLowerCase()) {
          case 'hex':
          case 'utf8':
          case 'utf-8':
          case 'ascii':
          case 'binary':
          case 'base64':
          case 'ucs2':
          case 'ucs-2':
          case 'utf16le':
          case 'utf-16le':
          case 'raw':
            return true
          default:
            return false
        }
      }
    function _normalizeEncoding(enc) {
      if (!enc) return 'utf8'
      var retried
      while (true) {
        switch (enc) {
          case 'utf8':
          case 'utf-8':
            return 'utf8'
          case 'ucs2':
          case 'ucs-2':
          case 'utf16le':
          case 'utf-16le':
            return 'utf16le'
          case 'latin1':
          case 'binary':
            return 'latin1'
          case 'base64':
          case 'ascii':
          case 'hex':
            return enc
          default:
            if (retried) return
            enc = ('' + enc).toLowerCase()
            retried = true
        }
      }
    }
    function normalizeEncoding(enc) {
      var nenc = _normalizeEncoding(enc)
      if (typeof nenc !== 'string' && (Buffer2.isEncoding === isEncoding || !isEncoding(enc)))
        throw new Error('Unknown encoding: ' + enc)
      return nenc || enc
    }
    exports.StringDecoder = StringDecoder
    function StringDecoder(encoding) {
      this.encoding = normalizeEncoding(encoding)
      var nb
      switch (this.encoding) {
        case 'utf16le':
          this.text = utf16Text
          this.end = utf16End
          nb = 4
          break
        case 'utf8':
          this.fillLast = utf8FillLast
          nb = 4
          break
        case 'base64':
          this.text = base64Text
          this.end = base64End
          nb = 3
          break
        default:
          this.write = simpleWrite
          this.end = simpleEnd
          return
      }
      this.lastNeed = 0
      this.lastTotal = 0
      this.lastChar = Buffer2.allocUnsafe(nb)
    }
    StringDecoder.prototype.write = function (buf) {
      if (buf.length === 0) return ''
      var r
      var i
      if (this.lastNeed) {
        r = this.fillLast(buf)
        if (r === void 0) return ''
        i = this.lastNeed
        this.lastNeed = 0
      } else {
        i = 0
      }
      if (i < buf.length) return r ? r + this.text(buf, i) : this.text(buf, i)
      return r || ''
    }
    StringDecoder.prototype.end = utf8End
    StringDecoder.prototype.text = utf8Text
    StringDecoder.prototype.fillLast = function (buf) {
      if (this.lastNeed <= buf.length) {
        buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, this.lastNeed)
        return this.lastChar.toString(this.encoding, 0, this.lastTotal)
      }
      buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, buf.length)
      this.lastNeed -= buf.length
    }
    function utf8CheckByte(byte) {
      if (byte <= 127) return 0
      else if (byte >> 5 === 6) return 2
      else if (byte >> 4 === 14) return 3
      else if (byte >> 3 === 30) return 4
      return byte >> 6 === 2 ? -1 : -2
    }
    function utf8CheckIncomplete(self2, buf, i) {
      var j = buf.length - 1
      if (j < i) return 0
      var nb = utf8CheckByte(buf[j])
      if (nb >= 0) {
        if (nb > 0) self2.lastNeed = nb - 1
        return nb
      }
      if (--j < i || nb === -2) return 0
      nb = utf8CheckByte(buf[j])
      if (nb >= 0) {
        if (nb > 0) self2.lastNeed = nb - 2
        return nb
      }
      if (--j < i || nb === -2) return 0
      nb = utf8CheckByte(buf[j])
      if (nb >= 0) {
        if (nb > 0) {
          if (nb === 2) nb = 0
          else self2.lastNeed = nb - 3
        }
        return nb
      }
      return 0
    }
    function utf8CheckExtraBytes(self2, buf, p) {
      if ((buf[0] & 192) !== 128) {
        self2.lastNeed = 0
        return '\uFFFD'
      }
      if (self2.lastNeed > 1 && buf.length > 1) {
        if ((buf[1] & 192) !== 128) {
          self2.lastNeed = 1
          return '\uFFFD'
        }
        if (self2.lastNeed > 2 && buf.length > 2) {
          if ((buf[2] & 192) !== 128) {
            self2.lastNeed = 2
            return '\uFFFD'
          }
        }
      }
    }
    function utf8FillLast(buf) {
      var p = this.lastTotal - this.lastNeed
      var r = utf8CheckExtraBytes(this, buf, p)
      if (r !== void 0) return r
      if (this.lastNeed <= buf.length) {
        buf.copy(this.lastChar, p, 0, this.lastNeed)
        return this.lastChar.toString(this.encoding, 0, this.lastTotal)
      }
      buf.copy(this.lastChar, p, 0, buf.length)
      this.lastNeed -= buf.length
    }
    function utf8Text(buf, i) {
      var total = utf8CheckIncomplete(this, buf, i)
      if (!this.lastNeed) return buf.toString('utf8', i)
      this.lastTotal = total
      var end = buf.length - (total - this.lastNeed)
      buf.copy(this.lastChar, 0, end)
      return buf.toString('utf8', i, end)
    }
    function utf8End(buf) {
      var r = buf && buf.length ? this.write(buf) : ''
      if (this.lastNeed) return r + '\uFFFD'
      return r
    }
    function utf16Text(buf, i) {
      if ((buf.length - i) % 2 === 0) {
        var r = buf.toString('utf16le', i)
        if (r) {
          var c = r.charCodeAt(r.length - 1)
          if (c >= 55296 && c <= 56319) {
            this.lastNeed = 2
            this.lastTotal = 4
            this.lastChar[0] = buf[buf.length - 2]
            this.lastChar[1] = buf[buf.length - 1]
            return r.slice(0, -1)
          }
        }
        return r
      }
      this.lastNeed = 1
      this.lastTotal = 2
      this.lastChar[0] = buf[buf.length - 1]
      return buf.toString('utf16le', i, buf.length - 1)
    }
    function utf16End(buf) {
      var r = buf && buf.length ? this.write(buf) : ''
      if (this.lastNeed) {
        var end = this.lastTotal - this.lastNeed
        return r + this.lastChar.toString('utf16le', 0, end)
      }
      return r
    }
    function base64Text(buf, i) {
      var n = (buf.length - i) % 3
      if (n === 0) return buf.toString('base64', i)
      this.lastNeed = 3 - n
      this.lastTotal = 3
      if (n === 1) {
        this.lastChar[0] = buf[buf.length - 1]
      } else {
        this.lastChar[0] = buf[buf.length - 2]
        this.lastChar[1] = buf[buf.length - 1]
      }
      return buf.toString('base64', i, buf.length - n)
    }
    function base64End(buf) {
      var r = buf && buf.length ? this.write(buf) : ''
      if (this.lastNeed) return r + this.lastChar.toString('base64', 0, 3 - this.lastNeed)
      return r
    }
    function simpleWrite(buf) {
      return buf.toString(this.encoding)
    }
    function simpleEnd(buf) {
      return buf && buf.length ? this.write(buf) : ''
    }
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/end-of-stream.js
var require_end_of_stream = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/end-of-stream.js'(
    exports,
    module2
  ) {
    'use strict'
    var ERR_STREAM_PREMATURE_CLOSE = require_errors().codes.ERR_STREAM_PREMATURE_CLOSE
    function once(callback) {
      var called = false
      return function () {
        if (called) return
        called = true
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key]
        }
        callback.apply(this, args)
      }
    }
    function noop() {}
    function isRequest(stream) {
      return stream.setHeader && typeof stream.abort === 'function'
    }
    function eos(stream, opts, callback) {
      if (typeof opts === 'function') return eos(stream, null, opts)
      if (!opts) opts = {}
      callback = once(callback || noop)
      var readable = opts.readable || (opts.readable !== false && stream.readable)
      var writable = opts.writable || (opts.writable !== false && stream.writable)
      var onlegacyfinish = function onlegacyfinish2() {
        if (!stream.writable) onfinish()
      }
      var writableEnded = stream._writableState && stream._writableState.finished
      var onfinish = function onfinish2() {
        writable = false
        writableEnded = true
        if (!readable) callback.call(stream)
      }
      var readableEnded = stream._readableState && stream._readableState.endEmitted
      var onend = function onend2() {
        readable = false
        readableEnded = true
        if (!writable) callback.call(stream)
      }
      var onerror = function onerror2(err) {
        callback.call(stream, err)
      }
      var onclose = function onclose2() {
        var err
        if (readable && !readableEnded) {
          if (!stream._readableState || !stream._readableState.ended)
            err = new ERR_STREAM_PREMATURE_CLOSE()
          return callback.call(stream, err)
        }
        if (writable && !writableEnded) {
          if (!stream._writableState || !stream._writableState.ended)
            err = new ERR_STREAM_PREMATURE_CLOSE()
          return callback.call(stream, err)
        }
      }
      var onrequest = function onrequest2() {
        stream.req.on('finish', onfinish)
      }
      if (isRequest(stream)) {
        stream.on('complete', onfinish)
        stream.on('abort', onclose)
        if (stream.req) onrequest()
        else stream.on('request', onrequest)
      } else if (writable && !stream._writableState) {
        stream.on('end', onlegacyfinish)
        stream.on('close', onlegacyfinish)
      }
      stream.on('end', onend)
      stream.on('finish', onfinish)
      if (opts.error !== false) stream.on('error', onerror)
      stream.on('close', onclose)
      return function () {
        stream.removeListener('complete', onfinish)
        stream.removeListener('abort', onclose)
        stream.removeListener('request', onrequest)
        if (stream.req) stream.req.removeListener('finish', onfinish)
        stream.removeListener('end', onlegacyfinish)
        stream.removeListener('close', onlegacyfinish)
        stream.removeListener('finish', onfinish)
        stream.removeListener('end', onend)
        stream.removeListener('error', onerror)
        stream.removeListener('close', onclose)
      }
    }
    module2.exports = eos
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/async_iterator.js
var require_async_iterator = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/async_iterator.js'(
    exports,
    module2
  ) {
    'use strict'
    var _Object$setPrototypeO
    function _defineProperty(obj, key, value) {
      if (key in obj) {
        Object.defineProperty(obj, key, {
          value,
          enumerable: true,
          configurable: true,
          writable: true
        })
      } else {
        obj[key] = value
      }
      return obj
    }
    var finished = require_end_of_stream()
    var kLastResolve = Symbol('lastResolve')
    var kLastReject = Symbol('lastReject')
    var kError = Symbol('error')
    var kEnded = Symbol('ended')
    var kLastPromise = Symbol('lastPromise')
    var kHandlePromise = Symbol('handlePromise')
    var kStream = Symbol('stream')
    function createIterResult(value, done) {
      return {
        value,
        done
      }
    }
    function readAndResolve(iter) {
      var resolve = iter[kLastResolve]
      if (resolve !== null) {
        var data = iter[kStream].read()
        if (data !== null) {
          iter[kLastPromise] = null
          iter[kLastResolve] = null
          iter[kLastReject] = null
          resolve(createIterResult(data, false))
        }
      }
    }
    function onReadable(iter) {
      process.nextTick(readAndResolve, iter)
    }
    function wrapForNext(lastPromise, iter) {
      return function (resolve, reject) {
        lastPromise.then(function () {
          if (iter[kEnded]) {
            resolve(createIterResult(void 0, true))
            return
          }
          iter[kHandlePromise](resolve, reject)
        }, reject)
      }
    }
    var AsyncIteratorPrototype = Object.getPrototypeOf(function () {})
    var ReadableStreamAsyncIteratorPrototype = Object.setPrototypeOf(
      ((_Object$setPrototypeO = {
        get stream() {
          return this[kStream]
        },
        next: function next() {
          var _this = this
          var error = this[kError]
          if (error !== null) {
            return Promise.reject(error)
          }
          if (this[kEnded]) {
            return Promise.resolve(createIterResult(void 0, true))
          }
          if (this[kStream].destroyed) {
            return new Promise(function (resolve, reject) {
              process.nextTick(function () {
                if (_this[kError]) {
                  reject(_this[kError])
                } else {
                  resolve(createIterResult(void 0, true))
                }
              })
            })
          }
          var lastPromise = this[kLastPromise]
          var promise
          if (lastPromise) {
            promise = new Promise(wrapForNext(lastPromise, this))
          } else {
            var data = this[kStream].read()
            if (data !== null) {
              return Promise.resolve(createIterResult(data, false))
            }
            promise = new Promise(this[kHandlePromise])
          }
          this[kLastPromise] = promise
          return promise
        }
      }),
      _defineProperty(_Object$setPrototypeO, Symbol.asyncIterator, function () {
        return this
      }),
      _defineProperty(_Object$setPrototypeO, 'return', function _return() {
        var _this2 = this
        return new Promise(function (resolve, reject) {
          _this2[kStream].destroy(null, function (err) {
            if (err) {
              reject(err)
              return
            }
            resolve(createIterResult(void 0, true))
          })
        })
      }),
      _Object$setPrototypeO),
      AsyncIteratorPrototype
    )
    var createReadableStreamAsyncIterator = function createReadableStreamAsyncIterator2(stream) {
      var _Object$create
      var iterator = Object.create(
        ReadableStreamAsyncIteratorPrototype,
        ((_Object$create = {}),
        _defineProperty(_Object$create, kStream, {
          value: stream,
          writable: true
        }),
        _defineProperty(_Object$create, kLastResolve, {
          value: null,
          writable: true
        }),
        _defineProperty(_Object$create, kLastReject, {
          value: null,
          writable: true
        }),
        _defineProperty(_Object$create, kError, {
          value: null,
          writable: true
        }),
        _defineProperty(_Object$create, kEnded, {
          value: stream._readableState.endEmitted,
          writable: true
        }),
        _defineProperty(_Object$create, kHandlePromise, {
          value: function value(resolve, reject) {
            var data = iterator[kStream].read()
            if (data) {
              iterator[kLastPromise] = null
              iterator[kLastResolve] = null
              iterator[kLastReject] = null
              resolve(createIterResult(data, false))
            } else {
              iterator[kLastResolve] = resolve
              iterator[kLastReject] = reject
            }
          },
          writable: true
        }),
        _Object$create)
      )
      iterator[kLastPromise] = null
      finished(stream, function (err) {
        if (err && err.code !== 'ERR_STREAM_PREMATURE_CLOSE') {
          var reject = iterator[kLastReject]
          if (reject !== null) {
            iterator[kLastPromise] = null
            iterator[kLastResolve] = null
            iterator[kLastReject] = null
            reject(err)
          }
          iterator[kError] = err
          return
        }
        var resolve = iterator[kLastResolve]
        if (resolve !== null) {
          iterator[kLastPromise] = null
          iterator[kLastResolve] = null
          iterator[kLastReject] = null
          resolve(createIterResult(void 0, true))
        }
        iterator[kEnded] = true
      })
      stream.on('readable', onReadable.bind(null, iterator))
      return iterator
    }
    module2.exports = createReadableStreamAsyncIterator
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/from.js
var require_from = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/from.js'(
    exports,
    module2
  ) {
    'use strict'
    function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
      try {
        var info = gen[key](arg)
        var value = info.value
      } catch (error) {
        reject(error)
        return
      }
      if (info.done) {
        resolve(value)
      } else {
        Promise.resolve(value).then(_next, _throw)
      }
    }
    function _asyncToGenerator(fn) {
      return function () {
        var self2 = this,
          args = arguments
        return new Promise(function (resolve, reject) {
          var gen = fn.apply(self2, args)
          function _next(value) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'next', value)
          }
          function _throw(err) {
            asyncGeneratorStep(gen, resolve, reject, _next, _throw, 'throw', err)
          }
          _next(void 0)
        })
      }
    }
    function ownKeys(object, enumerableOnly) {
      var keys = Object.keys(object)
      if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object)
        if (enumerableOnly)
          symbols = symbols.filter(function (sym) {
            return Object.getOwnPropertyDescriptor(object, sym).enumerable
          })
        keys.push.apply(keys, symbols)
      }
      return keys
    }
    function _objectSpread(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i] != null ? arguments[i] : {}
        if (i % 2) {
          ownKeys(Object(source), true).forEach(function (key) {
            _defineProperty(target, key, source[key])
          })
        } else if (Object.getOwnPropertyDescriptors) {
          Object.defineProperties(target, Object.getOwnPropertyDescriptors(source))
        } else {
          ownKeys(Object(source)).forEach(function (key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key))
          })
        }
      }
      return target
    }
    function _defineProperty(obj, key, value) {
      if (key in obj) {
        Object.defineProperty(obj, key, {
          value,
          enumerable: true,
          configurable: true,
          writable: true
        })
      } else {
        obj[key] = value
      }
      return obj
    }
    var ERR_INVALID_ARG_TYPE = require_errors().codes.ERR_INVALID_ARG_TYPE
    function from(Readable, iterable, opts) {
      var iterator
      if (iterable && typeof iterable.next === 'function') {
        iterator = iterable
      } else if (iterable && iterable[Symbol.asyncIterator])
        iterator = iterable[Symbol.asyncIterator]()
      else if (iterable && iterable[Symbol.iterator]) iterator = iterable[Symbol.iterator]()
      else throw new ERR_INVALID_ARG_TYPE('iterable', ['Iterable'], iterable)
      var readable = new Readable(
        _objectSpread(
          {
            objectMode: true
          },
          opts
        )
      )
      var reading = false
      readable._read = function () {
        if (!reading) {
          reading = true
          next()
        }
      }
      function next() {
        return _next2.apply(this, arguments)
      }
      function _next2() {
        _next2 = _asyncToGenerator(function* () {
          try {
            var _ref = yield iterator.next(),
              value = _ref.value,
              done = _ref.done
            if (done) {
              readable.push(null)
            } else if (readable.push(yield value)) {
              next()
            } else {
              reading = false
            }
          } catch (err) {
            readable.destroy(err)
          }
        })
        return _next2.apply(this, arguments)
      }
      return readable
    }
    module2.exports = from
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/_stream_readable.js
var require_stream_readable = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/_stream_readable.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = Readable
    var Duplex
    Readable.ReadableState = ReadableState
    var EE = require('events').EventEmitter
    var EElistenerCount = function EElistenerCount2(emitter, type) {
      return emitter.listeners(type).length
    }
    var Stream = require_stream()
    var Buffer2 = require('buffer').Buffer
    var OurUint8Array = global.Uint8Array || function () {}
    function _uint8ArrayToBuffer(chunk) {
      return Buffer2.from(chunk)
    }
    function _isUint8Array(obj) {
      return Buffer2.isBuffer(obj) || obj instanceof OurUint8Array
    }
    var debugUtil = require('util')
    var debug
    if (debugUtil && debugUtil.debuglog) {
      debug = debugUtil.debuglog('stream')
    } else {
      debug = function debug2() {}
    }
    var BufferList = require_buffer_list()
    var destroyImpl = require_destroy()
    var _require = require_state()
    var getHighWaterMark = _require.getHighWaterMark
    var _require$codes = require_errors().codes
    var ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE
    var ERR_STREAM_PUSH_AFTER_EOF = _require$codes.ERR_STREAM_PUSH_AFTER_EOF
    var ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED
    var ERR_STREAM_UNSHIFT_AFTER_END_EVENT = _require$codes.ERR_STREAM_UNSHIFT_AFTER_END_EVENT
    var StringDecoder
    var createReadableStreamAsyncIterator
    var from
    require_inherits()(Readable, Stream)
    var errorOrDestroy = destroyImpl.errorOrDestroy
    var kProxyEvents = ['error', 'close', 'destroy', 'pause', 'resume']
    function prependListener(emitter, event, fn) {
      if (typeof emitter.prependListener === 'function') return emitter.prependListener(event, fn)
      if (!emitter._events || !emitter._events[event]) emitter.on(event, fn)
      else if (Array.isArray(emitter._events[event])) emitter._events[event].unshift(fn)
      else emitter._events[event] = [fn, emitter._events[event]]
    }
    function ReadableState(options2, stream, isDuplex) {
      Duplex = Duplex || require_stream_duplex()
      options2 = options2 || {}
      if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex
      this.objectMode = !!options2.objectMode
      if (isDuplex) this.objectMode = this.objectMode || !!options2.readableObjectMode
      this.highWaterMark = getHighWaterMark(this, options2, 'readableHighWaterMark', isDuplex)
      this.buffer = new BufferList()
      this.length = 0
      this.pipes = null
      this.pipesCount = 0
      this.flowing = null
      this.ended = false
      this.endEmitted = false
      this.reading = false
      this.sync = true
      this.needReadable = false
      this.emittedReadable = false
      this.readableListening = false
      this.resumeScheduled = false
      this.paused = true
      this.emitClose = options2.emitClose !== false
      this.autoDestroy = !!options2.autoDestroy
      this.destroyed = false
      this.defaultEncoding = options2.defaultEncoding || 'utf8'
      this.awaitDrain = 0
      this.readingMore = false
      this.decoder = null
      this.encoding = null
      if (options2.encoding) {
        if (!StringDecoder) StringDecoder = require_string_decoder().StringDecoder
        this.decoder = new StringDecoder(options2.encoding)
        this.encoding = options2.encoding
      }
    }
    function Readable(options2) {
      Duplex = Duplex || require_stream_duplex()
      if (!(this instanceof Readable)) return new Readable(options2)
      var isDuplex = this instanceof Duplex
      this._readableState = new ReadableState(options2, this, isDuplex)
      this.readable = true
      if (options2) {
        if (typeof options2.read === 'function') this._read = options2.read
        if (typeof options2.destroy === 'function') this._destroy = options2.destroy
      }
      Stream.call(this)
    }
    Object.defineProperty(Readable.prototype, 'destroyed', {
      enumerable: false,
      get: function get() {
        if (this._readableState === void 0) {
          return false
        }
        return this._readableState.destroyed
      },
      set: function set(value) {
        if (!this._readableState) {
          return
        }
        this._readableState.destroyed = value
      }
    })
    Readable.prototype.destroy = destroyImpl.destroy
    Readable.prototype._undestroy = destroyImpl.undestroy
    Readable.prototype._destroy = function (err, cb) {
      cb(err)
    }
    Readable.prototype.push = function (chunk, encoding) {
      var state = this._readableState
      var skipChunkCheck
      if (!state.objectMode) {
        if (typeof chunk === 'string') {
          encoding = encoding || state.defaultEncoding
          if (encoding !== state.encoding) {
            chunk = Buffer2.from(chunk, encoding)
            encoding = ''
          }
          skipChunkCheck = true
        }
      } else {
        skipChunkCheck = true
      }
      return readableAddChunk(this, chunk, encoding, false, skipChunkCheck)
    }
    Readable.prototype.unshift = function (chunk) {
      return readableAddChunk(this, chunk, null, true, false)
    }
    function readableAddChunk(stream, chunk, encoding, addToFront, skipChunkCheck) {
      debug('readableAddChunk', chunk)
      var state = stream._readableState
      if (chunk === null) {
        state.reading = false
        onEofChunk(stream, state)
      } else {
        var er
        if (!skipChunkCheck) er = chunkInvalid(state, chunk)
        if (er) {
          errorOrDestroy(stream, er)
        } else if (state.objectMode || (chunk && chunk.length > 0)) {
          if (
            typeof chunk !== 'string' &&
            !state.objectMode &&
            Object.getPrototypeOf(chunk) !== Buffer2.prototype
          ) {
            chunk = _uint8ArrayToBuffer(chunk)
          }
          if (addToFront) {
            if (state.endEmitted) errorOrDestroy(stream, new ERR_STREAM_UNSHIFT_AFTER_END_EVENT())
            else addChunk(stream, state, chunk, true)
          } else if (state.ended) {
            errorOrDestroy(stream, new ERR_STREAM_PUSH_AFTER_EOF())
          } else if (state.destroyed) {
            return false
          } else {
            state.reading = false
            if (state.decoder && !encoding) {
              chunk = state.decoder.write(chunk)
              if (state.objectMode || chunk.length !== 0) addChunk(stream, state, chunk, false)
              else maybeReadMore(stream, state)
            } else {
              addChunk(stream, state, chunk, false)
            }
          }
        } else if (!addToFront) {
          state.reading = false
          maybeReadMore(stream, state)
        }
      }
      return !state.ended && (state.length < state.highWaterMark || state.length === 0)
    }
    function addChunk(stream, state, chunk, addToFront) {
      if (state.flowing && state.length === 0 && !state.sync) {
        state.awaitDrain = 0
        stream.emit('data', chunk)
      } else {
        state.length += state.objectMode ? 1 : chunk.length
        if (addToFront) state.buffer.unshift(chunk)
        else state.buffer.push(chunk)
        if (state.needReadable) emitReadable(stream)
      }
      maybeReadMore(stream, state)
    }
    function chunkInvalid(state, chunk) {
      var er
      if (
        !_isUint8Array(chunk) &&
        typeof chunk !== 'string' &&
        chunk !== void 0 &&
        !state.objectMode
      ) {
        er = new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer', 'Uint8Array'], chunk)
      }
      return er
    }
    Readable.prototype.isPaused = function () {
      return this._readableState.flowing === false
    }
    Readable.prototype.setEncoding = function (enc) {
      if (!StringDecoder) StringDecoder = require_string_decoder().StringDecoder
      var decoder = new StringDecoder(enc)
      this._readableState.decoder = decoder
      this._readableState.encoding = this._readableState.decoder.encoding
      var p = this._readableState.buffer.head
      var content = ''
      while (p !== null) {
        content += decoder.write(p.data)
        p = p.next
      }
      this._readableState.buffer.clear()
      if (content !== '') this._readableState.buffer.push(content)
      this._readableState.length = content.length
      return this
    }
    var MAX_HWM = 1073741824
    function computeNewHighWaterMark(n) {
      if (n >= MAX_HWM) {
        n = MAX_HWM
      } else {
        n--
        n |= n >>> 1
        n |= n >>> 2
        n |= n >>> 4
        n |= n >>> 8
        n |= n >>> 16
        n++
      }
      return n
    }
    function howMuchToRead(n, state) {
      if (n <= 0 || (state.length === 0 && state.ended)) return 0
      if (state.objectMode) return 1
      if (n !== n) {
        if (state.flowing && state.length) return state.buffer.head.data.length
        else return state.length
      }
      if (n > state.highWaterMark) state.highWaterMark = computeNewHighWaterMark(n)
      if (n <= state.length) return n
      if (!state.ended) {
        state.needReadable = true
        return 0
      }
      return state.length
    }
    Readable.prototype.read = function (n) {
      debug('read', n)
      n = parseInt(n, 10)
      var state = this._readableState
      var nOrig = n
      if (n !== 0) state.emittedReadable = false
      if (
        n === 0 &&
        state.needReadable &&
        ((state.highWaterMark !== 0 ? state.length >= state.highWaterMark : state.length > 0) ||
          state.ended)
      ) {
        debug('read: emitReadable', state.length, state.ended)
        if (state.length === 0 && state.ended) endReadable(this)
        else emitReadable(this)
        return null
      }
      n = howMuchToRead(n, state)
      if (n === 0 && state.ended) {
        if (state.length === 0) endReadable(this)
        return null
      }
      var doRead = state.needReadable
      debug('need readable', doRead)
      if (state.length === 0 || state.length - n < state.highWaterMark) {
        doRead = true
        debug('length less than watermark', doRead)
      }
      if (state.ended || state.reading) {
        doRead = false
        debug('reading or ended', doRead)
      } else if (doRead) {
        debug('do read')
        state.reading = true
        state.sync = true
        if (state.length === 0) state.needReadable = true
        this._read(state.highWaterMark)
        state.sync = false
        if (!state.reading) n = howMuchToRead(nOrig, state)
      }
      var ret
      if (n > 0) ret = fromList(n, state)
      else ret = null
      if (ret === null) {
        state.needReadable = state.length <= state.highWaterMark
        n = 0
      } else {
        state.length -= n
        state.awaitDrain = 0
      }
      if (state.length === 0) {
        if (!state.ended) state.needReadable = true
        if (nOrig !== n && state.ended) endReadable(this)
      }
      if (ret !== null) this.emit('data', ret)
      return ret
    }
    function onEofChunk(stream, state) {
      debug('onEofChunk')
      if (state.ended) return
      if (state.decoder) {
        var chunk = state.decoder.end()
        if (chunk && chunk.length) {
          state.buffer.push(chunk)
          state.length += state.objectMode ? 1 : chunk.length
        }
      }
      state.ended = true
      if (state.sync) {
        emitReadable(stream)
      } else {
        state.needReadable = false
        if (!state.emittedReadable) {
          state.emittedReadable = true
          emitReadable_(stream)
        }
      }
    }
    function emitReadable(stream) {
      var state = stream._readableState
      debug('emitReadable', state.needReadable, state.emittedReadable)
      state.needReadable = false
      if (!state.emittedReadable) {
        debug('emitReadable', state.flowing)
        state.emittedReadable = true
        process.nextTick(emitReadable_, stream)
      }
    }
    function emitReadable_(stream) {
      var state = stream._readableState
      debug('emitReadable_', state.destroyed, state.length, state.ended)
      if (!state.destroyed && (state.length || state.ended)) {
        stream.emit('readable')
        state.emittedReadable = false
      }
      state.needReadable = !state.flowing && !state.ended && state.length <= state.highWaterMark
      flow(stream)
    }
    function maybeReadMore(stream, state) {
      if (!state.readingMore) {
        state.readingMore = true
        process.nextTick(maybeReadMore_, stream, state)
      }
    }
    function maybeReadMore_(stream, state) {
      while (
        !state.reading &&
        !state.ended &&
        (state.length < state.highWaterMark || (state.flowing && state.length === 0))
      ) {
        var len = state.length
        debug('maybeReadMore read 0')
        stream.read(0)
        if (len === state.length) break
      }
      state.readingMore = false
    }
    Readable.prototype._read = function (n) {
      errorOrDestroy(this, new ERR_METHOD_NOT_IMPLEMENTED('_read()'))
    }
    Readable.prototype.pipe = function (dest, pipeOpts) {
      var src = this
      var state = this._readableState
      switch (state.pipesCount) {
        case 0:
          state.pipes = dest
          break
        case 1:
          state.pipes = [state.pipes, dest]
          break
        default:
          state.pipes.push(dest)
          break
      }
      state.pipesCount += 1
      debug('pipe count=%d opts=%j', state.pipesCount, pipeOpts)
      var doEnd =
        (!pipeOpts || pipeOpts.end !== false) && dest !== process.stdout && dest !== process.stderr
      var endFn = doEnd ? onend : unpipe
      if (state.endEmitted) process.nextTick(endFn)
      else src.once('end', endFn)
      dest.on('unpipe', onunpipe)
      function onunpipe(readable, unpipeInfo) {
        debug('onunpipe')
        if (readable === src) {
          if (unpipeInfo && unpipeInfo.hasUnpiped === false) {
            unpipeInfo.hasUnpiped = true
            cleanup()
          }
        }
      }
      function onend() {
        debug('onend')
        dest.end()
      }
      var ondrain = pipeOnDrain(src)
      dest.on('drain', ondrain)
      var cleanedUp = false
      function cleanup() {
        debug('cleanup')
        dest.removeListener('close', onclose)
        dest.removeListener('finish', onfinish)
        dest.removeListener('drain', ondrain)
        dest.removeListener('error', onerror)
        dest.removeListener('unpipe', onunpipe)
        src.removeListener('end', onend)
        src.removeListener('end', unpipe)
        src.removeListener('data', ondata)
        cleanedUp = true
        if (state.awaitDrain && (!dest._writableState || dest._writableState.needDrain)) ondrain()
      }
      src.on('data', ondata)
      function ondata(chunk) {
        debug('ondata')
        var ret = dest.write(chunk)
        debug('dest.write', ret)
        if (ret === false) {
          if (
            ((state.pipesCount === 1 && state.pipes === dest) ||
              (state.pipesCount > 1 && indexOf(state.pipes, dest) !== -1)) &&
            !cleanedUp
          ) {
            debug('false write response, pause', state.awaitDrain)
            state.awaitDrain++
          }
          src.pause()
        }
      }
      function onerror(er) {
        debug('onerror', er)
        unpipe()
        dest.removeListener('error', onerror)
        if (EElistenerCount(dest, 'error') === 0) errorOrDestroy(dest, er)
      }
      prependListener(dest, 'error', onerror)
      function onclose() {
        dest.removeListener('finish', onfinish)
        unpipe()
      }
      dest.once('close', onclose)
      function onfinish() {
        debug('onfinish')
        dest.removeListener('close', onclose)
        unpipe()
      }
      dest.once('finish', onfinish)
      function unpipe() {
        debug('unpipe')
        src.unpipe(dest)
      }
      dest.emit('pipe', src)
      if (!state.flowing) {
        debug('pipe resume')
        src.resume()
      }
      return dest
    }
    function pipeOnDrain(src) {
      return function pipeOnDrainFunctionResult() {
        var state = src._readableState
        debug('pipeOnDrain', state.awaitDrain)
        if (state.awaitDrain) state.awaitDrain--
        if (state.awaitDrain === 0 && EElistenerCount(src, 'data')) {
          state.flowing = true
          flow(src)
        }
      }
    }
    Readable.prototype.unpipe = function (dest) {
      var state = this._readableState
      var unpipeInfo = {
        hasUnpiped: false
      }
      if (state.pipesCount === 0) return this
      if (state.pipesCount === 1) {
        if (dest && dest !== state.pipes) return this
        if (!dest) dest = state.pipes
        state.pipes = null
        state.pipesCount = 0
        state.flowing = false
        if (dest) dest.emit('unpipe', this, unpipeInfo)
        return this
      }
      if (!dest) {
        var dests = state.pipes
        var len = state.pipesCount
        state.pipes = null
        state.pipesCount = 0
        state.flowing = false
        for (var i = 0; i < len; i++) {
          dests[i].emit('unpipe', this, {
            hasUnpiped: false
          })
        }
        return this
      }
      var index = indexOf(state.pipes, dest)
      if (index === -1) return this
      state.pipes.splice(index, 1)
      state.pipesCount -= 1
      if (state.pipesCount === 1) state.pipes = state.pipes[0]
      dest.emit('unpipe', this, unpipeInfo)
      return this
    }
    Readable.prototype.on = function (ev, fn) {
      var res = Stream.prototype.on.call(this, ev, fn)
      var state = this._readableState
      if (ev === 'data') {
        state.readableListening = this.listenerCount('readable') > 0
        if (state.flowing !== false) this.resume()
      } else if (ev === 'readable') {
        if (!state.endEmitted && !state.readableListening) {
          state.readableListening = state.needReadable = true
          state.flowing = false
          state.emittedReadable = false
          debug('on readable', state.length, state.reading)
          if (state.length) {
            emitReadable(this)
          } else if (!state.reading) {
            process.nextTick(nReadingNextTick, this)
          }
        }
      }
      return res
    }
    Readable.prototype.addListener = Readable.prototype.on
    Readable.prototype.removeListener = function (ev, fn) {
      var res = Stream.prototype.removeListener.call(this, ev, fn)
      if (ev === 'readable') {
        process.nextTick(updateReadableListening, this)
      }
      return res
    }
    Readable.prototype.removeAllListeners = function (ev) {
      var res = Stream.prototype.removeAllListeners.apply(this, arguments)
      if (ev === 'readable' || ev === void 0) {
        process.nextTick(updateReadableListening, this)
      }
      return res
    }
    function updateReadableListening(self2) {
      var state = self2._readableState
      state.readableListening = self2.listenerCount('readable') > 0
      if (state.resumeScheduled && !state.paused) {
        state.flowing = true
      } else if (self2.listenerCount('data') > 0) {
        self2.resume()
      }
    }
    function nReadingNextTick(self2) {
      debug('readable nexttick read 0')
      self2.read(0)
    }
    Readable.prototype.resume = function () {
      var state = this._readableState
      if (!state.flowing) {
        debug('resume')
        state.flowing = !state.readableListening
        resume(this, state)
      }
      state.paused = false
      return this
    }
    function resume(stream, state) {
      if (!state.resumeScheduled) {
        state.resumeScheduled = true
        process.nextTick(resume_, stream, state)
      }
    }
    function resume_(stream, state) {
      debug('resume', state.reading)
      if (!state.reading) {
        stream.read(0)
      }
      state.resumeScheduled = false
      stream.emit('resume')
      flow(stream)
      if (state.flowing && !state.reading) stream.read(0)
    }
    Readable.prototype.pause = function () {
      debug('call pause flowing=%j', this._readableState.flowing)
      if (this._readableState.flowing !== false) {
        debug('pause')
        this._readableState.flowing = false
        this.emit('pause')
      }
      this._readableState.paused = true
      return this
    }
    function flow(stream) {
      var state = stream._readableState
      debug('flow', state.flowing)
      while (state.flowing && stream.read() !== null) {}
    }
    Readable.prototype.wrap = function (stream) {
      var _this = this
      var state = this._readableState
      var paused = false
      stream.on('end', function () {
        debug('wrapped end')
        if (state.decoder && !state.ended) {
          var chunk = state.decoder.end()
          if (chunk && chunk.length) _this.push(chunk)
        }
        _this.push(null)
      })
      stream.on('data', function (chunk) {
        debug('wrapped data')
        if (state.decoder) chunk = state.decoder.write(chunk)
        if (state.objectMode && (chunk === null || chunk === void 0)) return
        else if (!state.objectMode && (!chunk || !chunk.length)) return
        var ret = _this.push(chunk)
        if (!ret) {
          paused = true
          stream.pause()
        }
      })
      for (var i in stream) {
        if (this[i] === void 0 && typeof stream[i] === 'function') {
          this[i] = (function methodWrap(method) {
            return function methodWrapReturnFunction() {
              return stream[method].apply(stream, arguments)
            }
          })(i)
        }
      }
      for (var n = 0; n < kProxyEvents.length; n++) {
        stream.on(kProxyEvents[n], this.emit.bind(this, kProxyEvents[n]))
      }
      this._read = function (n2) {
        debug('wrapped _read', n2)
        if (paused) {
          paused = false
          stream.resume()
        }
      }
      return this
    }
    if (typeof Symbol === 'function') {
      Readable.prototype[Symbol.asyncIterator] = function () {
        if (createReadableStreamAsyncIterator === void 0) {
          createReadableStreamAsyncIterator = require_async_iterator()
        }
        return createReadableStreamAsyncIterator(this)
      }
    }
    Object.defineProperty(Readable.prototype, 'readableHighWaterMark', {
      enumerable: false,
      get: function get() {
        return this._readableState.highWaterMark
      }
    })
    Object.defineProperty(Readable.prototype, 'readableBuffer', {
      enumerable: false,
      get: function get() {
        return this._readableState && this._readableState.buffer
      }
    })
    Object.defineProperty(Readable.prototype, 'readableFlowing', {
      enumerable: false,
      get: function get() {
        return this._readableState.flowing
      },
      set: function set(state) {
        if (this._readableState) {
          this._readableState.flowing = state
        }
      }
    })
    Readable._fromList = fromList
    Object.defineProperty(Readable.prototype, 'readableLength', {
      enumerable: false,
      get: function get() {
        return this._readableState.length
      }
    })
    function fromList(n, state) {
      if (state.length === 0) return null
      var ret
      if (state.objectMode) ret = state.buffer.shift()
      else if (!n || n >= state.length) {
        if (state.decoder) ret = state.buffer.join('')
        else if (state.buffer.length === 1) ret = state.buffer.first()
        else ret = state.buffer.concat(state.length)
        state.buffer.clear()
      } else {
        ret = state.buffer.consume(n, state.decoder)
      }
      return ret
    }
    function endReadable(stream) {
      var state = stream._readableState
      debug('endReadable', state.endEmitted)
      if (!state.endEmitted) {
        state.ended = true
        process.nextTick(endReadableNT, state, stream)
      }
    }
    function endReadableNT(state, stream) {
      debug('endReadableNT', state.endEmitted, state.length)
      if (!state.endEmitted && state.length === 0) {
        state.endEmitted = true
        stream.readable = false
        stream.emit('end')
        if (state.autoDestroy) {
          var wState = stream._writableState
          if (!wState || (wState.autoDestroy && wState.finished)) {
            stream.destroy()
          }
        }
      }
    }
    if (typeof Symbol === 'function') {
      Readable.from = function (iterable, opts) {
        if (from === void 0) {
          from = require_from()
        }
        return from(Readable, iterable, opts)
      }
    }
    function indexOf(xs, x) {
      for (var i = 0, l = xs.length; i < l; i++) {
        if (xs[i] === x) return i
      }
      return -1
    }
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/_stream_transform.js
var require_stream_transform = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/_stream_transform.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = Transform
    var _require$codes = require_errors().codes
    var ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED
    var ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK
    var ERR_TRANSFORM_ALREADY_TRANSFORMING = _require$codes.ERR_TRANSFORM_ALREADY_TRANSFORMING
    var ERR_TRANSFORM_WITH_LENGTH_0 = _require$codes.ERR_TRANSFORM_WITH_LENGTH_0
    var Duplex = require_stream_duplex()
    require_inherits()(Transform, Duplex)
    function afterTransform(er, data) {
      var ts = this._transformState
      ts.transforming = false
      var cb = ts.writecb
      if (cb === null) {
        return this.emit('error', new ERR_MULTIPLE_CALLBACK())
      }
      ts.writechunk = null
      ts.writecb = null
      if (data != null) this.push(data)
      cb(er)
      var rs = this._readableState
      rs.reading = false
      if (rs.needReadable || rs.length < rs.highWaterMark) {
        this._read(rs.highWaterMark)
      }
    }
    function Transform(options2) {
      if (!(this instanceof Transform)) return new Transform(options2)
      Duplex.call(this, options2)
      this._transformState = {
        afterTransform: afterTransform.bind(this),
        needTransform: false,
        transforming: false,
        writecb: null,
        writechunk: null,
        writeencoding: null
      }
      this._readableState.needReadable = true
      this._readableState.sync = false
      if (options2) {
        if (typeof options2.transform === 'function') this._transform = options2.transform
        if (typeof options2.flush === 'function') this._flush = options2.flush
      }
      this.on('prefinish', prefinish)
    }
    function prefinish() {
      var _this = this
      if (typeof this._flush === 'function' && !this._readableState.destroyed) {
        this._flush(function (er, data) {
          done(_this, er, data)
        })
      } else {
        done(this, null, null)
      }
    }
    Transform.prototype.push = function (chunk, encoding) {
      this._transformState.needTransform = false
      return Duplex.prototype.push.call(this, chunk, encoding)
    }
    Transform.prototype._transform = function (chunk, encoding, cb) {
      cb(new ERR_METHOD_NOT_IMPLEMENTED('_transform()'))
    }
    Transform.prototype._write = function (chunk, encoding, cb) {
      var ts = this._transformState
      ts.writecb = cb
      ts.writechunk = chunk
      ts.writeencoding = encoding
      if (!ts.transforming) {
        var rs = this._readableState
        if (ts.needTransform || rs.needReadable || rs.length < rs.highWaterMark)
          this._read(rs.highWaterMark)
      }
    }
    Transform.prototype._read = function (n) {
      var ts = this._transformState
      if (ts.writechunk !== null && !ts.transforming) {
        ts.transforming = true
        this._transform(ts.writechunk, ts.writeencoding, ts.afterTransform)
      } else {
        ts.needTransform = true
      }
    }
    Transform.prototype._destroy = function (err, cb) {
      Duplex.prototype._destroy.call(this, err, function (err2) {
        cb(err2)
      })
    }
    function done(stream, er, data) {
      if (er) return stream.emit('error', er)
      if (data != null) stream.push(data)
      if (stream._writableState.length) throw new ERR_TRANSFORM_WITH_LENGTH_0()
      if (stream._transformState.transforming) throw new ERR_TRANSFORM_ALREADY_TRANSFORMING()
      return stream.push(null)
    }
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/_stream_passthrough.js
var require_stream_passthrough = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/_stream_passthrough.js'(
    exports,
    module2
  ) {
    'use strict'
    module2.exports = PassThrough
    var Transform = require_stream_transform()
    require_inherits()(PassThrough, Transform)
    function PassThrough(options2) {
      if (!(this instanceof PassThrough)) return new PassThrough(options2)
      Transform.call(this, options2)
    }
    PassThrough.prototype._transform = function (chunk, encoding, cb) {
      cb(null, chunk)
    }
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/pipeline.js
var require_pipeline = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/lib/internal/streams/pipeline.js'(
    exports,
    module2
  ) {
    'use strict'
    var eos
    function once(callback) {
      var called = false
      return function () {
        if (called) return
        called = true
        callback.apply(void 0, arguments)
      }
    }
    var _require$codes = require_errors().codes
    var ERR_MISSING_ARGS = _require$codes.ERR_MISSING_ARGS
    var ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED
    function noop(err) {
      if (err) throw err
    }
    function isRequest(stream) {
      return stream.setHeader && typeof stream.abort === 'function'
    }
    function destroyer(stream, reading, writing, callback) {
      callback = once(callback)
      var closed = false
      stream.on('close', function () {
        closed = true
      })
      if (eos === void 0) eos = require_end_of_stream()
      eos(
        stream,
        {
          readable: reading,
          writable: writing
        },
        function (err) {
          if (err) return callback(err)
          closed = true
          callback()
        }
      )
      var destroyed = false
      return function (err) {
        if (closed) return
        if (destroyed) return
        destroyed = true
        if (isRequest(stream)) return stream.abort()
        if (typeof stream.destroy === 'function') return stream.destroy()
        callback(err || new ERR_STREAM_DESTROYED('pipe'))
      }
    }
    function call(fn) {
      fn()
    }
    function pipe(from, to) {
      return from.pipe(to)
    }
    function popCallback(streams) {
      if (!streams.length) return noop
      if (typeof streams[streams.length - 1] !== 'function') return noop
      return streams.pop()
    }
    function pipeline() {
      for (var _len = arguments.length, streams = new Array(_len), _key = 0; _key < _len; _key++) {
        streams[_key] = arguments[_key]
      }
      var callback = popCallback(streams)
      if (Array.isArray(streams[0])) streams = streams[0]
      if (streams.length < 2) {
        throw new ERR_MISSING_ARGS('streams')
      }
      var error
      var destroys = streams.map(function (stream, i) {
        var reading = i < streams.length - 1
        var writing = i > 0
        return destroyer(stream, reading, writing, function (err) {
          if (!error) error = err
          if (err) destroys.forEach(call)
          if (reading) return
          destroys.forEach(call)
          callback(error)
        })
      })
      return streams.reduce(pipe)
    }
    module2.exports = pipeline
  }
})

// ../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/readable.js
var require_readable = __commonJS({
  '../../node_modules/.pnpm/readable-stream@3.6.0/node_modules/readable-stream/readable.js'(
    exports,
    module2
  ) {
    var Stream = require('stream')
    if (process.env.READABLE_STREAM === 'disable' && Stream) {
      module2.exports = Stream.Readable
      Object.assign(module2.exports, Stream)
      module2.exports.Stream = Stream
    } else {
      exports = module2.exports = require_stream_readable()
      exports.Stream = Stream || exports
      exports.Readable = exports
      exports.Writable = require_stream_writable()
      exports.Duplex = require_stream_duplex()
      exports.Transform = require_stream_transform()
      exports.PassThrough = require_stream_passthrough()
      exports.finished = require_end_of_stream()
      exports.pipeline = require_pipeline()
    }
  }
})

// ../../node_modules/.pnpm/bl@4.1.0/node_modules/bl/BufferList.js
var require_BufferList = __commonJS({
  '../../node_modules/.pnpm/bl@4.1.0/node_modules/bl/BufferList.js'(exports, module2) {
    'use strict'
    var { Buffer: Buffer2 } = require('buffer')
    var symbol = Symbol.for('BufferList')
    function BufferList(buf) {
      if (!(this instanceof BufferList)) {
        return new BufferList(buf)
      }
      BufferList._init.call(this, buf)
    }
    BufferList._init = function _init(buf) {
      Object.defineProperty(this, symbol, { value: true })
      this._bufs = []
      this.length = 0
      if (buf) {
        this.append(buf)
      }
    }
    BufferList.prototype._new = function _new(buf) {
      return new BufferList(buf)
    }
    BufferList.prototype._offset = function _offset(offset) {
      if (offset === 0) {
        return [0, 0]
      }
      let tot = 0
      for (let i = 0; i < this._bufs.length; i++) {
        const _t = tot + this._bufs[i].length
        if (offset < _t || i === this._bufs.length - 1) {
          return [i, offset - tot]
        }
        tot = _t
      }
    }
    BufferList.prototype._reverseOffset = function (blOffset) {
      const bufferId = blOffset[0]
      let offset = blOffset[1]
      for (let i = 0; i < bufferId; i++) {
        offset += this._bufs[i].length
      }
      return offset
    }
    BufferList.prototype.get = function get(index) {
      if (index > this.length || index < 0) {
        return void 0
      }
      const offset = this._offset(index)
      return this._bufs[offset[0]][offset[1]]
    }
    BufferList.prototype.slice = function slice(start, end) {
      if (typeof start === 'number' && start < 0) {
        start += this.length
      }
      if (typeof end === 'number' && end < 0) {
        end += this.length
      }
      return this.copy(null, 0, start, end)
    }
    BufferList.prototype.copy = function copy(dst, dstStart, srcStart, srcEnd) {
      if (typeof srcStart !== 'number' || srcStart < 0) {
        srcStart = 0
      }
      if (typeof srcEnd !== 'number' || srcEnd > this.length) {
        srcEnd = this.length
      }
      if (srcStart >= this.length) {
        return dst || Buffer2.alloc(0)
      }
      if (srcEnd <= 0) {
        return dst || Buffer2.alloc(0)
      }
      const copy2 = !!dst
      const off = this._offset(srcStart)
      const len = srcEnd - srcStart
      let bytes = len
      let bufoff = (copy2 && dstStart) || 0
      let start = off[1]
      if (srcStart === 0 && srcEnd === this.length) {
        if (!copy2) {
          return this._bufs.length === 1 ? this._bufs[0] : Buffer2.concat(this._bufs, this.length)
        }
        for (let i = 0; i < this._bufs.length; i++) {
          this._bufs[i].copy(dst, bufoff)
          bufoff += this._bufs[i].length
        }
        return dst
      }
      if (bytes <= this._bufs[off[0]].length - start) {
        return copy2
          ? this._bufs[off[0]].copy(dst, dstStart, start, start + bytes)
          : this._bufs[off[0]].slice(start, start + bytes)
      }
      if (!copy2) {
        dst = Buffer2.allocUnsafe(len)
      }
      for (let i = off[0]; i < this._bufs.length; i++) {
        const l = this._bufs[i].length - start
        if (bytes > l) {
          this._bufs[i].copy(dst, bufoff, start)
          bufoff += l
        } else {
          this._bufs[i].copy(dst, bufoff, start, start + bytes)
          bufoff += l
          break
        }
        bytes -= l
        if (start) {
          start = 0
        }
      }
      if (dst.length > bufoff) return dst.slice(0, bufoff)
      return dst
    }
    BufferList.prototype.shallowSlice = function shallowSlice(start, end) {
      start = start || 0
      end = typeof end !== 'number' ? this.length : end
      if (start < 0) {
        start += this.length
      }
      if (end < 0) {
        end += this.length
      }
      if (start === end) {
        return this._new()
      }
      const startOffset = this._offset(start)
      const endOffset = this._offset(end)
      const buffers = this._bufs.slice(startOffset[0], endOffset[0] + 1)
      if (endOffset[1] === 0) {
        buffers.pop()
      } else {
        buffers[buffers.length - 1] = buffers[buffers.length - 1].slice(0, endOffset[1])
      }
      if (startOffset[1] !== 0) {
        buffers[0] = buffers[0].slice(startOffset[1])
      }
      return this._new(buffers)
    }
    BufferList.prototype.toString = function toString(encoding, start, end) {
      return this.slice(start, end).toString(encoding)
    }
    BufferList.prototype.consume = function consume(bytes) {
      bytes = Math.trunc(bytes)
      if (Number.isNaN(bytes) || bytes <= 0) return this
      while (this._bufs.length) {
        if (bytes >= this._bufs[0].length) {
          bytes -= this._bufs[0].length
          this.length -= this._bufs[0].length
          this._bufs.shift()
        } else {
          this._bufs[0] = this._bufs[0].slice(bytes)
          this.length -= bytes
          break
        }
      }
      return this
    }
    BufferList.prototype.duplicate = function duplicate() {
      const copy = this._new()
      for (let i = 0; i < this._bufs.length; i++) {
        copy.append(this._bufs[i])
      }
      return copy
    }
    BufferList.prototype.append = function append(buf) {
      if (buf == null) {
        return this
      }
      if (buf.buffer) {
        this._appendBuffer(Buffer2.from(buf.buffer, buf.byteOffset, buf.byteLength))
      } else if (Array.isArray(buf)) {
        for (let i = 0; i < buf.length; i++) {
          this.append(buf[i])
        }
      } else if (this._isBufferList(buf)) {
        for (let i = 0; i < buf._bufs.length; i++) {
          this.append(buf._bufs[i])
        }
      } else {
        if (typeof buf === 'number') {
          buf = buf.toString()
        }
        this._appendBuffer(Buffer2.from(buf))
      }
      return this
    }
    BufferList.prototype._appendBuffer = function appendBuffer(buf) {
      this._bufs.push(buf)
      this.length += buf.length
    }
    BufferList.prototype.indexOf = function (search, offset, encoding) {
      if (encoding === void 0 && typeof offset === 'string') {
        encoding = offset
        offset = void 0
      }
      if (typeof search === 'function' || Array.isArray(search)) {
        throw new TypeError(
          'The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.'
        )
      } else if (typeof search === 'number') {
        search = Buffer2.from([search])
      } else if (typeof search === 'string') {
        search = Buffer2.from(search, encoding)
      } else if (this._isBufferList(search)) {
        search = search.slice()
      } else if (Array.isArray(search.buffer)) {
        search = Buffer2.from(search.buffer, search.byteOffset, search.byteLength)
      } else if (!Buffer2.isBuffer(search)) {
        search = Buffer2.from(search)
      }
      offset = Number(offset || 0)
      if (isNaN(offset)) {
        offset = 0
      }
      if (offset < 0) {
        offset = this.length + offset
      }
      if (offset < 0) {
        offset = 0
      }
      if (search.length === 0) {
        return offset > this.length ? this.length : offset
      }
      const blOffset = this._offset(offset)
      let blIndex = blOffset[0]
      let buffOffset = blOffset[1]
      for (; blIndex < this._bufs.length; blIndex++) {
        const buff = this._bufs[blIndex]
        while (buffOffset < buff.length) {
          const availableWindow = buff.length - buffOffset
          if (availableWindow >= search.length) {
            const nativeSearchResult = buff.indexOf(search, buffOffset)
            if (nativeSearchResult !== -1) {
              return this._reverseOffset([blIndex, nativeSearchResult])
            }
            buffOffset = buff.length - search.length + 1
          } else {
            const revOffset = this._reverseOffset([blIndex, buffOffset])
            if (this._match(revOffset, search)) {
              return revOffset
            }
            buffOffset++
          }
        }
        buffOffset = 0
      }
      return -1
    }
    BufferList.prototype._match = function (offset, search) {
      if (this.length - offset < search.length) {
        return false
      }
      for (let searchOffset = 0; searchOffset < search.length; searchOffset++) {
        if (this.get(offset + searchOffset) !== search[searchOffset]) {
          return false
        }
      }
      return true
    }
    ;(function () {
      const methods = {
        readDoubleBE: 8,
        readDoubleLE: 8,
        readFloatBE: 4,
        readFloatLE: 4,
        readInt32BE: 4,
        readInt32LE: 4,
        readUInt32BE: 4,
        readUInt32LE: 4,
        readInt16BE: 2,
        readInt16LE: 2,
        readUInt16BE: 2,
        readUInt16LE: 2,
        readInt8: 1,
        readUInt8: 1,
        readIntBE: null,
        readIntLE: null,
        readUIntBE: null,
        readUIntLE: null
      }
      for (const m in methods) {
        ;(function (m2) {
          if (methods[m2] === null) {
            BufferList.prototype[m2] = function (offset, byteLength) {
              return this.slice(offset, offset + byteLength)[m2](0, byteLength)
            }
          } else {
            BufferList.prototype[m2] = function (offset = 0) {
              return this.slice(offset, offset + methods[m2])[m2](0)
            }
          }
        })(m)
      }
    })()
    BufferList.prototype._isBufferList = function _isBufferList(b) {
      return b instanceof BufferList || BufferList.isBufferList(b)
    }
    BufferList.isBufferList = function isBufferList(b) {
      return b != null && b[symbol]
    }
    module2.exports = BufferList
  }
})

// ../../node_modules/.pnpm/bl@4.1.0/node_modules/bl/bl.js
var require_bl = __commonJS({
  '../../node_modules/.pnpm/bl@4.1.0/node_modules/bl/bl.js'(exports, module2) {
    'use strict'
    var DuplexStream = require_readable().Duplex
    var inherits = require_inherits()
    var BufferList = require_BufferList()
    function BufferListStream(callback) {
      if (!(this instanceof BufferListStream)) {
        return new BufferListStream(callback)
      }
      if (typeof callback === 'function') {
        this._callback = callback
        const piper = function piper2(err) {
          if (this._callback) {
            this._callback(err)
            this._callback = null
          }
        }.bind(this)
        this.on('pipe', function onPipe(src) {
          src.on('error', piper)
        })
        this.on('unpipe', function onUnpipe(src) {
          src.removeListener('error', piper)
        })
        callback = null
      }
      BufferList._init.call(this, callback)
      DuplexStream.call(this)
    }
    inherits(BufferListStream, DuplexStream)
    Object.assign(BufferListStream.prototype, BufferList.prototype)
    BufferListStream.prototype._new = function _new(callback) {
      return new BufferListStream(callback)
    }
    BufferListStream.prototype._write = function _write(buf, encoding, callback) {
      this._appendBuffer(buf)
      if (typeof callback === 'function') {
        callback()
      }
    }
    BufferListStream.prototype._read = function _read(size) {
      if (!this.length) {
        return this.push(null)
      }
      size = Math.min(size, this.length)
      this.push(this.slice(0, size))
      this.consume(size)
    }
    BufferListStream.prototype.end = function end(chunk) {
      DuplexStream.prototype.end.call(this, chunk)
      if (this._callback) {
        this._callback(null, this.slice())
        this._callback = null
      }
    }
    BufferListStream.prototype._destroy = function _destroy(err, cb) {
      this._bufs.length = 0
      this.length = 0
      cb(err)
    }
    BufferListStream.prototype._isBufferList = function _isBufferList(b) {
      return (
        b instanceof BufferListStream || b instanceof BufferList || BufferListStream.isBufferList(b)
      )
    }
    BufferListStream.isBufferList = BufferList.isBufferList
    module2.exports = BufferListStream
    module2.exports.BufferListStream = BufferListStream
    module2.exports.BufferList = BufferList
  }
})

// ../../node_modules/.pnpm/ora@5.4.1/node_modules/ora/index.js
var require_ora = __commonJS({
  '../../node_modules/.pnpm/ora@5.4.1/node_modules/ora/index.js'(exports, module2) {
    'use strict'
    var readline = require('readline')
    var chalk = require_source()
    var cliCursor = require_cli_cursor()
    var cliSpinners = require_cli_spinners()
    var logSymbols = require_log_symbols()
    var stripAnsi = require_strip_ansi()
    var wcwidth = require_wcwidth()
    var isInteractive = require_is_interactive()
    var isUnicodeSupported = require_is_unicode_supported()
    var { BufferListStream } = require_bl()
    var TEXT = Symbol('text')
    var PREFIX_TEXT = Symbol('prefixText')
    var ASCII_ETX_CODE = 3
    var StdinDiscarder = class {
      constructor() {
        this.requests = 0
        this.mutedStream = new BufferListStream()
        this.mutedStream.pipe(process.stdout)
        const self2 = this
        this.ourEmit = function (event, data, ...args) {
          const { stdin } = process
          if (self2.requests > 0 || stdin.emit === self2.ourEmit) {
            if (event === 'keypress') {
              return
            }
            if (event === 'data' && data.includes(ASCII_ETX_CODE)) {
              process.emit('SIGINT')
            }
            Reflect.apply(self2.oldEmit, this, [event, data, ...args])
          } else {
            Reflect.apply(process.stdin.emit, this, [event, data, ...args])
          }
        }
      }
      start() {
        this.requests++
        if (this.requests === 1) {
          this.realStart()
        }
      }
      stop() {
        if (this.requests <= 0) {
          throw new Error('`stop` called more times than `start`')
        }
        this.requests--
        if (this.requests === 0) {
          this.realStop()
        }
      }
      realStart() {
        if (process.platform === 'win32') {
          return
        }
        this.rl = readline.createInterface({
          input: process.stdin,
          output: this.mutedStream
        })
        this.rl.on('SIGINT', () => {
          if (process.listenerCount('SIGINT') === 0) {
            process.emit('SIGINT')
          } else {
            this.rl.close()
            process.kill(process.pid, 'SIGINT')
          }
        })
      }
      realStop() {
        if (process.platform === 'win32') {
          return
        }
        this.rl.close()
        this.rl = void 0
      }
    }
    var stdinDiscarder
    var Ora = class {
      constructor(options2) {
        if (!stdinDiscarder) {
          stdinDiscarder = new StdinDiscarder()
        }
        if (typeof options2 === 'string') {
          options2 = {
            text: options2
          }
        }
        this.options = {
          text: '',
          color: 'cyan',
          stream: process.stderr,
          discardStdin: true,
          ...options2
        }
        this.spinner = this.options.spinner
        this.color = this.options.color
        this.hideCursor = this.options.hideCursor !== false
        this.interval = this.options.interval || this.spinner.interval || 100
        this.stream = this.options.stream
        this.id = void 0
        this.isEnabled =
          typeof this.options.isEnabled === 'boolean'
            ? this.options.isEnabled
            : isInteractive({ stream: this.stream })
        this.isSilent = typeof this.options.isSilent === 'boolean' ? this.options.isSilent : false
        this.text = this.options.text
        this.prefixText = this.options.prefixText
        this.linesToClear = 0
        this.indent = this.options.indent
        this.discardStdin = this.options.discardStdin
        this.isDiscardingStdin = false
      }
      get indent() {
        return this._indent
      }
      set indent(indent = 0) {
        if (!(indent >= 0 && Number.isInteger(indent))) {
          throw new Error('The `indent` option must be an integer from 0 and up')
        }
        this._indent = indent
      }
      _updateInterval(interval) {
        if (interval !== void 0) {
          this.interval = interval
        }
      }
      get spinner() {
        return this._spinner
      }
      set spinner(spinner) {
        this.frameIndex = 0
        if (typeof spinner === 'object') {
          if (spinner.frames === void 0) {
            throw new Error('The given spinner must have a `frames` property')
          }
          this._spinner = spinner
        } else if (!isUnicodeSupported()) {
          this._spinner = cliSpinners.line
        } else if (spinner === void 0) {
          this._spinner = cliSpinners.dots
        } else if (spinner !== 'default' && cliSpinners[spinner]) {
          this._spinner = cliSpinners[spinner]
        } else {
          throw new Error(
            `There is no built-in spinner named '${spinner}'. See https://github.com/sindresorhus/cli-spinners/blob/main/spinners.json for a full list.`
          )
        }
        this._updateInterval(this._spinner.interval)
      }
      get text() {
        return this[TEXT]
      }
      set text(value) {
        this[TEXT] = value
        this.updateLineCount()
      }
      get prefixText() {
        return this[PREFIX_TEXT]
      }
      set prefixText(value) {
        this[PREFIX_TEXT] = value
        this.updateLineCount()
      }
      get isSpinning() {
        return this.id !== void 0
      }
      getFullPrefixText(prefixText = this[PREFIX_TEXT], postfix = ' ') {
        if (typeof prefixText === 'string') {
          return prefixText + postfix
        }
        if (typeof prefixText === 'function') {
          return prefixText() + postfix
        }
        return ''
      }
      updateLineCount() {
        const columns = this.stream.columns || 80
        const fullPrefixText = this.getFullPrefixText(this.prefixText, '-')
        this.lineCount = 0
        for (const line of stripAnsi(fullPrefixText + '--' + this[TEXT]).split('\n')) {
          this.lineCount += Math.max(1, Math.ceil(wcwidth(line) / columns))
        }
      }
      get isEnabled() {
        return this._isEnabled && !this.isSilent
      }
      set isEnabled(value) {
        if (typeof value !== 'boolean') {
          throw new TypeError('The `isEnabled` option must be a boolean')
        }
        this._isEnabled = value
      }
      get isSilent() {
        return this._isSilent
      }
      set isSilent(value) {
        if (typeof value !== 'boolean') {
          throw new TypeError('The `isSilent` option must be a boolean')
        }
        this._isSilent = value
      }
      frame() {
        const { frames } = this.spinner
        let frame = frames[this.frameIndex]
        if (this.color) {
          frame = chalk[this.color](frame)
        }
        this.frameIndex = ++this.frameIndex % frames.length
        const fullPrefixText =
          typeof this.prefixText === 'string' && this.prefixText !== '' ? this.prefixText + ' ' : ''
        const fullText = typeof this.text === 'string' ? ' ' + this.text : ''
        return fullPrefixText + frame + fullText
      }
      clear() {
        if (!this.isEnabled || !this.stream.isTTY) {
          return this
        }
        for (let i = 0; i < this.linesToClear; i++) {
          if (i > 0) {
            this.stream.moveCursor(0, -1)
          }
          this.stream.clearLine()
          this.stream.cursorTo(this.indent)
        }
        this.linesToClear = 0
        return this
      }
      render() {
        if (this.isSilent) {
          return this
        }
        this.clear()
        this.stream.write(this.frame())
        this.linesToClear = this.lineCount
        return this
      }
      start(text) {
        if (text) {
          this.text = text
        }
        if (this.isSilent) {
          return this
        }
        if (!this.isEnabled) {
          if (this.text) {
            this.stream.write(`- ${this.text}
`)
          }
          return this
        }
        if (this.isSpinning) {
          return this
        }
        if (this.hideCursor) {
          cliCursor.hide(this.stream)
        }
        if (this.discardStdin && process.stdin.isTTY) {
          this.isDiscardingStdin = true
          stdinDiscarder.start()
        }
        this.render()
        this.id = setInterval(this.render.bind(this), this.interval)
        return this
      }
      stop() {
        if (!this.isEnabled) {
          return this
        }
        clearInterval(this.id)
        this.id = void 0
        this.frameIndex = 0
        this.clear()
        if (this.hideCursor) {
          cliCursor.show(this.stream)
        }
        if (this.discardStdin && process.stdin.isTTY && this.isDiscardingStdin) {
          stdinDiscarder.stop()
          this.isDiscardingStdin = false
        }
        return this
      }
      succeed(text) {
        return this.stopAndPersist({ symbol: logSymbols.success, text })
      }
      fail(text) {
        return this.stopAndPersist({ symbol: logSymbols.error, text })
      }
      warn(text) {
        return this.stopAndPersist({ symbol: logSymbols.warning, text })
      }
      info(text) {
        return this.stopAndPersist({ symbol: logSymbols.info, text })
      }
      stopAndPersist(options2 = {}) {
        if (this.isSilent) {
          return this
        }
        const prefixText = options2.prefixText || this.prefixText
        const text = options2.text || this.text
        const fullText = typeof text === 'string' ? ' ' + text : ''
        this.stop()
        this.stream.write(`${this.getFullPrefixText(prefixText, ' ')}${
          options2.symbol || ' '
        }${fullText}
`)
        return this
      }
    }
    var oraFactory = function (options2) {
      return new Ora(options2)
    }
    module2.exports = oraFactory
    module2.exports.promise = (action, options2) => {
      if (typeof action.then !== 'function') {
        throw new TypeError('Parameter `action` must be a Promise')
      }
      const spinner = new Ora(options2)
      spinner.start()
      ;(async () => {
        try {
          await action
          spinner.succeed()
        } catch {
          spinner.fail()
        }
      })()
      return spinner
    }
  }
})

// index.js
var import_fs2 = __toESM(require('fs'), 1)
var import_path2 = __toESM(require('path'), 1)

var import_minimist = __toESM(require_minimist(), 1)
var import_prompts = __toESM(require_prompts3(), 1)

// ../../node_modules/.pnpm/kolorist@1.5.1/node_modules/kolorist/dist/esm/index.mjs
var enabled = true
var globalVar =
  typeof self !== 'undefined'
    ? self
    : typeof window !== 'undefined'
    ? window
    : typeof global !== 'undefined'
    ? global
    : {}
var supportLevel = 0
if (globalVar.process && globalVar.process.env && globalVar.process.stdout) {
  const { FORCE_COLOR, NODE_DISABLE_COLORS, TERM } = globalVar.process.env
  if (NODE_DISABLE_COLORS || FORCE_COLOR === '0') {
    enabled = false
  } else if (FORCE_COLOR === '1') {
    enabled = true
  } else if (TERM === 'dumb') {
    enabled = false
  } else if (
    'CI' in globalVar.process.env &&
    ['TRAVIS', 'CIRCLECI', 'APPVEYOR', 'GITLAB_CI', 'GITHUB_ACTIONS', 'BUILDKITE', 'DRONE'].some(
      (vendor) => vendor in globalVar.process.env
    )
  ) {
    enabled = true
  } else {
    enabled = process.stdout.isTTY
  }
  if (enabled) {
    supportLevel = TERM && TERM.endsWith('-256color') ? 2 : 1
  }
}
var options = {
  enabled,
  supportLevel
}
function kolorist(start, end, level = 1) {
  const open = `\x1B[${start}m`
  const close = `\x1B[${end}m`
  const regex = new RegExp(`\\x1b\\[${end}m`, 'g')
  return (str) => {
    return options.enabled && options.supportLevel >= level
      ? open + ('' + str).replace(regex, open) + close
      : '' + str
  }
}
var reset = kolorist(0, 0)
var bold = kolorist(1, 22)
var dim = kolorist(2, 22)
var italic = kolorist(3, 23)
var underline = kolorist(4, 24)
var inverse = kolorist(7, 27)
var hidden = kolorist(8, 28)
var strikethrough = kolorist(9, 29)
var black = kolorist(30, 39)
var red = kolorist(31, 39)
var green = kolorist(32, 39)
var yellow = kolorist(33, 39)
var blue = kolorist(34, 39)
var magenta = kolorist(35, 39)
var cyan = kolorist(36, 39)
var white = kolorist(97, 39)
var gray = kolorist(90, 39)
var lightGray = kolorist(37, 39)
var lightRed = kolorist(91, 39)
var lightGreen = kolorist(92, 39)
var lightYellow = kolorist(93, 39)
var lightBlue = kolorist(94, 39)
var lightMagenta = kolorist(95, 39)
var lightCyan = kolorist(96, 39)
var bgBlack = kolorist(40, 49)
var bgRed = kolorist(41, 49)
var bgGreen = kolorist(42, 49)
var bgYellow = kolorist(43, 49)
var bgBlue = kolorist(44, 49)
var bgMagenta = kolorist(45, 49)
var bgCyan = kolorist(46, 49)
var bgWhite = kolorist(107, 49)
var bgGray = kolorist(100, 49)
var bgLightRed = kolorist(101, 49)
var bgLightGreen = kolorist(102, 49)
var bgLightYellow = kolorist(103, 49)
var bgLightBlue = kolorist(104, 49)
var bgLightMagenta = kolorist(105, 49)
var bgLightCyan = kolorist(106, 49)
var bgLightGray = kolorist(47, 49)

// utils/directoryTraverse.js
var import_fs = __toESM(require('fs'), 1)
var import_path = __toESM(require('path'), 1)

function postOrderDirectoryTraverse(dir, dirCallback, fileCallback) {
  for (const filename of import_fs.default.readdirSync(dir)) {
    const fullpath = import_path.default.resolve(dir, filename)
    if (import_fs.default.lstatSync(fullpath).isDirectory()) {
      postOrderDirectoryTraverse(fullpath, dirCallback, fileCallback)
      dirCallback(fullpath)
      continue
    }
    fileCallback(fullpath)
  }
}

// utils/getCommand.js
function getCommand(packageManager, scriptName) {
  if (scriptName === 'install') {
    return packageManager === 'yarn' ? 'yarn' : `${packageManager} install`
  }
  return packageManager === 'npm' ? `npm run ${scriptName}` : `${packageManager} ${scriptName}`
}

// index.js
var import_promise = __toESM(require_promise(), 1)
var import_ora = __toESM(require_ora(), 1)
async function loading(fn, message, ...args) {
  const spinner = (0, import_ora.default)(message)
  spinner.start()
  try {
    const result = await fn(...args)
    spinner.succeed()
    return result
  } catch (error) {
    console.log(error)
    spinner.fail('Request failed, refetch...')
  }
}
function changePackageInfo(root, packageName) {
  const pkgJSONPath = import_path2.default.join(root, 'package.json')
  const pkg = JSON.parse(import_fs2.default.readFileSync(pkgJSONPath))
  pkg.name = packageName
  pkg.version = '0.0.0'
  delete pkg.author
  import_fs2.default.writeFileSync(pkgJSONPath, JSON.stringify(pkg, null, 2) + '\n')
}
function removeDir(root, dir) {
  const deleteFolderRecursive = function (path3) {
    if (import_fs2.default.existsSync(path3)) {
      import_fs2.default.readdirSync(path3).forEach(function (file) {
        let curPath = path3 + '/' + file
        if (import_fs2.default.lstatSync(curPath).isDirectory()) {
          deleteFolderRecursive(curPath)
        } else {
          import_fs2.default.unlinkSync(curPath)
        }
      })
      import_fs2.default.rmdirSync(path3)
    }
  }
  deleteFolderRecursive(import_path2.default.join(root, dir))
}
function isValidPackageName(projectName) {
  return /^(?:@[a-z0-9-*~][a-z0-9-*._~]*\/)?[a-z0-9-~][a-z0-9-._~]*$/.test(projectName)
}
function toValidPackageName(projectName) {
  return String(projectName)
    .trim()
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/^[._]/, '')
    .replace(/[^a-z0-9-~]+/g, '-')
}
function canSafelyOverwrite(dir) {
  return !import_fs2.default.existsSync(dir) || import_fs2.default.readdirSync(dir).length === 0
}
function emptyDir(dir) {
  postOrderDirectoryTraverse(
    dir,
    (dir2) => import_fs2.default.rmdirSync(dir2),
    (file) => import_fs2.default.unlinkSync(file)
  )
}
async function init() {
  const downloadUrl = 'https://gitee.com/maleweb/fast-vue3.git'
  const cwd = process.cwd()
  const argv = (0, import_minimist.default)(process.argv.slice(2))
  let targetDir = argv._[0]
  const defaultProjectName = !targetDir ? 'fast-vue3-demo' : targetDir
  const forceOverwrite = argv.force
  let result = {}
  try {
    result = await (0, import_prompts.default)(
      [
        {
          name: 'template',
          type: 'select',
          message: 'Choice a Template:',
          choices: [
            {
              title: 'template-pc',
              description: 'This will generate template for web scene',
              value: 'web'
            },
            {
              title: 'template-mobile',
              description: 'This will generate template for mobile scene',
              value: 'mobile'
            }
          ],
          initial: 0
        },
        {
          name: 'projectName',
          type: targetDir ? null : 'text',
          message: 'Project name:',
          initial: defaultProjectName,
          onState: (state) => (targetDir = String(state.value).trim() || defaultProjectName)
        },
        {
          name: 'shouldOverwrite',
          type: () => (canSafelyOverwrite(String(targetDir)) || forceOverwrite ? null : 'confirm'),
          message: () => {
            const dirForPrompt =
              targetDir === '.' ? 'Current directory' : `Target directory "${targetDir}"`
            return `${dirForPrompt} is not empty. Remove existing files and continue?`
          }
        },
        {
          name: 'overwriteChecker',
          type: (prev, values = {}) => {
            if (values.shouldOverwrite === false) {
              throw new Error(red('\u2716') + ' Operation cancelled')
            }
            return null
          }
        },
        {
          name: 'packageName',
          type: () => (isValidPackageName(targetDir) ? null : 'text'),
          message: 'Package name:',
          initial: () => toValidPackageName(targetDir),
          validate: (dir) => isValidPackageName(dir) || 'Invalid package.json name'
        }
      ],
      {
        onCancel: () => {
          throw new Error(red('\u2716') + ' Operation cancelled')
        }
      }
    )
  } catch (cancelled) {
    console.log(cancelled.message)
    process.exit(1)
  }
  const { packageName = toValidPackageName(defaultProjectName), shouldOverwrite, template } = result
  const root = import_path2.default.join(cwd, String(targetDir))
  if (shouldOverwrite) {
    emptyDir(root)
  }
  const templates = {
    web: 'main',
    mobile: 'mobile-template'
  }
  console.log(`
Scaffolding project in ${root}...`)
  await loading(import_promise.default, 'waiting download template', downloadUrl, root, {
    checkout: templates[template]
  })
  removeDir(root, 'packages')
  removeDir(root, '.git')
  changePackageInfo(root, packageName)
  const packageManager = /pnpm/.test(process.env.npm_execpath)
    ? 'pnpm'
    : /yarn/.test(process.env.npm_execpath)
    ? 'yarn'
    : 'npm'
  console.log(`
Done. Now run:
`)
  if (root !== cwd) {
    console.log(`   ${bold(green(`cd ${import_path2.default.relative(cwd, root)}`))}`)
  }
  console.log(`  ${bold(green(getCommand(packageManager, 'install')))}`)
  console.log(`  ${bold(green(getCommand(packageManager, 'dev')))}`)
  console.log()
}
init().catch((e) => {
  console.error(e)
})
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */
