{"name": "create-fast-vue3", "type": "module", "version": "0.0.13", "description": "a easy way to create vue3 project based of fast-vue3 template", "bin": {"create-fast-vue3": "outfile.cjs"}, "files": ["outfile.cjs"], "scripts": {"format": "prettier --write .", "build": "esbuild --bundle index.js --format=cjs --platform=node --outfile=outfile.cjs"}, "keywords": ["vue3", "vite", "fast-vue3"], "homepage": "https://github.com/study-vue3/fast-vue3/tree/main/packages/create-fast-vue3#readme", "repository": {"type": "git", "url": "git+https://github.com/study-vue3/fast-vue3.git", "directory": "packages/create-fast-vue3"}, "author": "liulei", "license": "MIT", "devDependencies": {"esbuild": "^0.14.14", "git-clone": "^0.2.0", "husky": "^7.0.4", "kolorist": "^1.5.1", "lint-staged": "^12.3.2", "minimist": "^1.2.5", "ora": "^5.0", "prettier": "^2.5.1", "prompts": "^2.4.2"}, "lint-staged": {"*.{js,json}": ["prettier --write"]}}