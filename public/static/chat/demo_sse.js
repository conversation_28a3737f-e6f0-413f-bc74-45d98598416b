
// SessionId，保持上下文使用，假如页面存在重置等动作，则重新生成新的sessionId
const SessionId = uuid();

// 渲染对话页面
window.bot = new window.ChatSDK({
  root: document.querySelector('#chatbot'),
  config: {
    navbar: {
      title: '心理自助',
    },
    robot: {
      avatar: './avatar_ai.png',
    },
    user: {
      avatar: './avatar_user.png',
    },
    messages: [
      {
        type: 'text',
        content: {
          text: '请问有什么可以帮您？',
        },
      },
    ],
  },
  requests: {
    send(msg) {
      send(msg.content.text);
    },
  },
});



// 发送消息
async function send(text) {
   window.ChatSSE.fetchBeebotSSE({
    url: `https://music.zhisongkeji.com/zhisong-music/sse/chat?sessionId=${SessionId}&query=${text}`,
    json: {
    },
    onJsonText(data) {
      // 接收到消息的回调，一次请求，会触发多次onJsonText调用，假如不使用updateOrAppendMessage，则需要自己判断新增消息还是更新消息
      console.log('receiver:', data);
      // 响应消息解析和渲染
      window.bot.updateOrAppendMessage(window.ChatSDK.isvParser({ data }));
    },
    onerror(err) {
      // 接收消息出错时处理
      console.log('error', err);
    },
  });
}

// 进行聊天窗的渲染
window.bot.run();

function uuid() {
  const temp_url = URL.createObjectURL(new Blob());
  const uuid = temp_url.toString();
  URL.revokeObjectURL(temp_url);
  return uuid.substr(uuid.lastIndexOf('/') + 1);
}
