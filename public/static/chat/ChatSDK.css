.ChatMessage {
  display: -ms-flexbox;
  display: flex;
  flex: 1 1;
  flex-direction: inherit;
  flex-wrap: wrap;
  min-width: 0
}

.ChatMessage .mix-content,
.ChatMessage>:first-child {
  box-sizing: border-box;
  max-width: 90%
}

.ChatSDK-is-mobile .ChatMessage .mix-content,
.ChatSDK-is-mobile .ChatMessage>:first-child {
  max-width: calc(100% - 50px)
}

.ChatMessage .mix-content.ScrollView--fullWidth,
.ChatMessage .mix-content.SystemMessage,
.ChatMessage .mix-content.mix-message,
.ChatMessage>:first-child.ScrollView--fullWidth,
.ChatMessage>:first-child.SystemMessage,
.ChatMessage>:first-child.mix-message {
  max-width: 100%
}

.ChatMessage.ChatFullWidth :first-child,
.ChatSDK-is-mobile .ChatMessage.ChatFullWidth :first-child {
  max-width: none
}

.NameCard {
  margin: 20px auto 0;
  overflow: visible;
  overflow: initial;
  padding: 24px 8px 6px;
  position: relative;
  text-align: center;
  width: 210px
}

.NameCard--wide {
  width: 320px
}

.NameCard-avatar {
  background-color: #fff;
  left: 50%;
  overflow: hidden;
  position: absolute;
  top: -18px;
  transform: translateX(-50%)
}

.NameCard-avatar img {
  object-fit: contain
}

.NameCard-label {
  color: var(--gray-3)
}

.NameCard-channelList {
  display: -ms-flexbox;
  display: flex;
  margin-top: 10px
}

.NameCard-channel {
  flex: 1 1;
  margin-left: 8px;
  min-width: 0;
  text-align: center
}

.NameCard-channel:first-child {
  margin: 0
}

.NameCard-channelImg {
  background-position: bottom;
  background-repeat: no-repeat;
  background-size: contain;
  display: inline-block;
  height: 20px;
  width: 20px
}

.NameCard-channelName {
  color: var(--gray-1);
  font-size: 12px
}

.NameCard-channelDesc {
  color: var(--gray-3);
  font-size: 10px
}

.Promotion {
  padding-bottom: 5px
}

.RecommendCardWrap {
  position: relative;
  width: 310px
}

.RecommendCardWrap--hasToggle {
  margin-bottom: 18px
}

.RecommendCard {
  display: -ms-flexbox;
  display: flex
}

.RecommendCard .List {
  flex: 1 1;
  min-width: 0
}

.RecommendCard .ListItem {
  margin-left: 10px;
  padding: 11px 10px 11px 0
}

.RecommendCard .ListItem:hover {
  background: #fff
}

.RecommendCard .ListItem[data-hot]:after {
  background: var(--brand-2);
  border-radius: 20px 20px 20px 2px;
  color: #fff;
  content: "hot";
  display: inline-block;
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  margin-left: 2px;
  text-align: center;
  width: 22px
}

.RecommendCard .ListItem-content {
  flex: none;
  max-width: calc(100% - 24px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.RecommendCard--fold {
  height: 175px
}

.RecommendCard-cover {
  background-position: top;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 12px 0 0 12px;
  width: 80px
}

.RecommendCard-toggleBtn {
  background: #fff;
  border: 0;
  border-radius: 0 0 8px 8px;
  bottom: -15px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, .16);
  color: #dddedf;
  height: 15px;
  left: 50%;
  margin: 0 0 0 -30px;
  padding: 0;
  position: absolute;
  text-align: center;
  width: 60px
}

.RecommendCard--fold+.RecommendCard-toggleBtn .Icon {
  transform: rotate(180deg)
}

.SkillCard .CardTitle-subtitle,
.SkillCard .CardTitle-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.SkillCard-title {
  padding: 8px;
  text-align: center
}

.Feedback-inner {
  padding: 10px 15px
}

.Feedback .InputWrapper {
  margin-top: 10px
}

.Feedback-card {
  margin-top: 12px
}

.Feedback-title {
  margin-bottom: 10px
}

.Feedback--hidden {
  display: none
}

.Feedback-radio-group {
  margin-top: 0
}

.mix-message {
  width: 100%
}

.mix-message .mix-message-flex {
  display: -ms-flexbox;
  display: flex
}

.mix-message .mix-content {
  border-radius: 1.25rem
}

.mix-message .mix-content [data-code=image] {
  font-size: 0
}

.RateActions {
  margin: 0 8px;
  width: 32px
}

.RateActions .RateBtn {
  background: #fff;
  border-radius: 50%;
  font-size: 20px;
  padding: 6px
}

.RateActions .RateBtn+.RateBtn {
  margin-top: 9px
}

.RateActions .RateBtn[data-type=good].active,
.RateActions .RateBtn[data-type=good]:hover {
  color: var(--brand-1)
}

.RateActions .RateBtn[data-type=bad].active,
.RateActions .RateBtn[data-type=bad]:hover {
  color: red
}

.MessageTip {
  color: #aaa;
  font-size: 14px;
  line-height: 12px;
  margin-left: 6px;
  margin-top: 4px;
}

.tongyi-ui-markdown {
  font-size: 21px !important;
}

.RelatedQuestionList {
  color: #aaa;
  margin-top: 8px
}

.RelatedQuestionList .RelatedQuestionList-title {
  margin-left: 4px
}

.RelatedQuestionList .RelatedQuestionList-list {
  margin-top: 10px
}

.RelatedQuestionList .RelatedQuestionList-list:first-child {
  margin-top: 8px
}

.RelatedQuestionList .RelatedQuestionList-content {
  background: transparent;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  color: grey;
  cursor: pointer;
  display: inline-block;
  line-height: 22px;
  padding: 0 8px
}

.RelatedQuestionList .RelatedQuestionList-content:hover {
  background: #fff;
  border-color: #fff;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, .08)
}

.Brand {
  margin-right: 25px
}

.Brand-logo {
  display: block
}

.Brand-logo img {
  height: 33px
}

.Brand-name {
  font-size: 16px
}

.Brand-logo+.Brand-name {
  border-left: 1px solid #e8e9ec;
  margin-left: 8px;
  padding-left: 8px
}

@media (max-width:400px) {
  .Brand-name {
    display: none
  }
}

.ChatSDKStyle {
  /*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
}

.ChatSDKStyle *,
.ChatSDKStyle :after,
.ChatSDKStyle :before {
  box-sizing: border-box
}

.ChatSDKStyle hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible
}

.ChatSDKStyle h1,
.ChatSDKStyle h2,
.ChatSDKStyle h3,
.ChatSDKStyle h4,
.ChatSDKStyle h5,
.ChatSDKStyle h6 {
  font-weight: 400;
  margin: 0
}

.ChatSDKStyle p {
  margin: 0
}

.ChatSDKStyle ol,
.ChatSDKStyle ul {
  list-style-type: none;
  margin: 0;
  padding: 0
}

.ChatSDKStyle a {
  background-color: transparent;
  color: #39f;
  color: var(--blue);
  text-decoration: none
}

.ChatSDKStyle b,
.ChatSDKStyle strong {
  font-weight: bolder
}

.ChatSDKStyle small {
  font-size: 80%
}

.ChatSDKStyle img {
  border-style: none;
  vertical-align: middle
}

.ChatSDKStyle svg {
  overflow: hidden;
  vertical-align: middle
}

.ChatSDKStyle button {
  border-radius: 0
}

.ChatSDKStyle button,
.ChatSDKStyle input,
.ChatSDKStyle optgroup,
.ChatSDKStyle select,
.ChatSDKStyle textarea {
  font-family: inherit;
  font-size: 20px;
  line-height: 1.15;
  margin: 0
}

.ChatSDKStyle button,
.ChatSDKStyle input {
  overflow: visible
}

.ChatSDKStyle button,
.ChatSDKStyle select {
  text-transform: none
}

.ChatSDKStyle [type=button],
.ChatSDKStyle [type=reset],
.ChatSDKStyle [type=submit],
.ChatSDKStyle button {
  -webkit-appearance: button
}

.ChatSDKStyle [type=button]::-moz-focus-inner,
.ChatSDKStyle [type=reset]::-moz-focus-inner,
.ChatSDKStyle [type=submit]::-moz-focus-inner,
.ChatSDKStyle button::-moz-focus-inner {
  border-style: none;
  padding: 0
}

.ChatSDKStyle textarea {
  overflow: auto;
  resize: none
}

.ChatSDKStyle [type=checkbox],
.ChatSDKStyle [type=radio] {
  box-sizing: border-box;
  padding: 0
}

.ChatSDKStyle [type=number]::-webkit-inner-spin-button,
.ChatSDKStyle [type=number]::-webkit-outer-spin-button {
  height: auto
}

.ChatSDKStyle [type=search] {
  -webkit-appearance: textfield
}

.ChatSDKStyle [type=search]::-webkit-search-decoration {
  -webkit-appearance: none
}

.ChatSDKStyle ::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit
}

.ChatSDKStyle details {
  display: block
}

.ChatSDKStyle summary {
  display: list-item
}

.ChatSDKStyle template {
  display: none
}

.ChatSDKStyle [hidden] {
  display: none !important
}

.ChatSDKStyle * {
  outline: none
}

.S--wide {
  align-items: center;
  background-size: cover;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  justify-content: center;
  width: 100%
}

.ScrollView--hasControls .ScrollView-scroller {
  flex: 1 1 auto
}

.Checkbox--checked,
.Radio--checked {
  background: #ffe48c;
  background: var(--brand-3)
}

:root {
  --brand-1: #ffb300;
  --brand-2: #ffc233;
  --brand-3: #ffe48c;
  --black: #000;
  --white: #fff;
  --gray-1: #111;
  --gray-2: #666;
  --gray-3: #999;
  --gray-4: #ccc;
  --gray-5: #ddd;
  --gray-6: #eee;
  --gray-7: #f5f5f5;
  --gray-8: #f8f8f8;
  --light-1: #eee;
  --light-2: #f5f5f5;
  --highlight-1: #ffb300;
  --highlight-2: #ffc233;
  --link-color: var(--blue);
  --blue: #39f;
  --gray-dark: #333;
  --green: #62d957;
  --orange: #f70;
  --red: #f56262;
  --yellow: #ffc233;
  --yellow-light: #fff9db;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.75rem;
  --radius-lg: 1.25rem;
  --shadow-1: 0 3px 4px 0 rgba(0, 0, 0, .04);
  --shadow-2: 0 4px 8px 0 rgba(0, 0, 0, .08);
  --shadow-3: 0 6px 10px 0 rgba(0, 0, 0, .08);
  --safe-top: 0px;
  --safe-bottom: 0px;
  --gutter: 12px;
  --btn-primary-border-color: transparent;
  --btn-primary-bg: linear-gradient(90deg, var(--brand-2) 0%, var(--brand-1) 98%);
  --btn-primary-color: var(--white);
  --rate-width: 32Px
}

@supports (top:constant(safe-area-inset-top)) {
  :root {
    --safe-top: constant(safe-area-inset-top);
    --safe-bottom: constant(safe-area-inset-bottom)
  }
}

@supports (top:env(safe-area-inset-top)) {
  :root {
    --safe-top: env(safe-area-inset-top);
    --safe-bottom: env(safe-area-inset-bottom)
  }
}

.S--invisible {
  clip: rect(0, 0, 0, 0);
  position: absolute
}

.pb-safe {
  padding-bottom: 0;
  padding-bottom: var(--safe-bottom)
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(100px)
  }

  to {
    opacity: 1;
    transform: translateX(0)
  }
}

.slide-in-right-item {
  animation: slideInRight .5s ease-in-out both
}

.slide-in-right-item:nth-child(2) {
  animation-delay: .1s
}

.slide-in-right-item:nth-child(3) {
  animation-delay: .2s
}

.slide-in-right-item:nth-child(4) {
  animation-delay: .3s
}

.slide-in-right-item:nth-child(5) {
  animation-delay: .4s
}

.slide-in-right-item:nth-child(6) {
  animation-delay: .5s
}

.slide-in-right-item:nth-child(7) {
  animation-delay: .6s
}

.slide-in-right-item:nth-child(8) {
  animation-delay: .7s
}

.slide-in-right-item:nth-child(9) {
  animation-delay: .8s
}

.slide-in-right-item:nth-child(10) {
  animation-delay: .9s
}

.slide-in-right-item:nth-child(11) {
  animation-delay: 1s
}

@keyframes fadeIn {
  0% {
    opacity: 0
  }

  to {
    opacity: 1
  }
}

.A-fadeIn {
  animation: fadeIn .6s
}

.Avatar {
  border-radius: 50%;
  display: inline-block;
  overflow: hidden
}

.Avatar img {
  display: block;
  height: 56px;
  object-fit: cover;
  width: 56px
}

.Avatar--sm img {
  height: 24px;
  width: 24px
}

.Avatar--lg img {
  height: 40px;
  width: 40px
}

.Avatar--square {
  border-radius: 4px
}

.Backdrop {
  background: rgba(0, 0, 0, .7);
  bottom: 0;
  height: 100vh;
  left: 0;
  opacity: 0;
  outline: 0;
  position: fixed;
  right: 0;
  transition: .3s;
  width: 100vw;
  z-index: 100
}

.Backdrop.active {
  opacity: 1
}

.Bubble {
  background: #fff;
  background: var(--white);
  border-radius: 1.25rem;
  display: inline-block;
  max-width: 680px;
  min-width: 1px
}

.Bubble.text,
.Bubble.typing {
  word-wrap: break-word;
  box-sizing: border-box;
  min-width: 40px;
  overflow-wrap: break-word;
  padding: 10px 16px;
  white-space: pre-wrap;
  font-size: 21px;
}

.Bubble.text p,
.Bubble.typing p {
  display: inline-block
}

.Bubble.image img {
  border-radius: inherit;
  display: block;
  height: auto;
  max-height: 200px;
  max-width: 200px
}

.Bubble p {
  margin: 0
}

button.Btn {
  -webkit-tap-highlight-color: transparent;
  align-items: center;
  -webkit-appearance: none;
  background: #fff;
  background: var(--white);
  border: 1px solid #ddd;
  border: 1px solid var(--gray-5);
  border-radius: 999px;
  color: #111;
  color: var(--gray-1);
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: inherit;
  font-size: .875rem;
  font-weight: 400;
  justify-content: center;
  line-height: 1.5;
  margin: 0;
  min-width: 80Px;
  outline: none;
  overflow: visible;
  padding: 5px 12px;
  text-align: center;
  text-transform: none;
  transition: .15s ease-in-out;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  white-space: nowrap
}

button.Btn:not(:disabled) {
  cursor: pointer
}

button.Btn:active {
  background: rgba(0, 0, 0, .08)
}

button.Btn:disabled {
  color: #ccc;
  color: var(--gray-4);
  opacity: .5;
  pointer-events: none
}

button.Btn--primary {
  background: linear-gradient(90deg, #ffc233, #ffb300 98%);
  background: var(--btn-primary-bg);
  background-origin: border-box;
  border-color: transparent;
  border-color: var(--btn-primary-border-color);
  color: #fff;
  color: var(--btn-primary-color);
  font-size: 20px;
}

button.Btn--primary:active {
  opacity: .8
}

button.Btn--primary:disabled {
  background: #ccc;
  background: var(--gray-4);
  border-color: transparent;
  color: #666;
  color: var(--gray-2)
}

button.Btn--outlinebutton.Btn--primary {
  border-color: #ffb300;
  border-color: var(--brand-1);
  color: #ffb300;
  color: var(--brand-1)
}

button.Btn--sm {
  font-size: .875rem;
  padding: 4px 12px
}

button.Btn--lg {
  font-size: 1rem;
  padding: 7px 12px
}

button.Btn--block {
  display: block;
  width: 100%
}

button.Btn-icon {
  align-self: center;
  display: -ms-inline-flexbox;
  display: inline-flex;
  flex-shrink: 0;
  margin-inline-end: .5rem
}

[data-old-ios] button.Btn:not(.Btn--block) {
  display: inline-block
}

@media (hover:hover) {
  button.Btn:hover:not(.Btn--primary):not(.Btn--outline):not(:disabled) {
    background: rgba(0, 0, 0, .04)
  }

  button.Btn--primary:not(.Btn--outline):not(:disabled):hover {
    background: linear-gradient(90deg, #ffc233, #ffb300 98%);
    background: var(--btn-primary-bg);
    opacity: .9
  }
}

button.Btn--text {
  border: 0;
  color: #39f;
  color: var(--link-color);
  font-size: inherit;
  padding: 0;
  vertical-align: baseline;
  vertical-align: initial
}

button.Btn--text,
button.Btn--text:active,
button.Btn--text:hover {
  background: transparent
}

.BackBottom {
  bottom: 68px;
  overflow: hidden;
  position: absolute;
  right: 0;
  z-index: 10
}

.BackBottom .Btn {
  background: hsla(0, 0%, 100%, .85);
  border-radius: 50px 0 0 50px;
  border-right: 0;
  color: #ffb300;
  color: var(--brand-1);
  font-size: .875rem;
  font-size: var(--font-size-sm)
}

.Divider {
  align-items: center;
  color: #999;
  color: var(--gray-3);
  display: -ms-flexbox;
  display: flex;
  font-size: .75rem;
  margin: 12px 0
}

.Divider:after,
.Divider:before {
  border-top: 1px solid #ddd;
  border-top: 1px solid var(--gray-5);
  content: "";
  display: block;
  flex: 1 1
}

.Divider--text-center:before,
.Divider--text-left:before,
.Divider--text-right:before {
  margin-right: 12px;
  margin-right: var(--gutter)
}

.Divider--text-center:after,
.Divider--text-left:after,
.Divider--text-right:after {
  margin-left: 12px;
  margin-left: var(--gutter)
}

.Divider--text-left:before,
.Divider--text-right:after {
  max-width: 10%
}

.Empty {
  padding: 30px;
  text-align: center
}

.Empty-img {
  height: 125px
}

.Empty-tip {
  color: #ccc;
  color: var(--gray-4);
  margin: 20px 0
}

.Flex {
  display: -ms-flexbox;
  display: flex
}

.Flex--inline {
  display: -ms-inline-flexbox;
  display: inline-flex
}

.Flex--center {
  align-items: center;
  justify-content: center
}

.Flex--d-r {
  flex-direction: row
}

.Flex--d-rr {
  flex-direction: row-reverse
}

.Flex--d-c {
  flex-direction: column
}

.Flex--d-cr {
  flex-direction: column-reverse
}

.Flex--w-n {
  flex-wrap: nowrap
}

.Flex--w-w {
  flex-wrap: wrap
}

.Flex--w-wr {
  flex-wrap: wrap-reverse
}

.Flex--jc-fs {
  justify-content: flex-start
}

.Flex--jc-fe {
  justify-content: flex-end
}

.Flex--jc-c {
  justify-content: center
}

.Flex--jc-sb {
  justify-content: space-between
}

.Flex--jc-sa {
  justify-content: space-around
}

.Flex--ai-fs {
  align-items: flex-start
}

.Flex--ai-fe {
  align-items: flex-end
}

.Flex--ai-c {
  align-items: center
}

.FlexItem {
  flex: 1 1;
  min-height: 0;
  min-width: 0
}

.HelpText {
  color: #ccc;
  color: var(--gray-4);
  font-size: .75rem
}

.Icon {
  stroke-width: 0;
  fill: currentColor;
  display: inline-block;
  height: 1em;
  transition: all .3s cubic-bezier(.18, .89, .32, 1.28);
  width: 1em
}

.is-spin {
  animation: spin 1s linear infinite
}

@keyframes spin {
  0% {
    transform: rotate(0deg)
  }

  to {
    transform: rotate(1turn)
  }
}

button.IconBtn {
  background: transparent;
  border: 0;
  border-radius: 3px;
  color: #666;
  color: var(--gray-2);
  font-size: 18px;
  min-width: 0;
  padding: 0
}

button.IconBtn.Btn--primary {
  color: #ffc233;
  color: var(--brand-2)
}

button.IconBtn:disabled {
  border-color: #eee;
  border-color: var(--gray-6);
  color: #eee;
  color: var(--gray-6)
}

button.IconBtn.Btn--lg {
  border-radius: 6px;
  font-size: 24px
}

button.IconBtn>.Icon {
  display: block
}

button.IconBtn>img {
  display: block;
  height: 1em;
  width: 1em
}

.Image {
  border-style: none;
  display: inline-block;
  overflow: hidden;
  position: relative
}

.Image--fluid {
  height: auto;
  max-width: 100%
}

.InfiniteScroll {
  -webkit-overflow-scrolling: touch;
  overflow-y: scroll
}

.InputWrapper {
  position: relative
}

.InputWrapper.has-counter {
  padding-bottom: 20px
}

.InputWrapper.has-counter+.HelpText {
  margin-top: -20px
}

.Input {
  -webkit-tap-highlight-color: transparent;
  -webkit-appearance: none;
  background: #fff;
  background: var(--white);
  border: 1px solid #eee;
  border: 1px solid var(--gray-6);
  border-radius: 12px;
  box-sizing: border-box;
  color: #111;
  color: var(--gray-1);
  display: block;
  font-family: inherit;
  font-size: .875rem;
  line-height: 1.5;
  margin: 0;
  min-height: 24px;
  padding: 5px 12px;
  resize: none;
  transition: .2s ease-in-out;
  width: 100%
}

.Input:focus {
  border-color: #ffb300;
  border-color: var(--brand-1);
  outline: none
}

.Input:focus:not([disabled]):not([readonly])~.Input-line:after,
.Input:focus:not([disabled]):not([readonly])~.Input-line:before {
  width: 50%
}

.Input:-ms-input-placeholder {
  color: #ccc;
  color: var(--gray-4)
}

.Input::placeholder {
  color: #ccc;
  color: var(--gray-4)
}

.Input--filled {
  background-color: #eee;
  background-color: var(--gray-6);
  border-color: transparent
}

.Input--flushed {
  background: none;
  border-radius: 0;
  border-width: 0 0 1px;
  padding: 2px 12px;
  padding: 2px var(--gutter)
}

.Input--flushed:focus {
  box-shadow: 0 1px 0 0 #ffb300;
  box-shadow: var(--brand-1) 0 1px 0 0
}

.Input-counter {
  color: #999;
  color: var(--gray-3);
  float: right;
  font-size: .75rem;
  margin-right: 12px;
  margin-right: var(--gutter);
  position: relative;
  z-index: 1
}

.Label {
  color: #666;
  color: var(--gray-2);
  display: block;
  font-size: .75rem
}

.List {
  background: #fff;
  background: var(--white)
}

.List--bordered {
  border: 1px solid #f5f5f5;
  border: 1px solid var(--gray-7);
  border-radius: 2px
}

.ListItem {
  align-items: center;
  border: 0;
  box-sizing: border-box;
  color: #111;
  color: var(--gray-1);
  display: -ms-flexbox;
  display: flex;
  font-size: 15px;
  line-height: 1.6;
  padding: 10px 12px;
  padding: 10px var(--gutter);
  text-decoration: none;
  transition: .3s
}

.ListItem:focus:not(:focus-visible) {
  outline: 0
}

.ListItem+.ListItem {
  border-top: 1px solid #f5f5f5;
  border-top: 1px solid var(--gray-7)
}

.ListItem .Icon {
  color: #999;
  color: var(--gray-3)
}

button.ListItem {
  appearance: none;
  background: transparent;
  text-align: left;
  width: 100%
}

a.ListItem:active,
button.ListItem:active {
  background: #eee;
  background: var(--gray-6)
}

@media (hover:hover) {

  a.ListItem:hover,
  button.ListItem:hover {
    background: rgba(0, 0, 0, .04);
    background-clip: padding-box;
    cursor: pointer
  }
}

.ListItem-content {
  flex: 1 1
}

.Loading {
  color: #666;
  color: var(--gray-2);
  padding: 12px
}

.Loading .Icon {
  font-size: 30px
}

.Loading-tip {
  font-size: .875rem;
  margin: 0 0 0 6px
}

.MediaObject {
  display: -ms-flexbox;
  display: flex
}

.MediaObject-pic {
  margin-right: 10px;
  width: 70px
}

.MediaObject-pic>img {
  display: block;
  height: 100%;
  width: 100%
}

.MediaObject-info {
  flex: 1 1
}

.MediaObject-title {
  font-size: .875rem;
  font-weight: 400;
  margin: 0 0 6px
}

.MediaObject-meta {
  color: #666;
  color: var(--gray-2);
  font-size: .75rem
}

.Message {
  position: relative
}

.Message+.Message {
  margin-top: 12px;
  margin-top: var(--gutter)
}

.Message.left .Message-content>.Avatar,
.Message.left .Message-main>.Avatar {
  margin-right: 4px
}

.Message.left .Bubble {
  margin-right: 0
}

.Message.right .Message-content,
.Message.right .Message-main {
  flex-direction: row-reverse
}

.Message.right .Message-content>.Avatar,
.Message.right .Message-main>.Avatar {
  margin-left: 4px
}

.Message.right .Message-author {
  text-align: right
}

.Message.right .Bubble {
  background: #ffe48c;
  background: var(--brand-3);
  border-radius: 12px;
  margin-left: 40Px;
  margin-left: calc(var(--rate-width) + 8Px)
}

.Message.pop {
  display: none
}

.Message-meta {
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
  text-align: center
}

.Message-content,
.Message-main {
  align-items: flex-start;
  display: -ms-flexbox;
  display: flex
}

.Message-inner {
  flex: 1 1;
  min-width: 0
}

.Message-author {
  color: #666;
  color: var(--gray-2);
  font-size: .75rem;
  font-size: var(--font-size-xs);
  line-height: 1.1;
  margin-bottom: 6px
}

.SystemMessage {
  color: #666;
  color: var(--gray-2);
  padding: 0 15Px;
  text-align: center;
  width: 100%
}

.SystemMessage a {
  margin-left: 5px
}

.SystemMessage-inner {
  background: #f8f8f8;
  background: var(--gray-8);
  border-radius: 6px;
  display: inline-block;
  font-size: 12Px;
  padding: 6Px 9Px;
  text-align: left
}

.ChatApp[data-elder-mode=true] .SystemMessage-inner {
  font-size: 14Px;
  padding: 7Px 14Px
}

.MessageStatus {
  align-self: center;
  font-size: 15Px;
  margin-right: 12px
}

.MessageStatus[data-status=loading] .Icon {
  color: #ccc;
  color: var(--gray-4)
}

.MessageStatus[data-status=fail] .IconBtn {
  color: #ff5959
}

.MessageStatus .Icon,
.MessageStatus .IconBtn {
  display: block
}

.Message[data-type=text] .MessageStatus {
  margin-right: -28Px;
  margin-right: calc(4Px - var(--rate-width))
}

.Message[data-type=order] .MessageStatus {
  margin-left: 21Px;
  margin-left: calc(var(--rate-width) - 11Px);
  margin-right: -36Px;
  margin-right: calc(-4Px - var(--rate-width))
}

.Modal,
.Popup {
  align-items: center;
  bottom: 0;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  justify-content: center;
  left: 0;
  outline: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 100
}

.Modal.active .Modal-dialog {
  opacity: 1;
  transform: none
}

.Modal-dialog {
  background: #fff;
  background: var(--white);
  border-radius: 12px;
  font-size: 15px;
  max-width: 90vw;
  opacity: 0;
  overflow: hidden;
  position: relative;
  transform: translateY(-50px);
  transition: transform .3s ease-out, opacity .15s linear;
  width: 320px;
  z-index: 100
}

.Modal-header {
  padding: 18px 20px 10px;
  position: relative
}

.Modal-body {
  padding: 18px 15px
}

.Modal-header+.Modal-body {
  padding-top: 0
}

.Modal-footer {
  display: -ms-flexbox;
  display: flex
}

.Modal-footer .Btn--outline {
  border-color: #eee;
  border-color: var(--gray-6);
  border-radius: 0;
  border-width: 1px 0 0;
  padding-bottom: 10px;
  padding-top: 10px
}

.Modal-footer .Btn--outline:not(.Btn--primary) {
  color: #666;
  color: var(--gray-2)
}

.Modal-footer--h[data-variant=round] {
  padding: 0 15px 18px
}

.Modal-footer--h[data-variant=round] .Btn+.Btn {
  margin-left: 12px
}

.Modal-footer--h[data-variant=outline] .Btn+.Btn {
  border-left-width: 1px
}

.Modal-footer--h .Btn {
  flex: 1 1
}

.Modal-footer--v {
  flex-direction: column
}

.Confirm .Modal-body {
  text-align: center
}

.Popup {
  align-items: flex-end
}

.Popup.active .Popup-dialog {
  opacity: 1;
  transform: translateZ(0)
}

.Popup-content {
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  height: 100%
}

.Popup-dialog {
  background: #fff;
  background: var(--white);
  border-radius: 27px 27px 0 0;
  opacity: 0;
  overflow: hidden;
  position: relative;
  transform: translate3d(0, 100%, 0);
  transition: .3s;
  width: 100%;
  z-index: 100
}

.Popup-dialog[data-bg-color=gray] {
  background: #f5f5f5;
  background: var(--gray-7)
}

.Popup-dialog[data-height="80"] {
  height: 80vh
}

.Popup-dialog[data-height="60"] {
  height: 60vh
}

.Popup-dialog[data-height="40"] {
  height: 40vh
}

.Popup-header {
  padding: 18px 40px 10px;
  position: relative
}

.Modal-title,
.Popup-title {
  color: #111;
  color: var(--gray-1);
  font-size: 1.125rem;
  font-weight: 500;
  margin: 0;
  text-align: center
}

.Popup[data-elder-mode=true] .Modal-title,
.Popup[data-elder-mode=true] .Popup-title {
  font-size: 23Px
}

.Popup[data-elder-mode=true] .Popup-close {
  font-size: 32Px
}

.Modal-close,
.Popup-close {
  color: #111;
  color: var(--gray-1);
  position: absolute;
  right: 12px
}

.Modal-close {
  top: 12px
}

.Popup-title {
  min-height: 25Px
}

.Popup-close {
  top: 18px
}

.Popup-body {
  flex: 1 1;
  min-height: 0
}

.Popup-body.overflow {
  -webkit-overflow-scrolling: touch;
  max-height: 70vh;
  overflow-y: auto
}

.Popup-footer {
  background: #fff;
  background: var(--white);
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  padding: 9px 12px;
  padding-bottom: calc(9px + var(--safe-bottom));
  position: relative
}

.Popup-footer .Btn+.Btn {
  margin-top: 9px
}

.S--modalOpen,
.S--modalOpen .MessageContainer>.PullToRefresh {
  overflow: hidden
}

.Popup {
  align-items: center
}

.Popup-dialog {
  border-radius: 12px;
  width: 480px
}

.Popup-footer--v {
  padding-bottom: 12px
}

.ChatSDK-is-mobile .Popup {
  align-items: flex-end
}

.ChatSDK-is-mobile .Popup-dialog {
  border-radius: 27px 27px 0 0;
  width: 100%
}

.ChatSDK-is-mobile .Popup-footer--v {
  padding-bottom: 0
}

.Navbar {
  /* background: #eee;
  background: var(--gray-6);
  border-bottom: 1px solid #ddd;
  border-bottom: 1px solid var(--gray-5);
  padding: 0 12px;
  padding: 0 var(--gutter);
  padding-top: 0;
  padding-top: var(--safe-top);
  position: relative;
  z-index: 10 */
}

.Navbar,
.Navbar-main {
  align-items: center;
  display: -ms-flexbox;
  display: flex
}

.Navbar-main {
  box-sizing: border-box;
  flex: 1 1;
  justify-content: center;
  min-height: 44Px;
  padding: 3Px 0
}

.Navbar-left,
.Navbar-right {
  display: -ms-flexbox;
  display: flex;
  width: 58Px
}

.Navbar-right {
  justify-content: flex-end
}

.Navbar-title {
  color: #111;
  color: var(--gray-1);
  font-size: 17Px;
  font-weight: 500;
  margin: 0
}

.Navbar-logo {
  height: 38Px;
  width: auto
}

.Navbar .IconBtn {
  color: #111;
  color: var(--gray-1);
  font-size: 24Px
}

.Navbar .IconBtn+.IconBtn {
  margin-left: 9Px
}

.Navbar--left .Navbar-left {
  width: 24Px
}

.Navbar--left .Navbar-main {
  justify-content: flex-start;
  min-height: 62Px
}

.Navbar--left .Navbar-logo,
.Navbar-logo+.Navbar-inner {
  margin-left: 9Px
}

.Navbar-desc {
  color: #666;
  color: var(--gray-2);
  font-size: 12Px
}

.ChatApp[data-elder-mode=true] .Navbar .IconBtn {
  font-size: 26Px
}

.ChatApp[data-elder-mode=true] .Navbar-logo {
  height: 43Px
}

.ChatApp[data-elder-mode=true] .Navbar-title {
  font-size: 18Px
}

.ChatApp[data-elder-mode=true] .Navbar-desc {
  font-size: 13Px
}

.Notice {
  align-items: center;
  background: #fff;
  background: var(--white);
  border-radius: 12px;
  display: -ms-flexbox;
  display: flex;
  padding: 12px
}

.Notice-icon {
  margin-right: 6px
}

.Notice-close {
  margin-left: 6px
}

.Notice .Icon {
  color: #ffb300;
  color: var(--brand-1);
  font-size: 16px
}

.Notice-content {
  color: #111;
  color: var(--gray-1);
  flex: 1 1;
  font-size: 13px;
  min-width: 0
}

.Popover {
  font-size: .875rem;
  left: 0;
  position: absolute;
  top: 0;
  transform: translateY(-10px);
  z-index: 1030
}

.Popover-body {
  background: #fff;
  background: var(--white);
  border-radius: 6px;
  box-shadow: 0 6px 10px 0 rgba(0, 0, 0, .08);
  box-shadow: var(--shadow-3)
}

.Popover-arrow {
  fill: #fff;
  fill: var(--white);
  display: block;
  height: 5px;
  margin-left: 10px;
  width: 9px
}

.Price {
  display: inline;
  font-size: .875rem;
  font-weight: 500
}

.Price--original {
  color: #999;
  color: var(--gray-3);
  text-decoration: line-through
}

.Price-currency {
  font-size: .8em
}

.Progress {
  background-color: #ddd;
  background-color: var(--gray-5);
  border-radius: 100px;
  display: -ms-flexbox;
  display: flex;
  height: 2px;
  overflow: hidden
}

.Progress-bar {
  background-color: #39f;
  background-color: var(--blue);
  overflow: hidden;
  transition: width .6s ease
}

.Progress--success .Progress-bar {
  background-color: #62d957;
  background-color: var(--green)
}

.Progress--error .Progress-bar {
  background-color: #f56262;
  background-color: var(--red)
}

.PullToRefresh {
  -webkit-overflow-scrolling: touch;
  height: 100%;
  overflow-y: scroll
}

.PullToRefresh-fallback {
  padding-top: 12px;
  padding-top: var(--gutter);
  text-align: center
}

.PullToRefresh-loadMore {
  font-size: 14Px
}

.PullToRefresh-inner {
  min-height: 100%;
  overflow: hidden
}

.PullToRefresh-indicator {
  color: grey;
  height: 30px;
  line-height: 30px;
  margin-top: -30px;
  text-align: center
}

.PullToRefresh-spinner {
  color: #999;
  color: var(--gray-3);
  font-size: 27px
}

.PullToRefresh-transition {
  transition: transform .3s
}

.QuickReplies {
  padding: 9Px 12px;
  padding: 9Px var(--gutter);
  transition: opacity .3s
}

.QuickReplies[data-visible=false] {
  opacity: 0;
  visibility: hidden
}

.QuickReplies:not(.ScrollView--hasControls) {
  padding-left: 0;
  padding-right: 0
}

.QuickReplies:not(.ScrollView--hasControls) .ScrollView-inner {
  padding: 0 12px;
  padding: 0 var(--gutter)
}

.QuickReplies:not(.ScrollView--hasControls) .ScrollView-item:last-child {
  padding-right: 12px;
  padding-right: var(--gutter)
}

button.QuickReply {
  background: #fff;
  background: var(--white);
  border: 1px solid transparent;
  border-radius: 99PX;
  color: #111;
  color: var(--gray-1);
  cursor: pointer;
  display: block;
  font-size: 14Px;
  line-height: 20Px;
  margin: 0;
  padding: 4Px 12Px;
  position: relative;
  transition: .15s ease-in-out
}

button.QuickReply .QuickReply.new:after {
  background: #f56262;
  background: var(--red);
  border: 1px solid #fff;
  border-radius: 50%;
  content: "";
  height: 8px;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
  width: 8px
}

button.QuickReply .QuickReply.highlight {
  background: #fff;
  border-color: #ffd0bf;
  font-weight: 500
}

button.QuickReply .QuickReply-inner {
  align-items: center;
  display: -ms-flexbox;
  display: flex
}

button.QuickReply .QuickReply-img {
  max-height: 13Px
}

button.QuickReply .QuickReply-img,
button.QuickReply .QuickReply-inner>.Icon {
  margin-right: 3Px
}

button.QuickReply .Icon {
  color: #ffb300;
  color: var(--brand-1);
  font-size: 15Px
}

.ChatApp[data-elder-mode=true] .QuickReply {
  font-size: 17Px;
  line-height: 26Px
}

.ChatApp[data-elder-mode=true] .QuickReply .Icon {
  font-size: 17Px
}

.ChatApp[data-elder-mode=true] .QuickReply-img {
  max-height: 17Px
}

.Checkbox,
.Radio {
  -webkit-tap-highlight-color: transparent;
  background: #fff;
  background: var(--white);
  border: 1px solid #eee;
  border: 1px solid var(--gray-6);
  border-radius: 9px;
  color: #666;
  color: var(--gray-2);
  cursor: pointer;
  display: inline-block;
  font-size: .875rem;
  line-height: 20px;
  margin: 9px 12px 0 0;
  padding: 4px 12px;
  position: relative;
  text-align: center;
  transition: .15s ease-in-out
}

.RadioGroup {
  margin-top: -9px
}

.RadioGroup--block .Radio {
  display: block;
  margin-right: 9px
}

.CheckboxGroup--block .Checkbox {
  display: block;
  margin-right: 0
}

.Checkbox--disabled,
.Radio--disabled {
  cursor: auto;
  opacity: .5
}

.Checkbox--checked,
.Radio--checked {
  border-color: #ffb300;
  border-color: var(--brand-1);
  color: #ffb300;
  color: var(--brand-1)
}

.Checkbox-input,
.Radio-input {
  cursor: inherit;
  height: 100%;
  left: 0;
  margin: 0;
  opacity: 0;
  padding: 0;
  position: absolute;
  top: 0;
  width: 100%
}

.ChatApp[data-elder-mode=true] {
  --rate-width: 38Px
}

.RateActions {
  align-self: flex-end;
  margin: 0 8Px;
  position: relative;
  width: 32Px;
  width: var(--rate-width);
  z-index: 10
}

button.RateBtn {
  background: #fff;
  background: var(--white);
  border-radius: 50%;
  font-size: 20Px;
  font-size: calc(var(--rate-width) - 12Px);
  padding: 6Px
}

button.RateBtn+.RateBtn {
  margin-top: 8Px
}

button.RateBtn[data-type=up].active,
button.RateBtn[data-type=up]:hover {
  color: #ffb300;
  color: var(--brand-1)
}

button.RateBtn[data-type=down].active,
button.RateBtn[data-type=down]:hover {
  color: #39f;
  color: var(--blue)
}

.RichText {
  word-wrap: break-word;
  overflow-wrap: break-word
}

.ScrollView {
  overflow: hidden
}

.ScrollView-scroller {
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
  scrollbar-width: none
}

.ScrollView-scroller::-webkit-scrollbar {
  display: none
}

.ScrollView--fullWidth {
  margin: 0 -12px;
  margin: 0 calc(var(--gutter)*-1)
}

.ScrollView--fullWidth:not(.ScrollView--hasControls) .ScrollView-inner {
  padding: 0 12px;
  padding: 0 var(--gutter)
}

.ScrollView--x .ScrollView-scroller {
  display: -ms-flexbox;
  display: flex;
  margin-bottom: -18Px;
  overflow-x: scroll;
  overflow-y: hidden;
  padding-bottom: 18Px
}

.ScrollView--x .ScrollView-inner {
  display: -ms-flexbox;
  display: flex
}

.ScrollView--x .ScrollView-item {
  flex: 0 0 auto;
  margin-left: 6px
}

.ScrollView--x .ScrollView-item:first-child {
  margin-left: 0
}

.ScrollView--hasControls {
  align-items: center;
  display: -ms-flexbox;
  display: flex
}

.ScrollView--hasControls .ScrollView-scroller {
  flex: 1 1
}

.ScrollView-control {
  color: #999;
  color: var(--gray-3);
  font-size: 1rem;
  padding: 6px
}

.ScrollView-control:not(:disabled):hover {
  color: #ffb300;
  color: var(--brand-1)
}

.Search {
  align-items: center;
  background: #fff;
  background: var(--white);
  border-radius: 50px;
  display: -ms-flexbox;
  display: flex;
  padding: 3px 5px 3px 15px
}

.Search-clear,
.Search-icon {
  font-size: 1.125rem;
  font-size: var(--font-size-lg)
}

.Search-icon {
  color: #999;
  color: var(--gray-3)
}

.Search-input {
  border: 0;
  flex: 1 1;
  padding: 0 9px
}

.Search-input::-webkit-search-cancel-button {
  display: none
}

.Search-input:focus+.Search-clear,
.Search-input:focus~.Btn--primary {
  opacity: 1
}

.Search-clear {
  color: #ddd;
  color: var(--gray-5);
  opacity: 0
}

.Search-clear:hover {
  background: transparent none repeat 0 0/auto auto padding-box border-box scroll;
  background: initial;
  color: #999;
  color: var(--gray-3)
}

.Search .Btn--primary {
  font-size: .75rem;
  font-size: var(--font-size-xs);
  margin-left: 6px;
  min-width: 56px;
  opacity: 0;
  padding: 2px 12px
}

.Select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
  background-position: right .75rem center;
  background-repeat: no-repeat;
  background-size: 16px 12px
}

.Select:disabled {
  background-color: #f5f5f5;
  background-color: var(--gray-7)
}

@keyframes shimmer {
  0% {
    background-position: -468px 0
  }

  to {
    background-position: 468px 0
  }
}

.Skeleton {
  animation: shimmer 1.25s linear infinite forwards;
  background: linear-gradient(90deg, #f6f6f6 8%, #f0f0f0 18%, #f6f6f6 33%);
  background-size: 800px 104px
}

.Skeleton--r-sm {
  border-radius: 2px
}

.Skeleton--r-md {
  border-radius: 6px
}

.Skeleton--r-xl {
  border-radius: 32px
}

.Stepper {
  list-style-type: none;
  margin: 0;
  padding: 0
}

.Step {
  padding-bottom: 18px;
  padding-left: 29px;
  position: relative
}

.Step:last-child {
  padding-bottom: 0
}

.Step:last-child .Step-line {
  display: none
}

.Step--active .Step-dot {
  background: #ffb300;
  background: var(--brand-1)
}

.Step--active .Step-title span {
  color: #ffb300;
  color: var(--brand-1);
  font-weight: 500
}

.Step--active .Step-desc {
  color: #111;
  color: var(--gray-1)
}

.Step--active[data-status] .Step-line {
  top: 26px
}

.Step--active[data-status] .Step-icon {
  color: #f56262;
  color: var(--red)
}

.Step--active[data-status=success] .Step-icon {
  color: #62d957;
  color: var(--green)
}

.Step-icon {
  align-items: center;
  display: -ms-flexbox;
  display: flex;
  font-size: 24px;
  height: 24px;
  justify-content: center;
  left: 0;
  position: absolute;
  top: 0;
  width: 24px
}

.Step-dot {
  background: #fff;
  background: var(--white);
  border: 1px solid #ffb300;
  border: 1px solid var(--brand-1);
  border-radius: 50%;
  height: 8px;
  width: 8px
}

.Step-line {
  background: #ffb300;
  background: var(--brand-1);
  bottom: -4px;
  left: 12px;
  opacity: .5;
  position: absolute;
  top: 20px;
  width: 1px
}

.Step-title {
  align-items: center;
  color: #111;
  color: var(--gray-1);
  display: -ms-flexbox;
  display: flex;
  font-size: 1rem;
  font-size: var(--font-size-md)
}

.Step-title small {
  font-size: .75rem;
  font-size: var(--font-size-xs);
  margin-left: 12px
}

.Step-desc,
.Step-title small {
  color: #999;
  color: var(--gray-3)
}

.Step-desc {
  font-size: .875rem;
  font-size: var(--font-size-sm);
  margin-top: 3px
}

.Tabs-nav {
  display: -ms-flexbox;
  display: flex;
  margin: 7px 0;
  position: relative
}

.Tabs-nav::-webkit-scrollbar {
  display: none
}

.Tabs--scrollable .Tabs-nav {
  -webkit-overflow-scrolling: touch;
  margin-bottom: -11px;
  overflow: hidden;
  overflow-x: auto;
  padding-bottom: 18px
}

.Tabs--scrollable .Tabs-navPointer {
  bottom: 18px
}

.Tabs--scrollable .Tabs-navItem {
  flex: 1 0 auto
}

.Tabs-navItem {
  flex: 1 1;
  text-align: center
}

.Tabs-navLink {
  background: transparent;
  border: 0;
  border-radius: 20px;
  color: #666;
  color: var(--gray-2);
  font-size: .875rem;
  padding: 4px 12px;
  transition: .3s
}

.Tabs-navLink:hover {
  color: #111;
  color: var(--gray-1);
  cursor: pointer
}

.Tabs-navLink.active {
  color: #111;
  color: var(--gray-1);
  font-weight: 700;
  position: relative;
  z-index: 1
}

.Tabs-navPointer {
  background: linear-gradient(90deg, #ffc233, #ffb300 98%);
  background: var(--btn-primary-bg);
  border-radius: 2px;
  bottom: 0;
  height: 3px;
  left: 0;
  position: absolute;
  transition: .3s
}

.Tabs-pane {
  display: none
}

.Tabs-pane.active {
  display: block
}

.Tag {
  border: 1px solid #ffb300;
  border: 1px solid var(--brand-1);
  border-radius: 4px;
  color: #ffb300;
  color: var(--brand-1);
  display: inline-block;
  font-size: 12px;
  line-height: 1.25;
  margin: 0 4px 0 0;
  padding: 0 6px;
  position: relative;
  white-space: nowrap
}

.Tag--primary {
  border-color: transparent;
  color: #f70;
  color: var(--orange)
}

.Tag--primary:before {
  background: currentColor;
  border-radius: inherit;
  bottom: 0;
  content: "";
  left: 0;
  margin: -1px;
  opacity: .14;
  position: absolute;
  right: 0;
  top: 0
}

.Tag--success {
  background: #62d957;
  background: var(--green);
  border-color: #62d957;
  border-color: var(--green);
  color: #fff
}

.Tag--danger {
  background: #f56262;
  background: var(--red);
  border-color: #f56262;
  border-color: var(--red);
  color: #fff
}

.Tag--warning {
  background: #f70;
  background: var(--orange);
  border-color: #f70;
  border-color: var(--orange);
  color: #fff
}

.Text--truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.Text--break {
  overflow-wrap: break-word !important;
  word-break: break-word !important
}

.Text--ellipsis {
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis
}

.Time {
  color: #999;
  color: var(--gray-3);
  font-size: 12px
}

.Toast {
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  left: 0;
  opacity: 0;
  position: fixed;
  right: 0;
  top: 30%;
  transform: translateY(-50%);
  transition: all .3s ease 0s;
  visibility: hidden;
  z-index: 200
}

.Toast[data-type=success] .Icon {
  color: #62d957;
  color: var(--green)
}

.Toast[data-type=error] .Icon {
  color: #f56262;
  color: var(--red)
}

.Toast[data-type=loading] .Icon {
  color: #ffb300;
  color: var(--brand-1)
}

.Toast.show {
  opacity: 1;
  visibility: visible
}

.Toast .Icon {
  font-size: 24px;
  margin-right: 6px
}

.Toast-content {
  background: rgba(0, 0, 0, .7);
  border-radius: 12px;
  box-sizing: border-box;
  display: -ms-flexbox;
  display: flex;
  max-width: 90vw;
  padding: 18px 24px
}

.Toast-message {
  color: #fff;
  color: var(--white);
  flex: 1 1;
  font-size: 16px;
  margin: 0;
  word-break: break-word
}

.Toolbar {
  padding: 12px;
  padding: var(--gutter);
  padding-top: 0
}

.Toolbar-item {
  display: inline-block;
  margin-top: 12px;
  margin-top: var(--gutter);
  text-align: center;
  width: 25%
}

button.Toolbar-btn {
  border: 0;
  border-radius: 0;
  color: #666;
  color: var(--gray-2);
  display: inline-block;
  max-width: 100%;
  min-width: 0;
  overflow: hidden;
  padding: 6Px;
  position: relative;
  vertical-align: top
}

button.Toolbar-btn,
button.Toolbar-btn:hover {
  background: transparent
}

button.Toolbar-btn:active .Toolbar-btnIcon {
  background: rgba(0, 0, 0, .04)
}

@media (hover:hover) {
  button.Toolbar-btn:hover .Toolbar-btnIcon {
    background: rgba(0, 0, 0, .04)
  }
}

.Toolbar-btnIcon {
  background: #fff;
  background: var(--white);
  border-radius: 12px;
  display: inline-block;
  padding: 12Px;
  transition: .3s
}

.Toolbar-btnIcon .Icon {
  font-size: 30Px;
  vertical-align: top
}

.Toolbar-img {
  height: 30Px;
  vertical-align: top;
  width: 30Px
}

.Toolbar-btnText {
  word-wrap: break-word;
  display: block;
  font-size: 14Px;
  line-height: 1.1;
  margin-top: 8Px;
  white-space: normal
}

.ChatApp[data-elder-mode=true] .Toolbar-btnIcon {
  padding: 16Px
}

@media (min-width:350px) {
  .ChatApp[data-elder-mode=true] .Toolbar-btnIcon {
    padding: 19Px
  }

  .ChatApp[data-elder-mode=true] .Toolbar-btnIcon .Icon {
    font-size: 36Px
  }
}

[data-tooltip] {
  cursor: pointer;
  position: relative
}

[data-tooltip]:after,
[data-tooltip]:before {
  bottom: 100%;
  left: 50%;
  opacity: 0;
  pointer-events: none;
  position: absolute;
  transform: translate(-50%, 4px);
  transform-origin: top;
  transition: all .18s ease-out .18s;
  z-index: 200
}

[data-tooltip]:after {
  background: #111;
  background: var(--gray-1);
  border-radius: 6px;
  color: #fff;
  color: var(--white);
  content: attr(aria-label);
  font-size: .75rem;
  margin-bottom: 10px;
  padding: .5em 1em;
  white-space: nowrap
}

[data-tooltip]:before {
  border: .3125rem solid transparent;
  border-top: .3125rem solid var(--gray-1);
  content: "";
  height: 0;
  transform-origin: top;
  width: 0
}

[data-tooltip]:hover:after,
[data-tooltip]:hover:before {
  opacity: 1;
  transform: translate(-50%)
}

.Tree {
  background: #fff;
  background: var(--white)
}

.TreeNode-title {
  align-items: center;
  border-bottom: 1px solid #f5f5f5;
  border-bottom: 1px solid var(--gray-7);
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
  padding: 10px 15px
}

.TreeNode-title:hover {
  background: #f5f5f5;
  background: var(--gray-7);
  color: #ffb300;
  color: var(--brand-1);
  cursor: pointer
}

.TreeNode:last-child .TreeNode-title {
  border: 0
}

.TreeNode-children-title {
  background: #f5f5f5;
  background: var(--gray-7);
  border-bottom: 1px solid #f5f5f5;
  border-bottom: 1px solid var(--gray-7)
}

.TreeNode-title-text {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  flex: 1 1;
  overflow: hidden;
  text-overflow: ellipsis
}

.TreeNode-children {
  display: none
}

.TreeNode-children-active {
  display: block
}

.Typing {
  align-items: center;
  display: -ms-flexbox;
  display: flex;
  height: 22.5px;
  transition: opacity .1s
}

.Typing-dot {
  animation: typing-dot 1.26s linear infinite;
  animation-direction: reverse;
  background: #999;
  background: var(--gray-3);
  border-radius: 50%;
  display: inline-block;
  height: 6px;
  margin-left: 6px;
  transform: scale(0);
  width: 6px
}

.Typing-dot:first-child {
  margin: 0
}

.Typing-dot:nth-child(2) {
  animation-delay: .18s
}

.Typing-dot:nth-child(3) {
  animation-delay: .36s
}

@keyframes typing-dot {
  0% {
    transform: scale(1)
  }

  16.67% {
    transform: scale(0)
  }

  50% {
    transform: scale(0)
  }

  66.67% {
    transform: scale(1)
  }

  to {
    transform: scale(1)
  }
}

.Video {
  border-radius: inherit;
  position: relative
}

.Video-cover,
.Video-video:not([hidden]) {
  border-radius: inherit;
  display: block;
  max-height: 100%;
  width: 100%
}

.Video-duration {
  bottom: 6px;
  color: #fff;
  color: var(--white);
  line-height: 1;
  position: absolute;
  right: 6px;
  z-index: 1
}

.Video-playBtn {
  background: transparent;
  border: 0;
  height: 100%;
  left: 0;
  margin: 0;
  padding: 0;
  position: absolute;
  top: 0;
  width: 100%
}

.Video-playBtn:hover {
  cursor: pointer
}

.Video-playIcon {
  font-size: 42px
}

.Video--playing .Video-playBtn {
  display: none
}

.Card {
  background: #fff;
  background: var(--white);
  border-radius: 12px;
  overflow: hidden
}

.Card--xl {
  width: 300Px
}

.Card--lg {
  width: 160px
}

.Card--md {
  width: 120px
}

.Card--sm {
  width: 104px
}

.Card--xs {
  width: 80px
}

.Card--fluid {
  max-width: 432Px;
  min-width: 260Px;
  width: calc(100% - 48Px);
  width: calc(100% - var(--rate-width) - 16Px)
}

.Card[data-fluid=order] {
  max-width: 360px
}

.CardMedia {
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative
}

.CardMedia:after {
  content: "";
  display: block;
  height: 0
}

.CardMedia--wide:after {
  padding-top: 56.25%
}

.CardMedia--square:after {
  padding-top: 100%
}

.CardMedia-content {
  height: 100%;
  left: 0;
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 100%
}

.CardHeader {
  padding: 12px 12px 9px;
  position: relative
}

.CardHeader[data-has-bg=true] {
  box-shadow: inset 0 1px 0 0 #fff;
  padding-bottom: 21px
}

.CardHeader[data-has-bg=true]+[class^=Card] {
  background-color: inherit;
  border-radius: 12px 12px 0 0;
  margin-top: -12px;
  position: relative
}

.CardHeader-logo {
  display: block;
  height: 24px
}

.CardHeader-icon {
  font-size: 20px;
  height: 20px;
  margin: 2px 6px 2px 0
}

.CardHeader-title {
  color: #111;
  color: var(--gray-1);
  font-size: 16px;
  font-weight: 500;
  margin: 0
}

.CardHeader-desc {
  color: #666;
  color: var(--gray-2);
  font-size: 14px;
  font-weight: 500;
  margin: 2px 0 0
}

.CardHeader-badge {
  background: 100%/contain no-repeat;
  margin: -12px;
  width: 90px
}

.CardHeader-badge,
.CardHeader-slot {
  margin-left: 9px
}

.CardHeader-slot {
  display: -ms-flexbox;
  display: flex
}

.CardHeader-slot .Btn {
  font-size: 12px;
  min-width: auto;
  padding: 2px 9px
}

.CardHeader-slot>a {
  align-items: center;
  color: #666;
  color: var(--gray-2);
  display: -ms-flexbox;
  display: flex;
  font-size: 14px;
  text-decoration: none
}

.CardHeader-slot>a .Icon {
  font-size: 16px
}

.CardTitle {
  padding: 12px 12px 6px
}

.CardTitle--center {
  padding: 4px 2px;
  text-align: center
}

.CardTitle-title {
  font-size: 1rem;
  font-weight: 500;
  margin: 0
}

.CardTitle-subtitle {
  color: #999;
  color: var(--gray-3);
  font-size: .625rem;
  margin: 0
}

.CardContent,
.CardText {
  padding: 12px
}

.CardText {
  color: #111;
  color: var(--gray-1)
}

.CardText p {
  margin: 0
}

.CardActions {
  display: -ms-flexbox;
  display: flex;
  padding: 12px 12px 18px
}

.CardActions .Btn {
  flex: 1 1;
  line-height: 1.5
}

.CardActions .Btn+.Btn {
  margin-left: 12px
}

.CardActions--column {
  flex-direction: column;
  padding: 0
}

.CardActions--column button.Btn {
  background: #fff;
  background: var(--white);
  border: 0;
  border-radius: 0;
  border-top: 1px solid #eee;
  border-top: 1px solid var(--gray-6);
  color: #999;
  color: var(--gray-3);
  padding: 10px
}

.CardActions--column button.Btn:last-child {
  border-radius: 0 0 12px 12px
}

.CardActions--column button.Btn:active {
  background: #f5f5f5;
  background: var(--gray-7)
}

.CardActions--column button.Btn:disabled {
  color: #ccc;
  color: var(--gray-4)
}

.CardActions--column .Btn+.Btn {
  margin: 0
}

.CardActions--column .Btn--primary {
  color: #ffb300;
  color: var(--brand-1)
}

@media (hover:hover) {
  .CardActions--column button.Btn:hover {
    background: #f5f5f5;
    background: var(--gray-7)
  }
}

.CardContent+[class^=Card],
.CardHeader[data-has-bg=false]+[class^=Card],
.CardText+[class^=Card],
.CardTitle+[class^=Card] {
  padding-top: 0
}

.Carousel {
  overflow: hidden;
  position: relative
}

.Carousel--draggable .Carousel-inner {
  cursor: grab;
  touch-action: pan-y
}

.Carousel--draggable .Carousel-inner:active {
  cursor: grabbing
}

.Carousel--rtl {
  direction: rtl
}

.Carousel--dragging .Carousel-item {
  pointer-events: none
}

.Carousel-inner {
  will-change: transform
}

.Carousel-dots,
.Carousel-inner {
  display: -ms-flexbox;
  display: flex
}

.Carousel-dots {
  bottom: 8px;
  justify-content: center;
  left: 50%;
  list-style-type: none;
  margin: 0;
  padding: 0;
  position: absolute;
  transform: translateX(-50%);
  z-index: 1
}

.Carousel-dot {
  background: #ccc;
  background: var(--gray-4);
  border: 0;
  border-radius: 50%;
  cursor: pointer;
  display: block;
  height: 8px;
  margin: 0 4px;
  padding: 0;
  transition: .3s;
  width: 8px
}

.Carousel-dot.active {
  background: #ffb300;
  background: var(--brand-1)
}

.Goods {
  font-size: 14px;
  padding: 12px
}

.Goods[data-elder-mode=true] .Goods-img {
  height: 56px;
  width: 56px
}

.Goods[data-elder-mode=true] .Price {
  color: #ffb300;
  color: var(--brand-1)
}

.Goods[data-variant=inList] .Goods-status {
  color: #ffc233;
  color: var(--highlight-2)
}

.Goods[data-variant=compact] {
  background: #f8f8f8;
  background: var(--gray-8);
  color: #666;
  color: var(--gray-2);
  padding: 6px
}

.Goods[data-variant=compact] .Goods-img {
  border-radius: 9px;
  height: 44px;
  width: 44px
}

.Goods[data-variant=compact] .Price {
  font-weight: 400
}

.Goods+.Goods {
  border-top: 1px solid #f5f5f5;
  border-top: 1px solid var(--gray-7)
}

.Goods-img {
  border-radius: 12px;
  height: 72px;
  margin-right: 9Px;
  object-fit: cover;
  width: 72px
}

.Goods-name {
  font-size: .875rem;
  font-weight: 400;
  margin: 0
}

.Goods-main .Price {
  margin-right: 9Px
}

.Goods-desc {
  margin-top: 2Px;
  word-break: break-all
}

.Goods-desc,
.Goods-meta {
  color: #999;
  color: var(--gray-3)
}

.Goods-meta {
  font-size: .625rem
}

.Goods-countUnit {
  color: #999;
  color: var(--gray-3);
  font-size: .875rem;
  margin-left: 6px
}

.Goods-unit {
  font-size: .75rem;
  margin-left: 3px
}

.Goods-buyBtn {
  color: #fff;
  float: right;
  padding: 2px
}

.Goods-buyBtn,
.Goods-buyBtn:hover {
  background: #ffb300;
  background: var(--brand-1)
}

.Goods-aside {
  align-items: flex-end;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  margin-left: 6Px
}

.Goods-status {
  align-items: center;
  color: #ffb300;
  color: var(--highlight-1);
  display: -ms-inline-flexbox;
  display: inline-flex
}

.Goods .Btn {
  min-width: 0
}

.Goods-countUnit,
.Goods-slot,
.Goods-status,
.Goods-tags {
  margin-top: 2Px
}

.FileCard {
  padding: 8px
}

.FileCard-icon {
  color: #666;
  color: var(--gray-2);
  height: 60px;
  margin-right: 8px;
  position: relative
}

.FileCard-icon[data-type=pdf] {
  color: #f56262;
  color: var(--red)
}

.FileCard-icon[data-type*=doc] {
  color: #39f;
  color: var(--blue)
}

.FileCard-icon[data-type*=ppt],
.FileCard-icon[data-type=key] {
  color: #f70;
  color: var(--orange)
}

.FileCard-icon[data-type*=xls] {
  color: #62d957;
  color: var(--green)
}

.FileCard-icon[data-type=rar],
.FileCard-icon[data-type=zip] {
  color: #ffb300;
  color: var(--brand-1)
}

.FileCard-icon .Icon {
  font-size: 60px
}

.FileCard-name {
  height: 38px;
  line-height: 1.4;
  margin-bottom: 4px
}

.FileCard-ext {
  bottom: 15px;
  font-size: 1rem;
  font-weight: 700;
  left: 20px;
  max-width: 50px;
  position: absolute;
  text-transform: uppercase;
  transform: scale(.5);
  transform-origin: left bottom
}

.FileCard-meta {
  color: #999;
  color: var(--gray-3);
  font-size: .75rem
}

.FileCard-meta>a,
.FileCard-meta>span {
  margin-right: 10px
}

.FileCard-meta a {
  color: #39f;
  color: var(--link-color);
  text-decoration: none
}

.Form {
  background: #fff;
  background: var(--white)
}

.Form.is-light .FormItem {
  padding: 0
}

.Form.is-light .HelpText,
.Form.is-light .Label,
.FormItem {
  padding: 0 12px;
  padding: 0 var(--gutter)
}

.FormItem {
  position: relative
}

.FormItem+.FormItem {
  margin-top: 20px
}

.FormItem.required .Label:after {
  color: #f56262;
  color: var(--red);
  content: "*";
  display: inline-block;
  font-family: SimSun, sans-serif;
  font-size: .875rem;
  line-height: 1;
  vertical-align: middle
}

.FormItem.is-invalid .HelpText,
.FormItem.is-invalid .Label {
  color: #f56262;
  color: var(--red)
}

.FormItem.is-invalid .Input {
  border-color: #f56262;
  border-color: var(--red)
}

.FormItem .CheckboxGroup,
.FormItem .RadioGroup {
  margin-top: 10px
}

.FormItem .Label+.Input {
  margin-top: 5px
}

.FormActions {
  background: #fff;
  background: var(--white);
  display: -ms-flexbox;
  display: flex;
  padding: 10px 12px;
  padding: 10px var(--gutter)
}

.FormActions .Btn {
  flex: 1 1
}

.FormActions .Btn+.Btn {
  margin-left: 6px
}

.MessageContainer {
  display: -ms-flexbox;
  display: flex;
  flex: 1 1;
  flex-direction: column;
  min-height: 0;
  position: relative;
  padding: 0 20px;
}

.MessageContainer>.PullToRefresh {
  flex: 1 1
}

.MessageContainer:focus {
  outline: 0
}

.MessageList {
  font-size: 14px;
  padding: 12px;
  padding: var(--gutter)
}

.RecorderToast {
  background: rgba(51, 51, 51, .87);
  border-radius: 12px;
  color: #fff;
  color: var(--white);
  height: 160px;
  left: 50%;
  padding: 10px;
  position: fixed;
  text-align: center;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 160px;
  z-index: 100
}

.Recorder--cancel .RecorderToast {
  color: #f56262;
  color: var(--red)
}

.Recorder--cancel .Recorder-btn {
  background: rgba(0, 0, 0, .04);
  color: #999;
  color: var(--gray-3)
}

.RecorderToast-icon {
  font-size: 37px;
  position: relative
}

.RecorderToast-waves {
  height: 100%;
  position: absolute;
  transition: .3s;
  width: 100%;
  z-index: -1
}

.RecorderToast-wave-1,
.RecorderToast-wave-2,
.RecorderToast-wave-3 {
  animation: wave 10s linear infinite;
  color: #ffc233;
  color: var(--brand-2);
  position: absolute;
  z-index: -1
}

.RecorderToast-wave-1 {
  font-size: 176px;
  left: 14px;
  opacity: .2;
  top: -25px
}

.RecorderToast-wave-2 {
  font-size: 186px;
  left: -21px;
  opacity: .4;
  top: -12px
}

.RecorderToast-wave-3 {
  font-size: 71px;
  left: 55px;
  opacity: .8;
  top: 40px
}

.Recorder-btn {
  background: #fff;
  background: var(--white);
  border-radius: 20px;
  color: #333;
  color: var(--gray-dark);
  cursor: pointer;
  height: 36px;
  line-height: 36px;
  text-align: center;
  transition: .3s
}

@keyframes wave {
  0% {
    transform: translateY(5%) rotate(0)
  }

  50% {
    transform: translateY(-5%) rotate(180deg)
  }

  to {
    transform: translateY(5%) rotate(1turn)
  }
}

.Composer {
  align-items: flex-end;
  display: -ms-flexbox;
  display: flex;
  padding: 9px 12px;
  padding: 9px var(--gutter);
  box-shadow: 0px 7px 20px 1px rgba(187,228,255,0.52);
  border-radius: 31px;
}

.Composer>div+div {
  margin-left: 9px
}

.Composer-actions {
  align-items: center;
  display: -ms-flexbox;
  display: flex;
  height: 36Px
}

.Composer-actions .IconBtn {
  color: #111;
  color: var(--gray-1);
  font-size: 30Px
}

.Composer-toggleBtn {
  transition: transform .2s
}

.Composer-toggleBtn.active .Icon {
  transform: rotate(45deg)
}

.Composer-inputWrap {
  flex: 1 1;
  position: relative
}

.Composer-input {
  border: 0;
  caret-color: #111;
  caret-color: var(--gray-1);
  font-size: 15Px;
  line-height: 20Px;
  max-height: 132px;
  overflow-x: hidden;
  padding: 8px 12px;
  transition: border-color .15s ease-in-out
}

.Composer-sendBtn {
  min-width: 0;
  padding-left: 16Px;
  padding-right: 16Px
}

.ChatApp[data-elder-mode=true] .Composer-actions {
  height: 50Px
}

.ChatApp[data-elder-mode=true] .Composer-actions .IconBtn {
  font-size: 34Px
}

.ChatApp[data-elder-mode=true] .Composer-input {
  font-size: 21Px;
  line-height: 32Px;
  padding: 9Px 12Px
}

.ChatApp[data-elder-mode=true] .Composer-sendBtn {
  font-size: 17Px;
  padding: 4Px 13Px
}

.SendConfirm .Modal-dialog {
  margin: 20px;
  width: 480px
}

.SendConfirm-inner {
  height: 320px;
  text-align: center
}

.SendConfirm-inner img {
  max-height: 100%;
  max-width: 100%
}

html {
  height: 100vh
}

body,
html[data-safari] {
  height: 100%
}

body {
  margin: 0;
  overflow: hidden
}

@media (hover:none) {
  body {
    -webkit-touch-callout: none;
    -ms-user-select: none;
    user-select: none
  }
}

.ChatApp {
  -webkit-tap-highlight-color: transparent;
  /* background: #eee; */
  /* background: var(--gray-6); */
  color: #111;
  color: var(--gray-1);
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  height: 100%;
  line-height: 1.5
}

.S--focusing {
  --safe-bottom: 0px
}

@media (hover:none) {
  .S--focusing .MessageList {
    margin-top: 75vh
  }
}

.ChatFooter {
  /* background: #f5f5f5; */
  /* background: var(--gray-7); */
  padding-bottom: 0;
  padding-bottom: var(--safe-bottom);
  position: relative;
  z-index: 10;
  padding: 0 40px 10px 40px;
}

.ChatWrapper {
  /* background: #f4f6f8; */
  border-radius: 8px;
  max-height: 100%;
  max-width: 100%;
  overflow: hidden
}

.ChatWrapper,
.S--wide .ChatWrapper {
  height: 100%;
  width: 100%
}

@media screen and (max-height:700px) {
  .S--wide .ChatWrapper {
    max-height: 100vh
  }
}

@media screen and (max-height:960px) {
  .S--wide .ChatWrapper {
    max-width: 100vw
  }
}

.ChatSDK-is-mobile .ChatWrapper {
  border-radius: 0;
  height: 100%;
  margin: 0 auto;
  max-height: none;
  max-width: none;
  width: 100%
}

.ChatWrapper-header {
  background: #fff;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 2px 8px 0 #e5e8ee;
  padding: 12px 16px;
  position: relative;
  z-index: 1
}

.ChatWrapper-body {
  display: -ms-flexbox;
  display: flex
}

.ChatWrapper-body:first-child,
.ChatWrapper-body>div {
  height: 100%
}

.Main {
  flex: 1 1
}

.AutoComplete {
  background: var(--white);
  border-radius: 8px;
  bottom: 100%;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .15);
  left: 0;
  margin-bottom: 8px;
  max-height: 200px;
  max-width: 400px;
  opacity: 0;
  overflow-y: auto;
  pointer-events: none;
  position: absolute;
  transform: translateY(5px);
  transition: .2s;
  width: 100%;
  z-index: 150
}

.AutoComplete.show {
  opacity: 1;
  pointer-events: auto;
  transform: translateY(0)
}

.AutoComplete a {
  border-top: 1px solid #eee;
  color: var(--gray-dark);
  display: block;
  padding: 9px 16px
}

.AutoComplete a:hover {
  background: var(--light-2)
}

.AutoComplete mark {
  background: transparent;
  color: var(--red)
}

.Sidebar {
  background: #fff;
  border-left: 1px solid #eee;
  overflow-y: auto;
  padding: 20px;
  width: 281px
}

.Sidebar:empty {
  display: none
}

.Sidebar h3 {
  font-size: 18px;
  font-weight: 500
}

@media (max-width:600px) {
  .Sidebar {
    display: none
  }
}

.Sidebar-panel {
  margin-top: 24px
}

.Sidebar-panel:first-child {
  margin-top: 0
}