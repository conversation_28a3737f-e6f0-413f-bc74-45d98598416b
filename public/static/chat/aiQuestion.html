<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8"/>
    <meta charset="utf-8"/>
    <meta name="renderer" content="webkit"/>
    <meta name="force-rendering" content="webkit"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0, viewport-fit=cover"
    />
    <link
            rel="stylesheet"
            href="./ChatSDK.css"
    />
</head>
<body>
<div id="chatbot" style="width: 100%; height: 100%;"></div>
<script src="./ChatSDK.js"></script>
<script src="./ChatSSE.js"></script>
<script src="./index.js"></script>
<script src="./demo_sse.js"></script>
<script src="https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.10/libs/cn/index.js"></script>
<script>
new CozeWebSDK.WebChatClient({
  config: {
          bot_id: '7538709349795921971',
  },
  componentProps: {
          title: 'Coze',
  },
  auth: {
          type: 'token',
          token: 'cztei_hyco2QlQLa8M1M8EFdjQKdWYP4GaaQrL00LHqR3QXOjJzyFYQ7FygJOEkAE73b7Pk',
          onRefreshToken: function () {
          return 'cztei_hyco2QlQLa8M1M8EFdjQKdWYP4GaaQrL00LHqR3QXOjJzyFYQ7FygJOEkAE73b7Pk'
          }
  },
  ui: {
    // asstBtn: false
  }
});
</script>
</body>
</html>
