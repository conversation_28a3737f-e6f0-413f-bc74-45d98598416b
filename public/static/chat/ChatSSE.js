!function(e, t) {
    "object" == typeof exports && "object" == typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define([], t) : "object" == typeof exports ? exports.ChatSSE = t() : e.ChatSSE = t()
}(self, (function() {
    return function() {
        var e = {
            944: function(e) {
                function t(n) {
                    return e.exports = t = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
                        return typeof e
                    }
                    : function(e) {
                        return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
                    }
                    ,
                    e.exports.__esModule = !0,
                    e.exports.default = e.exports,
                    t(n)
                }
                e.exports = t,
                e.exports.__esModule = !0,
                e.exports.default = e.exports
            }
        }
          , t = {};
        function n(r) {
            var o = t[r];
            if (void 0 !== o)
                return o.exports;
            var a = t[r] = {
                exports: {}
            };
            return e[r](a, a.exports, n),
            a.exports
        }
        n.d = function(e, t) {
            for (var r in t)
                n.o(t, r) && !n.o(e, r) && Object.defineProperty(e, r, {
                    enumerable: !0,
                    get: t[r]
                })
        }
        ,
        n.o = function(e, t) {
            return Object.prototype.hasOwnProperty.call(e, t)
        }
        ,
        n.r = function(e) {
            "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
                value: "Module"
            }),
            Object.defineProperty(e, "__esModule", {
                value: !0
            })
        }
        ;
        var r = {};
        return function() {
            "use strict";
            function e(e, t, n) {
                return t in e ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = n,
                e
            }
            function t(t) {
                for (var n = 1; n < arguments.length; n++) {
                    var r = null != arguments[n] ? arguments[n] : {}
                      , o = Object.keys(r);
                    "function" == typeof Object.getOwnPropertySymbols && (o = o.concat(Object.getOwnPropertySymbols(r).filter((function(e) {
                        return Object.getOwnPropertyDescriptor(r, e).enumerable
                    }
                    )))),
                    o.forEach((function(n) {
                        e(t, n, r[n])
                    }
                    ))
                }
                return t
            }
            function o(e, t) {
                return t = null != t ? t : {},
                Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : function(e, t) {
                    var n = Object.keys(e);
                    if (Object.getOwnPropertySymbols) {
                        var r = Object.getOwnPropertySymbols(e);
                        t && (r = r.filter((function(t) {
                            return Object.getOwnPropertyDescriptor(e, t).enumerable
                        }
                        ))),
                        n.push.apply(n, r)
                    }
                    return n
                }(Object(t)).forEach((function(n) {
                    Object.defineProperty(e, n, Object.getOwnPropertyDescriptor(t, n))
                }
                )),
                e
            }
            function a(e) {
                var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
                if (!e)
                    return t;
                try {
                    return JSON.parse(e) || t
                } catch (n) {
                    return console.warn("JSON解析失败:", n, e),
                    t
                }
            }
            function c(e, t, n, r, o, a, c) {
                try {
                    var u = e[a](c)
                      , i = u.value
                } catch (e) {
                    return void n(e)
                }
                u.done ? t(i) : Promise.resolve(i).then(r, o)
            }
            function u(e) {
                return function() {
                    var t = this
                      , n = arguments;
                    return new Promise((function(r, o) {
                        var a = e.apply(t, n);
                        function u(e) {
                            c(a, r, o, u, i, "next", e)
                        }
                        function i(e) {
                            c(a, r, o, u, i, "throw", e)
                        }
                        u(void 0)
                    }
                    ))
                }
            }
            function i(e, t) {
                if (null == e)
                    return {};
                var n, r, o = function(e, t) {
                    if (null == e)
                        return {};
                    var n, r, o = {}, a = Object.keys(e);
                    for (r = 0; r < a.length; r++)
                        n = a[r],
                        t.indexOf(n) >= 0 || (o[n] = e[n]);
                    return o
                }(e, t);
                if (Object.getOwnPropertySymbols) {
                    var a = Object.getOwnPropertySymbols(e);
                    for (r = 0; r < a.length; r++)
                        n = a[r],
                        t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (o[n] = e[n])
                }
                return o
            }
            n.r(r),
            n.d(r, {
                fetchBeebotSSE: function() {
                    return S
                },
                fetchEventSource: function() {
                    return g
                }
            });
            n(944);
            function s(e, t) {
                var n, r, o, a, c = {
                    label: 0,
                    sent: function() {
                        if (1 & o[0])
                            throw o[1];
                        return o[1]
                    },
                    trys: [],
                    ops: []
                };
                return a = {
                    next: u(0),
                    throw: u(1),
                    return: u(2)
                },
                "function" == typeof Symbol && (a[Symbol.iterator] = function() {
                    return this
                }
                ),
                a;
                function u(u) {
                    return function(i) {
                        return function(u) {
                            if (n)
                                throw new TypeError("Generator is already executing.");
                            for (; a && (a = 0,
                            u[0] && (c = 0)),
                            c; )
                                try {
                                    if (n = 1,
                                    r && (o = 2 & u[0] ? r.return : u[0] ? r.throw || ((o = r.return) && o.call(r),
                                    0) : r.next) && !(o = o.call(r, u[1])).done)
                                        return o;
                                    switch (r = 0,
                                    o && (u = [2 & u[0], o.value]),
                                    u[0]) {
                                    case 0:
                                    case 1:
                                        o = u;
                                        break;
                                    case 4:
                                        return c.label++,
                                        {
                                            value: u[1],
                                            done: !1
                                        };
                                    case 5:
                                        c.label++,
                                        r = u[1],
                                        u = [0];
                                        continue;
                                    case 7:
                                        u = c.ops.pop(),
                                        c.trys.pop();
                                        continue;
                                    default:
                                        if (!(o = c.trys,
                                        (o = o.length > 0 && o[o.length - 1]) || 6 !== u[0] && 2 !== u[0])) {
                                            c = 0;
                                            continue
                                        }
                                        if (3 === u[0] && (!o || u[1] > o[0] && u[1] < o[3])) {
                                            c.label = u[1];
                                            break
                                        }
                                        if (6 === u[0] && c.label < o[1]) {
                                            c.label = o[1],
                                            o = u;
                                            break
                                        }
                                        if (o && c.label < o[2]) {
                                            c.label = o[2],
                                            c.ops.push(u);
                                            break
                                        }
                                        o[2] && c.ops.pop(),
                                        c.trys.pop();
                                        continue
                                    }
                                    u = t.call(e, c)
                                } catch (e) {
                                    u = [6, e],
                                    r = 0
                                } finally {
                                    n = o = 0
                                }
                            if (5 & u[0])
                                throw u[1];
                            return {
                                value: u[0] ? u[1] : void 0,
                                done: !0
                            }
                        }([u, i])
                    }
                }
            }
            Object.create;
            Object.create;
            "function" == typeof SuppressedError && SuppressedError;
            var l;
            function f(e, t) {
                return d.apply(this, arguments)
            }
            function d() {
                return (d = u((function(e, t) {
                    var n, r, o;
                    return s(this, (function(a) {
                        switch (a.label) {
                        case 0:
                            (r = null == e || null === (n = e.getReader) || void 0 === n ? void 0 : n.call(e)) || (console.log("[sse]error: Can Not Find Reader Object"),
                            console.log("[sse]StreamInfo: ", e)),
                            a.label = 1;
                        case 1:
                            return [4, r.read()];
                        case 2:
                            if ((o = a.sent()).done)
                                return [3, 3];
                            try {
                                t(o.value)
                            } catch (e) {
                                console.log("[sse-error][onmessage] pleach check `onmessage` or `onJsonText`.", e)
                            }
                            return [3, 1];
                        case 3:
                            return [2]
                        }
                    }
                    ))
                }
                ))).apply(this, arguments)
            }
            function p(e) {
                var t, n, r, o = !1;
                return function(a) {
                    var c, u, i;
                    void 0 === t ? (t = a,
                    n = 0,
                    r = -1) : (c = t,
                    u = a,
                    (i = new Uint8Array(c.length + u.length)).set(c),
                    i.set(u, c.length),
                    t = i);
                    for (var s = t.length, l = 0; n < s; ) {
                        o && (10 === t[n] && (l = ++n),
                        o = !1);
                        for (var f = -1; n < s && -1 === f; ++n)
                            switch (t[n]) {
                            case 58:
                                -1 === r && (r = n - l);
                                break;
                            case 13:
                                o = !0;
                            case 10:
                                f = n
                            }
                        if (-1 === f)
                            break;
                        e(t.subarray(l, f), r),
                        l = n,
                        r = -1
                    }
                    l === s ? t = void 0 : 0 !== l && (t = t.subarray(l),
                    n -= l)
                }
            }
            function b(e, t, n) {
                var r = y()
                  , o = new TextDecoder;
                return function(a, c) {
                    if (0 === a.length)
                        null == n || n(r),
                        r = y();
                    else if (c > 0) {
                        var u = o.decode(a.subarray(0, c))
                          , i = c + (32 === a[c + 1] ? 2 : 1)
                          , s = o.decode(a.subarray(i));
                        switch (u) {
                        case "data":
                            r.data = r.data ? "".concat(r.data, "\n").concat(s) : s;
                            break;
                        case "event":
                            r.event = s;
                            break;
                        case "id":
                            e(r.id = s);
                            break;
                        case "retry":
                            var l = parseInt(s, 10);
                            isNaN(l) || t(r.retry = l)
                        }
                    }
                }
            }
            function y() {
                return {
                    data: "",
                    event: "",
                    id: "",
                    retry: void 0
                }
            }
            function v() {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 100;
                return new Promise((function(t) {
                    setTimeout(t, e)
                }
                ))
            }
            !function(e) {
                e[e.NewLine = 10] = "NewLine",
                e[e.CarriageReturn = 13] = "CarriageReturn",
                e[e.Space = 32] = "Space",
                e[e.Colon = 58] = "Colon"
            }(l || (l = {}));
            var h = "text/event-stream"
              , m = "last-event-id";
            function g(e, n) {
                var r = n.signal
                  , a = n.headers
                  , c = n.onopen
                  , l = n.onmessage
                  , d = n.onclose
                  , y = n.onerror
                  , g = n.fetch
                  , O = n.autoRetryTime
                  , S = n.timeout
                  , j = n.debug
                  , x = void 0 !== j && j
                  , P = i(n, ["signal", "headers", "onopen", "onmessage", "onclose", "onerror", "fetch", "autoRetryTime", "timeout", "debug"])
                  , T = 0;
                return new Promise((function(n, i) {
                    var j = function() {
                        var e;
                        window.clearTimeout(J),
                        window.clearTimeout(R),
                        null == C || null === (e = C.abort) || void 0 === e || e.call(C)
                    }
                      , E = function() {
                        window.clearTimeout(R),
                        0 !== S && (R = window.setTimeout((function() {
                            i(new Error("timeout")),
                            null == y || y(new Error("timeout")),
                            j()
                        }
                        ), S || 6e4))
                    }
                      , k = t({}, a);
                    k.accept || (k.accept = h);
                    var N = 1e3
                      , J = 0
                      , R = 0
                      , C = new AbortController;
                    null == r || r.addEventListener("abort", (function() {
                        j(),
                        n()
                    }
                    ));
                    var _ = null != g ? g : window.fetch
                      , I = null != c ? c : w;
                    function L() {
                        return B.apply(this, arguments)
                    }
                    function B() {
                        return (B = u((function() {
                            var a, c, u, h;
                            return s(this, (function(s) {
                                switch (s.label) {
                                case 0:
                                    return s.trys.push([0, 6, , 7]),
                                    [4, _(e, o(t({}, P), {
                                        headers: k,
                                        signal: C.signal
                                    }))];
                                case 1:
                                    return a = s.sent(),
                                    E(),
                                    [4, I(a)];
                                case 2:
                                    return s.sent(),
                                    a.body ? [3, 4] : (x && console.log("[sse]no body, delay 100ms"),
                                    [4, v(100)]);
                                case 3:
                                    s.sent(),
                                    s.label = 4;
                                case 4:
                                    return x && console.log("[sse]response.body", a.body),
                                    [4, f(a.body, p(b((function(e) {
                                        e ? k[m] = e : delete k[m]
                                    }
                                    ), (function(e) {
                                        N = e
                                    }
                                    ), (function(e) {
                                        E(),
                                        l(e)
                                    }
                                    ))))];
                                case 5:
                                    return s.sent(),
                                    C = null,
                                    null == d || d(),
                                    j(),
                                    n(),
                                    [3, 7];
                                case 6:
                                    if ((c = s.sent()) && console.error("[sse]err", c),
                                    !(null == r ? void 0 : r.aborted) && O && T < O)
                                        try {
                                            T += 1,
                                            h = null !== (u = null == y ? void 0 : y(c)) && void 0 !== u ? u : N,
                                            window.clearTimeout(J),
                                            J = window.setTimeout(L, h)
                                        } catch (e) {
                                            j(),
                                            i(e)
                                        }
                                    else
                                        j(),
                                        null == y || y(c),
                                        i(c);
                                    return [3, 7];
                                case 7:
                                    return [2]
                                }
                            }
                            ))
                        }
                        ))).apply(this, arguments)
                    }
                    L()
                }
                ))
            }
            function w(e) {
                var t = e.headers.get("content-type");
                if (!(null == t ? void 0 : t.startsWith(h)))
                    throw new Error("Expected content-type to be ".concat(h, ", Actual: ").concat(t))
            }
            function O(e, t) {
                if (e)
                    throw new Error(t)
            }
            function S(e) {
                O(!e.json && e.data, "`fetchBeebotSSE`的参数data已废弃，请改为使用`json`参数"),
                O(!e.url, "`fetchBeebotSSE`需要url参数"),
                O(!e.json, "`fetchBeebotSSE`需要json参数"),
                O(Array.isArray(e.json), "`fetchBeebotSSE`的参数`json`不能是数组格式，仅支持对象格式");
                var n = e.messageId || function() {
                    var e = URL.createObjectURL(new Blob([]))
                      , t = e.substring(e.lastIndexOf("/") + 1);
                    return URL.revokeObjectURL(e),
                    t
                }();
                return g(e.url, o(t({}, e), {
                    method: "POST",
                    headers: t({
                        "Content-Type": "application/json"
                    }, e.headers),
                    body: JSON.stringify({
                        messageId: n,
                        action: e.action,
                        version: e.version || "2022-04-08",
                        data: [{
                            type: "JSON_TEXT",
                            value: JSON.stringify(e.json)
                        }]
                    }),
                    onmessage: function(r) {
                        var c;
                        if (null === (c = e.onmessage) || void 0 === c || c.call(e, r),
                        e.onJsonText) {
                            var u = r.data;
                            if ("{" === u[0]) {
                                var i, s, l = a(u);
                                if ("JSON_TEXT" === (null === (i = l.data) || void 0 === i || null === (s = i[0]) || void 0 === s ? void 0 : s.type)) {
                                    var f, d = a(null === (f = l.data[0]) || void 0 === f ? void 0 : f.value), p = t(o(t({
                                        messageId: n,
                                        success: l.success
                                    }, l), {
                                        data: null
                                    }), d);
                                    return e.onJsonText(p)
                                }
                                if (l.code) {
                                    var b = o(t({}, l), {
                                        type: "system",
                                        content: {
                                            text: l.message || l.code
                                        }
                                    });
                                    return e.onJsonText(b)
                                }
                            }
                        }
                        console.warn("[beebot-common]未支持的消息格式", r)
                    }
                }))
            }
        }(),
        r
    }()
}
));
