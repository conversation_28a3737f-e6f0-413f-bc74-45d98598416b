/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ABackTop: typeof import('ant-design-vue/es')['BackTop']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACascader: typeof import('ant-design-vue/es')['Cascader']
    Action: typeof import('./../src/components/GameModels/action.vue')['default']
    Action4: typeof import('./../src/components/GameModels/lib/game4/action4.vue')['default']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATimePicker: typeof import('ant-design-vue/es')['TimePicker']
    BgMusic: typeof import('./../src/components/GameModels/lib/component/bgMusic.vue')['default']
    Btn: typeof import('./../src/components/Btn.vue')['default']
    Carousel: typeof import('./../src/components/Carousel/index.vue')['default']
    Container: typeof import('./../src/components/Container/index.vue')['default']
    Countdown: typeof import('./../src/components/GameModels/lib/component/countdown.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Game1: typeof import('./../src/components/GameModels/lib/game1/index.vue')['default']
    Game10: typeof import('./../src/components/GameModels/lib/game10/index.vue')['default']
    Game100: typeof import('./../src/components/GameModels/lib/game100/index.vue')['default']
    Game101: typeof import('./../src/components/GameModels/lib/game101/index.vue')['default']
    Game102: typeof import('./../src/components/GameModels/lib/game102/index.vue')['default']
    Game103: typeof import('./../src/components/GameModels/lib/game103/index.vue')['default']
    Game104: typeof import('./../src/components/GameModels/lib/game104/index.vue')['default']
    Game105: typeof import('./../src/components/GameModels/lib/game105/index.vue')['default']
    Game107: typeof import('./../src/components/GameModels/lib/game107/index.vue')['default']
    Game108: typeof import('./../src/components/GameModels/lib/game108/index.vue')['default']
    Game109: typeof import('./../src/components/GameModels/lib/game109/index.vue')['default']
    Game11: typeof import('./../src/components/GameModels/lib/game11/index.vue')['default']
    Game110: typeof import('./../src/components/GameModels/lib/game110/index.vue')['default']
    Game111: typeof import('./../src/components/GameModels/lib/game111/index.vue')['default']
    Game112: typeof import('./../src/components/GameModels/lib/game112/index.vue')['default']
    Game113: typeof import('./../src/components/GameModels/lib/game113/index.vue')['default']
    Game114: typeof import('./../src/components/GameModels/lib/game114/index.vue')['default']
    Game115: typeof import('./../src/components/GameModels/lib/game115/index.vue')['default']
    Game116: typeof import('./../src/components/GameModels/lib/game116/index.vue')['default']
    Game117: typeof import('./../src/components/GameModels/lib/game117/index.vue')['default']
    Game118: typeof import('./../src/components/GameModels/lib/game118/index.vue')['default']
    Game119: typeof import('./../src/components/GameModels/lib/game119/index.vue')['default']
    Game12: typeof import('./../src/components/GameModels/lib/game12/index.vue')['default']
    Game120: typeof import('./../src/components/GameModels/lib/game120/index.vue')['default']
    Game121: typeof import('./../src/components/GameModels/lib/game121/index.vue')['default']
    Game122: typeof import('./../src/components/GameModels/lib/game122/index.vue')['default']
    Game123: typeof import('./../src/components/GameModels/lib/game123/index.vue')['default']
    Game124: typeof import('./../src/components/GameModels/lib/game124/index.vue')['default']
    Game125: typeof import('./../src/components/GameModels/lib/game125/index.vue')['default']
    Game126: typeof import('./../src/components/GameModels/lib/game126/index.vue')['default']
    Game127: typeof import('./../src/components/GameModels/lib/game127/index.vue')['default']
    Game128: typeof import('./../src/components/GameModels/lib/game128/index.vue')['default']
    Game129: typeof import('./../src/components/GameModels/lib/game129/index.vue')['default']
    Game130: typeof import('./../src/components/GameModels/lib/game130/index.vue')['default']
    Game131: typeof import('./../src/components/GameModels/lib/game131/index.vue')['default']
    Game132: typeof import('./../src/components/GameModels/lib/game132/index.vue')['default']
    Game133: typeof import('./../src/components/GameModels/lib/game133/index.vue')['default']
    Game134: typeof import('./../src/components/GameModels/lib/game134/index.vue')['default']
    Game135: typeof import('./../src/components/GameModels/lib/game135/index.vue')['default']
    Game136: typeof import('./../src/components/GameModels/lib/game136/index.vue')['default']
    Game137: typeof import('./../src/components/GameModels/lib/game137/index.vue')['default']
    Game138: typeof import('./../src/components/GameModels/lib/game138/index.vue')['default']
    Game139: typeof import('./../src/components/GameModels/lib/game139/index.vue')['default']
    Game14: typeof import('./../src/components/GameModels/lib/game14/index.vue')['default']
    Game140: typeof import('./../src/components/GameModels/lib/game140/index.vue')['default']
    Game141: typeof import('./../src/components/GameModels/lib/game141/index.vue')['default']
    Game142: typeof import('./../src/components/GameModels/lib/game142/index.vue')['default']
    Game143: typeof import('./../src/components/GameModels/lib/game143/index.vue')['default']
    Game144: typeof import('./../src/components/GameModels/lib/game144/index.vue')['default']
    Game145: typeof import('./../src/components/GameModels/lib/game145/index.vue')['default']
    Game146: typeof import('./../src/components/GameModels/lib/game146/index.vue')['default']
    Game147: typeof import('./../src/components/GameModels/lib/game147/index.vue')['default']
    Game148: typeof import('./../src/components/GameModels/lib/game148/index.vue')['default']
    Game149: typeof import('./../src/components/GameModels/lib/game149/index.vue')['default']
    Game15: typeof import('./../src/components/GameModels/lib/game15/index.vue')['default']
    Game150: typeof import('./../src/components/GameModels/lib/game150/index.vue')['default']
    Game151: typeof import('./../src/components/GameModels/lib/game151/index.vue')['default']
    Game152: typeof import('./../src/components/GameModels/lib/game152/index.vue')['default']
    Game153: typeof import('./../src/components/GameModels/lib/game153/index.vue')['default']
    Game154: typeof import('./../src/components/GameModels/lib/game154/index.vue')['default']
    Game155: typeof import('./../src/components/GameModels/lib/game155/index.vue')['default']
    Game156: typeof import('./../src/components/GameModels/lib/game156/index.vue')['default']
    Game157: typeof import('./../src/components/GameModels/lib/game157/index.vue')['default']
    Game158: typeof import('./../src/components/GameModels/lib/game158/index.vue')['default']
    Game159: typeof import('./../src/components/GameModels/lib/game159/index.vue')['default']
    Game16: typeof import('./../src/components/GameModels/lib/game16/index.vue')['default']
    Game160: typeof import('./../src/components/GameModels/lib/game160/index.vue')['default']
    Game161: typeof import('./../src/components/GameModels/lib/game161/index.vue')['default']
    Game162: typeof import('./../src/components/GameModels/lib/game162/index.vue')['default']
    Game163: typeof import('./../src/components/GameModels/lib/game163/index.vue')['default']
    Game164: typeof import('./../src/components/GameModels/lib/game164/index.vue')['default']
    Game165: typeof import('./../src/components/GameModels/lib/game165/index.vue')['default']
    Game166: typeof import('./../src/components/GameModels/lib/game166/index.vue')['default']
    Game167: typeof import('./../src/components/GameModels/lib/game167/index.vue')['default']
    Game168: typeof import('./../src/components/GameModels/lib/game168/index.vue')['default']
    Game169: typeof import('./../src/components/GameModels/lib/game169/index.vue')['default']
    Game17: typeof import('./../src/components/GameModels/lib/game17/index.vue')['default']
    Game170: typeof import('./../src/components/GameModels/lib/game170/index.vue')['default']
    Game171: typeof import('./../src/components/GameModels/lib/game171/index.vue')['default']
    Game172: typeof import('./../src/components/GameModels/lib/game172/index.vue')['default']
    Game173: typeof import('./../src/components/GameModels/lib/game173/index.vue')['default']
    Game174: typeof import('./../src/components/GameModels/lib/game174/index.vue')['default']
    Game175: typeof import('./../src/components/GameModels/lib/game175/index.vue')['default']
    Game176: typeof import('./../src/components/GameModels/lib/game176/index.vue')['default']
    Game177: typeof import('./../src/components/GameModels/lib/game177/index.vue')['default']
    Game178: typeof import('./../src/components/GameModels/lib/game178/index.vue')['default']
    Game179: typeof import('./../src/components/GameModels/lib/game179/index.vue')['default']
    Game18: typeof import('./../src/components/GameModels/lib/game18/index.vue')['default']
    Game180: typeof import('./../src/components/GameModels/lib/game180/index.vue')['default']
    Game181: typeof import('./../src/components/GameModels/lib/game181/index.vue')['default']
    Game182: typeof import('./../src/components/GameModels/lib/game182/index.vue')['default']
    Game183: typeof import('./../src/components/GameModels/lib/game183/index.vue')['default']
    Game184: typeof import('./../src/components/GameModels/lib/game184/index.vue')['default']
    Game185: typeof import('./../src/components/GameModels/lib/game185/index.vue')['default']
    Game186: typeof import('./../src/components/GameModels/lib/game186/index.vue')['default']
    Game187: typeof import('./../src/components/GameModels/lib/game187/index.vue')['default']
    Game188: typeof import('./../src/components/GameModels/lib/game188/index.vue')['default']
    Game189: typeof import('./../src/components/GameModels/lib/game189/index.vue')['default']
    Game19: typeof import('./../src/components/GameModels/lib/game19/index.vue')['default']
    Game190: typeof import('./../src/components/GameModels/lib/game190/index.vue')['default']
    Game191: typeof import('./../src/components/GameModels/lib/game191/index.vue')['default']
    Game192: typeof import('./../src/components/GameModels/lib/game192/index.vue')['default']
    Game193: typeof import('./../src/components/GameModels/lib/game193/index.vue')['default']
    Game194: typeof import('./../src/components/GameModels/lib/game194/index.vue')['default']
    Game195: typeof import('./../src/components/GameModels/lib/game195/index.vue')['default']
    Game196: typeof import('./../src/components/GameModels/lib/game196/index.vue')['default']
    Game197: typeof import('./../src/components/GameModels/lib/game197/index.vue')['default']
    Game198: typeof import('./../src/components/GameModels/lib/game198/index.vue')['default']
    Game199: typeof import('./../src/components/GameModels/lib/game199/index.vue')['default']
    Game20: typeof import('./../src/components/GameModels/lib/game20/index.vue')['default']
    Game200: typeof import('./../src/components/GameModels/lib/game200/index.vue')['default']
    Game201: typeof import('./../src/components/GameModels/lib/game201/index.vue')['default']
    Game202: typeof import('./../src/components/GameModels/lib/game202/index.vue')['default']
    Game203: typeof import('./../src/components/GameModels/lib/game203/index.vue')['default']
    Game204: typeof import('./../src/components/GameModels/lib/game204/index.vue')['default']
    Game205: typeof import('./../src/components/GameModels/lib/game205/index.vue')['default']
    Game206: typeof import('./../src/components/GameModels/lib/game206/index.vue')['default']
    Game207: typeof import('./../src/components/GameModels/lib/game207/index.vue')['default']
    Game208: typeof import('./../src/components/GameModels/lib/game208/index.vue')['default']
    Game209: typeof import('./../src/components/GameModels/lib/game209/index.vue')['default']
    Game21: typeof import('./../src/components/GameModels/lib/game21/index.vue')['default']
    Game210: typeof import('./../src/components/GameModels/lib/game210/index.vue')['default']
    Game211: typeof import('./../src/components/GameModels/lib/game211/index.vue')['default']
    Game212: typeof import('./../src/components/GameModels/lib/game212/index.vue')['default']
    Game213: typeof import('./../src/components/GameModels/lib/game213/index.vue')['default']
    Game214: typeof import('./../src/components/GameModels/lib/game214/index.vue')['default']
    Game215: typeof import('./../src/components/GameModels/lib/game215/index.vue')['default']
    Game216: typeof import('./../src/components/GameModels/lib/game216/index.vue')['default']
    Game217: typeof import('./../src/components/GameModels/lib/game217/index.vue')['default']
    Game218: typeof import('./../src/components/GameModels/lib/game218/index.vue')['default']
    Game22: typeof import('./../src/components/GameModels/lib/game22/index.vue')['default']
    Game23: typeof import('./../src/components/GameModels/lib/game23/index.vue')['default']
    Game24: typeof import('./../src/components/GameModels/lib/game24/index.vue')['default']
    Game25: typeof import('./../src/components/GameModels/lib/game25/index.vue')['default']
    Game26: typeof import('./../src/components/GameModels/lib/game26/index.vue')['default']
    Game27: typeof import('./../src/components/GameModels/lib/game27/index.vue')['default']
    Game28: typeof import('./../src/components/GameModels/lib/game28/index.vue')['default']
    Game29: typeof import('./../src/components/GameModels/lib/game29/index.vue')['default']
    Game30: typeof import('./../src/components/GameModels/lib/game30/index.vue')['default']
    Game31: typeof import('./../src/components/GameModels/lib/game31/index.vue')['default']
    Game32: typeof import('./../src/components/GameModels/lib/game32/index.vue')['default']
    Game33: typeof import('./../src/components/GameModels/lib/game33/index.vue')['default']
    Game34: typeof import('./../src/components/GameModels/lib/game34/index.vue')['default']
    Game35: typeof import('./../src/components/GameModels/lib/game35/index.vue')['default']
    Game36: typeof import('./../src/components/GameModels/lib/game36/index.vue')['default']
    Game37: typeof import('./../src/components/GameModels/lib/game37/index.vue')['default']
    Game38: typeof import('./../src/components/GameModels/lib/game38/index.vue')['default']
    Game39: typeof import('./../src/components/GameModels/lib/game39/index.vue')['default']
    Game4: typeof import('./../src/components/GameModels/lib/game4/index.vue')['default']
    Game40: typeof import('./../src/components/GameModels/lib/game40/index.vue')['default']
    Game41: typeof import('./../src/components/GameModels/lib/game41/index.vue')['default']
    Game42: typeof import('./../src/components/GameModels/lib/game42/index.vue')['default']
    Game43: typeof import('./../src/components/GameModels/lib/game43/index.vue')['default']
    Game44: typeof import('./../src/components/GameModels/lib/game44/index.vue')['default']
    Game45: typeof import('./../src/components/GameModels/lib/game45/index.vue')['default']
    Game46: typeof import('./../src/components/GameModels/lib/game46/index.vue')['default']
    Game47: typeof import('./../src/components/GameModels/lib/game47/index.vue')['default']
    Game48: typeof import('./../src/components/GameModels/lib/game48/index.vue')['default']
    Game50: typeof import('./../src/components/GameModels/lib/game50/index.vue')['default']
    Game51: typeof import('./../src/components/GameModels/lib/game51/index.vue')['default']
    Game52: typeof import('./../src/components/GameModels/lib/game52/index.vue')['default']
    Game55: typeof import('./../src/components/GameModels/lib/game55/index.vue')['default']
    Game56: typeof import('./../src/components/GameModels/lib/game56/index.vue')['default']
    Game57: typeof import('./../src/components/GameModels/lib/game57/index.vue')['default']
    Game59: typeof import('./../src/components/GameModels/lib/game59/index.vue')['default']
    Game6: typeof import('./../src/components/GameModels/lib/game6/index.vue')['default']
    Game60: typeof import('./../src/components/GameModels/lib/game60/index.vue')['default']
    Game61: typeof import('./../src/components/GameModels/lib/game61/index.vue')['default']
    Game64: typeof import('./../src/components/GameModels/lib/game64/index.vue')['default']
    Game65: typeof import('./../src/components/GameModels/lib/game65/index.vue')['default']
    Game66: typeof import('./../src/components/GameModels/lib/game66/index.vue')['default']
    Game67: typeof import('./../src/components/GameModels/lib/game67/index.vue')['default']
    Game68: typeof import('./../src/components/GameModels/lib/game68/index.vue')['default']
    Game69: typeof import('./../src/components/GameModels/lib/game69/index.vue')['default']
    Game70: typeof import('./../src/components/GameModels/lib/game70/index.vue')['default']
    Game71: typeof import('./../src/components/GameModels/lib/game71/index.vue')['default']
    Game72: typeof import('./../src/components/GameModels/lib/game72/index.vue')['default']
    Game73: typeof import('./../src/components/GameModels/lib/game73/index.vue')['default']
    Game74: typeof import('./../src/components/GameModels/lib/game74/index.vue')['default']
    Game76: typeof import('./../src/components/GameModels/lib/game76/index.vue')['default']
    Game77: typeof import('./../src/components/GameModels/lib/game77/index.vue')['default']
    Game78: typeof import('./../src/components/GameModels/lib/game78/index.vue')['default']
    Game79: typeof import('./../src/components/GameModels/lib/game79/index.vue')['default']
    Game8: typeof import('./../src/components/GameModels/lib/game8/index.vue')['default']
    Game80: typeof import('./../src/components/GameModels/lib/game80/index.vue')['default']
    Game81: typeof import('./../src/components/GameModels/lib/game81/index.vue')['default']
    Game82: typeof import('./../src/components/GameModels/lib/game82/index.vue')['default']
    Game83: typeof import('./../src/components/GameModels/lib/game83/index.vue')['default']
    Game84: typeof import('./../src/components/GameModels/lib/game84/index.vue')['default']
    Game85: typeof import('./../src/components/GameModels/lib/game85/index.vue')['default']
    Game9: typeof import('./../src/components/GameModels/lib/game9/index.vue')['default']
    Game92: typeof import('./../src/components/GameModels/lib/game92/index.vue')['default']
    Game95: typeof import('./../src/components/GameModels/lib/game95/index.vue')['default']
    Game96: typeof import('./../src/components/GameModels/lib/game96/index.vue')['default']
    Game97: typeof import('./../src/components/GameModels/lib/game97/index.vue')['default']
    Game99: typeof import('./../src/components/GameModels/lib/game99/index.vue')['default']
    GameModels: typeof import('./../src/components/GameModels/index.vue')['default']
    Header: typeof import('./../src/components/Header/index.vue')['default']
    Mask: typeof import('./../src/components/GameModels/lib/component/mask.vue')['default']
    QuitCofirm: typeof import('./../src/components/GameModels/lib/component/quitCofirm.vue')['default']
    ReportDrawer: typeof import('./../src/components/ReportDrawer/index.vue')['default']
    ResultPage: typeof import('./../src/components/GameModels/resultPage.vue')['default']
    ResultPage2: typeof import('./../src/components/GameModels/lib/component/resultPage2.vue')['default']
    ResultPage25: typeof import('./../src/components/GameModels/lib/component/resultPage25.vue')['default']
    ResultPageCmpLib: typeof import('./../src/components/GameModels/lib/component/resultPageCmpLib.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SettingPage: typeof import('./../src/components/GameModels/settingPage.vue')['default']
    SettingPage2: typeof import('./../src/components/GameModels/lib/component/settingPage2.vue')['default']
    SettingPageCmpLib: typeof import('./../src/components/GameModels/lib/component/settingPageCmpLib.vue')['default']
    Tab: typeof import('./../src/components/tab.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
