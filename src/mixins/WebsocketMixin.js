import { ElMessage } from 'element-plus';
import { getUserInfo } from '/@/utils/user';
import { getpsUserById, getpsUserStartTrain, postPsUserAddForHome, getpsUserCheckUnfinishedMeasure } from '/@/api/index';

let isInit = false;
let userId = '';

export const WebsocketMixin = {
  data() {
    return {
      socket: null,
      lockReconnect: false, //是否真正建立连接
      heartCheck: null,
    };
  },
  mounted() {
    // 初始化websocket
    this.initWebSocket();
    this.heartCheckFun();
  },
  destroyed: function () {
    // 离开页面生命周期函数
    this.websocketOnclose();
  },
  methods: {
    initWebSocket: function () {
      console.log('开始WebSocket连接...');
      // WebSocket与普通的请求所用协议有所不同，ws等同于http，wss等同于https
      var userInfo = JSON.parse(getUserInfo());
      if (userInfo && !isInit && userId != userInfo.id) {
        var url = import.meta.env.VITE_APP_WSS_URL + '/websocket/' + userInfo.id;
        console.log(url);
        this.websock = new WebSocket(url);
        this.websock.onopen = this.websocketOnopen;
        this.websock.onerror = this.websocketOnerror;
        this.websock.onmessage = this.websocketOnmessage;
        this.websock.onclose = this.websocketOnclose;
        isInit = true;
      }
    },
    websocketOnopen: function () {
      console.log('WebSocket连接成功');
      //心跳检测重置
      this.heartCheck.reset().start();
    },
    websocketOnerror: function (e) {
      // console.log('WebSocket连接发生错误')
      this.reconnect();
    },
    websocketOnclose: function (e) {
      // console.log('WebSocket连接关闭')
      isInit = false;
      console.log(111);
      this.websock.close();
      // this.reconnect();
    },
    websocketSend(text) {
      // 数据发送
      try {
        this.websock.send(text);
      } catch (err) {
        console.log('send failed (' + err.code + ')');
      }
    },
    reconnect() {
      var that = this;
      if (that.lockReconnect) return;
      that.lockReconnect = true;
      //没连接上会一直重连，设置延迟避免请求过多 10s连接一次
      setTimeout(function () {
        that.initWebSocket();
        that.lockReconnect = false;
      }, 10000);
    },
    websocketOnmessage: function (msg) {
      isInit = true;
      let strs = msg.data.split('&&');
      let that = this;
      // 主控推主控
      if (strs[0] == '101') {
        let message = JSON.parse(strs[1]);
        if (message.syncMode && message.doctorId && message.terminalDoctorId) {
          if (message.type == '1' || message.type == '0') {
            this.$router.push({
              path: 'info',
              // name: 'answerPage',
              query: {
                userId: message.psUserId,
                type: message.type,
                doctorId: message.doctorId,
                terminalDoctorId: message.terminalDoctorId,
                isTerminal: true,
                resultId: message.dxResultId,
                packageMark: message.packageMark,
              },
            });
          } else if (message.type == '2') {
            // this.$router.push({
            //   name: 'answerPage',
            //   params: { resultId: message.dxResultId, type: message.type, doctorId: message.doctorId, terminalDoctorId: message.terminalDoctorId, isTerminal: true }
            // })
          }
        } else {
          if (message.type == '1' || message.type == '0') {
            this.$router.push({
              path: 'answerpage/AnswerPage',
              // name: 'answerPage',
              query: {
                userId: message.psUserId,
                type: message.type,
                doctorId: message.doctorId,
                terminalDoctorId: message.terminalDoctorId,
                isTerminal: true,
                resultId: message.dxResultId,
                packageMark: message.packageMark,
              },
            });
            // this.$router.push({
            //   name: 'answerPage',
            //   params: { userId: message.psUserId, type: message.type,packageMark: message.packageMark ,isTerminal:message.isTerminal, resultId: message.dxResultId }
            // })
          } else if (message.type == '2') {
            this.$router.push({
              name: 'answerPage',
              params: { resultId: message.dxResultId, type: message.type },
            });
          }
        }
      }
      // 中控推终端
      if (strs[0] == '102') {
        let message = JSON.parse(strs[1]);
        console.log(message);
        // doctorId 主控端id
        // measureIds 量表id
        // psUserId 患者id
        // terminalDoctorId 终端端id
        // type 类型
        if (message.measureIds && message.terminalDoctorId) {
          this.$confirm({
            title: '提示',
            content: '主机推送题目信息，点击【确定】开始答题',
            okText: '确定',
            cancelText: '取消',
            async onOk() {
              const user = await that.handleSearch(message.psUserId);
              console.log(user);
              console.log(message);
              const pageData = await that.goAnswerPage(user, message);
              console.log(pageData);
              await that.pushMeasureAnswerPage(pageData, message.doctorId);
              // that.$router.push({
              //   name: 'answerPage',
              //   params: { userId: message.psUserId, measureIds: message.measureIds, type: message.type, doctorId: message.doctorId, terminalDoctorId: message.terminalDoctorId, isTerminal: true,packageMark: message.packageMark }
              // })
            },
            onCancel() {},
          });
        }
      }
      //训练游戏
      if (strs[0] == '201') {
        //训练游戏小游戏的跳转地址
        let trainPath = strs[1];
        //小游戏对应的记录id
        let cogTrainResultId = strs[2];
        that.$router.push({
          path: '../cognitive/info',
          query: { gameName: trainPath, id: cogTrainResultId },
        });
        // that.$router.push('/sh/cognitive/games/index?gameName=' + trainPath + '&id=' + cogTrainResultId)
      }
      //训练游戏终端推送
      if (strs[0] == '202') {
        //训练游戏小游戏的跳转地址
        let trainIds = strs[1];
        let userId = strs[2];
        let terminalId = strs[3];
        if (trainIds) {
          this.$confirm({
            title: '提示',
            content: '主机推送训练游戏，点击【确定】开始训练',
            okText: '确定',
            cancelText: '取消',
            onOk: async () => {
              const user = await this.handleSearch(userId);
              this.pushAnswerPage(user, trainIds, terminalId);
            },
            onCancel() {},
          });
        }
      }
      //训练游戏终端推送
      if (strs[0] == '302') {
        //训练游戏小游戏的跳转地址
        let trainPath = strs[1];
        //小游戏对应的记录id
        let cogTrainResultId = strs[2];
        if (trainPath && cogTrainResultId) {
          this.$confirm({
            title: '提示',
            content: '主机推送训练游戏，点击【确定】开始训练',
            okText: '确定',
            cancelText: '取消',
            onOk() {
              that.$router.push('/games/' + trainPath + '?id=' + cogTrainResultId);
            },
            onCancel() {},
          });
        }
      }
      // 主控端推给终端
      if (strs[0] == '401') {
        let message = JSON.parse(strs[1]);
        if (message.questionNum) {
          this.onChangeSlider(message.questionNum);
        }
        if (message.isFinish) {
          // this.toDashboard()
          this.loadData();
        }
        if (message.type == '1' || message.type == '0') {
          this.hasNext = message.hasNext;
          this.loadData();
        }
      }
      // 终端推给主控端
      if (strs[0] == '402') {
        let message = JSON.parse(strs[1]);
        console.log(message);
        if (message.type == '1' || message.type == '0') {
          if (message.hasNext) {
            this.quesionStart();
          } else {
            this.$router.push({
              name: 'answerPage',
              params: {
                userId: message.userId,
                type: message.type,
                doctorId: message.doctorId,
                terminalDoctorId: message.terminalDoctorId,
                isControl: true,
                packageMark: message.packageMark,
              },
            });
          }
        } else {
          if (message.hasNext) {
            this.quesionStart();
          } else {
            this.$router.push({
              name: 'answerPage',
              params: {
                resultId: message.resultId,
                type: message.type,
                doctorId: message.doctorId,
                terminalDoctorId: message.terminalDoctorId,
                isControl: true,
              },
            });
          }
        }

        if (message.questionNum) {
          this.setAnswer(message.questionNum, message.imgUrl);
        }
      }
      //心跳检测重置
      this.heartCheck.reset().start();
    },
    goAnswerPage(formData, message) {
      console.log(message);
      formData.measureIds = message.measureIds.split(',');
      return getpsUserCheckUnfinishedMeasure(formData).then((res) => {
        if (res.success) {
          return formData;
        } else {
          if (res.code == 10001) {
            this.$confirm({
              title: '提示',
              content: res.message,
              okText: '新增',
              cancelText: '覆盖',
              onOk() {
                formData.id = null;
                return formData;
              },
              onCancel() {
                return formData;
              },
            });
          } else {
            ElMessage({
              message: res.message,
              type: 'error',
            });
            return false;
          }
        }
      });
    },
    pushMeasureAnswerPage(formData, terminalId) {
      console.log('kaishi');
      console.log(this.$route.params);
      console.log(this.$route.params.doctorId);
      let httpurl = this.url.addForHome;
      formData.answerType = 0;
      formData.terminalId = terminalId;
      return postPsUserAddForHome(formData).then((res) => {
        if (res.success) {
          return true;
        } else {
          ElMessage({
            message: res.message,
            type: 'error',
          });
          return false;
        }
      });
    },
    handleSearch(userId) {
      return getpsUserById({ id: userId }).then((res) => {
        if (res.success) {
          return res.result;
        } else {
          return false;
        }
      });
    },
    pushAnswerPage(formData, trainIds, terminalId) {
      let httpurl = this.url.startTrain;
      formData.trainIds = trainIds.split(',');
      formData.terminalId = terminalId;
      getpsUserStartTrain(formData).then((res) => {
        if (res.success) {
          return true;
        } else {
          ElMessage({
            message: res.message,
            type: 'error',
          });
        }
      });
    },
    pushTerminalOpenUserForm(measureIds, terminalId) {
      this.$refs.UserForm.pushTerminalLoad(measureIds.split(','), terminalId);
      this.$refs.UserForm.title = '添加测试者信息';
    },
    pushTerminalOpenUserForm2(trainIds) {
      this.$refs.UserForm2.loadMeasureIds(trainIds.split(','));
      this.$refs.UserForm2.title = '添加测试者信息';
    },
    heartCheckFun() {
      var that = this;
      //心跳检测,每20s心跳一次
      that.heartCheck = {
        timeout: 30000,
        timeoutObj: null,
        serverTimeoutObj: null,
        reset: function () {
          clearTimeout(this.timeoutObj);
          //clearTimeout(this.serverTimeoutObj);
          return this;
        },
        start: function () {
          // console.log("心跳检测...")
          var self = this;
          this.timeoutObj = setTimeout(function () {
            //这里发送一个心跳，后端收到后，返回一个心跳消息，
            //onmessage拿到返回的心跳就说明连接正常
            that.websocketSend('HeartBeat');
          }, this.timeout);
        },
      };
    },
  },
};
