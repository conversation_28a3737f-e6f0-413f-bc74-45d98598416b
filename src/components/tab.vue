<template>
  <div class="tab h-70px flex">
    <div :class="'tab-item ' + (current == index ? 'current' : '')" @click="changeTab(index)" :key="index" v-for="(item, index) of tabs">
      {{ item }}
    </div>
  </div>
</template>

<script setup>
const props = defineProps(['tabs', 'current']);
const emit = defineEmits(['change']);
function changeTab (index) {
  emit('change', index);
}
</script>

<style lang="scss">
.tab {
  padding: 0 68px 50px;
  height: 100px;
  overflow: hidden;
  overflow-x: auto;
  width: 1600px;

  &::-webkit-scrollbar {
    width: 10px * 0.7;
    height: 19px * 0.7;
  }

  &::-webkit-scrollbar-thumb {
    background: #f1d297;
  }

  &::-webkit-scrollbar-track {
    background: rgba(204, 204, 204, 0);
  }
  &-item {
    height: 70px;
    font-family: PingFangSC, 'PingFang SC';
    font-weight: 600;
    font-size: 32px;
    color: #1393dd;
    line-height: 70px;
    padding: 0 31px;
    // background-image: url('/static/icon/tab-bg.png');
    // background-size: 100% 100%;
    background-color: #fff;
    box-shadow: 1px 10px 1px #95caf1;
    border-radius: 10px;
    margin-right: 51px;
    // min-width: 8em;
    white-space: nowrap;
    display: flex;
    justify-content: center;
  }

  &-item:active {
    // background-image: url('../assets/images/tab-bg-cover.png');
    background-color: #e5f3fe;
  }

  &-item:last-child {
    margin: 0;
  }

  .current {
    // background-image: url('../assets/images/tab-bg-cover.png');
    background-color: #e5f3fe;
  }
}
</style>
