.carousel-page {
  height: 100%;

  .card {
    &-item {
      position: relative;

      &-cover {
        border-radius: 20px 20px 0 0 ;
        width: 100%;
        cursor: pointer;
        height: 245px;
      }

      &-label {
        width: 294px;
        position: absolute;
        bottom: 0;
        text-align: center;
        font-family: PingFangSC, 'PingFang SC';
        font-weight: 400;
        font-size: 24px;
        color: #000;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        background: #BEE7FF;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 5px 0 #85CCF5;

        // white-space: nowrap;
        // overflow: hidden;
      }
    }
  }

  .single {
    display: grid;
    grid-template-columns: repeat(4, 294px);
    grid-template-rows: repeat(1, 280px);
    grid-column-gap: 36px;
  }

  .double {
    display: grid;
    grid-template-columns: repeat(4, 294px);
    grid-template-rows: repeat(2, 280px);
    grid-gap: 36px;
  }
}
