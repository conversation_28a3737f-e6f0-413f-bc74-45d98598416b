<template>
  <el-carousel
    ref="carousel"
    class="carousel-content w-full h-full"
    :autoplay="false"
    indicator-position="outside"
    arrow="always"
    height="100%"
    :loop="false"
  >
    <el-carousel-item v-for="(item, index) of list" :key="index">
      <div class="carousel-page flex items-center justify-center">
        <div :class="'card ' + (single ? 'single' : 'double')">
          <div class="card-item flex flex-col" @click="goInfo(citem.id)" :key="citem.id" v-for="citem of item">
            <img class="card-item-cover" :src="getAssetsFile('science', citem.cover)" alt="" />
            <span class="card-item-label w-full">{{ citem.label }}</span>
          </div>
        </div>
      </div>
    </el-carousel-item>
  </el-carousel>
</template>

<script setup>
  import { getAssetsFile } from '/@/utils/tool';

  const props = defineProps(['list', 'single']);
  const emit = defineEmits(['info']);
  const carousel = ref(null);
  // onActivated(()=>{
  //   carousel.value.setActiveItem(0);
  // })
  // onDeactivated(() => {
  //   carousel.value.setActiveItem(0);
  // })
  function goInfo(id) {
    emit('info', id);
  }
</script>

<style lang="scss">
  @import './style.scss';

  /* 自定义Carousel右箭头 */
  .carousel-content .el-carousel__arrow--right {
    color: #1e79ae;
    font-size: 72px;
    font-weight: 700;
    background-color: transparent; /* 箭头颜色 */
  }

  /* 自定义Carousel左箭头 */
  .carousel-content .el-carousel__arrow--left {
    color: #1e79ae;
    font-size: 72px;
    font-weight: 700;
    background-color: transparent; /* 箭头颜色 */
  }
</style>
