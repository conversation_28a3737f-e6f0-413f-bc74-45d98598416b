.header {
  margin: 50px auto 35px;
  position: relative;

  .info-realname-box{
    display: flex;
    align-items: center;
  }

  .info-realname{
    max-width: 7em;
    display: inline-block;
    overflow: hidden; 
    white-space: nowrap; 
    text-overflow: ellipsis; 
  }

  &-icon {
    position: absolute;
    left: 0;

    &-item {
      width: 100px;
      height: 100px;
      background-size: 100% 100%;
    }

    .back {
      background-image: url('/@/assets/images/back.png');
    }

    .home {
      background-image: url('/@/assets/images/home.png');
    }

    .back:active {
      background-image: url('/@/assets/images/back-active.png');
    }

    .home:active {
      background-image: url('/@/assets/images/home-active.png');
    }
  }

  &-title {
    &-content{
      background-image: url('/@/assets/images/title_bg.png');
      background-size: 100% 100%;
      font-family: PingFangSC, 'PingFang SC';
      font-weight: 600;
      font-size: 2.2em;
      color: #1e79ae;
      line-height: 100px;
      text-align: center;
    }
  }

  .header-actions{
    margin-right: 30px;

    .header-actions-item{
      display: flex;
      align-items: center;

      .header-actions-item-title{
        margin-left: 10px;
        font-family: PingFangSC, 'PingFang SC';
        font-weight: 400;
        font-size: 24px;
        color: #fff;
      }
    }
  }

  .info {
    font-family: PingFangSC, 'PingFang SC';
    font-weight: 400;
    font-size: 24px;
    color: #fff;
    line-height: 33px;
    position: absolute;
    right: 0;

    .cursor-pointer{
      cursor: pointer;
    }

    &-login {
      height: 47px;
      background: #00a4ff;
      border-radius: 31px;
      padding: 0 29px;
      text-align: center;
      line-height: 47px;
    }

    &-wifi {
      width: 24px;
      height: 24px;
      margin: 0 17px 0 58px;
    }
  }
}
