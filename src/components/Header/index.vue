<template>
  <div class="header w-1758px h-100px flex justify-center items-center">
    <div class="header-icon">
      <div class="header-icon-item back" @click="back()" v-if="status == 'back'"></div>
      <div class="header-icon-item home" @click="home()" v-else-if="status == 'home'"></div>
    </div>
    <div class="header-title ">
      <div class="header-title-content w-273px h-100px">
        {{ title }}
      </div>
    </div>
    <div class="info flex items-center">
      <div class="header-actions">
        <div class="header-actions-item">
          <el-switch size="big" v-model="colorWeak" @change="onColorWeak" />
          <div class="header-actions-item-title">色弱模式</div>
        </div>
      </div>
      <span class="info-realname-box">
        <span class="info-realname">{{userInfo.realname}}</span>
        <span class="info-login cursor-pointer" @click="login()">切换账号</span>
      </span>
      <img class="info-wifi" src="/@/assets/images/wifi.png" alt="" />
      <span class="info-date">{{ time }}</span>
    </div>
  </div>
</template>

<script setup>
import { getNowTime } from '/@/utils/tool';
import { getUserInfo, clearUserInfo } from '/@/utils/user';

import { userLogout } from '/@/api/index';
const props = defineProps(['title', 'status']);
const router = useRouter();
function back () {
  router.back();
}
function home () {
  router.back();
}
const colorWeak = ref(false);
function onColorWeak (val) {
  colorWeak.value = val;
  val ? document.body.classList.add('colorWeak') : document.body.classList.remove('colorWeak')
}
function login () {
  router.push('/login/login');
}
function loginout (params) {
  userLogout().then((result) => {
    userInfo.value = {}
    clearUserInfo().then(_ => {
      getUserInfoFun()
      ElMessage({
        message: '切换游客模式成功',
        type: 'success',
      });
    })
  })
}
function getUserInfoFun (params) {
  let _userInfo = JSON.parse(getUserInfo())
  if (_userInfo) {
    userInfo.value = _userInfo
  }
}
function setTime () {
  time.value = getNowTime()
  setTimeout(() => {
    setTime()
  }, 60000)
}
const time = ref('00:00');
const userInfo = ref({})
onMounted(() => {
  if (document.body.className.indexOf('colorWeak') == -1) {
    colorWeak.value = false;
  } else {
    colorWeak.value = true;
  }
  getUserInfoFun()
  setTime()
})
watch(() => router.currentRoute.value.path, (n, o) => {
  if (document.body.className.indexOf('colorWeak') == -1) {
    colorWeak.value = false;
  } else {
    colorWeak.value = true;
  }
  getUserInfoFun()
})
  // back返回上一页 back返回主页
  // login登录 register注册
  // const type = ref('register');
  // import path from 'path';
  // import { ref, onMounted } from 'vue';
  // // import { useUserStore } from './store/index';
  // import { useAppStore } from '/@/store/index';
  // // 使用 useUserStore 并返回定义模块的相关状态以及方法
  // const useApp = useAppStore();
  // const emit = defineEmits(['back']);

  // const menus = ref([
  //   { name: '首页', path: '', type: 'normal' },
  //   { name: '极速融资', path: '', type: 'normal' },
  //   { name: '技术需求', path: '', type: 'normal' },
  //   { name: 'BP评测辅导', path: '', type: 'url' },
  // ]);
  // onMounted(() => {});

  // function changeMenu(item, index) {
  //   useApp.current = index;
  // }
</script>

<style lang="scss">
@import './style.scss';
</style>
