<template>
  <div class="page-content page-content4">
    <div class="content">
      <div class="number-bg"></div>
      <slot></slot>
      <div class="number" v-if="fraction">
        <div class="value">{{fraction}}</div>
        <div class="unit">分</div>
      </div>
      <div class="info">{{info}}</div>
    </div>
    <div class="actions">
      <img src="/static/game_assets/game1/images/btn4.png" class="btn" alt="" @click="reset">
      <img src="/static/game_assets/game1/images/btn6.png" class="btn" alt="" @click="nextPage">
    </div>
  </div>
</template>

<script>
export default {
  props: ['info', 'fraction'],
  methods: {
    nextPage (params) {
      this.$emit('nextPage', params)
    },
    reset (params) {
      this.$emit('reset', params)
    }
  }
}
</script>

<style>
</style>