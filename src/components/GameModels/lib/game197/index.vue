<template>
  <div class="game197-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="合成数字" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请根据提示要求，选择对应等式:</p>
        <p class="synopsis-content">2、训练结束会出现成绩单;</p>
        <p class="synopsis-content">3、本训练难度为B（普通）。</p>
      </div>
    </settingPageLib>

    <div class="game-content" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game206/title_bg.png" />
        <span class="title-text">合成数字-B（普通）</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game196/content_bg.png" />
        <img class="left-icon" src="/static/game_assets/game196/img1.png" />
        <img class="right-icon" src="/static/game_assets/game196/img2.png" />
        <div class="content-title">{{`如何得到${currect.sum}`}}</div>

        <div class="content-main">
          <div class="content-item" v-for="(item, index) in currect.answerList" :key="index + 'item'" @click="chooseItem(item.index)">
            <img class="item-bg" src="/static/game_assets/game196/btn_bg_1.png" />
            <img class="item-bg" v-if="answer === item.index" src="/static/game_assets/game196/btn_bg_2.png" />
            <span class="item-text">{{item.text}}</span>
          </div>
        </div>

        <div class="content-img store">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_1.png" />
          <span class="item-text">题目分数</span>
          <span class="item-num">{{store}}</span>
        </div>

        <div class="content-img number">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_2.png" />
          <span class="item-text">题目数量</span>
          <span class="item-num">{{number}}</span>
        </div>

        <div class="content-img time">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_1.png" />
          <span class="item-text">训练用时</span>
          <span class="item-time">{{time}}</span>
        </div>
      </div>

      <div class="footer">
        <img v-if="answer" class="img" src="/static/game_assets/common/confirm.png" @click="confirm" />
      </div>
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'

export default {
  name: 'game197',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      level: 2,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isPlay: false,
      show: false,

      answer: 0,
      currect: {
        sum: 0,
        answer: [],
        answerList: []
      },


      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
    goHome () {
      this.pause()
      this.$router.go(-1)
    },

    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start () {
      this.level = this.info.level || 2
      this.status = 3
      // this.timing()
      this.startProcess()
      this.play()
    },

    startProcess () {
      this.currect.answerList = []
      let list = []
      while (this.currect.answerList.length < 4) {
        const num1 = api.randomNum(11, 20)
        const num2 = api.randomNum(0, 10)
        if (!list.includes(num1 + num2)) {
          this.currect.answerList.push({
            text: `${num1} + ${num2}`,
            index: list.length + 1
          })
          list.push(num1 + num2)
        }
      }
      this.currect.sum = list[0]
      this.currect.answer = 1
      this.currect.answerList = api.shuffle(this.currect.answerList)
      this.number++
    },

    chooseItem (index) {
      this.answer = index
    },

    confirm () {
      if (this.currect.answer === this.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      this.store = 10 * this.succesNum
      if (this.number >= 10) {
        this.submit()
      } else {
        this.answer = 0
        this.startProcess()
      }
    },

    stop () {
      this.pause()
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.pause()
      this.isStop = true
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = 0
      this.currect = {
        sum: 0,
        answer: 0,
        answerList: []
      },
        this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game197-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game126/bg.png');
  }

  .game-synopsis {
    width: 1605px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 40px * 0.7;
      left: 105px * 0.7;
      width: 175px * 0.7;
      display: flex;
      justify-content: space-between;

      .top-icon {
        width: 80px * 0.7;
        cursor: pointer;
      }
    }

    .title {
      position: absolute;
      top: 0;
      width: 932px * 0.7;
      height: 125px * 0.7;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px * 0.7;
        height: 125px * 0.7;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 28px * 0.7 0 56px * 0.7 0;
        font-size: 36px * 0.7;
        line-height: 36px * 0.7;
        text-align: center;
        color: #1b1d2d;
      }
    }

    .content {
      position: relative;
      width: 1786px * 0.7;
      height: 922px * 0.7;
      margin-top: 158px * 0.7;
      margin-left: 76px * 0.7;
      padding: 160px * 0.7 618px * 0.7 326px * 0.7 413px * 0.7;

      .content-bg {
        position: absolute;
        top: 0;
        left: 18px * 0.7;
        width: 1655px * 0.7;
      }

      .left-icon {
        position: absolute;
        bottom: 87px * 0.7;
        left: 0;
        width: 440px * 0.7;
      }

      .right-icon {
        position: absolute;
        bottom: 74px * 0.7;
        right: 0;
        width: 541px * 0.7;
      }

      .content-title {
        position: relative;
        width: 100%;
        padding-bottom: 83px * 0.7;
        font-size: 44px * 0.7;
        line-height: 44px * 0.7;
        text-align: center;
        font-weight: 500;
        color: #1b1d2d;
      }

      .content-main {
        position: relative;
        width: 100%;
        height: 310px * 0.7;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;

        .content-item {
          position: relative;
          width: 353px * 0.7;
          height: 138px * 0.7;
          cursor: pointer;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 353px * 0.7;
          }

          .item-text {
            position: relative;
            display: block;
            font-size: 77px * 0.7;
            line-height: 138px * 0.7;
            text-align: center;
            color: #1b1d2d;
          }
        }
      }

      .content-img {
        position: absolute;
        right: 123px * 0.7;
        width: 369px * 0.7;
        height: 219px * 0.7;
        padding: 28px * 0.7 42px * 0.7;
        display: flex;

        .item-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 369px * 0.7;
        }

        .item-text {
          position: relative;
          display: inline-block;
          width: 36px * 0.7;
          font-size: 36px * 0.7;
          line-height: 40px * 0.7;
          text-align: center;
          font-weight: 500;
          color: #253000;
        }

        .item-num {
          position: relative;
          display: block;
          width: 100%;
          height: 100%;
          padding-left: 37px * 0.7;
          font-size: 92px * 0.7;
          line-height: 170px * 0.7;
          text-align: center;
          color: #312b4f;
          font-family: Impact;
        }

        .item-time {
          position: relative;
          display: block;
          width: 100%;
          height: 100%;
          padding-left: 37px * 0.7;
          font-size: 48px * 0.7;
          line-height: 170px * 0.7;
          text-align: center;
          color: #312b4f;
          font-family: Impact;
        }
      }

      .store {
        top: 63px * 0.7;
      }

      .number {
        top: 290px * 0.7;
      }

      .time {
        top: 518px * 0.7;
      }
    }

    .footer {
      position: absolute;
      bottom: 72px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1480px * 0.7;

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }
}
</style>