<template>
  <div class="game129-page">
    <div class="page-bg"></div>
    <!-- <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio> -->
    <settingPageLib title="色彩斑斓" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、通过学习并对比来掌握对颜色的认知。</p>
        <p class="synopsis-content">2、根据提示选择与其他不同的颜色。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="top">
        <div class="top-left">
          <div class="left-item">
            <img class="item-icon" src="/static/game_assets/common/clock.png" />
            <span class="item-text">时间：{{countdown}}</span>
          </div>

          <div class="left-item">
            <img class="item-icon" src="/static/game_assets/game83/icon_2.png" />
            <span class="item-text">分数：{{store}}</span>
          </div>
        </div>
      </div>

      <div class="content">
        <div :class="['content-item', 'content-item-' + number]" :style="{'background': currentIndex === item ? current[1] : current[0]}" v-for="item in (number + 1) * (number + 1)" :key="item + 'item'" @click="chooseItem(item)"></div>
      </div>

      <img class="footer-img" src="/static/game_assets/common/stop.png" @click="stop" />
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game129',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 1,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isPlay: false,
      isRePlay: false,
      show: false,
      index: 0,
      current: [],
      currentIndex: 0,
      countdown: 60,

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.countdown = 60 - this.second

        if (this.countdown <= 0) {
          this.submit()
        } else {
          this.timing()
        }
      }, 1000)
    },

    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      this.current = api.getRandomArray(api.getRandomArray(data.data, 1)[0], 2)
      this.currentIndex = api.randomNum(1, (this.number + 1) * (this.number + 1))

      if (this.index < 5) {
        this.index++
      } else {
        this.number < 9 ? this.number++ : ''
        this.index = 0
      }
    },

    chooseItem (item) {
      if (item === this.currentIndex) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      this.store = (2 * this.succesNum) > 100 ? 100 : (2 * this.succesNum)
      this.startProcess()
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 1
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.current = []
      this.currentIndex = 0
      this.index = 0
      this.countdown = 60
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game129-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game83/bg.png');
  }

  .game-synopsis {
    width: 707px * 0.7;
    height: 444px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fffef3;
    border: 2px * 0.7 solid #014747;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      padding: 30px * 0.7 230px * 0.7 0 70px * 0.7;
      display: flex;
      justify-content: space-between;

      .top-left {
        display: flex;
        justify-content: space-between;
        width: 480px * 0.7;

        .left-item {
          font-size: 0;
          .item-icon {
            width: 60px * 0.7;
            height: 60px * 0.7;
            vertical-align: middle;
          }

          .item-text {
            display: inline-block;
            padding-left: 14px * 0.7;
            font-size: 37px * 0.7;
            line-height: 60px * 0.7;
            font-weight: 600;
            vertical-align: middle;
          }
        }
      }

      .top-right {
        display: flex;
        justify-content: space-between;
        width: 380px * 0.7;

        .right-icon {
          width: 60px * 0.7;
          height: 60px * 0.7;
        }
      }
    }

    .content {
      position: relative;
      width: 726px * 0.7;
      height: 726px * 0.7;
      margin-left: 64px * 0.7;
      margin-top: 62px * 0.7;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;

      .content-item {
        border-radius: 2px * 0.7;
        border-style: solid;
        border-color: #fff;
        cursor: pointer;
      }

      .content-item-1 {
        width: 337px * 0.7;
        height: 337px * 0.7;
        margin: 13px * 0.7;
        border-width: 3px * 0.7;
      }

      .content-item-2 {
        width: 216px * 0.7;
        height: 216px * 0.7;
        margin: 13px * 0.7;
        border-width: 3px * 0.7;
      }

      .content-item-3 {
        width: 155px * 0.7;
        height: 155px * 0.7;
        margin: 13px * 0.7;
        border-width: 3px * 0.7;
      }

      .content-item-4 {
        width: 121px * 0.7;
        height: 121px * 0.7;
        margin: 12px * 0.7;
        border-width: 2px * 0.7;
      }

      .content-item-5 {
        width: 97px * 0.7;
        height: 97px * 0.7;
        margin: 12px * 0.7;
        border-width: 2px * 0.7;
      }

      .content-item-6 {
        width: 81px * 0.7;
        height: 81px * 0.7;
        margin: 11px * 0.7;
        border-width: 2px * 0.7;
      }

      .content-item-7 {
        width: 68px * 0.7;
        height: 68px * 0.7;
        margin: 11px * 0.7;
        border-width: 1px * 0.7;
      }

      .content-item-8 {
        width: 60px * 0.7;
        height: 60px * 0.7;
        margin: 10px * 0.7;
        border-width: 1px * 0.7;
      }

      .content-item-9 {
        width: 52px * 0.7;
        height: 52px * 0.7;
        margin: 10px * 0.7;
        border-width: 1px * 0.7;
      }
    }

    .footer-img {
      position: absolute;
      left: 200px * 0.7;
      bottom: 30px * 0.7;
      height: 115px * 0.7;
      cursor: pointer;
    }

    .footer {
      position: absolute;
      bottom: 50px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1790px * 0.7;

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }
}
</style>