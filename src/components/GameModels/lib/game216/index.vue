<template>
  <div class="game216-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib @start="status = 2" @challenge="start()" v-if="status < 3">
      <div class="content-warp">
        <div class="content">
          <img class="bg" src="/static/game_assets/game216/content_bg.png" />
          <div class="main">
            <p class="title">饮食建议</p>
            <div class="game-synopsis">
              <p class="synopsis-content">本训练用于提高训练者的各种食物认知。</p>
            </div>
          </div>
        </div>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="question-title">{{answer}}{{questions[answer - 1].title}}</div>
      <div class="content2">
        <div class="content-item" v-for="(item, index) in questions[answer - 1].items" :key="index" @click="chooseItem(index + 1)">
          <img class="item-img" :src="item.image" />
          <div class="choose-label">{{item.label}}</div>
          <img class="choose-item" v-if="(index + 1) == choose" src="/static/game_assets/game216/choose_right.jpg" alt="">
        </div>
      </div>

      <div class="btn-group">
        <div class="btn" v-if="choose" @click="startGame">
          <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
          <span class="text">提交</span>
        </div>
      </div>

      <div class="judge" v-if="isShowCorrect || isShowError">
        <div class="content-warp">
          <div class="content">
            <img class="bg" src="/static/game_assets/game216/content_bg.png" />
            <div class="answer-type">
              <div v-if="isShowCorrect" class="choose-type-view">
                <img class="choose-type-icon" src="/static/game_assets/game216/choose_right.jpg" alt="">
                答对了
              </div>
              <div v-if="isShowError" class="choose-type-view">
                <img class="choose-type-icon" src="/static/game_assets/game216/choose_worring.png" alt="">
                答错了
              </div>
              <div class="choose-content">{{questions[answer - 1].items[choose - 1].content}}</div>
              <img v-if="answer < 4" class="next-icon" src="/static/game_assets/common/next_green.png" alt="" @click="nextQuestion()" />
              <img v-else class="next-icon" src="/static/game_assets/common/submit.png" @click="submit()">
            </div>
          </div>
        </div>
      </div>
    </div>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
// 每轮游戏3个回合

export default {
  name: 'game216',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      musicUrl: '',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      show: false,
      status: 1,

      isPlayTitle: false,
      isPlay: false,
      isShowCorrect: false,
      isShowError: false,
      questions: [
        {
          title: '.请选择热量最低的食物？',
          content: '',
          items: [{
            image: '/static/game_assets/game216/item_1_1.png',
            label: 'A.香蕉',
            content: '正确选项为C.苹果，香蕉的热量较高：100克香蕉的热量为93千卡，所以不宜大量食用，尤其是减肥期间用香蕉代替主食不可取'
          }, {
            image: '/static/game_assets/game216/item_1_2.png',
            label: 'B.西瓜',
            content: '正确选项为C.苹果，一个大约有100克重的西瓜，包含30大卡左右的热量，吃一个西瓜，相当于吃5到6碗米饭。西瓜中含有丰富的水分，含有丰富的维生素、糖分、蛋白质、果胶等'
          }, {
            image: '/static/game_assets/game216/item_1_3.png',
            label: 'C.苹果',
            content: '一个苹果大约含有55-130卡路里的热量。苹 果是一种低热量、高营养的水果。一个苹果含 有丰富的纤维、多种维生素和矿物质。'
          }, {
            image: '/static/game_assets/game216/item_1_4.png',
            label: 'D.榴莲',
            content: '正确选项为C.苹果，一般情况下，一个榴莲的实际果肉大概是1000克左右，通常每100克榴莲含有热量150大卡。那么一个榴莲的热量约是1500大卡'
          }],
          audioPath: '/static/game_assets/audio/game216/item_1.mp3',
          index: 3
        },
        {
          title: '.请选择下列哪一项适合寒性体质的人食用？',
          content: '大枣具有补虚益气、养血安神、健脾和胃等功效，是脾胃虚弱、气血不足、倦怠无力、失眠等患者良好的保健营养品。',
          items: [{
            image: '/static/game_assets/game216/item_2_1.png',
            label: 'A.红枣',
            content: '大枣具有补虚益气、养血安神、健脾和胃等功效，是脾胃虚弱、气血不足、倦怠无力、失眠等患者良好的保健营养品。'
          }, {
            image: '/static/game_assets/game216/item_2_2.png',
            label: 'B.山竹',
            content: '正确选项为A.红枣，山竹属于寒凉性的食物，因此对于体质偏寒的人来说，过多食用可能会加重体内的寒气，不利于身体健康。'
          }, {
            image: '/static/game_assets/game216/item_1_2.png',
            label: 'C.西瓜',
            content: '正确选项为A.红枣，西瓜性寒，如果体寒的人吃西瓜就有可能会导致体寒的情况更加严重，使患者出现严重的腹泻，影响身体健康，所以体寒的人不能吃西瓜。'
          }, {
            image: '/static/game_assets/game216/item_2_4.png',
            label: 'D.草莓',
            content: '正确选项为A.红枣，草莓的性质寒凉，对肺热咳嗽、暑热烦渴有益。草莓具有润肺止咳的功效，也有清热除烦的效果。草莓不适宜腹部寒冷、脾胃虚寒患者食用：会导致腹痛、腹泻等胃部不适的病症。'
          }],
          audioPath: '/static/game_assets/audio/game216/item_2.mp3',
          index: 1
        },
        {
          title: '.下列哪一项含维C最高？',
          items: [{
            image: '/static/game_assets/game216/item_3_1.png',
            label: 'A.猕猴桃',
            content: '猕猴桃中含有的维生素C含量可为橘子的4-6倍。维生素C是人体不可缺少的重要营养物质。有促进原蛋白合成、保护皮肤、抗衰老、预防贫血，增强免疫力等功效，每天都需要补充。'
          }, {
            image: '/static/game_assets/game216/item_3_2.png',
            label: 'B.龙眼',
            content: '正确选项为A.猕猴桃，龙眼中含有丰富的糖类,主要是葡萄糖、果糖和蔗糖。相较于猕猴桃、柠檬、血橙等水果,枇杷维生素C含量不是特别高。'
          }, {
            image: '/static/game_assets/game216/item_3_3.png',
            label: 'C.枇杷',
            content: '正确选项为A.猕猴桃，相较于猕猴桃、柠檬、血橙等水果,枇杷维生素C含量不是特别高。'
          }, {
            image: '/static/game_assets/game216/item_3_4.png',
            label: 'D.菠萝',
            content: '正确选项为A.猕猴桃，相较于猕猴桃、柠檬、血橙等水果,菠萝维生素C含量不是特别高。'
          }],
          audioPath: '/static/game_assets/audio/game216/item_3.mp3',
          index: 1
        },
        {
          title: '.哪一类食物蛋白质含量比较高？',
          content: '蛋类，如鸡蛋、鸭蛋、鹌鹑蛋等，蛋白质是构成人体的重要物质，身体中各种组织--肌肉、骨骼、皮肤、神经等都含有蛋白质。',
          images: [
            '/static/game_assets/game216/item_4_1.png',
            '/static/game_assets/game216/item_4_2.png',
            '/static/game_assets/game216/item_4_3.png',
            '/static/game_assets/game216/item_4_4.png',
          ],
          items: [{
            image: '/static/game_assets/game216/item_4_1.png',
            label: 'A.谷类',
            content: '正确选项为D.蛋类，谷类蛋白质含量约8%～12%。'
          }, {
            image: '/static/game_assets/game216/item_4_2.png',
            label: 'B.水果类',
            content: '正确选项为D.蛋类，水果一般有蛋白质,但含量相对较少。'
          }, {
            image: '/static/game_assets/game216/item_4_3.png',
            label: 'C.蔬菜类',
            content: '正确选项为D.蛋类，蔬菜中蛋白质含量不高，每100g蔬菜中仅含有1g的蛋白质。'
          }, {
            image: '/static/game_assets/game216/item_4_4.png',
            label: 'D.蛋类',
            content: '蛋类，如鸡蛋、鸭蛋、鹌鹑蛋等，蛋白质是构成人体的重要物质，身体中各种组织--肌肉、骨骼、皮肤、神经等都含有蛋白质。'
          }],
          audioPath: '/static/game_assets/audio/game216/item_4.mp3',
          index: 4
        }
      ],
      answer: 0,
      index: 0,
      choose: 0,
      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play (url) {
      if (url) {
        this.musicUrl = url
        this.$refs.music.load()
      }
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnded () {
      this.isPlay = false
      if (this.isPlayTitle) {
        this.isPlayTitle = false
      }
    },

    start () {
      this.questions = api.shuffle(this.questions)
      this.status = 3
      this.startProcess()
      this.play(this.questions[this.answer - 1].audioPath)
      this.isPlayTitle = true
    },

    startGame () {
      if (this.isShowCorrect || this.isShowError || this.isPlayTitle) return
      console.log(this.answer)
      console.log(this.choose)
      console.log(this.questions[this.answer - 1])
      if (this.choose === this.questions[this.answer - 1].index) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }
    },

    nextQuestion () {
      this.isShowCorrect = false
      this.isShowError = false

      if (this.answer >= 4) {
        this.submit()
      } else {
        this.choose = 0
        this.startProcess()
        this.play(this.questions[this.answer - 1].audioPath)
      }
    },

    // 开始流程
    async startProcess () {
      this.answer++
    },

    chooseItem (item) {
      if (this.isPlayTitle) return
      this.choose = item
    },

    stop () {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
      if (this.isPlay) this.pause()
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.play()
      if (this.answerStatus === 1) return
      this.timing()
    },

    submit () {
      this.pause()
      this.isStop = true
      this.store = 25 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },
    again () {
      this.isStop = false
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.status = 3
      this.choose = 0
      this.answer = 0
      this.answerStatus = 1
      this.isShowCorrect = false
      this.isShowError = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game216-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/wskx/bg.png');
  }

  .content-warp {
    width: 100%;
    height: 708px * 0.7;
    padding-bottom: 32px * 0.7;

    .content {
      position: relative;
      width: 854px * 0.7;
      height: 672px * 0.7;
      margin: 0 auto;

      .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 854px * 0.7;
        height: 672px * 0.7;
      }

      .main {
        position: relative;
        padding: 35px * 0.7 82px * 0.7 0 71px * 0.7;

        .title {
          padding-bottom: 26px * 0.7;
          line-height: 60px * 0.7;
          font-weight: bold;
          text-align: center;
          font-size: 32px * 0.7;
          color: #0055a6;
          user-select: none;
        }
        .game-synopsis {
          margin-top: 30px * 0.7;
          .synopsis-content {
            padding-top: 90px * 0.7;
            margin: 0;
            font-size: 32px * 0.7;
            color: rgba(0, 0, 0, 0.85);
            line-height: 45px * 0.7;
          }
        }
      }
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .question-title {
      font-weight: 600;
      font-size: 40px * 0.7;
      color: #0055a6;
      line-height: 56px * 0.7;
      margin: 50px * 0.7 0;
      text-align: center;
    }

    .left-icon {
      position: absolute;
      bottom: 125px * 0.7;
      left: 40px * 0.7;
      width: 328px * 0.7;
      height: 366px * 0.7;

      .icon {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 158px * 0.7;
      }

      .left-content {
        position: absolute;
        top: 0;
        right: 0;
        width: 161px * 0.7;
        height: 178px * 0.7;

        .img-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 161px * 0.7;
        }

        .img {
          position: absolute;
          top: 15px * 0.7;
          right: 17px * 0.7;
          width: 128px * 0.7;
        }
      }
    }

    .content1 {
      position: relative;
      width: 1253px * 0.7;
      height: 708px * 0.7;
      margin-bottom: 82px * 0.7;
      padding: 98px * 0.7 467px * 0.7 0 430px * 0.7;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 854px * 0.7;
      }

      .left-btn {
        position: absolute;
        top: 300px * 0.7;
        left: 30px * 0.7;
        width: 64px * 0.7;
        cursor: pointer;
      }

      .right-btn {
        position: absolute;
        top: 300px * 0.7;
        right: 48px * 0.7;
        width: 64px * 0.7;
        cursor: pointer;
      }

      .content-img {
        position: relative;
        width: 356px * 0.7;
      }

      .content-text {
        position: relative;
        padding: 52px * 0.7;
        font-size: 74px * 0.7;
        line-height: 97px * 0.7;
        text-align: center;
        color: #a83a01;
      }
    }

    .content2 {
      position: relative;
      height: 788px * 0.7;
      margin-bottom: 96px * 0.7;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;

      .content-item {
        position: relative;
        margin: 34px * 0.7;
        width: 341px * 0.7;
        height: 267px * 0.7;
        display: flex;
        flex-direction: column;
        cursor: pointer;

        .item-img {
          width: 341px * 0.7;
          height: 267px * 0.7;
        }

        .choose-label {
          font-size: 28px * 0.7;
          color: #0055a6;
          line-height: 33px * 0.7;
          width: 100%;
          text-align: center;
        }

        .choose-item {
          width: 70px * 0.7;
          height: 70px * 0.7;
          position: absolute;
          top: 30px * 0.7;
          right: 30px * 0.7;
        }
      }
    }

    .btn-group {
      position: absolute;
      bottom: 82px * 0.7;
      width: 1450px * 0.7;
      height: 84px * 0.7;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px * 0.7;
        height: 84px * 0.7;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px * 0.7;
          height: 84px * 0.7;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px * 0.7 0 21px * 0.7 0;
          font-size: 38px * 0.7;
          line-height: 53px * 0.7;
          text-align: center;
          color: #a83a01;
        }
      }
    }

    .judge {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding-top: 10px * 0.7;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      justify-content: center;
      align-items: center;

      .answer-type {
        position: relative;
        display: flex;
        flex-direction: column;
        padding: 40px * 0.7 50px * 0.7;
        align-items: center;
        .choose-type-view {
          font-weight: 600;
          font-size: 48px * 0.7;
          color: rgba(0, 0, 0, 0.85);
          line-height: 67px * 0.7;
          margin: 30px * 0.7 0;
          .choose-type-icon {
            width: 70px * 0.7;
            height: 70px * 0.7;
          }
        }
        .choose-content {
          font-weight: 400;
          font-size: 32px * 0.7;
          color: rgba(0, 0, 0, 0.85);
          line-height: 58px * 0.7;
          padding: 0 20px * 0.7;
        }
        .next-icon {
          width: 270px * 0.7;
          height: 115px * 0.7;
          cursor: pointer;
        }
        .answer-footer {
          display: flex;
          justify-content: space-between;
          padding-bottom: 100px * 0.7;

          img {
            width: 270px * 0.7;
            height: 115px * 0.7;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>