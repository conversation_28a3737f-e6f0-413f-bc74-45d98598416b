<template>
  <div class="game25-page">
    <audio v-if="musicUrl" ref="music" muted controls="controls" autoplay="autoplay" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
    <div class="page-bg" :class="getClass()"></div>
    <div class="setting-page-container" v-if="status == 1">
      <settingPageLib title="视觉分配--数字" @start="count" @challenge="start" v-if="status < 3">
        <div class="game-synopsis">
          <p class="synopsis-title">挑战游戏说明</p>
          <div class="synopsis-content">
            <p>1、这是一组训练一个人同时做两件事的能力的作业。</p>
            <p>2、训练中，您一方面要进行计算，同时要判断和命名屏幕上出现的数字。</p>
            <p>3、根据病人的情况，在时间设定和内容上可进行选择，从易到难，循序渐进。</p>
            <p>4、正确行越高，时间越短，说明您在注意分配方面的功能越好，请加油努力！</p>
          </div>
        </div>
      </settingPageLib>
    </div>

    <div class="game-content" v-if="status === 2">
      <div class="pause-start">
        <div class="pause-item" v-if="!pause" @click="pauseFn"></div>
        <div class="start-item" v-else @click="startFn"></div>
      </div>
      <div class="game-content-top">
        <div class="game-top-left">
          <div class="canvas-container">
            <div class="svg-container" v-if="showSvg">
              <svg height="230" :viewBox="chosedSvg.viewBox">
                <path :d="chosedSvg.path" stroke="#00000000" stroke-width="0.1" fill="none" />
              </svg>
              <div class="point-img"></div>
            </div>
          </div>
        </div>
        <div class="game-top-right">
          <div class="right-content">
            <span>{{
                `${nowCalculateData.num1}${nowCalculateData.type}${nowCalculateData.num2}=${nowCalculateData.num3}`
            }}</span>
          </div>
          <div class="right-ctrl">
            <div class="ctrl-right _btn" @click="clickCtrlBtn(true)"></div>
            <div class="ctrl-error _btn" @click="clickCtrlBtn(false)"></div>
          </div>
        </div>
      </div>
      <div class="game-content-time">
        <div class="time-text">
          剩余时间：
        </div>
        <div class="time-box">
          <div class="time-box-container">
            <div class="time-item" :style="{ 'width': (resetTime / time) * 100 + '%' }"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="data-content" v-if="status === 3">

      <div class="data-content-box">
        <div class="data-left-content">
          <div class="left-title-content">
            请选择小球描述的图形
          </div>
          <div class="left-chose-content">
            <div class="chosed-item" v-for="(item, index) in svgList" :key="index">
              <div class="chosed-item-text">{{ item.name }}</div>
              <div class="chosed-item-img" @click="leftChose(index)">
                <img v-if="leftChosed == index" src="/static/game_assets/game25/duihao.png" alt="">
              </div>
            </div>
          </div>
          <div class="left-chose-btn">
            <img @click="toResult" class="_btn" src="/static/game_assets/game25/btn.png" alt="">
          </div>
        </div>
        <div class="data-right-content">
          <div class="calc-item" v-for="(item, index) in calculateData" :key="index">
            <div class="calc-chosed-img">
              <img v-if="item.yes" src="/static/game_assets/game25/duihao.png" alt="">
              <img v-else src="/static/game_assets/game25/chahao.png" alt="">
            </div>
            <div class="calc-chosed-text">
              {{ `${item.num1}${item.type}${item.num2}=${item.num3}` }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPage25.vue'
import anime from '/public/static/game_assets/game25/anime'
export default {
  name: 'game28',
  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },
  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      pause: false,
      showSvg: false,
      chosedSvg: null,
      svgList: [
        {
          name: '1',
          path: 'M 28 25 L 50 0 V 166 M 30 166 H 80',
          viewBox: '9 -1 62 168'
        },
        {
          name: '2',
          path: 'M 0 0 C 44 -38 151 11 -10 145 H 94',
          viewBox: '-11 -13.4692 106 159.5'
        },
        {
          name: '3',
          path: 'M 0 0 C 104 -25 91 77 19 77 C 146 70 82 208 -10 161 H 94',
          viewBox: '-11 -4.94491 106 176.7'
        },
        {
          name: '4',
          path: 'M 43 0 L 7 76 H 82 M 63 0 V 112',
          viewBox: '6 -1 77 114'
        },
        {
          name: '5',
          path: 'M 28 1 V 44 C 114 35 40 131 12 80 M 28 17 H 62',
          viewBox: '11 0 58.34 96.02'
        },
        {
          name: '6',
          path: 'M 58 13 C 33 24 18 72 15 109 C 14 149 61 156 72 112 C 78 61 18 57 15 109',
          viewBox: '13.9843 12 59.44 131.1'
        },
        {
          name: '7',
          path: 'M 0 -3 H 93 C 72 37 54 98 50 149',
          viewBox: '-1 -4 95 154'
        },
        {
          name: '8',
          path: 'M 0 0 A 1 1 0 0 0 1 70 A 1 1 0 0 1 0 166 A 1 1 0 0 1 2 70 A 1 1 0 0 0 0 0',
          viewBox: '-48.0104 -1.01428 97.51 168'
        },
        {
          name: '9',
          path: 'M 72 19 C 66 -18 13 -1 27 45 C 34 67 65 71 72 19 L 55 132',
          viewBox: '23.6547 -2.16211 49.35 135.2'
        },
        {
          name: '0',
          path: 'M 0 0 C -100 0 -100 200 0 200 C 100 200 100 0 0 0',
          viewBox: '-76 -1 152 202'
        },
      ],
      a: null,
      ctx: null,
      img: null,
      pauflag: false,
      chosedGraphNum: null,
      leftChosed: null,
      time: 30,
      resetTime: 30,
      timeout: null,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      numList: [],
      selectItem: 0,
      errorNumber: 0, // 错误的数字
      isStop: false,
      isPlay: false,
      params: {},
      infos: [
        {
          key: '难度等级：',
          value: '1'
        },
        {
          key: '右侧计算判断正确数：',
          value: 0
        },
        {
          key: '右侧计算判断错误数：',
          value: 0
        },
        {
          key: '左侧计算判断正确数：',
          value: 0
        },
        {
          key: '左侧计算判断错误数：',
          value: 0
        },
        {
          key: '反应时间：',
          value: 0
        },
      ],
      calculateData: [
      ],
      // type:0 + ,1 - ,2 × ,3 ÷
      nowCalculateData: { num1: null, num2: null, type: null, num3: null, current: null, yes: null },
    }
  },
  mounted () {
    this.$refs.music.play()
    // this.init()
    // this.initCalc()
  },
  methods: {
    pauseFn () {
      this.pause = true
      this.a.pause()
      clearTimeout(this.timeout)
    },
    startFn () {
      this.pause = false
      this.a.play()
      this.timing()
    },
    toResult () {
      this.infos[1].value = this.calculateData.filter(item => { return item.yes }).length
      this.infos[2].value = this.calculateData.filter(item => { return !item.yes }).length
      if (this.chosedGraphNum == this.leftChosed) {
        this.infos[3].value = 1
        this.infos[4].value = 0
      } else {

        this.infos[4].value = 1
        this.infos[3].value = 0
      }
      this.getStore()
      this.status = 4
    },
    getStore () {
      if (this.infos[1].value == 0 && this.infos[3].value == 0) {
        this.store = 0
      } else {
        this.store = Number((((this.infos[1].value + this.infos[3].value) / (this.infos[1].value + this.infos[2].value + this.infos[3].value + this.infos[4].value)) * 100).toFixed())
      }
    },
    count () {
      this.$refs.music.play()
      setTimeout(() => {
        this.status = 2
        this.start()
      }, 3000);
    },
    getClass () {
      // console.log('getClass');
      if (this.status == 1) {
        return 'page-bg1'
      } else if (this.status == 2 || this.status == 3) {
        return 'page-bg2'
      } else if (this.status == 4) {
        return ''
      }
    },
    leftChose (index) {
      this.leftChosed = index
    },
    clickCtrlBtn (type) {
      if (this.pause) return
      if (type == this.nowCalculateData.current) {
        this.nowCalculateData.yes = true
      } else {
        this.nowCalculateData.yes = false
      }
      this.calculateData.push(this.nowCalculateData)
      this.initCalc()
    },
    // 初始化计算题
    initCalc () {
      let currentType = Math.floor((Math.random() * 4))
      let typeList = {
        0: { calc: '+', text: '+' },
        1: { calc: '-', text: '-' },
        2: { calc: '*', text: '×' },
        3: { calc: '/', text: '÷' },
      }
      let nowCalculateData = { num1: null, num2: null, type: null, num3: null, current: null, yes: null }
      let calcType = Math.floor((Math.random() * 3))
      // console.log(currentType);
      // 随机计算正确的题:错误的题=1：2
      if (currentType < 2) {
        nowCalculateData.num1 = Math.floor((Math.random() * 10)) + 1
        if (calcType == 3) {
          nowCalculateData.num2 = this.getChuShu(nowCalculateData.num1)
        } else {
          nowCalculateData.num2 = Math.floor((Math.random() * 10)) + 1
        }
        nowCalculateData.num3 = eval(nowCalculateData.num1 + typeList[calcType].calc + nowCalculateData.num2)
        nowCalculateData.type = typeList[calcType].text
        nowCalculateData.current = true
      } else {
        nowCalculateData.num1 = Math.floor((Math.random() * 10)) + 1
        nowCalculateData.num2 = Math.floor((Math.random() * 10)) + 1
        if (nowCalculateData.num1 > nowCalculateData.num2) {
          nowCalculateData.num3 = Math.floor((Math.random() * 100)) + nowCalculateData.num1
        } else if (nowCalculateData.num1 < nowCalculateData.num2) {
          nowCalculateData.num3 = Math.floor((Math.random() * 100)) + nowCalculateData.num2
        } else {
          nowCalculateData.num3 = Math.floor((Math.random() * 100)) + nowCalculateData.num1
        }
        nowCalculateData.type = typeList[calcType].text
        nowCalculateData.current = false
      }
      this.nowCalculateData = nowCalculateData
    },
    getChuShu (num) {
      let num2 = 1
      let list = []
      while (num2 < num + 1) {
        // console.log(num % num2 == 0);
        if (num % num2 == 0) {
          list.push(num2)
          num2 = num2 + 1
        } else {
          num2 = num2 + 1
        }
      }
      return list[Math.floor((Math.random() * (list.length - 1)))]
    },
    // 初始化动画
    init () {
      this.showSvg = false
      this.chosedGraphNum = Math.floor((Math.random() * this.svgList.length))
      // this.chosedGraphNum = this.svgList.length - 1
      this.chosedSvg = this.svgList[this.chosedGraphNum]
      console.log(this.chosedSvg);
      this.showSvg = true
      setTimeout(() => {
        var path = anime.path('path');
        this.a = anime({
          targets: '.point-img',
          translateX: path('x'),
          translateY: path('y'),
          duration: 30000,
          loop: true,
          easing: 'linear'
        });
      }, 100);
    },
    timing () {
      // console.log('tim', this.resetTime);
      if (this.isStop) return
      if (this.resetTime == 0) {
        clearTimeout(this.timeout)
        this.status = 3
        return
      }
      this.timeout = setTimeout(() => {
        this.resetTime--
        this.timing()
      }, 1000)
    },
    start () {
      // const list = [1, 2, 3, 4, 5, 6, 7, 8, 9]
      // this.numList = api.shuffle(list)
      this.status = 2
      this.calculateData = []
      this.resetTime = 30
      this.leftChosed = null
      setTimeout(() => {
        this.init()
      }, 100);
      this.initCalc()
      this.timing()
    },

    again () {
      // this.selectItem = 0
      // this.errorNumber = 0
      // this.succesNum = 0
      // this.errorNum = 0
      // this.second = 0
      // this.store = 0
      // this.numList = []
      this.pauflag = false
      this.start()
    }

  }
}
</script>

<style lang="scss" scoped>
._btn {
  cursor: pointer;
}

.point-img {
  position: absolute;
  top: -15px * 0.7;
  left: -15px * 0.7;
  width: 30px * 0.7;
  height: 30px * 0.7;
  border-radius: 50%;
  // background-color: #fff;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-image: url('/static/game_assets/game25/point.png');
}

.game25-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url('/static/game_assets/game25/bg.png');
  }

  .page-bg1 {
    background-image: url('/static/game_assets/game25/bg_1.png');
  }

  .page-bg2 {
    background-image: url('/static/game_assets/game25/bg_2.png');
  }

  .setting-page-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 6% 0;
  }

  .game-synopsis {
    width: 1000px * 0.7;
    height: 504px * 0.7;
    padding-left: 200px * 0.7;
    padding-top: 50px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #333;
    }

    .synopsis-content {
      padding-top: 25px * 0.7;

      p {
        margin: 0;
        font-size: 25px * 0.7;
        line-height: 40px * 0.7;
        font-weight: 400;
        color: #333;
      }
    }
  }

  .data-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding-bottom: 40px * 0.7;

    .data-content-box {
      width: 1046.4px * 0.7;
      height: 600px * 0.7;
      margin-bottom: 20px * 0.7;
      display: flex;

      .data-left-content {
        flex: 0 1 100%;
        width: 100%;
        margin-right: 90px * 0.7;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        .left-title-content {
          font-size: 36px * 0.7;
          font-family: PingFang SC;
          font-weight: 500;
        }

        .left-chose-content {
          display: flex;
          align-items: center;
          // justify-content: center;
          flex-wrap: wrap;
          margin-top: 44px * 0.7;
          margin-bottom: 22px * 0.7;

          .chosed-item {
            width: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 22px * 0.7;

            .chosed-item-text {
              font-size: 42px * 0.7;
              font-family: PingFang SC;
              margin-right: 20px * 0.7;
            }

            .chosed-item-img {
              width: 40px * 0.7;
              height: 40px * 0.7;
              background: url('/static/game_assets/game25/border.png') no-repeat;
              background-size: 100% 100%;
              display: flex;
              align-items: center;
              justify-content: center;

              img {
                width: 70%;
                height: 70%;
              }
            }
          }
        }

        .left-chose-btn {
          display: flex;
          align-items: center;

          img {
            width: 214px * 0.7;
            height: 89px * 0.7;
          }
        }
      }

      .data-right-content {
        position: relative;
        flex: 0 1 100%;
        overflow-y: auto;

        .calc-item {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px * 0.7;

          .calc-chosed-text {
            background: #ffffff;
            border-radius: 16px * 0.7;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 300px * 0.7;
            height: 60px * 0.7;
            font-size: 42px * 0.7;
            font-family: PingFang SC;
            font-weight: 400;
            color: #5d5e66;
            margin-left: 38px * 0.7;
          }

          .calc-chosed-img {
            width: 40px * 0.7;
            height: 40px * 0.7;
            background: url('/static/game_assets/game25/border.png') no-repeat;
            background-size: 100% 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              width: 70%;
              height: 70%;
            }
          }
        }
      }
    }
  }

  .game-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding-bottom: 40px * 0.7;

    .pause-start {
      position: absolute;
      left: 120px * 0.7;
      bottom: 90px * 0.7;
      cursor: pointer;

      .pause-item {
        width: 270px * 0.7;
        height: 115px * 0.7;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-image: url('/static/game_assets/game25/pause.png');
      }

      .start-item {
        width: 270px * 0.7;
        height: 115px * 0.7;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-image: url('/static/game_assets/game25/start.png');
      }
    }

    .game-content-top {
      width: 1046.4px * 0.7;
      height: 600px * 0.7;
      margin-bottom: 20px * 0.7;
      display: flex;

      .game-top-left {
        flex: 0 1 100%;
        width: 100%;
        margin-right: 90px * 0.7;
        display: flex;
        align-items: center;
        justify-content: center;

        .canvas-container {
          position: relative;
          width: 400px * 0.7;
          height: 400px * 0.7;
          transform: translateY(-40px * 0.7);
          // background-color: rgba(223, 223, 223, 0.4);
          border-radius: 5px * 0.7;

          .svg-container {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }

      .game-top-right {
        position: relative;
        flex: 0 1 100%;

        .right-content {
          position: absolute;
          top: 60px * 0.7;
          left: 50%;
          transform: translateX(-50%);
          width: 413px * 0.7;
          height: 242px * 0.7;
          background-repeat: no-repeat;
          background-size: 100% 100%;
          background-image: url('/static/game_assets/game25/game_bg.png');
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 83px * 0.7;
          font-family: PingFang SC;
          font-weight: 600;
        }

        .right-ctrl {
          position: absolute;
          width: 90%;
          bottom: 30px * 0.7;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          align-items: center;
          justify-content: space-between;

          > div {
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 188px * 0.7;
            height: 97px * 0.7;
            cursor: pointer;
          }

          .ctrl-right {
            background-image: url('/static/game_assets/game25/right.png');
          }

          .ctrl-error {
            background-image: url('/static/game_assets/game25/error.png');
          }
        }
      }
    }

    .game-content-time {
      position: absolute;
      bottom: 120px * 0.7;
      width: 1046.4px * 0.7;
      height: 70px * 0.7;
      display: flex;
      align-items: center;
      font-size: 32px * 0.7;
      font-family: PingFang SC;

      .time-text {
        flex: 0 0 180px * 0.7;
      }

      .time-box {
        flex: 0 1 100%;
        height: 70px * 0.7;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-image: url('/static/game_assets/game25/time-box.png');

        .time-box-container {
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          padding: 9px * 0.7 0 20px * 0.7 13px * 0.7;
        }

        .time-item {
          width: 100%;
          height: 100%;
          background-repeat: no-repeat;
          background-size: 100% 100%;
          background-image: url('/static/game_assets/game25/time.png');
        }
      }
    }
  }
}

::-webkit-scrollbar {
  width: 19px * 0.7;
  height: 10px * 0.7;
}

::-webkit-scrollbar-thumb {
  background: #85ccf5;
}

::-webkit-scrollbar-track {
  background: rgba(204, 204, 204, 0);
}

@keyframes shake {
  /* 水平抖动，核心代码 */
  10%,
  90% {
    transform: translate3d(-1px * 0.7, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(+2px * 0.7, 0, 0);
  }

  30%,
  70% {
    transform: translate3d(-4px * 0.7, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(+4px * 0.7, 0, 0);
  }

  50% {
    transform: translate3d(-4px * 0.7, 0, 0);
  }
}
</style>