<template>
  <div class="game74-page">
    <div class="page-bg"></div>
    <settingPageLib title="数字推理" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本训练提高您的数字推理能力。</p>
        <p class="synopsis-content">2、题型包括填空题和选择题。</p>
        <p class="synopsis-content">3、请根据题干推算答案。</p>
        <p class="synopsis-content">4、答案有错误者，可以复习答案后再联系1-2次，第3次仍然错误时可终止该题练习而选择新的题目。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <img class="content-bg" src="/static/game_assets/game74/content_bg.png" />
        <img class="left-icon" src="/static/game_assets/game74/left.png" />
        <img class="right-icon" src="/static/game_assets/game74/right.png" />

        <div class="title">
          <img class="title-bg" src="/static/game_assets/game74/title_bg.png" />
          <p class="title-text">{{title}}</p>
        </div>

        <div class="content-1" v-if="current === 1">
          <img class="content-bg" src="/static/game_assets//game74/item_bg4.png" />
          <p class="content-mark">?</p>
          <p :class="['content-text', 'content-text-' + Number(index + 1)]" v-for="(item, index) in data" :key="item + 'item1'">{{item}}</p>
        </div>

        <div class="content-2" v-if="current === 2">
          <div class="content-item" v-for="(item, index) in data" :key="index + 'item2'">
            <img class="item-bg" src="/static/game_assets/game74/btn.png" />
            <span class="item-text">{{item}}</span>
          </div>
        </div>

        <div class="content-3" v-if="current === 3">
          <img class="content-bg" src="/static/game_assets//game74/item_bg2.png" />
          <p :class="['content-text', 'content-text-' + Number(index + 1)]" v-for="(item, index) in data" :key="index + 'item3'">{{item}}</p>
        </div>

        <div class="content-4" v-if="current === 4">
          <img class="content-bg" src="/static/game_assets//game74/item_bg3.png" />
          <p :class="['content-text', 'content-text-' + Number(index + 1)]" v-for="(item, index) in data" :key="index + 'item4'">{{item}}</p>
        </div>

        <div class="content-5" v-if="current === 5">
          <div class="content-item">
            <img class="content-bg" src="/static/game_assets//game74/item_bg1.png" />
            <p :class="['content-text', 'content-text-' + Number(index + 1)]" v-for="(item, index) in data.slice(0, 5)" :key="item + 'item5-1'">{{item}}</p>
          </div>

          <div class="content-item">
            <img class="content-bg" src="/static/game_assets//game74/item_bg1.png" />
            <p :class="['content-text', 'content-text-' + Number(index + 1)]" v-for="(item, index) in data.slice(5)" :key="item + 'item5-2'">{{item}}</p>
          </div>
        </div>

        <div class="content-6" v-if="current === 6">
          <div class="content-item" v-for="item in data" :key="item + 'item6'" @click="chooseItem(item)">
            <img class="content-bg" src="/static/game_assets//game74/btn2.png" />
            <img v-if="choose === item" class="content-bg" src="/static/game_assets//game74/btn1.png" />
            <span class="content-text">{{item}}</span>
          </div>
        </div>

        <div class="content-key" v-if="current && current !== 6">
          <img class="key-bg" src="/static/game_assets/game74/btn_bg.png" />

          <div class="content-input">
            <img class="input-bg" src="/static/game_assets/game99/input.png" />
            <span class="btn-number">{{ choose }}</span>
          </div>

          <div class="content-delete" @click="choose = ''">
            <img class="delete-bg" src="/static/game_assets/game99/delete.png" />
            <span class="btn-text">X</span>
          </div>

          <div class="content-btn" v-for="item in 10" :key="item + 'btn'" @click="select(item % 10)">
            <img class="btn-bg" src="/static/game_assets/game99/small_btn.png" />
            <span class="btn-text">{{item % 10}}</span>
          </div>

          <div class="content-confirm" @click="confirm">
            <img class="confirm-bg" src="/static/game_assets/game99/big_btn.png" />
            <span class="btn-text">确定</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <img v-if="current === 6 && choose" class="img3" src="/static/game_assets/common/submit.png" @click="confirm">
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 每次4个题
// 6个题数型
// 1: 相减等于8
// 2: 右侧数为左侧数的平方
// 3: 相减等于3
// 4: 上方数是下方数的和
// 5: 顶部数是下方四个数的和
// 6: 不同的是不是3的倍

export default {
  name: 'game74',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,
      title: '',
      data: [],
      questionList: [],
      current: 0,
      choose: '',

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      const list = [1, 2, 3, 4, 5, 6]
      this.questionList = api.getRandomArray(list, 4)
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      this.current = this.questionList[this.number]
      this.number++
      this.data = []
      if (this.current === 1) {
        this.title = '应该用什么数字替代气球上的问号？'
        const num = api.randomNum(1, 9)
        this.data.push(num)
        this.data.unshift(num + 8)
        this.data.unshift(num + 16)
        this.answer = num + 24
      } else if (this.current === 2) {
        this.title = '应该用什么数字替代空格中的问号？'
        const list = [1, 2, 3, 4, 5, 6, 7, 8, 9]
        const li = api.getRandomArray(list, 5)
        li.forEach((item, index) => {
          this.data.push(item)
          if (index < li.length - 1) {
            this.data.push(item * item)
          } else {
            this.data.push('?')
            this.answer = item * item
          }
        })
      } else if (this.current === 3) {
        this.title = '应该用什么数字替代图中的问号？'
        const num = api.randomNum(1, 9)
        for (let i = 0; i < 6; i++) {
          if (i === 4) {
            this.data.push('?')
            this.answer = num + 3 * i
          } else {
            this.data.push(num + 3 * i)
          }
        }
      } else if (this.current === 4) {
        this.title = '应该用什么数字替代空格中的问号？'
        const list = [1, 2, 3, 4, 5, 6, 7, 8, 9]
        this.data = api.getRandomArray(list, 4)
        this.data.push(this.data[0] + this.data[1])
        this.data.push(this.data[1] + this.data[2])
        this.data.push(this.data[2] + this.data[3])
        this.data.push(this.data[4] + this.data[5])
        this.data.push(this.data[5] + this.data[6])
        this.data.push('?')
        this.answer = this.data[7] + this.data[8]
      } else if (this.current === 5) {
        this.title = '根据左侧图中的逻辑关系，在右侧图中，该用什么数字替代问号？'
        const list = [1, 2, 3, 4, 5, 6, 7, 8, 9]
        this.data = api.getRandomArray(list, 4)
        this.data.push(this.data[0] + this.data[1] + this.data[2] + this.data[3])

        this.data = this.data.concat(api.getRandomArray(list, 4))
        this.data.push('?')
        this.answer = this.data[5] + this.data[6] + this.data[7] + this.data[8]
      } else if (this.current === 6) {
        this.title = '下面哪组数与众不同？'
        for (let i = 0; i < 4; i++) {
          this.data.push(api.randomNum(34, 333) * 3)
        }
        const list = [11, 13, 14, 16, 17, 8]
        const num = api.getRandomArray(list, 1)[0]
        this.data.push(num * 14)
        this.answer = num * 14
      }
    },

    select (item) {
      if (this.choose.length >= 2 || this.isShowCorrect || this.isShowError) return
      this.choose = this.choose + (item === 10 ? 0 : item).toString()
    },

    chooseItem (item) {
      this.choose = item
    },

    confirm () {
      if (!this.choose || this.isShowCorrect || this.isShowError) return

      if (Number(this.choose) === this.answer) {
        this.isShowCorrect = true
        this.succesNum++
      } else {
        this.isShowError = true
        this.errorNum++
      }

      setTimeout(() => {
        this.isShowCorrect = false
        this.isShowError = false

        if (this.number < 4) {
          this.choose = ''
          this.startProcess()
        } else {
          this.submit()
        }
      }, 800)
    },

    stop () {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.store = 25 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.choose = ''
      this.current = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game74-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game80/bg.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1912px * 0.7;
      height: 1080px * 0.7;
      padding: 220px * 0.7 350px * 0.7 68px * 0.7 290px * 0.7;
      display: flex;
      justify-content: space-between;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1912px * 0.7;
      }

      .left-icon {
        position: absolute;
        bottom: 66px * 0.7;
        left: 0;
        width: 450px * 0.7;
      }

      .right-icon {
        position: absolute;
        bottom: 57px * 0.7;
        right: 0;
        width: 490px * 0.7;
      }

      .title {
        position: absolute;
        width: 932px * 0.7;
        height: 125px * 0.7;
        top: 67px * 0.7;
        left: 486px * 0.7;

        .title-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 932px * 0.7;
        }

        .title-text {
          position: relative;
          margin: 0;
          padding: 29px * 0.7 0 60px * 0.7 0;
          font-size: 36px * 0.7;
          line-height: 36px * 0.7;
          color: #1b1d2d;
          text-align: center;
        }
      }

      .content-1 {
        position: relative;
        width: 682px * 0.7;
        height: 731px * 0.7;

        .content-bg {
          position: absolute;
          top: 86px * 0.7;
          left: 75px * 0.7;
          width: 416px * 0.7;
        }

        .content-mark {
          display: inline-block;
          position: absolute;
          top: 156px * 0.7;
          left: 355px * 0.7;
          font-size: 60px * 0.7;
          line-height: 60px * 0.7;
          font-weight: 600;
          color: #3d4166;
        }

        .content-text {
          position: absolute;
          width: 60px * 0.7;
          text-align: center;
          font-size: 60px * 0.7;
          line-height: 60px * 0.7;
          font-weight: 600;
          color: #3d4166;
        }

        .content-text-1 {
          top: 290px * 0.7;
          left: 195px * 0.7;
        }

        .content-text-2 {
          top: 360px * 0.7;
          left: 360px * 0.7;
        }

        .content-text-3 {
          top: 512px * 0.7;
          left: 255px * 0.7;
        }
      }

      .content-2 {
        width: 682px * 0.7;
        height: 731px * 0.7;
        padding: 30px * 0.7 50px * 0.7 30px * 0.7 228px * 0.7;
        display: flex;
        flex-wrap: wrap;

        .content-item {
          position: relative;
          width: 207px * 0.7;
          height: 148px * 0.7;
          margin: -10px * 0.7;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 207px * 0.7;
          }

          .item-text {
            display: block;
            position: relative;
            font-size: 60px * 0.7;
            font-weight: 600;
            line-height: 148px * 0.7;
            text-align: center;
            color: #3d4166;
          }
        }
      }

      .content-3 {
        position: relative;
        width: 682px * 0.7;
        height: 731px * 0.7;

        .content-bg {
          position: absolute;
          top: 50px * 0.7;
          left: 50px * 0.7;
          width: 540px * 0.7;
        }

        .content-text {
          position: absolute;
          width: 60px * 0.7;
          text-align: center;
          font-size: 60px * 0.7;
          line-height: 60px * 0.7;
          font-weight: 600;
          color: #3d4166;
        }

        .content-text-1 {
          top: 230px * 0.7;
          left: 155px * 0.7;
        }

        .content-text-2 {
          top: 155px * 0.7;
          left: 290px * 0.7;
        }

        .content-text-3 {
          top: 230px * 0.7;
          left: 413px * 0.7;
        }

        .content-text-4 {
          top: 362px * 0.7;
          left: 415px * 0.7;
        }

        .content-text-5 {
          top: 444px * 0.7;
          left: 288px * 0.7;
        }

        .content-text-6 {
          top: 362px * 0.7;
          left: 155px * 0.7;
        }
      }

      .content-4 {
        position: relative;
        width: 682px * 0.7;
        height: 731px * 0.7;

        .content-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 682px * 0.7;
        }

        .content-text {
          position: absolute;
          width: 60px * 0.7;
          text-align: center;
          font-size: 60px * 0.7;
          line-height: 60px * 0.7;
          font-weight: 600;
          color: #3d4166;
        }

        .content-text-1 {
          top: 565px * 0.7;
          left: 128px * 0.7;
        }

        .content-text-2 {
          top: 565px * 0.7;
          left: 250px * 0.7;
        }

        .content-text-3 {
          top: 565px * 0.7;
          left: 373px * 0.7;
        }

        .content-text-4 {
          top: 565px * 0.7;
          left: 494px * 0.7;
        }

        .content-text-5 {
          top: 426px * 0.7;
          left: 192px * 0.7;
        }

        .content-text-6 {
          top: 426px * 0.7;
          left: 307px * 0.7;
        }

        .content-text-7 {
          top: 426px * 0.7;
          left: 421px * 0.7;
        }

        .content-text-8 {
          top: 281px * 0.7;
          left: 258px * 0.7;
        }

        .content-text-9 {
          top: 281px * 0.7;
          left: 359px * 0.7;
        }

        .content-text-10 {
          top: 149px * 0.7;
          left: 295px * 0.7;
        }
      }

      .content-5 {
        width: 682px * 0.7;
        height: 731px * 0.7;
        padding: 15px * 0.7 0 0 35px * 0.7;
        display: flex;
        justify-content: space-between;

        .content-item {
          position: relative;
          width: 318px * 0.7;
          height: 663px * 0.7;

          .content-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 318px * 0.7;
          }

          .content-text {
            position: absolute;
            width: 60px * 0.7;
            text-align: center;
            font-size: 60px * 0.7;
            line-height: 60px * 0.7;
            font-weight: 600;
            color: #3d4166;
          }

          .content-text-1 {
            top: 565px * 0.7;
            left: 47px * 0.7;
          }

          .content-text-2 {
            top: 565px * 0.7;
            left: 207px * 0.7;
          }

          .content-text-3 {
            top: 317px * 0.7;
            left: 47px * 0.7;
          }

          .content-text-4 {
            top: 317px * 0.7;
            left: 207px * 0.7;
          }

          .content-text-5 {
            top: 29px * 0.7;
            left: 130px * 0.7;
          }
        }
      }

      .content-6 {
        width: 100%;
        height: 100%;
        padding: 137px * 0.7 84px * 0.7 183px * 0.7 154px * 0.7;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;

        .content-item {
          position: relative;
          width: 342px * 0.7;
          height: 211px * 0.7;
          padding: 23px * 0.7 11px * 0.7;

          .content-bg {
            position: absolute;
            top: 23px * 0.7;
            left: 11px * 0.7;
            width: 320px * 0.7;
          }

          .content-text {
            position: relative;
            display: block;
            padding: 30px * 0.7 0 65px * 0.7 0;
            font-size: 70px * 0.7;
            line-height: 70px * 0.7;
            text-align: center;
            font-weight: 600;
            color: #fff;
          }
        }
      }

      .content-key {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        align-items: center;
        padding: 80px * 0.7 48px * 0.7 34px * 0.7 48px * 0.7;
        width: 523px * 0.7;
        height: 720px * 0.7;

        .key-bg {
          position: absolute;
          top: 30px * 0.7;
          left: 0;
          width: 523px * 0.7;
        }

        .content-input {
          position: relative;
          width: 336px * 0.7;
          height: 124px * 0.7;

          .input-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 336px * 0.7;
          }

          .btn-number {
            position: relative;
            display: block;
            font-size: 60px * 0.7;
            line-height: 124px * 0.7;
            font-weight: 600;
            color: #3d4166;
            text-align: center;
          }
        }

        .content-delete {
          position: relative;
          width: 61px * 0.7;
          height: 118px * 0.7;
          padding: 11px * 0.7 0;
          cursor: pointer;

          .delete-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 61px * 0.7;
          }
        }

        .content-btn {
          position: relative;
          width: 124px * 0.7;
          height: 95px * 0.7;
          cursor: pointer;

          .btn-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 124px * 0.7;
            height: 95px * 0.7;
          }
        }

        .content-confirm {
          position: relative;
          width: 270px * 0.7;
          height: 95px * 0.7;
          cursor: pointer;

          .confirm-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 270px * 0.7;
            height: 95px * 0.7;
          }
        }

        .btn-text {
          position: relative;
          display: block;
          font-size: 60px * 0.7;
          line-height: 95px * 0.7;
          font-weight: 600;
          color: #3d4166;
          text-align: center;
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 17px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1752px * 0.7;
      padding-left: 105px * 0.7;

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img3 {
        width: 267px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
      z-index: 99;
    }
  }
}
</style>