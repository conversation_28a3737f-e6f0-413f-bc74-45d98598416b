<template>
  <div class="game82-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="辨色训练" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本训练用于训练/检验训练者的辨色能力。</p>
        <p class="synopsis-content">2、根据屏幕上方大图的内容，在屏幕下方选出对应图。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <img class="content-bg" src="/static/game_assets/game82/content_bg.png" />
        <div class="content-top">{{current.answer}}</div>

        <div class="content-bottom">
          <div :class="['content-item', choose === item.index && 'choose-item', isJudge && current.answer === item.index && choose !== item.index && 'correct-item']" v-for="item in current.answerList" :key="item.index + 'item'" @click="chooseItem(item.index)">
            <span>{{item.index}}</span>
            <img class="icon" v-if="isJudge && current.answer === item.index && choose === item.index" src="/static/game_assets/common/correct.png">
            <img class="icon" v-if="isJudge && current.answer !== item.index && choose === item.index" src="/static/game_assets/common/error.png">
          </div>
        </div>
      </div>

      <div class="title">
        <img class="title-bg" src="/static/game_assets/game61/title_bg.png" />
        <span class="title-text">仔细看，下图是什么？</span>
      </div>

      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>

      <div class="footer">
        <img class="img" v-if="choose && !isJudge" src="/static/game_assets/common/confirm.png" @click="confirm">
        <img class="img" v-if="isJudge" src="/static/game_assets/common/continue.png" @click="goon">
      </div>
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game82',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isJudge: false,
      isPlay: false,
      questionList: [],
      current: {},
      choose: 0,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
    goHome () {
      this.pause()
      this.$router.go(-1)
    },

    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start () {
      this.status = 3
      this.questionList = api.shuffle(data.data)
      this.startProcess()
      // this.timing()
      this.play()
    },

    startProcess () {
      this.current = this.questionList[this.number]
      this.current.answerList = api.shuffle(this.current.answerList)
      this.number++
    },

    chooseItem (item) {
      this.choose = item
    },

    confirm () {
      this.isJudge = true
      if (this.choose === this.current.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }
    },

    goon () {
      this.choose = 0
      this.isJudge = false

      if (this.number >= 5) {
        this.pause()
        this.submit()
      } else {
        this.startProcess()
      }
    },

    submit () {
      this.isStop = true
      this.store = 20 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isJudge = false
      this.choose = 0
      this.current = {}
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game82-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game126/bg.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 40px * 0.7;
      left: 105px * 0.7;
      width: 175px * 0.7;
      display: flex;
      justify-content: space-between;

      .top-icon {
        width: 80px * 0.7;
        cursor: pointer;
      }
    }

    .title {
      position: absolute;
      top: 0;
      width: 932px * 0.7;
      height: 125px * 0.7;
      z-index: 1;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px * 0.7;
        height: 125px * 0.7;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 31px * 0.7 0 58px * 0.7 0;
        font-size: 36px * 0.7;
        line-height: 36px * 0.7;
        text-align: center;
        color: #1b1d2d;
        user-select: none;
      }
    }

    .content {
      position: relative;
      width: 1920px * 0.7;
      height: 1065px * 0.7;
      margin-top: 15px * 0.7;
      padding: 187px * 0.7 478px * 0.7 304px * 0.7 574px * 0.7;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1920px * 0.7;
        height: 1065px * 0.7;
      }

      .content-top {
        position: relative;
        margin: 0 auto;
        width: 590px * 0.7;
        height: 344px * 0.7;
        border: 3px * 0.7 solid #d2d2d2;
        border-radius: 45px * 0.7;
      }

      .content-bottom {
        display: flex;
        justify-content: space-between;
        padding-top: 47px * 0.7;

        .content-item {
          position: relative;
          width: 284px * 0.7;
          height: 185px * 0.7;
          border: 3px * 0.7 solid #d2d2d2;
          border-radius: 45px * 0.7;
          cursor: pointer;

          .icon {
            position: absolute;
            right: 22px * 0.7;
            bottom: 19px * 0.7;
            width: 57px * 0.7;
          }
        }

        .choose-item {
          border: 5px * 0.7 solid #007cfb;
        }

        .correct-item {
          border: 5px * 0.7 solid #f1cc6e;
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 73px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1470px * 0.7;
      padding-right: 38px * 0.7;

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }
}
</style>