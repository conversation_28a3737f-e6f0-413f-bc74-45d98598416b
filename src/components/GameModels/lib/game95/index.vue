<template>
  <div class="game95-page">
    <div class="page-bg"></div>
    <settingPageLib title="残字识别-汉字" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请尽快识别屏幕上出现的汉字。</p>
        <p class="synopsis-content">2、如识别，请点击屏幕上的汉字，然后选择汉字。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content" v-show="answerStatus === 'question'">
        <img class="content-bg1" src="/static/game_assets/game95/content_bg1.png" />
        <img class="content-icon" src="/static/game_assets/game95/icon.png">
        <div class="content-main">
          <img class="item-bg" src="/static/game_assets/game95/item_bg.png" />
          <div class="item-text">{{current.question}}</div>
        </div>
        <div class="content-bar">
          <img class="bar-bg" src="/static/game_assets/game95/bar_bg.png" />
          <span class="bar-text">剩余时间:</span>
          <div class="bar-box">
            <div :class="['bar', isShowBar && 'line']"></div>
          </div>
        </div>
      </div>

      <div class="content" v-show="answerStatus === 'answer'">
        <img class="content-bg2" src="/static/game_assets/game95/content_bg2.png" />
        <div class="content-answer">
          <div :class="['content-item', choose === item && 'content-choose-item']" v-for="item in current.answerList" :key="item + 'text'" @click="chooseItem(item)">{{item}}</div>
        </div>
      </div>

      <div class="footer">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
        <img class="img" v-if="answerStatus === 'answer' && choose" src="/static/game_assets/common/confirm.png" @click="confirm">
        <img class="img" v-if="answerStatus === 'question'" src="/static/game_assets/common/choose.png" @click="startAnswer">
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'
// 每轮游戏4个题目


export default {
  name: 'game95',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,
      isShowBar: false,
      questionList: [],
      current: {},
      choose: '',
      answerStatus: 'question',
      timer: null,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      this.questionList = api.getRandomArray(data.word, 4)
      this.startProcess()
    },

    startProcess () {
      this.current = this.questionList[this.number]
      this.current.answerList = api.shuffle(this.current.answerList)
      this.number++

      setTimeout(() => {
        this.isShowBar = true
      }, 50)
      this.startTimer()
    },

    startTimer () {
      this.timer = setTimeout(() => {
        this.answerStatus = 'answer'
        this.isShowBar = false
      }, 5050)
    },

    startAnswer () {
      clearTimeout(this.timer)
      this.answerStatus = 'answer'
      this.isShowBar = false
    },

    chooseItem (item) {
      this.choose = item
    },

    confirm () {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      if (this.choose === this.current.question) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }

      setTimeout(() => {
        this.choose = ''
        this.isShowCorrect = false
        this.isShowError = false

        if (this.number < 4) {
          this.answerStatus = 'question'
          this.startProcess()
        } else {
          this.submit()
        }
      }, 800)
    },

    stop () {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
      if (this.answerStatus === 'question') {
        clearTimeout(this.timer)
        this.isShowBar = false
      }
    },

    // 继续游戏, 继续计时
    cancel () {
      if (this.answerStatus === 'question') {
        this.isShowBar = true
        this.startTimer()
        return
      }
      this.isStop = false
      this.timing()
    },

    submit () {
      this.store = 25 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.choose = ''
      this.current = {}
      this.answerStatus = 'question'
      this.timer = null
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game95-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game95/bg.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1920px * 0.7;
      height: 1062px * 0.7;
      margin-top: 18px * 0.7;

      .content-bg1 {
        position: absolute;
        top: 42px * 0.7;
        left: 0;
        width: 1920px * 0.7;
      }

      .content-icon {
        position: absolute;
        top: 229px * 0.7;
        left: 411px * 0.7;
        width: 384px * 0.7;
        z-index: 1;
      }

      .content-main {
        position: relative;
        padding: 139px * 0.7 407px * 0.7 80px * 0.7 704px * 0.7;

        .item-bg {
          position: absolute;
          top: 139px * 0.7;
          left: 704px * 0.7;
          width: 809px * 0.7;
        }

        .item-text {
          position: relative;
          width: 809px * 0.7;
          height: 504px * 0.7;
          font-size: 300px * 0.7;
          text-align: center;
          line-height: 500px * 0.7;
          color: #fff;
        }
      }

      .content-bar {
        position: relative;
        display: flex;
        padding: 0 443px * 0.7;

        .bar-bg {
          position: absolute;
          top: 0;
          right: 443px * 0.7;
          width: 847px * 0.7;
        }

        .bar-text {
          display: inline-block;
          padding-right: 28px * 0.7;
          font-size: 37px * 0.7;
          line-height: 70px * 0.7;
          font-weight: 600;
          color: #fff;
        }

        .bar-box {
          position: relative;
          width: 847px * 0.7;
          height: 70px * 0.7;
          padding: 11px * 0.7 15px * 0.7 19px * 0.7 14px * 0.7;

          .bar {
            width: 0px * 0.7;
            height: 100%;
            border-radius: 16px * 0.7;
            background: #ff566b;
            transition: all 5s linear;
          }

          .line {
            width: 100%;
          }
        }
      }

      .content-bg2 {
        position: absolute;
        top: 0;
        left: 0;
        width: 1920px * 0.7;
      }

      .content-answer {
        position: relative;
        height: 100%;
        padding: 162px * 0.7 620px * 0.7 270px * 0.7 596px * 0.7;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;

        .content-item {
          width: 192px * 0.7;
          height: 192px * 0.7;
          border: 2px * 0.7 dashed rgba(250, 250, 255, 0.6);

          font-size: 111px * 0.7;
          text-align: center;
          line-height: 172px * 0.7;
          font-weight: 600;
          font-family: PingFang SC;
          color: #fff;
          cursor: pointer;
        }

        .content-choose-item {
          border: 2px * 0.7 solid #9bf584;
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 73px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1470px * 0.7;
      padding-right: 38px * 0.7;

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
      z-index: 99;
    }
  }
}
</style>