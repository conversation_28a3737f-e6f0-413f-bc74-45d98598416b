<template>
  <div class="game103-page">
    <div class="page-bg"></div>
    <settingPageLib title="简图定位" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、通过查看地图找到指定地点是日常生活中经常用到的技能。</p>
        <p class="synopsis-content">2、选择不同类型的地图， 会提出相应的问题请您回答。</p>
        <p class="synopsis-content">3、请耐心查找，不要急躁 。一时找不到时不要灰心，不要放弃。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <img class="left-icon" src="/static/game_assets/game102/left_icon.png" />
      <img class="right-icon" src="/static/game_assets/game102/right_icon.png" />

      <div class="game-guide" v-show="answerStatus === 'ready'">
        <div class="guide-left">
          <img class="left-bg" src="/static/game_assets/game102/left_bg.png" />
          <img class="left-img" src="/static/game_assets/game102/content_img.png" />
        </div>

        <div class="guide-right">
          <img class="right-bg right-bg1" src="/static/game_assets/game102/book1.png" />
          <img class="right-bg right-bg2" src="/static/game_assets/game102/book2.png" />

          <p class="right-title">选择地图</p>

          <div class="right-choose">
            <p class="choose-text">中国省图</p>
            <div class="choose-icon">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img class="icon" src="/static/game_assets/game32/correct.png" />
            </div>
          </div>

          <!-- <div class="right-choose">
            <div class="choose-item">
              <p class="choose-text">北京交通</p>
              <div class="choose-icon" @click="chooseMapItem(1)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="chooseMap === 1" class="icon" src="/static/game_assets/game32/correct.png" />
              </div>
            </div>
            
            <div class="choose-item">
              <p class="choose-text">北京市政</p>
              <div class="choose-icon" @click="chooseMapItem(2)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="chooseMap === 2" class="icon" src="/static/game_assets/game32/correct.png" />
              </div>
            </div>

            <div class="choose-item">
              <p class="choose-text">中国省图</p>
              <div class="choose-icon" @click="chooseMapItem(3)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="chooseMap === 3" class="icon" src="/static/game_assets/game32/correct.png" />
              </div>
            </div>
          </div> -->

          <img class="right-btn" src="/static/game_assets/game102/btn1.png" @click="startGame" />
        </div>
      </div>

      <div class="content" v-show="answerStatus === 'answer'">
        <div class="content-left">
          <img class="left-bg" src="/static/game_assets/game102/left_bg.png" />
          <img class="left-icon" src="/static/game_assets/game102/top_icon.png" />
          <img class="left-img" src="/static/game_assets/game102/item2.png" />
        </div>

        <div class="content-right">
          <p class="right-title">{{current.question}}</p>

          <div class="right-item" v-for="item in current.answerList" :key="item.index + 'item'" @click="chooseItem(item.index)">
            <img class="item-bg" src="/static/game_assets/game102/btn_bg2.png" />
            <img class="item-bg" v-if="choose === item.index" src="/static/game_assets/game102/btn_bg1.png" />

            <span class="item-text">{{item.value}}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="index" v-if="answerStatus === 'answer'">
          <img class="index-bg" src="/static/game_assets/game102/icon.png" />
          <span class="index-text">{{number}}/5</span>
        </div>
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game103',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,
      answerStatus: 'ready',  // ready -- 准备 answer -- 答题
      question: [],
      current: {},
      chooseMap: 3, // 1 -- 北京交通 2 -- 北京市政 3 -- 中国省区
      choose: 0,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
    },

    startGame () {
      if (!this.chooseMap) return

      this.answerStatus = 'answer'
      // this.timing()
      this.question = api.getRandomArray(data.data, 5)
      this.startProcess()
    },

    startProcess () {
      this.current = this.question[this.number]
      this.current.answerList = api.shuffle(this.current.answerList)
      this.number++
    },

    chooseMapItem (item) {
      this.chooseMap = item
    },

    chooseItem (item) {
      if (this.isShowCorrect || this.isShowError) return
      this.choose = item

      if (this.choose === this.current.answer) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }

      setTimeout(() => {
        this.isShowError = false
        this.isShowCorrect = false

        if (this.number >= 5) {
          this.submit()
        } else {
          this.choose = 0
          this.startProcess()
        }
      }, 800)
    },

    stop () {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.store = 20 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answerStatus = 'ready'
      this.question = []
      this.choose = 0
      this.chooseMap = 3
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game103-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game102/bg.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .left-icon {
      position: absolute;
      left: 69px * 0.7;
      bottom: 0;
      width: 437px * 0.7;
    }

    .right-icon {
      position: absolute;
      bottom: 0;
      right: 30px * 0.7;
      width: 177px * 0.7;
    }

    .game-guide {
      position: relative;
      width: 1890px * 0.7;
      height: 960px * 0.7;
      margin-right: 30px * 0.7;
      margin-bottom: 58px * 0.7;

      .guide-left {
        position: absolute;
        left: 0;
        top: 0;
        width: 1341px * 0.7;
        height: 959px * 0.7;

        .left-bg {
          position: absolute;
          left: 0;
          top: 0;
          width: 1341px * 0.7;
          height: 959px * 0.7;
        }

        .left-img {
          position: absolute;
          top: 259px * 0.7;
          left: 208px * 0.7;
          width: 724px * 0.7;
        }
      }

      .guide-right {
        position: absolute;
        top: 139px * 0.7;
        right: 0;
        width: 674px * 0.7;
        height: 763px * 0.7;
        display: flex;
        flex-direction: column;
        padding: 168px * 0.7 210px * 0.7 0 230px * 0.7;
        align-items: center;

        .right-bg {
          position: absolute;
        }

        .right-bg1 {
          top: 0;
          left: 0;
          width: 627px * 0.7;
        }

        .right-bg2 {
          top: 18px * 0.7;
          left: 10px * 0.7;
          width: 665px * 0.7;
        }

        .right-title {
          position: relative;
          margin: 0;
          font-size: 56px * 0.7;
          line-height: 56px * 0.7;
          text-align: center;
          font-weight: 500;
          color: #fefefe;
          text-align: center;
        }

        .right-choose {
          display: inline-flex;
          justify-content: center;
          padding-top: 30px * 0.7;
          padding-bottom: 140px * 0.7;

          .choose-text {
            position: relative;
            margin: 0;
            padding-right: 18px * 0.7;
            font-size: 36px * 0.7;
            line-height: 41px * 0.7;
            color: #fff;
          }

          .choose-icon {
            position: relative;
            width: 41px * 0.7;
            height: 41px * 0.7;

            .bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 41px * 0.7;
              height: 41px * 0.7;
            }

            .icon {
              position: relative;
              width: 41px * 0.7;
              height: 35px * 0.7;
            }
          }
        }

        // .right-choose {
        //   display: flex;
        //   flex-direction: column;
        //   justify-content: space-between;
        //   height: 219px * 0.7;
        //   padding-top: 30px * 0.7;
        //   padding-bottom: 36px * 0.7;

        //   .choose-item {
        //     display: inline-flex;
        //     justify-content: center;

        //     .choose-text {
        //       position: relative;
        //       margin: 0;
        //       padding-right: 18px * 0.7;
        //       font-size: 36px * 0.7;
        //       line-height: 41px * 0.7;
        //       color: #fff;
        //     }

        //     .choose-icon {
        //       position: relative;
        //       width: 41px * 0.7;
        //       height: 41px * 0.7;
        //       cursor: pointer;

        //       .bg {
        //         position: absolute;
        //         top: 0;
        //         left: 0;
        //         width: 41px * 0.7;
        //         height: 41px * 0.7;
        //       }

        //       .icon {
        //         position: relative;
        //         width: 41px * 0.7;
        //         height: 35px * 0.7;
        //       }
        //     }
        //   }
        // }

        .right-btn {
          position: relative;
          width: 213px * 0.7;
          cursor: pointer;
        }
      }
    }

    .content {
      position: relative;
      width: 1997px * 0.7;
      height: 991px * 0.7;
      margin-right: 23px * 0.7;
      margin-bottom: 89px * 0.7;

      .content-left {
        position: absolute;
        top: 0;
        left: 0;
        width: 1330px * 0.7;
        height: 991px * 0.7;
        z-index: 1;

        .left-bg {
          position: absolute;
          top: 32px * 0.7;
          left: 0;
          width: 1330px * 0.7;
        }

        .left-icon {
          position: absolute;
          bottom: 88px * 0.7;
          left: 71px * 0.7;
          width: 204px * 0.7;
        }

        .left-img {
          position: absolute;
          top: 85px * 0.7;
          left: 107px * 0.7;
          width: 995px * 0.7;
        }
      }

      .content-right {
        position: absolute;
        top: 0;
        right: 0;
        width: 777px * 0.7;
        height: 991px * 0.7;
        background: #b8e09d;
        padding: 304px * 0.7 92px * 0.7 130px * 0.7 201px * 0.7;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .right-title {
          position: absolute;
          top: 147px * 0.7;
          left: 0;
          margin: 0;
          padding-left: 200px * 0.7;
          padding-bottom: 17px * 0.7;
          font-size: 42px * 0.7;
          line-height: 42px * 0.7;
          font-weight: 500;
          color: #192333;
        }

        .right-item {
          position: relative;
          width: 508px * 0.7;
          height: 128px * 0.7;
          padding: 32px * 0.7 0 46px * 0.7 0;
          cursor: pointer;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 508px * 0.7;
          }

          .item-text {
            position: relative;
            display: block;
            font-size: 50px * 0.7;
            line-height: 50px * 0.7;
            text-align: center;
            font-weight: 500;
            color: #f8f9ff;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 40px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1775px * 0.7;
      margin-left: 75px * 0.7;
      z-index: 2;

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .index {
        position: relative;
        width: 96px * 0.7;
        height: 97px * 0.7;

        .index-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 96px * 0.7;
          height: 97px * 0.7;
        }

        .index-text {
          position: relative;
          display: block;
          font-size: 28px * 0.7;
          line-height: 97px * 0.7;
          text-align: center;
          font-weight: 500;
          color: #fefefe;
        }
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
      z-index: 99;
    }
  }
}
</style>