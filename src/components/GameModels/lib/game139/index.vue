<template>
  <div class="game139-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="数字排序" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请根据提示按数字顺序或倒序排列起来；</p>
        <p class="synopsis-content">2、通过拖拽进行顺序变换；</p>
        <p class="synopsis-content">3、训练结束后会出现成绩单；</p>
        <p class="synopsis-content">4、本训练难度为C（困难）。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />

        <div class="top-text">将下面数字按照从{{type === 1 ? '小到大' : '大到小'}}的顺序排序</div>
      </div>

      <draggable class="content" v-model="dragItem" :group="group1">
        <div class="content-item" v-for="item in dragItem" :key="item + 'item'">{{item}}</div>
      </draggable>

      <div class="footer">
        <img class="img" src="/static/game_assets/common/submit.png" @click="submit">

        <div class="footer-right">
          <p class="item">题目分数：{{store}}</p>
          <p class="item">题目数量：{{number}}</p>
          <p class="item">用时：{{time}}</p>
        </div>
      </div>
    </div>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import api from '../../utils/common.js'

export default {
  name: 'game139',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    draggable
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      level: 3,
      status: 1,
      isPlay: false,
      isStop: false,

      type: 1, // 1-顺序 2-倒序
      dragItem: [],
      group1: {
        name: 'itemList',
        pull: true,
        put: true
      },

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start () {
      this.level = this.info.level || 3
      this.startProcess()
      this.status = 3
      // this.timing()
      this.play()
    },

    startProcess () {
      this.type = api.randomNum(1, 2)
      this.dragItem = []
      const num = api.randomNum(21, 99)
      if (num + 5 >= 99) {
        for (let i = num - 4; i <= num; i++) {
          this.dragItem.push(i)
        }
      } else {
        for (let i = num; i <= num + 4; i++) {
          this.dragItem.push(i)
        }
      }
      this.dragItem = api.shuffle(this.dragItem)
      this.number++
    },

    submit () {
      let flag = false
      for (let i = 1; i < 5; i++) {
        const num1 = this.dragItem[i - 1]
        const num2 = this.dragItem[i]
        if (this.type === 1 && num1 > num2) flag = true
        if (this.type === 2 && num1 < num2) flag = true
      }

      if (flag) {
        this.errorNum++
      } else {
        this.succesNum++
      }
      this.store = 10 * this.succesNum

      if (this.number < 10) {
        this.startProcess()
      } else {
        this.pause()
        this.isStop = true
        this.infos[0].value = this.level
        this.infos[1].value = this.second
        this.infos[2].value = this.succesNum
        this.infos[3].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: this.level,
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.dragItem = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game139-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game137/bg.png');
  }

  .game-synopsis {
    width: 707px * 0.7;
    height: 444px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border: 2px * 0.7 solid #50b5ce;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 40px * 0.7;
      left: 0;
      width: 100%;
      padding: 0 54px * 0.7;

      .top-icon {
        position: absolute;
        top: 0;
        left: 50px * 0.7;
        width: 80px * 0.7;
        cursor: pointer;
        z-index: 1;
      }

      .top-text {
        position: relative;
        font-size: 50px * 0.7;
        line-height: 82px * 0.7;
        text-align: center;
        font-weight: 600;
        color: #2f89a0;
      }
    }

    .content {
      position: relative;
      width: 1398px * 0.7;
      height: 237px * 0.7;
      margin-bottom: 230px * 0.7;
      display: flex;
      justify-content: space-between;

      .content-item {
        width: 237px * 0.7;
        height: 237px * 0.7;
        border-radius: 50%;
        background: #fff;

        font-size: 127px * 0.7;
        line-height: 237px * 0.7;
        text-align: center;
        font-weight: 600;
        color: #2f89a0;
        cursor: pointer;

        &:active {
          background: #ffe6e5;
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 88px * 0.7;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      width: 1620px * 0.7;

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }

      .footer-right {
        width: 690px * 0.7;
        display: inline-flex;
        justify-content: space-between;

        .item {
          margin: 0;
          width: 210px * 0.7;
          height: 76px * 0.7;
          border-radius: 4px * 0.7;
          background: #fff;

          font-size: 24px * 0.7;
          text-align: center;
          line-height: 74px * 0.7;
          color: #5f6fd7;
        }
      }
    }
  }
}
</style>