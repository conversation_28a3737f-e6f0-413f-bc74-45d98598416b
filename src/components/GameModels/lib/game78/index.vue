<template>
  <div class="game79-page">
    <div class="page-bg "></div>
    <div class="page-bg1" v-if="showBg || status == 1"></div>
    <settingPageLib @start="isStart" @challenge="start" v-if="status < 3">
      <div class=" game-synopsis">
        <p class="synopsis-title">辨认时间方式一</p>
        <div class="synopsis-content">
          <div class="training-that">训练说明</div>
          <p>1、这是一项与日常生活中思维能力相关的训练。</p>
          <p>2、要求正确分辨指针所表示的时间。</p>
        </div>
      </div>
    </settingPageLib>
    <div class="page-content page-bg2" v-if="status == 3">
      <div class="answer">
        <div :class="'answer-item ' + (currentClick == index ? 'current' :'' )" v-for="(item,index) of answer" :key="index" @click="selectItem(item, index)">
          <div class="answer-contanier">
            <img class="center" src='/static/game_assets/game78/center.png' alt="">
            <img class="minute" :style="{transform:`rotate(${item.minuteScale}deg)`}" src='/static/game_assets/game78/minute.png' alt="">
            <img class="hour" :style="{transform:`rotate(${item.hourScale}deg)`}" src='/static/game_assets/game78/hour.png' alt="">
          </div>
        </div>
      </div>
      <div class="question">
        <img class="question-icon" src="/static/game_assets/game78/icon1.png" alt="">
        <div class="question-label">{{question}}</div>
      </div>
      <div class="btn">
        <img class="btn_item" src='/static/game_assets/game78/stop.png' alt="" @click="stop">
        <img class="btn_item" src='/static/game_assets/game78/next.png' alt="" v-if="isSelect" @click="next">
      </div>
      <img class="check_answer" src="/static/game_assets/game78/right.png" v-if="isSelect && isAnswer == 0" alt="">
      <img class="check_answer" src="/static/game_assets/game78/false.png" v-if="isSelect && isAnswer == 1" alt="">
    </div>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'

export default {
  name: 'game78',
  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },
  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      formData: [
        {
          "label": ["2020-10-20 05:00:00", "2020-10-20 20:00:00", "2020-10-20 09:00:00", "2020-10-20 01:00:00"],
          "value": "2020-10-20 05:00:00"
        },
        {
          "label": ["2020-10-20 05:00:00", "2020-10-20 02:00:00", "2020-10-20 10:00:00", "2020-10-20 01:00:00"],
          "value": "2020-10-20 10:00:00"
        },
        {
          "label": ["2020-10-20 17:20:00", "2020-10-20 04:50:00", "2020-10-20 11:40:00", "2020-10-20 06:50:00"],
          "value": "2020-10-20 06:50:00"
        },
        {
          "label": ["2020-10-20 15:40:00", "2020-10-20 02:20:00", "2020-10-20 09:25:00", "2020-10-20 01:50:00"],
          "value": "2020-10-20 15:40:00"
        },
        {
          "label": ["2020-10-20 20:40:00", "2020-10-20 07:20:00", "2020-10-20 11:25:00", "2020-10-20 06:50:00"],
          "value": "2020-10-20 11:25:00"
        },

      ],
      status: 1,
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ],
      total: 0,
      answer: [],
      quetion: "",
      unit: 0,
      currentClick: -1,
      isSelect: false, // 已经选择时间
      isAnswer: false, // 答案是否正确错误
      showBg: true,
      number: 0, //得分
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      params: {},
      isStop: false,
      show: false,
      minuteScale: "",
      hourScale: "",
      currentClick: -1,
      isSelect: false, // 已经选择时间
      isAnswer: -1, // 答案是否正确错误
      showBg: true,
      select: ""
    }
  },
  mounted () {
    this.timing()
  },
  methods: {
    timing () { // 计时
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },
    start () {
      this.level = Number(this.info.level) || 1
      this.startProcess()
      this.status = 3
      // this.timing()
    },
    isStart () { //处理开始页面背景
      this.status = 2,
        this.showBg = false
    },
    stop () { // 停止
      this.isStop = true
      this.show = true
    },
    cancel () {  // 继续游戏, 继续计时
      this.isStop = false
      this.timing()
    },
    startProcess () { //处理题目
      let unit = this.unit
      let answer = this.formData[unit].label
      let question = this.formData[unit].value
      this.total = this.formData.length
      for (let item of answer) {
        this.answer.push({
          time: item,
          minuteScale: 0,
          hourScale: 0
        })
      }
      this.question = question.substring(11, 16)
      this.setClock(this.answer)
    },
    setClock (arr) { // 时钟指针
      for (let item of arr) {
        let _time = new Date(item.time);
        let second = _time.getSeconds();
        let min = _time.getMinutes() + second / 60;
        let hour = _time.getHours() + min / 60;
        // 计算各指针对应的角度
        let secondAngle = second * 6;
        let minAngle = min * 6;
        let hourAngle = hour * 30;
        // 转动指针
        item.minuteScale = minAngle + 11;
        item.hourScale = hourAngle + (-50);
      }
      this.answer = arr
    },
    next () { // 完成 下一题
      let unitPoint = Math.floor(100 / this.total)

      let select = this.formData[this.unit].value
      if (this.select == select) {
        this.isAnswer = 0
        this.number += unitPoint
        this.succesNum++
      } else {
        this.isAnswer = 1
        this.errorNum++

      }
      setTimeout(() => {
        this.isAnswer = -1
        if (this.unit < this.total - 1) {
          this.unit++
          this.answer = []
          this.currentClick = -1
          this.isSelect = false
          this.startProcess()
        } else {
          this.submit()
        }
      }, 500)

    },
    submit () {  // 提交
      if (this.succesNum == this.total) {
        this.number = 100
      }
      this.isStop = true
      this.store = this.number
      this.infos[0].value = this.level
      this.infos[1].value = api.getTime(this.second)
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },
    again () { //重新开始
      this.answer = []
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.unit = 0
      this.question = ""
      this.currentClick = -1
      this.isSelect = false
      this.isStop = false

      this.start()
      this.timing()
    },
    selectItem (item, index) {  // 答题
      this.currentClick = index
      this.isSelect = true
      this.select = item.time
    }
  }
}
</script>

<style lang="scss">
.game79-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game78/bg.png');
  }
  .page-bg1 {
    width: 1576px * 0.7;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game78/bg2.png');
  }
  .game-synopsis {
    width: 1576px * 0.7;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 33px * 0.7 31px * 0.7;
    // background-image: url('/static/game_assets/game78/bg2.png');
    background-size: 100% 100%;
    align-items: center;
    .synopsis-title {
      margin: 0;
      font-size: 48px * 0.7;
      font-family: PingFang SC;
      font-weight: 600;
      color: #93a843;
      line-height: 48px * 0.7;
      margin-top: 150px * 0.7;
    }
    .synopsis-garde {
      background: #fff6e3;
      border: 2px * 0.7 solid #c49e68;
      border-radius: 10px * 0.7;
      padding: 16px * 0.7 29px * 0.7;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36px * 0.7;
      font-family: PingFang SC;
      font-weight: 600;
      color: #414043;
      margin-top: 22px * 0.7;
    }
    .synopsis-content {
      padding-top: 25px * 0.7;
      width: 700px * 0.7;
      .training-that {
        font-size: 36px * 0.7;
        font-family: PingFang SC;
        font-weight: 600;
        color: #1e1e1d;
        line-height: 48px * 0.7;
        margin: 30px * 0.7 0;
      }
      p {
        font-size: 32px * 0.7;
        font-family: PingFang SC;
        font-weight: 400;
        color: #1e1e1d;
      }
    }
  }
  .page-content {
    width: 100%;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    padding: 0 27px * 0.7;
    .answer {
      display: flex;
      align-items: center;
      margin: 16vh auto 0;

      &-item {
        width: 300px * 0.7;
        height: 387px * 0.7;
        background-image: url('/static/game_assets/game78/clocks.png');
        background-size: 100% 100%;
        margin-top: 56px * 0.7;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        margin-right: 34px * 0.7;
        .answer-contanier {
          // background-color: rgba(147, 168, 67, 0.3);
          width: 265px * 0.7;
          height: 250px * 0.7;
          position: relative;
          margin-bottom: 45px * 0.7;
          margin-left: 15px * 0.7;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50%;

          .center {
            position: absolute;
            z-index: 3;
            top: 50%;
            left: 50%;
            width: 44px * 0.7;
            height: 44px * 0.7;
            margin-top: -22px * 0.7;
            margin-left: -22px * 0.7;
          }
          .minute {
            width: 22px * 0.7;
            height: 77px * 0.7;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-top: -80px * 0.7;
            margin-left: -18px * 0.7;
            transform-origin: right bottom;
            transform: rotate(19deg);
            z-index: 1;
          }
          .hour {
            width: 56px * 0.7;
            height: 52px * 0.7;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-top: -55px * 0.7;
            margin-left: 2px * 0.7;
            transform-origin: 0 100%;
            transform: rotate(-50deg);
            z-index: 2;
          }
        }
      }
      &-item:last-child {
        margin-right: 0;
      }
      .current {
        background-image: url('/static/game_assets/game79/currentbg.png');
      }
    }
    .question {
      display: flex;
      align-items: center;
      margin-top: 46px * 0.7;
      &-icon {
        width: 147px * 0.7;
        height: 147px * 0.7;
        margin-right: 19px * 0.7;
      }
      &-label {
        padding: 16px * 0.7 59px * 0.7 22px * 0.7 50px * 0.7;
        background-image: url('/static/game_assets/game78/icon4.png');
        background-size: 100% 100%;
        margin-right: 50px * 0.7;
        display: flex;
        align-items: center;
        padding-left: 59px * 0.7;
        font-size: 92px * 0.7;
        font-family: Impact;
        font-weight: 400;
        color: #ffffff;
      }
    }
    .btn {
      width: 1600px * 0.7;
      display: flex;
      align-items: center;
      &_item {
        width: 270px * 0.7;
        height: 115px * 0.7;
        margin-right: 53px * 0.7;
      }
    }
    .check_answer {
      width: 287px * 0.7;
      height: 287px * 0.7;
      position: absolute;
      z-index: 3;
      top: 40%;
    }
  }
  .page-bg2 {
    position: absolute;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url('/static/game_assets/game78/bg3.png');
  }
}
</style>
