<template>
  <div class="game214-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="符号命名" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本项目考察计算符号的命名。</p>
        <p class="synopsis-content">2、题目是多选题。</p>
        <p class="synopsis-content">3、一次8题全部答对可以终止这一项目练习。</p>
        <p class="synopsis-content">4、有错误者复习一下答案，再训练一次。第三次练习后仍有错误，也不再重新练习，而是继续做其他项目，次日接着训练本一项。</p>

        <div class="synopsis-choose">
          <p class="choose-title">训练方式:</p>

          <div class="item">
            <div class="item-icon" @click="setChoose(1)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 1" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">命名</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(2)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 2" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">听从</span>
          </div>
        </div>
      </div>
    </settingPageLib>

    <div class="game-content" v-if="status === 3">
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game206/title_bg.png" />
        <span class="title-text">符号命名</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game214/content_bg.png" />
        <img class="content-icon" src="/static/game_assets/game214/icon.png" />
        <div class="content-left" v-if="type === 1">
          <img class="left-bg" src="/static/game_assets/game215/text_bg.png" />
          <span class="left-text">{{currect.symbol}}</span>
        </div>

        <div :class="['content-main', level === 1 && 'content-small']">
          <div class="content-item" v-for="(item, index) in currect.answerList" :key="index + 'item'" @click="chooseItem(item.symbol)">
            <img class="item-bg" src="/static/game_assets/game214/btn_bg_1.png" />
            <img class="item-bg" v-if="answer === item.symbol" src="/static/game_assets/game214/btn_bg_2.png" />
            <span class="item-text">{{item.name}}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="footer-left">
          <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
          <img v-show="showNext" class="img" src="/static/game_assets/common/next.png" @click="toNext" />
          <img v-show="answer && !showNext" class="img" src="/static/game_assets/common/confirm.png" @click="confirm" />
        </div>
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 10道题 3个等级
// 1级 1个答案
// 2级 1-2个答案
// 3级 1-3个答案

export default {
  name: 'game214',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      musicUrl: '',
      number: 0,
      second: 0,
      store: 0,
      level: 1,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,
      showNext: false,

      questions: [],
      type: 1, // 训练模式
      answer: '',
      currect: {
        audio: '',
        symbol: '',
        answerList: []
      },


      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    setChoose (item) {
      this.type = item
    },

    play (url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start () {
      this.status = 3
      // this.timing()
      const list = [
        {
          symbol: '+',
          audio: '/static/game_assets/audio/game215/audio_1.mp3'
        },
        {
          symbol: '-',
          audio: '/static/game_assets/audio/game215/audio_2.mp3'
        },
        {
          symbol: '×',
          audio: '/static/game_assets/audio/game215/audio_3.mp3'
        },
        {
          symbol: '÷',
          audio: '/static/game_assets/audio/game215/audio_4.mp3'
        },
        {
          symbol: '=',
          audio: '/static/game_assets/audio/game215/audio_5.mp3'
        },
        {
          symbol: '≠',
          audio: '/static/game_assets/audio/game215/audio_6.mp3'
        },
        {
          symbol: '>',
          audio: '/static/game_assets/audio/game215/audio_7.mp3'
        },
        {
          symbol: '<',
          audio: '/static/game_assets/audio/game215/audio_8.mp3'
        }
      ]
      this.questions = api.shuffle(list)
      this.startProcess()
    },

    startProcess () {
      this.currect.answerList = []
      const answer1 = [
        {
          name: '加号',
          symbol: '+'
        },
        {
          name: '减号',
          symbol: '-'
        },
        {
          name: '乘号',
          symbol: '×'
        },
        {
          name: '除号',
          symbol: '÷'
        }
      ]
      const answer2 = [
        {
          name: '等于号',
          symbol: '='
        },
        {
          name: '不等号',
          symbol: '≠'
        },
        {
          name: '大于号',
          symbol: '>'
        },
        {
          name: '小于号',
          symbol: '<'
        }
      ]

      this.currect.symbol = this.questions[this.number].symbol
      this.currect.audio = this.questions[this.number].audio
      if (['+', '-', '×', '÷'].includes(this.currect.symbol)) {
        this.currect.answerList = api.shuffle(answer1)
      } else {
        this.currect.answerList = api.shuffle(answer2)
      }

      if (this.type === 2) {
        this.play(this.currect.audio)
      }
    },

    chooseItem (item) {
      if (this.answer === item || this.isShowError || this.isShowCorrect) return
      this.answer = item
    },

    confirm () {
      if (this.currect.symbol === this.answer) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }

      this.showNext = true
    },

    toNext () {
      this.showNext = false
      this.isShowCorrect = false
      this.isShowError = false
      this.answer = []
      this.number++
      if (this.number >= 8) {
        this.submit()
      } else {
        this.startProcess()
      }
    },

    stop () {
      this.isStop = true
      this.show = true
      if (this.type === 2) this.pause()
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
      if (this.type === 2) this.play()
    },

    submit () {
      this.isStop = true
      this.store = this.succesNum * 12.5
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.showNext = false
      this.answer = ''
      this.currect = {
        audio: '',
        symbol: '',
        answerList: []
      },
        this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game214-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game126/bg.png');
  }

  .game-synopsis {
    width: 1605px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
    }

    .synopsis-choose {
      display: flex;
      margin-top: 25px * 0.7;
      padding: 13px * 0.7 30px * 0.7;
      border: 1px * 0.7 solid #c49e68;
      border-radius: 10px * 0.7;
      background: #fff6e3;

      .choose-title {
        margin: 0;
        font-size: 36px * 0.7;
        line-height: 41px * 0.7;
        font-weight: 600;
        color: #414043;
      }

      .item {
        position: relative;
        display: inline-flex;
        align-items: center;
        padding-left: 25px * 0.7;

        .item-icon {
          position: relative;
          width: 41px * 0.7;
          height: 35px * 0.7;
          display: inline-flex;
          justify-content: center;
          cursor: pointer;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 41px * 0.7;
          }

          .icon1 {
            position: absolute;
            width: 41px * 0.7;
          }
        }

        .item-text {
          position: relative;
          top: 2px * 0.7;
          display: inline-block;
          padding-left: 10px * 0.7;
          font-size: 36px * 0.7;
          line-height: 41px * 0.7;
          font-weight: 600;
          color: #414043;
        }
      }
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      position: absolute;
      top: 0;
      width: 932px * 0.7;
      height: 125px * 0.7;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px * 0.7;
        height: 125px * 0.7;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 28px * 0.7 0 56px * 0.7 0;
        font-size: 36px * 0.7;
        line-height: 36px * 0.7;
        text-align: center;
        color: #1b1d2d;
      }
    }

    .content {
      position: relative;
      width: 1857px * 0.7;
      height: 855px * 0.7;
      margin-top: 150px * 0.7;
      padding: 234px * 0.7 436px * 0.7 290px * 0.7 376px * 0.7;
      display: flex;
      justify-content: center;
      align-content: flex-start;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1857px * 0.7;
      }

      .content-icon {
        position: absolute;
        bottom: 114px * 0.7;
        left: 217px * 0.7;
        width: 214px * 0.7;
      }

      .content-left {
        position: relative;
        width: 236px * 0.7;
        height: 330px * 0.7;
        padding-top: 13px * 0.7;

        .left-bg {
          position: absolute;
          top: 13px * 0.7;
          left: 0;
          width: 236px * 0.7;
        }

        .left-text {
          position: relative;
          display: block;
          padding-right: 30px * 0.7;
          font-size: 116px * 0.7;
          line-height: 317px * 0.7;
          text-align: center;
          font-weight: 600;
          font-family: PingFang SC;
          color: #fff;
        }
      }

      .content-main {
        position: relative;
        width: 766px * 0.7;
        height: 309;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-content: space-between;

        .content-item {
          position: relative;
          width: 383px * 0.7;
          height: 168px * 0.7;
          padding-left: 30px * 0.7;
          cursor: pointer;

          .item-bg {
            position: absolute;
            top: 0;
            left: 30px * 0.7;
            width: 353px * 0.7;
          }

          .item-text {
            position: relative;
            display: block;
            font-size: 77px * 0.7;
            line-height: 138px * 0.7;
            text-align: center;
            color: #1b1d2d;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 72px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1480px * 0.7;

      .footer-left {
        width: 620px * 0.7;
        display: inline-flex;
        justify-content: space-between;
      }

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
    }
  }
}
</style>