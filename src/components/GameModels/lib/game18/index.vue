<template>
  <div class="game18-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="找不同字" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、查找不同训练的目的是培养被训练者的观察力和提高对所关注信息的选择能力。</p>
        <p class="synopsis-content">2、训练内容丰富，比对的两组文字几乎一样。但有几处不同，请您尽快把它们找出来。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="title">
        <img src="/static/game_assets/game18/title_bg.png" />
        <span>找出两个方块中相同位置的不同文字</span>
      </div>

      <div class="content">
        <div class="left">
          <div class="item" v-for="item in questions1" :key="item + '-1'" @click="chooseItem(item, 1)">
            <img class="bg" src="/static/game_assets/game18/text_bg.png">
            <img v-if="errorArr.includes(item)" class="bg" src="/static/game_assets/game18/error_bg.png">
            <img v-if="correctArr.includes(item) || reply.includes(item)" class="bg" src="/static/game_assets/game18/correct_bg.png">
            <span :class="['text', correctArr.includes(item) || reply.includes(item) && 'correct-text', errorArr.includes(item) && 'error-text']">{{ item }}</span>
          </div>
        </div>

        <div class="right">
          <div class="item" v-for="item in questions2" :key="item + '-2'" @click="chooseItem(item, 2)">
            <img class="bg" src="/static/game_assets/game18/text_bg.png">
            <img v-if="errorArr.includes(item)" class="bg" src="/static/game_assets/game18/error_bg.png">
            <img v-if="correctArr.includes(item) || reply.includes(item)" class="bg" src="/static/game_assets/game18/correct_bg.png">
            <span :class="['text', correctArr.includes(item) || reply.includes(item) && 'correct-text', errorArr.includes(item) && 'error-text']">{{ item }}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <img v-if="reply.length >= 10" class="img3" src="/static/game_assets/common/submit.png" @click="submit">
      </div>
    </div>

    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 游戏分为2个回合

export default {
  name: 'game18',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/error_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      questions1: [],
      questions2: [],
      answer: [], // 不同的文字集合
      reply: [], // 用户选择的文字集合
      correctArr: [], // 正确文字集合
      errorArr: [], // 错误文字集合

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play () {
      this.$refs.music.play()
    },

    pause () {
      this.$refs.music.pause()
    },

    start () {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      const arr = ['苗', '大', '云', '五', '饲', '夕', '进', '纶', '甬', '故', '区', '诤', '妇', '生', '乎', '匣', '飞', '必', '洁', '沈', '也', '北', '咯', '伐', '买', '运', '坐', '浓', '化', '匹', '刑', '含', '丑', '木', '百', '瓦', '忽', '王', '刮', '巧', '凶', '已', '几', '甲', '厂', '马', '人', '同', '夜', '寿', '无', '丸', '爷', '万', '创', '己', '周']
      this.answer = api.getRandomArray(arr, 10)
      const list = arr.filter(item => !this.answer.includes(item))
      this.questions1 = api.shuffle(api.getRandomArray(list, 20).concat(this.answer.slice(0, 5)))

      let sort = 5
      this.questions2 = JSON.parse(JSON.stringify(this.questions1))
      this.questions2.forEach((item, index) => {
        if (this.answer.includes(item)) {
          this.questions2.splice(index, 1, this.answer[sort])
          sort++
        }
      })
    },

    chooseItem (item, flag) {
      if (this.reply.includes(item)) {
        this.reply = this.reply.filter(it => it !== item)
        if (flag === 1) {
          const index = this.questions1.indexOf(item)
          this.reply = this.reply.filter(it => it !== this.questions2[index])
        } else {
          const index = this.questions2.indexOf(item)
          this.reply = this.reply.filter(it => it !== this.questions1[index])
        }
        return
      }

      this.reply.push(item)
      if (flag === 1) {
        const index = this.questions1.indexOf(item)
        this.reply.push(this.questions2[index])
      } else {
        const index = this.questions2.indexOf(item)
        this.reply.push(this.questions1[index])
      }
    },

    submit () {
      this.reply.forEach(item => {
        if (this.answer.includes(item)) {
          this.succesNum++
          this.correctArr.push(item)
        } else {
          this.errorNum++
          this.errorArr.push(item)
        }
      })

      this.answer.forEach(item => {
        if (!this.correctArr.includes(item)) {
          this.correctArr.push(item)
          this.errorNum++
        }
      })
      this.reply = []
      this.number++
      setTimeout(() => {
        if (this.number >= 2) {
          this.pause()
          this.isStop = true
          this.succesNum = parseInt(this.succesNum / 2)
          this.errorNum = parseInt(this.errorNum / 2)
          this.store = parseInt(100 / (this.succesNum + this.errorNum) * this.succesNum)
          this.infos[0].value = this.second
          this.infos[1].value = this.succesNum
          this.infos[2].value = this.errorNum
          this.status = 4
          this.params = {
            id: this.info.id,
            grade: '',
            time: this.second,
            totalPoints: this.store
          }
        } else {
          this.answer = []
          this.questions1 = []
          this.questions2 = []
          this.correctArr = []
          this.errorArr = []
          this.startProcess()
        }
      }, 1500)
    },

    stop () {
      this.pause()
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    again () {
      this.number = 0
      this.second = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = []
      this.questions1 = []
      this.questions2 = []
      this.correctArr = []
      this.errorArr = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game18-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game12/bg.png');
  }

  .game-synopsis {
    width: 787px * 0.7;
    height: 484px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border: 2px * 0.7 solid #7bbd41;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    width: 100%;
    padding-top: 17px * 0.7;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .title {
      position: relative;
      width: 864px * 0.7;
      height: 221px * 0.7;

      img {
        position: absolute;
        top: 0;
        left: 0;
        width: 864px * 0.7;
        height: 221px * 0.7;
      }

      span {
        position: relative;
        display: inline-block;
        padding: 81px * 0.7 96px * 0.7;
        font-size: 42px * 0.7;
        line-height: 58px * 0.7;
        font-weight: 500;
        color: #5e381f;
        user-select: none;
      }
    }

    .content {
      width: 1362px * 0.7;
      height: 648px * 0.7;
      padding-top: 10px * 0.7;
      display: flex;
      justify-content: space-between;

      .left,
      .right {
        width: 651px * 0.7;
        height: 638px * 0.7;
        display: flex;
        flex-wrap: wrap;
        padding: 9px * 0.7 15px * 0.7;
        background: #e8efd9;
        border-radius: 12px * 0.7;
      }

      .item {
        position: relative;
        width: 124px * 0.7;
        height: 124px * 0.7;
        padding: 13px * 0.7;

        .bg {
          position: absolute;
          top: 13px * 0.7;
          left: 13px * 0.7;
          width: 98px * 0.7;
          height: 98px * 0.7;
          cursor: pointer;
        }

        .text {
          position: relative;
          display: block;
          font-size: 42px * 0.7;
          line-height: 98px * 0.7;
          text-align: center;
          font-weight: 800;
          color: #5e381f;
          cursor: pointer;
          user-select: none;
        }

        .correct-text {
          color: #118961;
        }

        .error-text {
          color: #902d1d;
        }
      }
    }

    .footer {
      position: relative;
      display: flex;
      justify-content: space-between;
      width: 1745px * 0.7;
      padding-bottom: 44px * 0.7;
      margin: 0 auto;

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img3 {
        width: 267px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }
}
</style>