<template>
  <div class="game76-page">
    <div class="page-bg" :class="status === 1 || status === 4 ? 'page-bg1': 'page-bg2'"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <audio ref="music2" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPageLib title="连线成物" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">请根据提示，顺序点击数字连线。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <canvas id="mycanvas" width="1400" height="800"></canvas>
        <template v-if="current === 1">
          <img :class="['item-1-img', choose === total && 'flash-item']" src="/static/game_assets/game76/item_1.png" />
          <div :class="['point-item', 'point-item-1-' + item, choose >= item && 'choose-point']" v-for="item in 5" :key="item + 'point1'" :ref="`point${item}`" @click="chooseItem(item)">{{item}}</div>
        </template>

        <template v-if="current === 2">
          <img :class="['item-2-img', choose === total && 'flash-item']" src="/static/game_assets/game76/item_2.png" />
          <div :class="['point-item', 'point-item-2-' + item, choose >= item && 'choose-point']" v-for="item in 6" :key="item + 'point2'" :ref="`point${item}`" @click="chooseItem(item)">{{item}}</div>
        </template>

        <template v-if="current === 3">
          <img :class="['item-3-img', choose === total && 'flash-item']" src="/static/game_assets/game76/item_3.png" />
          <div :class="['point-item', 'point-item-3-' + item, choose >= item && 'choose-point']" v-for="item in 6" :key="item + 'point3'" :ref="`point${item}`" @click="chooseItem(item)">{{item}}</div>
        </template>

        <template v-if="current === 4">
          <img :class="['item-4-img', choose === total && 'flash-item']" src="/static/game_assets/game76/item_4.png" />
          <div :class="['point-item', 'point-item-4-' + item, choose >= item && 'choose-point']" v-for="item in 8" :key="item + 'point4'" :ref="`point${item}`" @click="chooseItem(item)">{{item}}</div>
        </template>
      </div>

      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>

      <img class="footer" src="/static/game_assets/game76/next.png" @click="next">
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import api from '../../utils/common.js'

export default {
  name: 'game76',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      questionList: [],
      current: 0,
      choose: 0,
      total: 0,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      const list = [1, 2, 3, 4]
      this.questionList = api.getRandomArray(list, 3)
      // this.timing()
      this.startProcess()
      this.play()
    },

    startProcess () {
      this.current = this.questionList[this.number]
      this.number++
      if (this.current === 1) {
        this.total = 5
      } else if (this.current === 2 || this.current === 3) {
        this.total = 6
      } else if (this.current === 4) {
        this.total = 8
      }
    },

    chooseItem (item) {
      if (item !== this.choose + 1) {
        this.errorNum++
        this.playError()
        return
      }

      if (!this.choose) {
        this.choose = item
        this.succesNum++
        return
      }

      const ref1 = this.$refs[`point${this.choose}`][0]
      const x1 = Number(ref1.getBoundingClientRect().left) + Number(ref1.offsetHeight) / 2
      const y1 = Number(ref1.getBoundingClientRect().top) + Number(ref1.offsetWidth) / 2
      const ref2 = this.$refs[`point${item}`][0]
      const x2 = Number(ref2.getBoundingClientRect().left) + Number(ref2.offsetHeight) / 2
      const y2 = Number(ref2.getBoundingClientRect().top) + Number(ref2.offsetWidth) / 2
      this.drawLine(x1, y1, x2, y2)

      if (item === this.total) {
        const ref3 = this.$refs[`point${item}`][0]
        const x3 = Number(ref3.getBoundingClientRect().left) + Number(ref3.offsetHeight) / 2
        const y3 = Number(ref3.getBoundingClientRect().top) + Number(ref3.offsetWidth) / 2
        const ref4 = this.$refs[`point${1}`][0]
        const x4 = Number(ref4.getBoundingClientRect().left) + Number(ref4.offsetHeight) / 2
        const y4 = Number(ref4.getBoundingClientRect().top) + Number(ref4.offsetWidth) / 2
        this.drawLine(x3, y3, x4, y4)
      }
      this.succesNum++
      this.choose = item
    },

    drawDot (x1, y1, r) {
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.save()
      ctx.beginPath() //不写会和线连起来
      ctx.fillStyle = "#69BF7F"
      //绘制成矩形
      ctx.arc(x1, y1, r ? r : 2, 0, 2 * Math.PI)
      ctx.fill()
      ctx.restore()
    },

    drawLine (x1, y1, x2, y2) {
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.save()
      ctx.beginPath() //不写每次都会重绘上次的线
      ctx.lineCap = "round"
      ctx.lineJoin = "round"

      ctx.moveTo(x1, y1)
      ctx.lineTo(x2, y2)
      ctx.closePath()
      ctx.strokeStyle = "#69BF7F"
      ctx.lineWidth = 4
      ctx.stroke()
      ctx.restore()
    },

    // TODO: 返回首页
    goHome () {
      this.isStop = true
      this.pause()
      this.$router.go(-1)
    },

    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    playError () {
      this.$refs.music2.play()
    },

    pause () {
      this.$refs.music.pause()
      this.$refs.music2.pause()
      this.isPlay = false
    },

    next () {
      if (this.choose < this.total) {
        this.playError()
        return
      }
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, 1400, 800)

      this.errorNum = this.errorNum + (this.total - this.choose)

      if (this.number < 3) {
        this.choose = 0
        this.startProcess()
      } else {
        this.submit()
      }
    },

    submit () {
      this.pause()
      this.isStop = true
      this.store = 100 - 5 * this.errorNum > 0 ? 100 - 5 * this.errorNum : 0
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.questionList = []
      this.choose = 0
      this.current = 0
      this.total = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game76-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background-image: url('/static/game_assets/game80/bg.png');
  }

  .page-bg2 {
    background: url('/static/game_assets/game76/bg.png') center center no-repeat;
    background-size: cover;
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 40px * 0.7;
      left: 105px * 0.7;
      width: 175px * 0.7;
      display: flex;
      justify-content: space-between;

      .top-icon {
        width: 80px * 0.7;
        cursor: pointer;
      }
    }

    .content {
      position: relative;
      width: 100%;
      height: 100%;

      .point-item {
        position: absolute;
        width: 82px * 0.7;
        height: 82px * 0.7;
        background: #198279;
        border-radius: 50%;
        font-size: 69px * 0.7;
        font-weight: 600;
        font-family: PingFang SC;
        text-align: center;
        line-height: 82px * 0.7;
        color: #fff;
        cursor: pointer;
      }

      .choose-point {
        background: #69bf7f;
      }

      .item-1-img {
        position: absolute;
        top: 205px * 0.7;
        left: 724px * 0.7;
        width: 406px * 0.7;
        opacity: 0;
      }

      .item-2-img {
        position: absolute;
        top: 291px * 0.7;
        left: 734px * 0.7;
        width: 424px * 0.7;
        opacity: 0;
      }

      .item-3-img {
        position: absolute;
        top: 223px * 0.7;
        left: 752px * 0.7;
        width: 425px * 0.7;
        opacity: 0;
      }

      .item-4-img {
        position: absolute;
        top: 275px * 0.7;
        left: 738px * 0.7;
        width: 389px * 0.7;
        opacity: 0;
      }

      .flash-item {
        animation: flash 1.5s linear infinite;
      }

      .point-item-1-1 {
        top: 100px * 0.7;
        left: 930px * 0.7;
      }

      .point-item-1-2 {
        top: 347px * 0.7;
        right: 663px * 0.7;
      }

      .point-item-1-3 {
        top: 685px * 0.7;
        right: 731px * 0.7;
      }

      .point-item-1-4 {
        top: 632px * 0.7;
        left: 615px * 0.7;
      }

      .point-item-1-5 {
        top: 329px * 0.7;
        left: 651px * 0.7;
      }

      .point-item-2-1 {
        top: 423px * 0.7;
        right: 717px * 0.7;
      }

      .point-item-2-2 {
        top: 635px * 0.7;
        right: 658px * 0.7;
      }

      .point-item-2-3 {
        top: 641px * 0.7;
        right: 892px * 0.7;
      }

      .point-item-2-4 {
        top: 393px * 0.7;
        left: 626px * 0.7;
      }

      .point-item-2-5 {
        top: 239px * 0.7;
        left: 673px * 0.7;
      }

      .point-item-2-6 {
        top: 176px * 0.7;
        left: 817px * 0.7;
      }

      .point-item-3-1 {
        top: 95px * 0.7;
        left: 965px * 0.7;
      }

      .point-item-3-2 {
        top: 473px * 0.7;
        left: 1239px * 0.7;
      }

      .point-item-3-3 {
        top: 568px * 0.7;
        left: 971px * 0.7;
      }

      .point-item-3-4 {
        top: 738px * 0.7;
        left: 865px * 0.7;
      }

      .point-item-3-5 {
        top: 417px * 0.7;
        left: 625px * 0.7;
      }

      .point-item-3-6 {
        top: 289px * 0.7;
        left: 811px * 0.7;
      }

      .point-item-4-1 {
        top: 167px * 0.7;
        left: 1046px * 0.7;
      }

      .point-item-4-2 {
        top: 394px * 0.7;
        left: 1145px * 0.7;
      }

      .point-item-4-3 {
        top: 520px * 0.7;
        left: 1118px * 0.7;
      }

      .point-item-4-4 {
        top: 693px * 0.7;
        left: 1147px * 0.7;
      }

      .point-item-4-5 {
        top: 730px * 0.7;
        left: 810px * 0.7;
      }

      .point-item-4-6 {
        top: 487px * 0.7;
        left: 578px * 0.7;
      }

      .point-item-4-7 {
        top: 405px * 0.7;
        left: 728px * 0.7;
      }

      .point-item-4-8 {
        top: 271px * 0.7;
        left: 797px * 0.7;
      }
    }

    .footer {
      position: absolute;
      bottom: 75px * 0.7;
      right: 340px * 0.7;
      width: 152px * 0.7;
      cursor: pointer;
    }
  }
}
@keyframes flash {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>