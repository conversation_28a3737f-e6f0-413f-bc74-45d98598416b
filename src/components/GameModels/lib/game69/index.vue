<template>
  <div class="game69-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="找规律-数字" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请根据屏幕上方提供信息找出数字规律，给问号处选择相应答案。</p>
        <p class="synopsis-content">2、训练结束会出现成绩单。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="title">
        <img v-if="!isPlay" @click="play" class="icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="icon" src="/static/game_assets/common/pause.png" />

        <p class="text">问号处应该放什么数字？</p>
      </div>

      <div class="content">
        <div class="content-top">
          <div class="top-item" v-for="(item, index) in questionList" :key="index + 'top'" :src="`/static/game_assets/game68/item_${item}.png`">{{item}}</div>

          <img class="arrow" src="/static/game_assets/game68/arrow.png" />
          <img class="mark" src="/static/game_assets/game68/mark.png" />
        </div>

        <div class="content-bottom">
          <div :class="['bottom-item', choose === item ? 'correct-item' : '']" v-for="item in answerList" :key="item + 'bottom'" @click="chooseItem(item)">
            {{item}}
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="left">
          <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
          <img v-if="choose !== ''" class="img3" src="/static/game_assets/common/finish.png" @click="submit">
        </div>
        <div class="right">
          <span class="right-item">题目分数：{{store}}</span>
          <span class="right-item">题目数量：{{number}}</span>
          <span class="right-item">训练用时：{{time}}</span>
        </div>
      </div>
    </div>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'

export default {
  name: 'game69',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    draggable
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isPlay: false,
      isRePlay: false,
      show: false,
      questionList: [],
      answerList: [],
      answer: '',
      choose: '',

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start () {
      this.startProcess()
      this.status = 3
      // this.timing()
      this.$nextTick(() => {
        this.play()
      })
    },

    startProcess () {
      const list = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]

      const type = api.randomNum(1, 3) // 1 -- AAAA  2 -- ABAB  3 -- ABCA
      if (type === 1) {
        this.questionList = api.getRandomArray(list, 1)
        this.questionList = this.questionList.concat(this.questionList)
        this.questionList = this.questionList.concat(this.questionList)
        this.answer = this.questionList[0]
      } else if (type === 2) {
        this.questionList = api.getRandomArray(list, 2)
        this.questionList = this.questionList.concat(this.questionList)
        this.answer = this.questionList[0]
      } else if (type === 3) {
        this.questionList = api.getRandomArray(list, 3)
        this.questionList.push(this.questionList[0])
        this.answer = this.questionList[1]
      }
      const arr1 = list.filter(item => item !== this.answer)
      this.answerList = api.shuffle(api.getRandomArray(arr1, 3).concat([this.answer]))
      this.number++
    },

    chooseItem (item) {
      this.choose = item
    },

    stop () {
      if (this.isPlay) {
        this.pause()
        this.isRePlay = true
      }
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      if (this.isRePlay) {
        this.play()
        this.isRePlay = false
      }
      this.isStop = false
      this.timing()
    },

    submit () {
      if (this.choose === this.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }
      this.store = 20 * this.succesNum

      if (this.number < 5) {
        this.questionList = []
        this.answerList = []
        this.answer = ''
        this.choose = ''
        this.startProcess()
      } else {
        this.pause()
        this.isStop = true
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: '',
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.questionList = []
      this.answerList = []
      this.answer = ''
      this.choose = ''
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game69-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game68/bg.png');
  }

  .game-synopsis {
    width: 707px * 0.7;
    height: 444px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fffef3;
    border: 2px * 0.7 solid #014747;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .title {
      position: relative;
      width: 100%;
      padding-top: 70px * 0.7;
      font-size: 50px * 0.7;
      line-height: 70px * 0.7;
      text-align: center;
      font-weight: 600;
      color: #014747;
      user-select: none;

      .icon {
        position: absolute;
        top: 70px * 0.7;
        left: 50px * 0.7;
        width: 70px * 0.7;
        cursor: pointer;
      }
    }

    .content {
      position: relative;
      width: 1650px * 0.7;
      height: 660px * 0.7;
      display: flex;
      flex-direction: column;

      .content-top {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .top-item {
          width: 270px * 0.7;
          height: 270px * 0.7;

          font-size: 192px * 0.7;
          line-height: 270px * 0.7;
          text-align: center;
          font-weight: 600;
          color: #008282;
          font-family: PingFangSC-Semibold, PingFang SC;
          user-select: none;
        }

        .arrow {
          width: 200px * 0.7;
        }

        .mark {
          width: 280px * 0.7;
        }
      }

      .content-bottom {
        display: flex;
        justify-content: space-between;
        padding: 58px * 0.7 20px * 0.7 0 20px * 0.7;

        .bottom-item {
          width: 316px * 0.7;
          height: 316px * 0.7;
          border-radius: 23px * 0.7;
          background: #fff;
          cursor: pointer;

          font-size: 192px * 0.7;
          line-height: 316px * 0.7;
          text-align: center;
          font-weight: 600;
          color: #008282;
          font-family: PingFangSC-Semibold, PingFang SC;
          user-select: none;
        }

        .correct-item {
          background: #e7f8b5;
          border-color: #c6e069;
        }
      }
    }

    .footer {
      position: relative;
      display: flex;
      justify-content: space-between;
      width: 1790px * 0.7;
      padding-bottom: 47px * 0.7;
      margin: 0 auto;

      .left {
        width: 564px * 0.7;
        display: inline-flex;
        justify-content: space-between;
      }

      .right {
        width: 790px * 0.7;
        padding: 20px * 0.7 0;
        display: inline-flex;
        justify-content: space-between;

        .right-item {
          display: block;
          width: 245px * 0.7;
          height: 75px * 0.7;
          border: 2px * 0.7 solid #31996d;
          border-radius: 4px * 0.7;

          font-size: 24px * 0.7;
          line-height: 70px * 0.7;
          text-align: center;
          color: #37b982;
          user-select: none;
        }
      }

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img2 {
        width: 268px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img3 {
        width: 267px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }
}
</style>