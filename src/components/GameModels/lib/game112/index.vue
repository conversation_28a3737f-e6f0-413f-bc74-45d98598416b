<template>
  <div class="game112-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <audio ref="music2" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPageLib @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">本训练用于提高训练者的各个家属认知，采用卡通形象进行训练。</p>
      </div>
      </settingPageLib>
      <div class="game-content" v-if="status === 3">
        <div class="content1" v-if="number === 1">
          <img class="content-bg" src="/static/game_assets/game112/content_bg.png" />

          <div class="content-left">
            <div :class="['left-item', (index === 0 || index === 6) && 'big-item-left', (index === 1 || index === 7) && 'big-item-right']" v-for="(item, index) in questions" :key="index + 'item'">
              <img :class="['item-img', current.index === item.index && 'flash-item']" :src="`/static/game_assets/game112/item_${item.index}.png`" />
              <p class="item-text">{{item.name}}</p>
            </div>
          </div>

          <div class="content-right">
            <img class="right-img" :src="`/static/game_assets/game112/item_${current.index}.png`" />
            <p class="right-text">{{current.name}}</p>
          </div>
        </div>

        <div class="content2" v-if="number === 2">
          <div class="content-item" v-for="(item, index) in questions" :key="index + 'item2'" @click="chooseItem(item.index)">
            <img class="item-img" :src="`/static/game_assets/game112/item_${item.index}.png`" />
            <img v-if="choose === item.index" class="item-img" :src="`/static/game_assets/game112/choose_${item.index}.png`" />

            <img v-if="choose === item.index" class="item-icon" src="/static/game_assets/game112/icon.png" />
          </div>
        </div>

        <div class="content3" v-if="number === 3">
          <div :class="['content-item', 'content-item-' + index, itemClass(item, index)]" v-for="(item, index) in list" :key="index + 'img2'" @click="setChoose(item, index)">
            <img class="item-img" :src="`/static/game_assets/game112/item_${item.index}.png`" />
            <p class="item-name">{{item.name}}</p>
          </div>
        </div>

        <div class="btn-group">
          <div class="btn" @click="stop">
            <img class="bg" src="/static/game_assets/game8/red_bg.png">
            <span class="text">停止</span>
          </div>
        </div>
      </div>
      <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
      <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game112',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      musicUrl: '',
      number: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      show: false,
      status: 1,
      isStop: false,
      isPlay: false,
      canClick: true,
      isContinue: false,

      index: 0,
      current: {},
      choose: 0,
      questions: [],
      list: [],
      answer: [],
      chooseAnswer: [],

      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    itemClass (item, index) {
      const list1 = this.chooseAnswer.filter(it => it.name === item.index)
      const list2 = this.chooseAnswer.filter(it => it.index === index)
      if (list2.length && list1.length === 1) {
        return 'scale-item-' + index
      }
      if (list2.length && list1.length === 2) {
        return 'translate-item-' + index
      }
      return ''
    },

    play (url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    playError () {
      this.$refs.music2.play()
    },

    pause () {
      this.$refs.music2.pause()
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnded () {
      if (this.number === 1) {
        setTimeout(() => {
          if (this.show) return
          if (this.index >= 8) {
            this.index = 0
            this.number = 2
            this.isPlay = false
          }

          this.startProcess()
        }, 500)
      } else {
        this.isPlay = false
      }
    },

    start () {
      this.status = 3
      this.questions = api.getRandomArray(data.data, 1)[0]
      this.startProcess()
    },

    // 开始流程
    startProcess () {
      if (this.number === 1) {
        this.current = this.questions[this.index]
        this.index++
        this.play(this.current.audio)
      } else if (this.number === 2) {
        this.questions = api.shuffle(this.questions)
        this.answer = api.getRandomArray(this.questions, 5)
        this.play(this.answer[this.index].audio)
      } else {
        this.play('/static/game_assets/audio/game112/question.mp3')
        this.list = api.getRandomArray(this.questions, 5)
        this.list = this.list.concat(this.list)
        this.list = api.shuffle(this.list)
      }
    },

    chooseItem (item) {
      if (!this.canClick) return
      this.choose = item
      if (this.choose === this.answer[this.index].index) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      this.canClick = false
      setTimeout(() => {
        this.canClick = true
        if (this.index >= 4) {
          this.number = 3
          this.startProcess()
        } else {
          this.choose = 0
          this.index++
          this.play(this.answer[this.index].audio)
        }
      }, 800)
    },

    setChoose (item, index) {
      const list = this.chooseAnswer.filter(it => it.index === index)
      if (list.length) return
      if ((this.chooseAnswer.length % 2) && (item.index !== this.chooseAnswer[this.chooseAnswer.length - 1].name)) {
        this.errorNum++
        this.playError()
        return
      }
      this.succesNum++
      this.chooseAnswer.push({
        name: item.index,
        index
      })

      if (this.chooseAnswer.length >= 10) {
        setTimeout(() => {
          this.submit()
        }, 1500)
      }
    },

    stop () {
      this.isStop = true
      this.show = true
      if (this.isPlay) {
        this.pause()
        this.isContinue = true
      }
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
      if (this.isContinue) {
        this.play()
        this.isContinue = false
      }
    },

    submit () {
      this.pause()
      this.isStop = true
      this.store = 100 - 5 * this.errorNum > 0 ? 100 - 5 * this.errorNum : 0
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 1
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.choose = 0
      this.index = 0
      this.current = {}
      this.chooseAnswer = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game112-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game109/bg.png');
  }

  .game-synopsis {
    .synopsis-content {
      padding-top: 20px * 0.7;
      margin: 0;
      font-size: 30px * 0.7;
      line-height: 42px * 0.7;
      font-weight: 400;
      color: #a83a01;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .content1 {
      position: relative;
      width: 1465px * 0.7;
      height: 816px * 0.7;
      margin-bottom: 190px * 0.7;
      margin-right: 15px * 0.7;
      display: flex;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1465px * 0.7;
      }

      .content-left {
        position: relative;
        flex: 1;
        display: inline-flex;
        flex-wrap: wrap;
        align-content: flex-start;
        padding: 88px * 0.7 0 0 60px * 0.7;

        .left-item {
          width: 151px * 0.7;
          display: inline-flex;
          flex-direction: column;
          align-items: center;

          .item-img {
            width: 124px * 0.7;
            height: 124px * 0.7;
          }

          .flash-item {
            animation: flash 1s linear infinite;
          }

          .item-text {
            margin: 0;
            padding: 17px * 0.7 0 24px * 0.7 0;
            font-size: 28px * 0.7;
            line-height: 40px * 0.7;
            font-weight: 500;
            color: #a83a01;
          }
        }

        .big-item-left {
          width: 302px * 0.7;
          padding-left: 151px * 0.7;
        }

        .big-item-right {
          width: 302px * 0.7;
          padding-right: 151px * 0.7;
        }
      }

      .content-right {
        position: relative;
        flex: 1;
        display: inline-flex;
        flex-direction: column;
        align-items: center;

        .right-img {
          width: 375px * 0.7;
          height: 375px * 0.7;
          margin-top: 184px * 0.7;
          margin-bottom: 86px * 0.7;
        }

        .right-text {
          margin: 0;
          font-size: 28px * 0.7;
          line-height: 40px * 0.7;
          font-weight: 500;
          color: #a83a01;
        }
      }
    }

    .content2 {
      position: relative;
      width: 1000px * 0.7;
      height: 870px * 0.7;
      margin-bottom: 34px * 0.7;
      margin-right: 98px * 0.7;
      display: flex;
      flex-wrap: wrap;
      align-content: space-between;

      .content-item {
        position: relative;
        width: 306px * 0.7;
        height: 248px * 0.7;
        margin-left: 27px * 0.7;
        cursor: pointer;

        .item-img {
          position: absolute;
          top: 0;
          right: 0;
          width: 248px * 0.7;
        }

        .item-icon {
          position: absolute;
          top: 66px * 0.7;
          left: 0;
          width: 95px * 0.7;
        }
      }
    }

    .content3 {
      position: relative;
      width: 1750px * 0.7;
      height: 864px * 0.7;
      padding-bottom: 110px * 0.7;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;

      .content-item {
        position: relative;
        width: 286px * 0.7;
        height: 286px * 0.7;
        margin: 10px * 0.7;
        cursor: pointer;
        transition: all 1.5s ease-in-out;

        .item-img {
          position: absolute;
          top: 0;
          left: 0;
          width: 286px * 0.7;
          height: 286px * 0.7;
        }

        .item-name {
          position: absolute;
          width: 100%;
          bottom: 25px * 0.7;
          margin: 0;
          font-size: 28px * 0.7;
          line-height: 40px * 0.7;
          text-align: center;
          font-weight: 500;
          color: #a83a01;
        }
      }

      .scale-item-0,
      .scale-item-1,
      .scale-item-2,
      .scale-item-3,
      .scale-item-4,
      .scale-item-5,
      .scale-item-6,
      .scale-item-7,
      .scale-item-8,
      .scale-item-9 {
        transform: scale(1.2);
      }

      .translate-item-0 {
        transform: translate3d(722px * 0.7, 279px * 0.7, 0);
        opacity: 0;
      }

      .translate-item-1 {
        transform: translate3d(361px * 0.7, 279px * 0.7, 0);
        opacity: 0;
      }

      .translate-item-2 {
        transform: translate3d(0, 279px * 0.7, 0);
        opacity: 0;
      }

      .translate-item-3 {
        transform: translate3d(-361px * 0.7, 279px * 0.7, 0);
        opacity: 0;
      }

      .translate-item-4 {
        transform: translate3d(-722px * 0.7, 279px * 0.7, 0);
        opacity: 0;
      }

      .translate-item-5 {
        transform: translate3d(722px * 0.7, -279px * 0.7, 0);
        opacity: 0;
      }

      .translate-item-6 {
        transform: translate3d(361px * 0.7, -279px * 0.7, 0);
        opacity: 0;
      }

      .translate-item-7 {
        transform: translate3d(0, -279px * 0.7, 0);
        opacity: 0;
      }

      .translate-item-8 {
        transform: translate3d(-361px * 0.7, -279px * 0.7, 0);
        opacity: 0;
      }

      .translate-item-9 {
        transform: translate3d(-722px * 0.7, -279px * 0.7, 0);
        opacity: 0;
      }
    }

    .btn-group {
      position: absolute;
      bottom: 52px * 0.7;
      width: 1572px * 0.7;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px * 0.7;
        height: 84px * 0.7;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px * 0.7;
          height: 84px * 0.7;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px * 0.7 0 21px * 0.7 0;
          font-size: 38px * 0.7;
          line-height: 53px * 0.7;
          text-align: center;
          color: #a83a01;
        }
      }
    }
  }
}

@keyframes flash {
  0% {
    transform: scale(1);
  }
  80% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style>