<template>
  <div class="game14-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="数字注意-变化数" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、这是一组针对数字进行的注意训练。</p>
        <p class="synopsis-content">2、变化数：屏幕上有一些数字和英文字母，请您按顺序找到数字1到9。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="icon-img">
        <img src="/static/game_assets/common/clock.png" />
        <span>{{ time }}</span>
      </div>

      <div class="content">
        <div :class="['item', 'item' + (index + 1), answer >= item ? 'item-border' : '']" v-for="(item, index) in questions" :key="item">
          <img class="img" :src="`/static/game_assets/game14/card_${item}.png`" @click="chooseItem(item)">
        </div>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
      </div>
    </div>

    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'

export default {
  name: 'game14',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/error_audio.mp3',
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      questions: [],
      answer: 0,
      timer: null,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play () {
      this.$refs.music.play()
    },

    pause () {
      this.$refs.music.pause()
    },

    start () {
      this.status = 3
      // this.timing()
      let arr1 = [1, 2, 3, 4, 5, 6, 7, 8, 9]
      const arr2 = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33]
      this.questions = arr1.concat(api.getRandomArray(arr2, 18))
      this.questions = api.shuffle(this.questions)
      this.getArray()
    },

    getArray () {
      if (this.isStop) return
      this.timer = setTimeout(() => {
        let arr1 = [1, 2, 3, 4, 5, 6, 7, 8, 9]
        const arr2 = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33]
        this.questions = arr1.concat(api.getRandomArray(arr2, 18))
        this.questions = api.shuffle(this.questions)
        this.getArray()
      }, 5000)
    },

    chooseItem (item) {
      if (item !== this.answer + 1) {
        this.errorNum++
        this.play()
      } else {
        this.answer = item
        this.succesNum++
      }

      if (this.answer >= 9) {
        this.pause()
        this.isStop = true
        clearTimeout(this.timer)
        this.store = parseInt(100 / (this.succesNum + this.errorNum) * this.succesNum)
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: '',
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    stop () {
      this.pause()
      this.isStop = true
      this.show = true
      clearTimeout(this.timer)
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
      this.getArray()
    },

    again () {
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.answer = 0
      this.isStop = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game14-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game12/bg.png');
  }

  .game-synopsis {
    width: 787px * 0.7;
    height: 484px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border: 2px * 0.7 solid #7bbd41;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    width: 100%;

    .icon-img {
      position: absolute;
      top: 55px * 0.7;
      right: 200px * 0.7;
      width: 230px * 0.7;
      height: 76px * 0.7;
      padding: 4px * 0.7 11px * 0.7;
      border: 1px * 0.7 solid #37b982;
      border-radius: 38px * 0.7;
      font-size: 0;

      img {
        width: 58px * 0.7;
        height: 58px * 0.7;
        vertical-align: middle;
      }

      span {
        display: inline-block;
        padding-left: 20px * 0.7;
        font-size: 32px * 0.7;
        line-height: 58px * 0.7;
        font-weight: 500;
        vertical-align: middle;
        user-select: none;
      }
    }

    .content {
      position: relative;
      height: 100%;
      width: 100%;

      .item {
        position: absolute;
        width: 154px * 0.7;
        height: 154px * 0.7;

        .img {
          position: absolute;
          top: 23px * 0.7;
          left: 23px * 0.7;
          width: 94px * 0.7;
          height: 94px * 0.7;
          cursor: pointer;
        }
      }

      .item-border {
        border: 4px * 0.7 solid #ff9610;
        border-radius: 50%;
      }

      .item1,
      .item3,
      .item7,
      .item14,
      .item20 {
        transform: rotate(20deg);
        animation: swing1 1.5s 0.15s linear infinite;
      }

      .item5,
      .item11,
      .item18,
      .item22,
      .item26 {
        transform: rotate(45deg);
        animation: swing2 1.5s 0.15s linear infinite;
      }

      .item2,
      .item13,
      .item19,
      .item24,
      .item27 {
        transform: rotate(-20deg);
        animation: swing3 1.5s 0.15s linear infinite;
      }

      .item4,
      .item9,
      .item15,
      .item16,
      .item23 {
        transform: rotate(-40deg);
        animation: swing4 1.5s 0.15s linear infinite;
      }

      .item6,
      .item8,
      .item10,
      .item12,
      .item17,
      .item21,
      .item25 {
        animation: swing5 1.5s 0.15s linear infinite;
      }

      .item1 {
        top: 120px * 0.7;
        left: 234px * 0.7;
      }

      .item2 {
        top: 314px * 0.7;
        left: 143px * 0.7;
      }

      .item3 {
        top: 638px * 0.7;
        left: 186px * 0.7;
      }

      .item4 {
        top: 200px * 0.7;
        left: 400px * 0.7;
      }

      .item5 {
        top: 415px * 0.7;
        left: 393px * 0.7;
      }

      .item6 {
        top: 611px * 0.7;
        left: 386px * 0.7;
      }

      .item7 {
        top: 103px * 0.7;
        left: 576px * 0.7;
      }

      .item8 {
        top: 76px * 0.7;
        left: 772px * 0.7;
      }

      .item9 {
        top: 260px * 0.7;
        left: 678px * 0.7;
      }

      .item10 {
        top: 462px * 0.7;
        left: 609px * 0.7;
      }

      .item11 {
        top: 710px * 0.7;
        left: 597px * 0.7;
      }

      .item12 {
        top: 107px * 0.7;
        left: 940px * 0.7;
      }

      .item13 {
        top: 413px * 0.7;
        left: 862px * 0.7;
      }

      .item14 {
        top: 620px * 0.7;
        left: 785px * 0.7;
      }

      .item15 {
        top: 814px * 0.7;
        left: 772px * 0.7;
      }

      .item16 {
        top: 293px * 0.7;
        left: 1022px * 0.7;
      }

      .item17 {
        top: 574px * 0.7;
        left: 968px * 0.7;
      }

      .item18 {
        top: 843px * 0.7;
        left: 977px * 0.7;
      }

      .item19 {
        top: 96px * 0.7;
        left: 1222px * 0.7;
      }

      .item20 {
        top: 286px * 0.7;
        left: 1271px * 0.7;
      }

      .item21 {
        top: 448px * 0.7;
        left: 1138px * 0.7;
      }

      .item22 {
        top: 680px * 0.7;
        left: 1170px * 0.7;
      }

      .item23 {
        top: 873px * 0.7;
        left: 1208px * 0.7;
      }

      .item24 {
        top: 312px * 0.7;
        left: 1541px * 0.7;
      }

      .item25 {
        top: 565px * 0.7;
        left: 1565px * 0.7;
      }

      .item26 {
        top: 536px * 0.7;
        left: 1377px * 0.7;
      }

      .item27 {
        top: 769px * 0.7;
        left: 1406px * 0.7;
      }
    }

    .footer {
      position: absolute;
      bottom: 44px * 0.7;
      left: 64px * 0.7;

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }
}

@keyframes swing1 {
  0% {
    transform: rotate(20deg);
  }
  10% {
    transform: rotate(-10deg);
  }
  20% {
    transform: rotate(-30deg);
  }
  30% {
    transform: rotate(-10deg);
  }
  40% {
    transform: rotate(10deg);
  }
  50%,
  100% {
    transform: rotate(20deg);
  }
}

@keyframes swing2 {
  0% {
    transform: rotate(45deg);
  }
  10% {
    transform: rotate(15deg);
  }
  20% {
    transform: rotate(-15deg);
  }
  30% {
    transform: rotate(-25deg);
  }
  40% {
    transform: rotate(10deg);
  }
  50%,
  100% {
    transform: rotate(45deg);
  }
}

@keyframes swing3 {
  0% {
    transform: rotate(-20deg);
  }
  10% {
    transform: rotate(10deg);
  }
  20% {
    transform: rotate(30deg);
  }
  30% {
    transform: rotate(10deg);
  }
  40% {
    transform: rotate(-10deg);
  }
  50%,
  100% {
    transform: rotate(-20deg);
  }
}

@keyframes swing4 {
  0% {
    transform: rotate(-40deg);
  }
  10% {
    transform: rotate(-15deg);
  }
  20% {
    transform: rotate(15deg);
  }
  30% {
    transform: rotate(25deg);
  }
  40% {
    transform: rotate(-10deg);
  }
  50%,
  100% {
    transform: rotate(-40deg);
  }
}

@keyframes swing5 {
  10% {
    transform: rotate(15deg);
  }
  20% {
    transform: rotate(-10deg);
  }
  30% {
    transform: rotate(5deg);
  }
  40% {
    transform: rotate(-5deg);
  }
  50%,
  100% {
    transform: rotate(0deg);
  }
}
</style>