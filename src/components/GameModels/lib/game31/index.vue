<template>
  <div class="game31-page">
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <div class="page-bg" :class="status > 1 ? 'page-bg2': 'page-bg1'"></div>
    <settingPageLib title="找数训练--困难" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <div class="synopsis-content">
          <p>1、5*5数字方格，请按照数字序列顺序选择数字。</p>
          <p>2、选择错误，会提示您“错了”，请重新正确选择数字。</p>
          <p>3、训练用时和错误次数将作为成绩记录下来。</p>
          <p>4、本训练难度为C（困难）。</p>
        </div>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="game-bottom">
        <img class="bg" src="/static/game_assets/game29/bg.png" />
        <img class="bar-bg" src="/static/game_assets/game29/progress_bar_bg.png" />
        <div :class="['progress-bar', 'bar-' + (succesNum + 1)]"></div>

        <div class="btn-group">
          <div :class="['btn', errorNumber === item ? 'error-style' : '']" v-for="item in numList" :key="item" @click="select(item)">
            <img v-if="selectItem < item" class="btn-bg" src="/static/game_assets/game29/btn_middle_bg1.png" />
            <img v-else class="btn-bg" src="/static/game_assets/game29/btn_middle_bg2.png" />
            <span class="btn-num">{{ item }}</span>
          </div>
        </div>
      </div>

      <div class="game-top">
        <img v-if="!isPlay" class="icon" src="/static/game_assets/common/play.png" @click="play" />
        <img v-else class="icon" src="/static/game_assets/common/pause.png" @click="pause" />

        <div class="right">
          <div class="error">
            <img class="icon" src="/static/game_assets/game29/error.png" />
            <p class="text">错误次数：<span>{{ errorNum }}</span></p>
          </div>

          <div class="clock">
            <img class="icon" src="/static/game_assets/common/clock.png" />
            <p class="text">训练用时：<span>{{ second }}秒</span></p>
          </div>
        </div>
      </div>

      <div :class="['sucess', selectItem === numList.length ? 'translate-top' : '']">
        <img class="img" src="/static/game_assets/game29/hot_balloon.png">
      </div>
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import api from '../../utils/common.js'

export default {
  name: 'game31',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      numList: [],
      selectItem: 0,
      errorNumber: 0, // 错误的数字
      isStop: false,
      isPlay: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 'C'
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        },
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    select (item) {
      if (item !== (this.selectItem + 1)) {
        this.errorNumber = item
        this.errorNum++
        return
      }
      this.selectItem = item
      this.succesNum++
      if (item === this.numList.length) {
        this.pause()
        this.isStop = true
        this.store = parseInt(100 / (this.succesNum + this.errorNum) * this.succesNum)
        this.infos[1].value = this.second
        this.infos[2].value = this.succesNum
        this.infos[3].value = this.errorNum
        this.params = {
          id: this.info.id,
          grade: 3,
          time: this.second,
          totalPoints: this.store
        }
        setTimeout(() => {
          this.status = 4
        }, 2500)
      }
    },

    start () {
      const list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]
      this.numList = api.shuffle(list)
      this.status = 3
      // this.timing()
      this.play()
    },

    again () {
      this.selectItem = 0
      this.errorNumber = 0
      this.succesNum = 0
      this.errorNum = 0
      this.second = 0
      this.store = 0
      this.numList = []
      this.isStop = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game31-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background: url('/static/game_assets/game34/bg_1.png') center center no-repeat;
    background-size: cover;
  }
  .page-bg2 {
    background-image: url('/static/game_assets/game34/bg_2.png');
  }

  .game-synopsis {
    width: 1000px * 0.7;
    height: 504px * 0.7;
    padding-left: 200px * 0.7;
    padding-top: 50px * 0.7;
    overflow: hidden;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #333;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 25px * 0.7;

      p {
        margin: 0;
        font-size: 25px * 0.7;
        line-height: 40px * 0.7;
        font-weight: 400;
        color: #333;
        user-select: none;
      }
    }
  }

  .game-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 40px * 0.7;

    .game-top {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      display: flex;
      justify-content: space-between;
      padding: 35px * 0.7 118px * 0.7 0 105px * 0.7;

      .icon {
        margin-top: 5px * 0.7;
        width: 80px * 0.7;
        height: 80px * 0.7;
        cursor: pointer;
      }

      .right {
        display: inline-flex;
        justify-content: space-between;

        .error,
        .clock {
          margin-left: 30px * 0.7;
          padding: 5px * 0.7 20px * 0.7;
          border: 1px * 0.7 solid #37b982;
          border-radius: 38px * 0.7;
          font-size: 0;

          .icon {
            margin: 0;
            width: 65px * 0.7;
            height: 65px * 0.7;
            vertical-align: middle;
            cursor: auto;
          }

          .text {
            display: inline-block;
            margin: 0;
            padding: 0 13px * 0.7;
            font-size: 32px * 0.7;
            line-height: 65px * 0.7;
            color: #51547e;
            vertical-align: middle;
            user-select: none;
          }
        }

        .error {
          .text {
            span {
              padding-left: 5px * 0.7;
              color: #ef4835;
              font-weight: 500;
            }
          }
        }

        .clock {
          .text {
            span {
              padding-left: 5px * 0.7;
              color: #51547e;
              font-weight: 600;
            }
          }
        }
      }
    }

    .game-bottom {
      position: relative;
      width: 1763px * 0.7;
      height: 841px * 0.7;

      .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1763px * 0.7;
        height: 841px * 0.7;
      }

      .bar-bg {
        position: absolute;
        top: 135px * 0.7;
        left: 472px * 0.7;
        width: 847px * 0.7;
        height: 70px * 0.7;
      }

      .progress-bar {
        position: absolute;
        top: 145px * 0.7;
        left: 485px * 0.7;
        height: 40px * 0.7;
        background: #ff607a;
        border-radius: 8px * 0.7;
      }

      .bar-1 {
        width: 32px * 0.7;
      }

      .bar-2 {
        width: 63px * 0.7;
      }

      .bar-3 {
        width: 95px * 0.7;
      }

      .bar-4 {
        width: 126px * 0.7;
      }

      .bar-5 {
        width: 158px * 0.7;
      }

      .bar-6 {
        width: 189px * 0.7;
      }

      .bar-7 {
        width: 221px * 0.7;
      }

      .bar-8 {
        width: 252px * 0.7;
      }

      .bar-9 {
        width: 284px * 0.7;
      }

      .bar-10 {
        width: 315px * 0.7;
      }

      .bar-11 {
        width: 347px * 0.7;
      }

      .bar-12 {
        width: 378px * 0.7;
      }

      .bar-13 {
        width: 410px * 0.7;
      }

      .bar-14 {
        width: 441px * 0.7;
      }

      .bar-15 {
        width: 473px * 0.7;
      }

      .bar-16 {
        width: 504px * 0.7;
      }

      .bar-17 {
        width: 536px * 0.7;
      }

      .bar-18 {
        width: 567px * 0.7;
      }

      .bar-19 {
        width: 599px * 0.7;
      }

      .bar-20 {
        width: 630px * 0.7;
      }

      .bar-21 {
        width: 662px * 0.7;
      }

      .bar-22 {
        width: 693px * 0.7;
      }

      .bar-23 {
        width: 725px * 0.7;
      }

      .bar-24 {
        width: 756px * 0.7;
      }

      .bar-25 {
        width: 788px * 0.7;
      }

      .bar-26 {
        width: 819px * 0.7;
      }

      .bar-26 {
        width: 819px * 0.7;
      }

      .btn-group {
        position: absolute;
        top: 227px * 0.7;
        left: 470px * 0.7;
        display: flex;
        flex-wrap: wrap;
        width: 848px * 0.7;
        height: 454px * 0.7;
        justify-content: space-between;

        .btn {
          position: relative;
          width: 166px * 0.7;
          height: 87px * 0.7;
          cursor: pointer;

          .btn-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 166px * 0.7;
            height: 87px * 0.7;
          }

          .btn-num {
            position: relative;
            top: 17px * 0.7;
            left: 0;
            display: block;
            width: 166px * 0.7;
            height: 43px * 0.7;
            font-size: 43px * 0.7;
            line-height: 43px * 0.7;
            text-align: center;
            font-weight: 500;
            color: #fff;
            font-family: PingFang SC;
            user-select: none;
          }
        }

        .error-style {
          animation: shake 800ms ease-in-out;
        }
      }
    }

    .sucess {
      position: absolute;
      bottom: -762px * 0.7;
      width: 774px * 0.7;
      height: 762px * 0.7;
      transition: all 2s ease-in;

      .img {
        width: 774px * 0.7;
        height: 762px * 0.7;
      }
    }

    .translate-top {
      transform: translateY(-2442px * 0.7);
    }
  }

  .mask-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;

    .img {
      width: 287px * 0.7;
      height: 310px * 0.7;
      margin-top: 320px * 0.7;
    }
  }
}
@keyframes shake {
  /* 水平抖动，核心代码 */
  10%,
  90% {
    transform: translate3d(-1px * 0.7, 0, 0);
  }
  20%,
  80% {
    transform: translate3d(+2px * 0.7, 0, 0);
  }
  30%,
  70% {
    transform: translate3d(-4px * 0.7, 0, 0);
  }
  40%,
  60% {
    transform: translate3d(+4px * 0.7, 0, 0);
  }
  50% {
    transform: translate3d(-4px * 0.7, 0, 0);
  }
}
</style>