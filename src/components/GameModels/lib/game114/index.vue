<template>
  <div class="game114-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">本训练用于提高训练者的各种食物认知。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="left-icon">
        <img class="icon" src="/static/game_assets/game114/icon.png" />

        <div class="left-content" v-if="answerStatus === 2">
          <img class="img-bg" src="/static/game_assets/game114/content_bg.png" />
          <img class="img" :src="`/static/game_assets/game114/item_${answer}.png`" />
        </div>
      </div>

      <div class="content1" v-if="answerStatus === 1">
        <img class="content-bg" src="/static/game_assets/game8/content_bg.png" />
        <img class="left-btn" @click="toPrevious" src="/static/game_assets/game107/left.png" />
        <img class="right-btn" @click="toNext" src="/static/game_assets/game107/right.png" />

        <img class="content-img" :src="`/static/game_assets/game114/item_${questions[number].index}.png`" />
        <p class="content-text">{{questions[number].name}}</p>
      </div>

      <div class="content2" v-if="answerStatus === 2">
        <div :class="['content-item', choose === item.index && 'choose-item']" v-for="item in questions" :key="item.index + 'img'" @click="chooseItem(item.index)">
          <img class="item-img" :src="`/static/game_assets/game114/item_${item.index}.png`" />
        </div>
      </div>

      <div class="btn-group">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <div class="btn" v-if="answerStatus === 1 || answerStatus === 2 && choose" @click="startGame">
          <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
          <span class="text">{{answerStatus === 1 ? '开始训练' : '提交'}}</span>
        </div>
      </div>

      <div class="judge" v-if="isShowCorrect || isShowError">
        <img class="img" :src="`/static/game_assets/game8/${isShowCorrect ? 'success' : 'error'}.png`">
      </div>
    </div>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
// 每轮游戏3个回合

export default {
  name: 'game114',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      musicUrl: '',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      show: false,
      status: 1,

      isPlayTitle: false,
      isPlay: false,
      isShowCorrect: false,
      isShowError: false,
      answerStatus: 1, // 1--学习 2--测试
      questions: [
        {
          name: '薯条',
          index: 1
        },
        {
          name: '巧克力',
          index: 2
        },
        {
          name: '鸡肉',
          index: 3
        },
        {
          name: '面条',
          index: 4
        },
        {
          name: '牛奶',
          index: 5
        }
      ],
      answer: 0,
      index: 0,
      choose: 0,
      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play (url) {
      if (url) {
        this.musicUrl = url
        this.$refs.music.load()
      }
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnded () {
      this.isPlay = false
      if (this.isPlayTitle) {
        this.isPlayTitle = false
        this.play(`/static/game_assets/audio/game114/item_${this.answer}.mp3`)
      }
    },

    start () {
      this.status = 3
      this.play(`/static/game_assets/audio/game114/item_${this.questions[this.number].index}.mp3`)
    },

    toPrevious () {
      if (this.number > 0) {
        this.number--
        this.play(`/static/game_assets/audio/game114/item_${this.questions[this.number].index}.mp3`)
      } else {
        this.number = 4
        this.play(`/static/game_assets/audio/game114/item_${this.questions[this.number].index}.mp3`)
      }
    },

    toNext () {
      if (this.number >= 4) {
        this.number = 0
        this.play(`/static/game_assets/audio/game114/item_${this.questions[this.number].index}.mp3`)
      } else {
        this.number++
        this.play(`/static/game_assets/audio/game114/item_${this.questions[this.number].index}.mp3`)
      }
    },

    startGame () {
      if (this.isShowCorrect || this.isShowError || this.isPlayTitle) return

      if (this.answerStatus === 1) {
        this.answerStatus = 2
        this.startProcess()
        this.play('/static/game_assets/audio/game114/title.mp3')
        this.isPlayTitle = true
        // this.timing()
      } else {
        if (this.choose === this.answer) {
          this.succesNum++
          this.isShowCorrect = true
        } else {
          this.errorNum++
          this.isShowError = true
        }

        setTimeout(() => {
          this.isShowCorrect = false
          this.isShowError = false

          if (this.answer >= 5) {
            this.submit()
          } else {
            this.choose = 0
            this.startProcess()
            this.play(`/static/game_assets/audio/game114/item_${this.answer}.mp3`)
          }
        }, 800)
      }
    },

    // 开始流程
    async startProcess () {
      this.questions = api.shuffle(this.questions)
      this.answer++
    },

    chooseItem (item) {
      if (this.isPlayTitle) return
      this.choose = item
    },

    stop () {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
      if (this.isPlay) this.pause()
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.play()
      if (this.answerStatus === 1) return
      this.timing()
    },

    submit () {
      this.pause()
      this.isStop = true
      this.store = 20 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.isStop = false
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.status = 3
      this.choose = 0
      this.answer = 0
      this.answerStatus = 1
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game114-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game114/bg.png');
  }

  .game-synopsis {
    .synopsis-content {
      padding-top: 20px * 0.7;
      margin: 0;
      font-size: 30px * 0.7;
      line-height: 42px * 0.7;
      font-weight: 400;
      color: #a83a01;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .left-icon {
      position: absolute;
      bottom: 125px * 0.7;
      left: 40px * 0.7;
      width: 328px * 0.7;
      height: 366px * 0.7;

      .icon {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 158px * 0.7;
      }

      .left-content {
        position: absolute;
        top: 0;
        right: 0;
        width: 161px * 0.7;
        height: 178px * 0.7;

        .img-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 161px * 0.7;
        }

        .img {
          position: absolute;
          top: 15px * 0.7;
          right: 17px * 0.7;
          width: 128px * 0.7;
        }
      }
    }

    .content1 {
      position: relative;
      width: 1253px * 0.7;
      height: 708px * 0.7;
      margin-bottom: 82px * 0.7;
      padding: 98px * 0.7 467px * 0.7 0 430px * 0.7;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1253px * 0.7;
      }

      .left-btn {
        position: absolute;
        top: 300px * 0.7;
        left: 30px * 0.7;
        width: 64px * 0.7;
        cursor: pointer;
      }

      .right-btn {
        position: absolute;
        top: 300px * 0.7;
        right: 48px * 0.7;
        width: 64px * 0.7;
        cursor: pointer;
      }

      .content-img {
        position: relative;
        width: 356px * 0.7;
      }

      .content-text {
        position: relative;
        padding: 52px * 0.7;
        font-size: 74px * 0.7;
        line-height: 97px * 0.7;
        text-align: center;
        color: #a83a01;
      }
    }

    .content2 {
      position: relative;
      width: 1182px * 0.7;
      height: 788px * 0.7;
      margin-left: 128px * 0.7;
      margin-bottom: 96px * 0.7;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;

      .content-item {
        position: relative;
        margin: 34px * 0.7;
        width: 326px * 0.7;
        height: 326px * 0.7;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 5px * 0.7 solid #ea9a22;
        background: #fffcb0;
        cursor: pointer;

        .item-img {
          width: 214px * 0.7;
        }
      }

      .choose-item {
        border-color: #ff2e2e;
        background: #b0ffcc;
      }
    }

    .btn-group {
      position: absolute;
      bottom: 82px * 0.7;
      width: 1450px * 0.7;
      height: 84px * 0.7;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px * 0.7;
        height: 84px * 0.7;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px * 0.7;
          height: 84px * 0.7;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px * 0.7 0 21px * 0.7 0;
          font-size: 38px * 0.7;
          line-height: 53px * 0.7;
          text-align: center;
          color: #a83a01;
        }
      }
    }

    .judge {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding-top: 10px * 0.7;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      justify-content: center;
      align-items: center;

      .img {
        width: 460px * 0.7;
        height: 460px * 0.7;
      }
    }
  }
}
</style>