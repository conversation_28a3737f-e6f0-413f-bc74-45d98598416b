<template>
  <div class="game77-page">
    <div class="page-bg"></div>
    <settingPageLib title="超市商品" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">请您仔细浏览所提供的超市图片，根据提出的任务来答题。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game61/title_bg.png" />
        <span class="title-text">图中有几种{{current.question.topic}}？</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game77/content_bg.png" />
        <img class="left-icon" src="/static/game_assets/game77/left.png" />
        <img class="right-icon" src="/static/game_assets/game77/right.png" />

        <div :class="['content-item', choose.includes(item.name) && 'choose-item', isJudge && current.question.answer.includes(item.name) && !choose.includes(item.name) && 'correct-item']" v-for="item in current.answerList" :key="item.name+ 'item'" @click="chooseItem(item.name)">
          <img class="img" :src="item.img" />
          <p class="text">{{item.name}}</p>
          <img class="icon" v-if="isJudge && current.question.answer.includes(item) && choose.includes(item)" src="/static/game_assets/common/correct.png">
          <img class="icon" v-if="isJudge && !current.question.answer.includes(item) && choose.includes(item)" src="/static/game_assets/common/error.png">
        </div>
      </div>

      <div class="footer">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="group-btn">
          <img class="img" v-if="choose.length && !isJudge" src="/static/game_assets/common/confirm.png" @click="confirm" />

          <template v-if="isJudge">
            <img class="img" src="/static/game_assets/common/continue.png" @click="goOn" />
            <!-- <img class="img right-btn" src="/static/game_assets/common/reset.png" @click="reset"> -->
          </template>
        </div>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game77',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isJudge: false,
      current: {},
      questionList: [],
      choose: [],

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      this.questionList = api.shuffle(data.data)
      this.startProcess()
      // this.timing()
    },

    startProcess () {
      this.current = this.questionList[this.number]
      this.current.answerList = api.shuffle(this.current.answerList)
      this.number++
    },

    chooseItem (item) {
      if (this.isJudge) return
      if (this.choose.includes(item)) {
        this.choose = this.choose.filter(it => it !== item)
        return
      }
      this.choose.push(item)
      this.choose = Array.from(new Set(this.choose))
    },

    goOn () {
      if (this.number >= 5) {
        this.submit()
      } else {
        this.isJudge = false
        this.choose = []
        this.startProcess()
      }
    },

    reset () {
      this.isJudge = false
      this.choose = []
      this.number = 0
      this.startProcess()
    },

    confirm () {
      const answer = this.current.question.answer
      this.choose.forEach(item => {
        if (answer.includes(item)) {
          this.succesNum++
        } else {
          this.errorNum++
        }
      })
      this.isJudge = true
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.store = 10 * this.succesNum + 10
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isJudge = false
      this.current = {}
      this.choose = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game77-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game57/bg.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      position: absolute;
      top: 0;
      width: 932px * 0.7;
      height: 125px * 0.7;
      z-index: 1;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px * 0.7;
        height: 125px * 0.7;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 31px * 0.7 0 58px * 0.7 0;
        font-size: 36px * 0.7;
        line-height: 36px * 0.7;
        text-align: center;
        color: #1b1d2d;
        user-select: none;
      }
    }

    .content {
      position: relative;
      width: 1920px * 0.7;
      height: 988px * 0.7;
      margin-top: 90px * 0.7;
      padding: 144px * 0.7 250px * 0.7 266px * 0.7 230px * 0.7;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;

      .content-bg {
        position: absolute;
        top: 0;
        left: 135px * 0.7;
        width: 1655px * 0.7;
        height: 916px * 0.7;
      }

      .left-icon {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 603px * 0.7;
      }

      .right-icon {
        position: absolute;
        right: 20px * 0.7;
        bottom: 0;
        width: 684px * 0.7;
      }

      .content-item {
        position: relative;
        width: 280px * 0.7;
        height: 280px * 0.7;
        padding: 19px * 0.7 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        border: 3px * 0.7 solid #d2d2d2;
        border-radius: 45px * 0.7;

        .img {
          width: 220px * 0.7;
          height: 182px * 0.7;
        }

        .text {
          padding-top: 10px * 0.7;
          font-size: 32px * 0.7;
          line-height: 32px * 0.7;
          text-align: center;
          color: #1b1d2d;
          user-select: none;
        }

        .icon {
          position: absolute;
          right: 18px * 0.7;
          bottom: 21px * 0.7;
          width: 57px * 0.7;
        }
      }

      .choose-item {
        border: 10px * 0.7 solid #5f6fb2;
      }

      .correct-item {
        border: 10px * 0.7 solid #f1cc6e;
      }
    }

    .footer {
      position: absolute;
      bottom: 75px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1470px * 0.7;
      padding-right: 38px * 0.7;

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }

      .right-btn {
        margin-left: 43px * 0.7;
      }
    }
  }
}
</style>