<template>
  <div class="game46-page">
    <audio ref="music" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
    <settingPageLib @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、当记忆收到损伤时，会记不住经常出现在您身边、帮助您的人的姓名或身份。发生这种情况时，请不要泄气，我们将帮助您记住他们的名字。</p>
        <p class="synopsis-content">2、本组训练为相貌一人名在认。训练中，除了要记住一个人的相貌外，还要记住她/他的名字、她/他的职业以及她/他的爱好特征。训练分为“学习”和“判断”两部分。播放完学习阶段的照片后，在按不同顺序呈现每张人像，要求您回忆其姓名。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <template v-if="answerStatus === 'hint'">
        <div class="content1">
          <img class="content-bg" src="/static/game_assets/game8/content_bg.png">
          <p class="content-title">注意喽～</p>
          <p class="content-text">请记住即将划出的照片和名字、爱好、职业</p>
        </div>
      </template>

      <template v-if="answerStatus === 'play'">
        <div :class="['content2', isShowClass && 'opacity-content']">
          <div class="content-left">
            <img class="content-img" :src="current.img" />
          </div>
          <div class="content-right">
            <p class="content-text">姓名：{{current.name}}</p>
            <p class="content-text">职业：{{current.profession}}</p>
            <p class="content-text">爱好：{{current.hobby}}</p>
          </div>
        </div>
      </template>

      <template v-if="answerStatus === 'answer' || answerStatus === 'judge'">
        <div class="content3">
          <div class="content-left">
            <img class="content-img" :src="current.img" />
          </div>
          <div class="content-right">
            <p class="content-title">请选择{{index === 0 ? '姓名' : index === 1 ? '职业' : '爱好'}}：</p>
            <p :class="['content-item', choose === item && 'choose-item']" v-for="item in current.answerList[index]" :key="item + 'item'" @click="chooseItem(item)">{{item}}</p>
          </div>
        </div>
      </template>

      <div class="btn-group">
        <div class="btn" @click="stop" v-if="answerStatus !== 'play' && answerStatus !== 'hint'">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <div class="btn" @click="startGame" v-if="answerStatus === 'answerWait' || (answerStatus === 'answer' && choose)">
          <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
          <span class="text">{{ (answerStatus === 'answerWait' && '开始判断') || (answerStatus === 'answer' && '确定') }}</span>
        </div>
      </div>

      <div class="judge" v-if="answerStatus === 'judge'">
        <img class="img" :src="`/static/game_assets/game8/${isCorrect ? 'success' : 'error'}.png`">
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'
// 1级：两组图片
// 2级：三组图片
// 3级：四组图片

export default {
  name: 'game46',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      show: false,
      status: 1,
      isStop: false,
      isShowClass: false,
      index: 0,
      questions: [],
      current: {},
      answerStatus: 'hint', // play -- 题目 answer -- 回答 answerWait -- 回答前等待 hint -- 提示 judge -- 判定
      isCorrect: false,
      choose: '',
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.status = 3
      this.questions = api.getRandomArray(data.data, this.level + 1)
      setTimeout(() => {
        this.answerStatus = 'play'
        this.startProcess()
      }, 800)
    },

    startGame () {
      if (this.answerStatus === 'answerWait') {
        this.answerStatus = 'answer'
        this.current = this.questions[this.number]
        this.current.answerList[this.index] = api.shuffle(this.current.answerList[this.index])
        this.number++
      } else if (this.answerStatus === 'answer') {
        this.answerStatus = 'judge'
        if (this.index === 0) {
          if (this.choose === this.current.name) {
            this.succesNum++
            this.isCorrect = true
          } else {
            this.errorNum++
            this.isCorrect = false
          }
        } else if (this.index === 1) {
          if (this.choose === this.current.profession) {
            this.succesNum++
            this.isCorrect = true
          } else {
            this.errorNum++
            this.isCorrect = false
          }
        } else if (this.index === 2) {
          if (this.choose === this.current.hobby) {
            this.succesNum++
            this.isCorrect = true
          } else {
            this.errorNum++
            this.isCorrect = false
          }
        }

        setTimeout(() => {
          this.answerStatus = 'answer'
          if (this.number >= this.level + 1 && this.index === 2) {
            this.submit()
          } else if (this.number < this.level + 1 && this.index === 2) {
            this.index = 0
            this.choose = ''
            this.current = this.questions[this.number]
            this.number++
          } else if (this.index < 2) {
            this.index++
            this.choose = ''
            this.current.answerList[this.index] = api.shuffle(this.current.answerList[this.index])
          }
        }, 800)
      }
    },

    // 开始流程
    async startProcess () {
      this.current = this.questions[0]
      await this.playAnimation(500)
      this.isShowClass = true
      await this.playAnimation(2000)
      this.isShowClass = false

      this.current = this.questions[1]
      await this.playAnimation(500)
      this.isShowClass = true
      await this.playAnimation(2000)
      this.isShowClass = false

      if (this.level >= 2) {
        this.current = this.questions[2]
        await this.playAnimation(500)
        this.isShowClass = true
        await this.playAnimation(2000)
        this.isShowClass = false
      }

      if (this.level >= 3) {
        this.current = this.questions[3]
        await this.playAnimation(500)
        this.isShowClass = true
        await this.playAnimation(2000)
        this.isShowClass = false
      }
      this.answerStatus = 'answerWait'
    },

    playAnimation (time) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve(true)
        }, time)
      })
    },

    chooseItem (item) {
      this.choose = item
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    submit () {
      this.isStop = true
      if (this.level === 1) {
        this.store = this.succesNum > 1 ? 20 + 16 * (this.succesNum - 1) : this.succesNum ? 20 : 0
      } else if (this.level === 2) {
        this.store = this.succesNum > 1 ? 12 + 11 * (this.succesNum - 1) : this.succesNum ? 12 : 0
      } else if (this.level === 3) {
        this.store = this.succesNum > 1 ? 12 + 8 * (this.succesNum - 1) : this.succesNum ? 12 : 0
      }
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    // 继续游戏, 继续计时
    cancel () {
      if (this.answerStatus !== 'hint') return
      this.isStop = false
      this.timing()
    },

    again () {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.isStop = false
      this.status = 3
      this.answerStatus = 'hint'
      this.index = 0
      this.choose = ''
      this.current = {}
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game46-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game46/bg.png');
  }

  .game-synopsis {
    .synopsis-content {
      padding-top: 20px * 0.7;
      margin: 0;
      font-size: 30px * 0.7;
      line-height: 42px * 0.7;
      font-weight: 400;
      color: #a83a01;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content1 {
      position: relative;
      width: 1253px * 0.7;
      height: 708px * 0.7;
      margin-bottom: 80px * 0.7;
      padding-top: 220px * 0.7;
      padding-right: 13px * 0.7;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1253px * 0.7;
        height: 708px * 0.7;
      }

      .content-title {
        position: relative;
        margin: 0;
        font-size: 60px * 0.7;
        line-height: 84px * 0.7;
        text-align: center;
        font-weight: bold;
        color: #a83a01;
      }

      .content-text {
        position: relative;
        margin: 0;
        padding-top: 40px * 0.7;
        font-size: 48px * 0.7;
        line-height: 67px * 0.7;
        text-align: center;
        color: #a83a01;
      }
    }

    .content2 {
      width: 1180px * 0.7;
      height: 610px * 0.7;
      margin-bottom: 150px * 0.7;
      background: #4ea787;
      padding: 12px * 0.7;
      display: flex;
      justify-content: space-between;
      opacity: 0;

      .content-left {
        width: 438px * 0.7;
        height: 585px * 0.7;
        background: #fff;

        .content-img {
          width: 100%;
          height: 100%;
        }
      }

      .content-right {
        width: 705px * 0.7;
        height: 585px * 0.7;
        padding: 162px * 0.7 0;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        background: #fff;

        .content-text {
          margin: 0;
          font-size: 48px * 0.7;
          line-height: 67px * 0.7;
          text-align: center;
          font-weight: 500;
          color: #464f32;
        }
      }
    }

    .opacity-content {
      opacity: 1;
      transition: opacity 0.5s;
    }

    .content3 {
      width: 960px * 0.7;
      height: 610px * 0.7;
      margin-bottom: 60px * 0.7;
      margin-left: 70px * 0.7;
      display: flex;
      justify-content: space-between;

      .content-left {
        width: 462px * 0.7;
        height: 610px * 0.7;
        background: #4ea787;
        padding: 12px * 0.7;

        .content-img {
          width: 439px * 0.7;
          height: 586px * 0.7;
          background: #fff;
        }
      }

      .content-right {
        padding: 12px * 0.7 0 12px * 0.7 195px * 0.7;
        display: flex;
        flex-direction: column;

        .content-title {
          margin: 0;
          padding-left: 10px * 0.7;
          padding-bottom: 43px * 0.7;
          font-size: 38px * 0.7;
          line-height: 54px * 0.7;
          font-weight: 500;
          color: #a83a01;
        }

        .content-item {
          margin: 40px * 0.7 0 0 0;
          width: 298px * 0.7;
          height: 82px * 0.7;
          background: #fff;
          border-radius: 40px * 0.7;

          font-size: 38px * 0.7;
          line-height: 82px * 0.7;
          text-align: center;
          font-weight: 500;
          color: #163625;
          cursor: pointer;
        }

        .choose-item {
          color: #fff;
          background: #ea9a22;
        }
      }
    }

    .btn-group {
      position: absolute;
      bottom: 82px * 0.7;
      width: 1554px * 0.7;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px * 0.7;
        height: 84px * 0.7;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px * 0.7;
          height: 84px * 0.7;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px * 0.7 0 21px * 0.7 0;
          font-size: 38px * 0.7;
          line-height: 53px * 0.7;
          text-align: center;
          color: #a83a01;
        }
      }
    }

    .judge {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding-top: 10px * 0.7;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      justify-content: center;
      align-items: center;

      .img {
        width: 460px * 0.7;
        height: 460px * 0.7;
      }
    }
  }
}
</style>