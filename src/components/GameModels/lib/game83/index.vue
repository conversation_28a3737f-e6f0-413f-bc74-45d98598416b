<template>
  <div class="game83-page">
    <div class="page-bg"></div>
    <!-- <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio> -->
    <settingPageLib title="最亮颜色" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、通过学习并对比来掌握对颜色亮度的认知。</p>
        <p class="synopsis-content">2、根据提示选择最亮的颜色。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="top">
        <div class="top-left">
          <div class="left-item">
            <img class="item-icon" src="/static/game_assets/common/clock.png" />
            <span class="item-text">时间：{{second}}</span>
          </div>

          <div class="left-item">
            <img class="item-icon" src="/static/game_assets/game83/icon_2.png" />
            <span class="item-text">分数：{{store}}</span>
          </div>

          <!-- <div class="left-item">
            <img class="item-icon" src="/static/game_assets/game83/icon_1.png" />
            <span class="item-text">LV{{level}}</span>
          </div> -->
        </div>

        <div class="top-right">
          <template v-for="item in 5">
            <img v-if="number > item" class="right-icon" :key="item + 'img'" src="/static/game_assets/game83/icon_3.png" />
            <img v-else class="right-icon" :key="item + 'img'" src="/static/game_assets/game83/icon_4.png" />
          </template>
        </div>
      </div>

      <div class="content">
        <div :class="['content-item', item.index === current.answer && isHint && 'flash-item']" :style="{'background': item.color}" v-for="item in current.answerList" :key="item.index + 'item'" @click="chooseItem(item.index)"></div>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game83',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
    draggable
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isPlay: false,
      isRePlay: false,
      show: false,
      isHint: false,
      questionList: [],
      current: {},
      choose: 0,

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.status = 3
      // this.timing()
      this.questionList = api.shuffle(data.data)
      this.startProcess()
    },

    startProcess () {
      if (this.number >= 5) {
        this.submit()
        return
      }

      this.current = this.questionList[this.number]
      this.current.answerList = api.shuffle(this.current.answerList)
      this.number++
    },

    chooseItem (item) {
      if (item === this.current.answer) {
        if (this.choose !== this.number) {
          this.succesNum++
          this.store = 20 * this.succesNum
          this.choose = this.number
        }
        this.isHint = false
        this.startProcess()
      } else {
        if (this.choose !== this.number) this.choose = this.number
        this.errorNum++
        this.isHint = true
      }
    },

    stop () {
      if (this.isPlay) {
        this.pause()
        this.isRePlay = true
      }
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      if (this.isRePlay) {
        this.play()
        this.isRePlay = false
      }
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.questionList = []
      this.current = {}
      this.choose = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game83-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game83/bg.png');
  }

  .game-synopsis {
    width: 707px * 0.7;
    height: 444px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fffef3;
    border: 2px * 0.7 solid #014747;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.2);

    .top {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      padding: 30px * 0.7 230px * 0.7 0 70px * 0.7;
      display: flex;
      justify-content: space-between;

      .top-left {
        display: flex;
        justify-content: space-between;
        // width: 710px * 0.7;
        width: 506px * 0.7;

        .left-item {
          font-size: 0;
          .item-icon {
            width: 60px * 0.7;
            height: 60px * 0.7;
            vertical-align: middle;
          }

          .item-text {
            display: inline-block;
            padding-left: 14px * 0.7;
            font-size: 37px * 0.7;
            line-height: 60px * 0.7;
            font-weight: 600;
            vertical-align: middle;
            user-select: none;
          }
        }
      }

      .top-right {
        display: flex;
        justify-content: space-between;
        width: 380px * 0.7;

        .right-icon {
          width: 60px * 0.7;
          height: 60px * 0.7;
        }
      }
    }

    .content {
      position: relative;
      width: 924px * 0.7;
      height: 675px * 0.7;
      margin-left: 248px * 0.7;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .content-item {
        width: 248px * 0.7;
        height: 675px * 0.7;
        box-shadow: 0px * 0.7 2px * 0.7 56px * 0.7 0px * 0.7 #ffffff;
        border-radius: 2px * 0.7;
        border: 3px * 0.7 solid #ffffff;
        cursor: pointer;
      }

      .flash-item {
        animation: flash 1.5s linear infinite;
      }
    }
  }
}

@keyframes flash {
  0% {
    transform: scale(1);
  }
  60% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style>