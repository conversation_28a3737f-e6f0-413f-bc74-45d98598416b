<template>
  <div class="game180-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="数物品" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请根据提示配图，在屏幕正下方数字键盘中输入多少个物品；</p>
        <p class="synopsis-content">2、训练结束后会出现成绩单；</p>
        <p class="synopsis-content">3、本训练难度为A（简单）。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />

        <div class="top-text">有多少{{currect.text}}？</div>
      </div>

      <div class="content">
        <div class="content-top">
          <img class="top-img" v-for="item in currect.answer" :key="item + 'img1'" :src="`/static/game_assets/game180/item_${currect.imgIndex}.png`" />
        </div>

        <div class="content-bottom">
          <div class="left">
            <img v-for="item in 10" :key="item" class="btn" :src="`/static/game_assets/game35/btn_${item}.png`" @click="chooseItem(item)" />
          </div>
          <div class="right">
            <div class="input">
              <img class="img" src="/static/game_assets/game35/input.png" />
              <img class="clear" src="/static/game_assets/game35/clear.png" @click="answer = ''" />
              <span class="number">{{ answer }}</span>
            </div>
            <img class="img" src="/static/game_assets/game35/confirm.png" @click="submit" />
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="footer-right">
          <p class="item">题目分数：{{store}}</p>
          <p class="item">题目数量：{{number}}</p>
          <p class="item">用时：{{time}}</p>
        </div>
      </div>
    </div>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import api from '../../utils/common.js'

export default {
  name: 'game180',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    draggable
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      level: 1,
      status: 1,
      isPlay: false,
      isStop: false,

      answer: '',
      currect: {
        text: '',
        imgIndex: 0,
        answer: 0
      },

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.startProcess()
      this.status = 3
      // this.timing()
      this.play()
    },

    startProcess () {
      const list = [
        {
          index: 1,
          text: '只青蛙'
        },
        {
          index: 2,
          text: '个蛋糕'
        },
        {
          index: 3,
          text: '根胡萝卜'
        },
        {
          index: 4,
          text: '个橘子'
        },
        {
          index: 5,
          text: '串葡萄'
        },
        {
          index: 6,
          text: '个篮球'
        },
        {
          index: 7,
          text: '只鸭子'
        },
      ]
      this.currect.answer = api.randomNum(1, 10)
      this.currect.imgIndex = api.getRandomArray(list, 1)[0].index
      this.currect.text = list[this.currect.imgIndex - 1].text
      this.number++
    },

    chooseItem (item) {
      if (this.answer.length >= 2) return
      this.answer = this.answer + (item === 10 ? 0 : item).toString()
    },

    submit () {
      if (!this.answer) return
      if (Number(this.answer) === this.currect.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }
      this.store = 10 * this.succesNum

      if (this.number < 10) {
        this.answer = ''
        this.startProcess()
      } else {
        this.pause()
        this.isStop = true
        this.infos[0].value = this.level
        this.infos[1].value = this.second
        this.infos[2].value = this.succesNum
        this.infos[3].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: this.level,
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.isStop = false
      this.store = 0
      this.answer = ''
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game180-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game176/bg.png');
  }

  .game-synopsis {
    width: 707px * 0.7;
    height: 444px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border: 2px * 0.7 solid #58ad49;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 20px * 0.7;
      left: 0;
      width: 100%;
      padding: 0 54px * 0.7;

      .top-icon {
        position: absolute;
        top: 0;
        left: 50px * 0.7;
        width: 80px * 0.7;
        cursor: pointer;
        z-index: 1;
      }

      .top-text {
        position: relative;
        font-size: 50px * 0.7;
        line-height: 82px * 0.7;
        text-align: center;
        font-weight: 600;
        color: #0b4f5d;
      }
    }

    .content {
      position: relative;
      width: 1434px * 0.7;
      height: 638px * 0.7;
      margin-bottom: 40px * 0.7;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;

      .content-top {
        width: 725px * 0.7;
        height: 290px * 0.7;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;

        .top-img {
          width: 120px * 0.7;
          height: 120px * 0.7;
          margin: 10px * 0.7;
        }
      }

      .content-bottom {
        width: 1434px * 0.7;
        height: 309px * 0.7;
        display: flex;

        .left {
          display: flex;
          flex-wrap: wrap;
          align-content: space-between;
          width: 1083px * 0.7;
          height: 309px * 0.7;

          img {
            width: 180px * 0.7;
            height: 127px * 0.7;
            margin: 0 18px * 0.7;
            cursor: pointer;
          }
        }

        .right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: flex-end;
          width: 350px * 0.7;
          height: 309px * 0.7;
          padding-right: 10px * 0.7;

          .input {
            position: relative;
            width: 313px * 0.7;
            height: 129px * 0.7;

            .img {
              position: absolute;
              top: 0;
              left: 0;
              width: 313px * 0.7;
              height: 127px * 0.7;
            }

            .clear {
              position: absolute;
              top: 29px * 0.7;
              right: 24px * 0.7;
              width: 57px * 0.7;
              height: 58px * 0.7;
              cursor: pointer;
            }

            .number {
              position: absolute;
              top: 20px * 0.7;
              left: 80px * 0.7;
              width: 145px * 0.7;
              height: 74px * 0.7;
              font-size: 52px * 0.7;
              line-height: 74px * 0.7;
              text-align: center;
              font-family: Impact;
              color: #fff;
            }
          }

          .img {
            width: 313px * 0.7;
            height: 127px * 0.7;
            cursor: pointer;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 20px * 0.7;
      display: flex;
      justify-content: flex-end;
      align-items: flex-end;
      width: 1620px * 0.7;

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }

      .footer-right {
        width: 690px * 0.7;
        display: inline-flex;
        justify-content: space-between;

        .item {
          margin: 0;
          width: 210px * 0.7;
          height: 76px * 0.7;
          border-radius: 4px * 0.7;
          border: 1px * 0.7 solid #53872a;
          background: #b7eba4;

          font-size: 24px * 0.7;
          text-align: center;
          line-height: 74px * 0.7;
          color: #53872a;
        }
      }
    }
  }
}
</style>