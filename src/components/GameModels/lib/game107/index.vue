<template>
  <div class="game107-page">
    <div class="page-bg"></div>
    <settingPageLib @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、通过本训练的教学，提高对日常生活所涉及安全的认知。</p>
        <p class="synopsis-content">2、一共四部分，请根据提示进行操作。</p>
      </div>
      </settingPageLib>
      <div class="game-content" v-if="status === 3">
        <div class="content">
          <img class="content-bg" src="/static/game_assets/game8/content_bg.png" />
          <img class="left-btn" @click="toPrevious" src="/static/game_assets/game107/left.png" />
          <img class="right-btn" @click="toNext" src="/static/game_assets/game107/right.png" />

          <div class="content-left">
            <img class="left-img" :src="`/static/game_assets/game107/item_${questions[number].index}.png`" />
            <img class="left-icon" v-show="questions[number].index !== 2" src="/static/game_assets/game107/error.png" />
          </div>

          <div class="content-right">{{questions[number].text}}</div>
        </div>

        <div class="btn-group">
          <div class="btn" @click="stop">
            <img class="bg" src="/static/game_assets/game8/red_bg.png">
            <span class="text">停止</span>
          </div>
        </div>
      </div>
      <bgMusic :status="status"></bgMusic>
      <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
      <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 每轮游戏3个回合

export default {
  name: 'game107',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      questions: [],
      index: 0,
      show: false,
      status: 1,
      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      const list = [
        {
          text: '见插座不触碰',
          index: 3
        },
        {
          text: '红灯停绿灯行',
          index: 2
        },
        {
          text: '遇见火要远离',
          index: 1
        }
      ]
      this.questions = api.shuffle(list)
      // this.timing()
    },

    toPrevious () {
      if (this.number > 0) {
        this.number--
      } else {
        this.number = 2
      }
    },

    toNext () {
      if (this.number >= 2) {
        this.number = 0
      } else {
        this.number++
      }
    },

    // 开始流程
    async startProcess () {
      this.number++

    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    again () {
      this.isStop = false
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.status = 3
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game107-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game107/bg.png');
  }

  .game-synopsis {
    .synopsis-content {
      padding-top: 20px * 0.7;
      margin: 0;
      font-size: 30px * 0.7;
      line-height: 42px * 0.7;
      font-weight: 400;
      color: #a83a01;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1253px * 0.7;
      height: 708px * 0.7;
      margin-bottom: 82px * 0.7;
      padding: 108px * 0.7 220px * 0.7 0 150px * 0.7;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1253px * 0.7;
      }

      .left-btn {
        position: absolute;
        top: 300px * 0.7;
        left: 30px * 0.7;
        width: 64px * 0.7;
        cursor: pointer;
      }

      .right-btn {
        position: absolute;
        top: 300px * 0.7;
        right: 48px * 0.7;
        width: 64px * 0.7;
        cursor: pointer;
      }

      .content-left {
        position: relative;
        width: 448px * 0.7;
        height: 448px * 0.7;

        .left-img {
          width: 448px * 0.7;
        }

        .left-icon {
          position: absolute;
          top: 37px * 0.7;
          left: 38px * 0.7;
          width: 373px * 0.7;
        }
      }

      .content-right {
        position: relative;
        width: 365px * 0.7;
        padding: 45px * 0.7 0;
        font-size: 120px * 0.7;
        text-align: center;
        line-height: 175px * 0.7;
        color: #a83a01;
      }
    }

    .btn-group {
      position: absolute;
      bottom: 82px * 0.7;
      width: 1450px * 0.7;
      height: 84px * 0.7;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px * 0.7;
        height: 84px * 0.7;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px * 0.7;
          height: 84px * 0.7;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px * 0.7 0 21px * 0.7 0;
          font-size: 38px * 0.7;
          line-height: 53px * 0.7;
          text-align: center;
          color: #a83a01;
        }
      }
    }
  }
}
</style>