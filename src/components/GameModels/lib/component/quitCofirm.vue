<template>
  <div class="quit-confirm" v-if="show">
    <div class="content">
      <div class="title">确定要停止测试吗？</div>
      <div class="btn-group">
        <div class="cancel" @click="cancel">取消</div>
        <div class="confirm" @click="confirm">确定</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'quitConfirm',

  props: {
    show: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    cancel() {
      this.$emit('update:show', false)
      this.$emit('cancel')
    },

    confirm() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.quit-confirm {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99;

  .content {
    width: 984px * 0.7;
    height: 394px * 0.7;
    margin-top: 26px * 0.7;
    background: #fff;
    border-radius: 36px * 0.7;
    border: 1px * 0.7 solid #0E8C58;

    .title {
      padding: 116px * 0.7 0 93px * 0.7 0;
      font-size: 42px * 0.7;
      line-height: 59px * 0.7;
      text-align: center;
      color: #333;
      user-select: none;
    }

    .btn-group {
      display: flex;
      border-top: 1px * 0.7 solid #DBDBDB;

      .cancel, .confirm {
        flex: 1;
        padding: 31px * 0.7 0 35px * 0.7 0;
        font-size: 42px * 0.7;
        line-height: 59px * 0.7;
        text-align: center;
        cursor: pointer;
      }

      .cancel {
        color: #666;
        border-right: 1px * 0.7 solid #DBDBDB;
      }

      .confirm {
        color: #00AD68;
      }
    }
  }
}
</style>