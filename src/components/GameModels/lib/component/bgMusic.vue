<template>
  <audio ref="music" controls="controls" loop="loop" style="display:none">
    <source src="/static/game_assets/audio/bg_audio.mp3" type="audio/mpeg" />
  </audio>
</template>

<script>

export default {
  name: 'bgMusic',

  props: {
    status: {
      type: Number,
      default: 1
    },

    isStep: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      isPlay: false
    }
  },

  methods: {
    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },
  },

  watch: {
    status (newValue) {
      if (this.isStep) {
        if (newValue === 4) {
          this.play()
        } else if (newValue === 5) {
          this.pause()
        }
      } else {
        if (newValue === 3) {
          this.play()
        } else if (newValue === 4) {
          this.pause()
        }
      }
    }
  }
}
</script>
