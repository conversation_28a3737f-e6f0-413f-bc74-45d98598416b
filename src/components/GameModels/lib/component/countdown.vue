<template>
  <div class="countdown" v-if="show">
    <div class="num transform" v-if="max == 3">
      <img :src="'/static/game_assets/game1/images/num3.png'" class="img" alt="" />
    </div>
    <div class="num transform1" v-else-if="max == 2">
      <img :src="'/static/game_assets/game1/images/num2.png'" class="img" alt="" />
    </div>
    <div class="num transform2" v-else>
      <img :src="'/static/game_assets/game1/images/num1.png'" class="img" alt="" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'countdown',

  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data () {
    return {
      max: 3,
      min: 0,
    }
  },
  methods: {
    countdown () {
      let fun = () => {
        if (this.min == this.max) {
          this.$emit('update:show', false)
        } else {
          setTimeout(() => {
            this.max -= 1
            fun()
          }, 1000)
        }
      }
      fun()
    },
  },

  watch: {
    show: {
      handler (newValue) {
        if (newValue) {
          this.countdown()
        }
      },
      immediate: true,
    },
  },
}
</script>

<style lang="scss">
.countdown {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);

  .num {
    width: 726px * 0.7;
    height: 726px * 0.7;
    background-image: url('/static/game_assets/game1/images/bg4.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transform: rotateX(0deg);
    .img {
      width: 312px * 0.7;
      height: 312px * 0.7;
    }
    &.transform {
      transform: scale(0);
      animation-name: num;
      animation-duration: 1s;
      transition-timing-function: linear;
    }
    &.transform1 {
      transform: scale(0);
      animation-name: num;
      animation-duration: 1s;
      transition-timing-function: linear;
      // animation-delay: 1s;
    }
    &.transform2 {
      transform: scale(0);
      animation-name: num;
      animation-duration: 1s;
      transition-timing-function: linear;
      // animation-delay: 2s;
    }
  }

  @keyframes num {
    0% {
      transform: scale(0);
    }
    35% {
      transform: scale(1);
    }
    50% {
      transform: scale(0.8);
    }
    65% {
      transform: scale(1);
    }
    100% {
      transform: scale(0);
    }
  }
}
</style>