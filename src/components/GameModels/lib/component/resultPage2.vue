<template>
  <div class="result-page">
    <div class="content">
      <div class="main">
        <img class="content-bg" src="/static/game_assets/game8/content_bg.png">

        <div class="title">
          <img class="title-bg" src="/static/game_assets/game8/title_bg.png">
          <p class="text">你的分数</p>
        </div>

        <div class="top">
          <span class="number">{{store}}</span>
          <span class="text">分</span>
        </div>

        <div class="bottom">
          <p class="info" v-for="(item, index) in info" :key="index">
            {{item.key}}：{{item.value}}
          </p>
        </div>
      </div>
    </div>

    <div class="footer">
      <div class="btn" @click="quit">
        <img class="img" src="/static/game_assets/game8/yellow_bg.png">
        <span class="text">下一题</span>
      </div>

      <div class="btn" @click="again">
        <img class="img" src="/static/game_assets/game8/yellow_bg.png">
        <span class="text">再来一次</span>
      </div>
    </div>
  </div>
</template>

<script>
import api from '../../utils/common.js'

export default {
  name: 'resultPage',

  props: {
    store: {
      type: Number,
      default: 0
    },

    info: {
      type: Array,
      default: () => []
    },

    params: {
      type: Object,
      default: () => { }
    }
  },

  data () {
    return {

    }
  },

  methods: {
    quit () {
      api.save(this.params).then(res => {
        if (res.result && res.result.id) {

          window.location.href = window.location.origin + `/games/${res.result.nextPath}?id=${res.result.id}`
          // this.$router.replace(`/games/${res.result.nextPath}?id=${res.result.id}`)
        } else {
          this.$router.push({
            name: 'dashboard'
          })
          this.$notification['success']({
            message: '训练完成',
            description: '训练游戏已完成'
          })
        }
      })
      // this.$router.go(-1)
    },

    again () {
      this.$emit('again')
    }
  },

  watch: {
    info: {
      handler (newValue) {
        newValue.forEach(item => {
          if (item.key === '训练时长') {
            if (typeof item.value === 'string') return
            let m = parseInt(item.value / 60)
            let s = item.value % 60

            item.value = m ? m + '分' + s + '秒' : s + '秒'
          }
        })
      },

      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.result-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .content {
    flex: 1;
    display: inline-flex;
    align-items: center;
    padding-top: 40px * 0.7;

    .main {
      position: relative;
      width: 1253px * 0.7;
      height: 754px * 0.7;

      .content-bg {
        position: absolute;
        top: 46px * 0.7;
        left: 0;
        width: 1253px * 0.7;
        height: 708px * 0.7;
      }

      .title {
        position: absolute;
        top: 0;
        left: 346px * 0.7;
        width: 547px * 0.7;
        height: 124px * 0.7;

        .title-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 547px * 0.7;
          height: 124px * 0.7;
        }

        .text {
          position: relative;
          margin: 0;
          padding: 24px * 0.7 0 40px * 0.7 0;
          font-size: 44px * 0.7;
          line-height: 60px * 0.7;
          text-align: center;
          font-weight: bold;
          color: #fff;
          user-select: none;
        }
      }

      .top {
        position: relative;
        padding-top: 220px * 0.7;
        text-align: center;

        .number {
          display: inline-block;
          font-size: 140px * 0.7;
          font-weight: bold;
          line-height: 140px * 0.7;
          color: #a83a01;
          font-family: PingFang-SC-Bold, PingFang-SC;
          user-select: none;
        }

        .text {
          position: relative;
          top: -20px * 0.7;
          display: inline-block;
          padding-left: 15px * 0.7;
          font-size: 58px * 0.7;
          line-height: 58px * 0.7;
          font-weight: bold;
          color: #a83a01;
          user-select: none;
        }
      }

      .bottom {
        position: relative;
        padding: 60px * 0.7 225px * 0.7 0 307px * 0.7;
        display: flex;
        flex-wrap: wrap;

        .info {
          width: 360px * 0.7;
          padding-left: 20px * 0.7;
          padding-bottom: 16px * 0.7;
          margin: 0;
          font-size: 32px * 0.7;
          line-height: 45px * 0.7;
          font-weight: 600;
          color: #b86628;
          user-select: none;
        }
      }
    }
  }

  .footer {
    width: 800px * 0.7;
    display: flex;
    justify-content: space-between;
    padding-bottom: 84px * 0.7;

    .btn {
      position: relative;
      width: 294px * 0.7;
      height: 83px * 0.7;
      text-align: center;
      cursor: pointer;

      .img {
        position: absolute;
        top: 0;
        left: 0;
        width: 294px * 0.7;
        height: 83px * 0.7;
      }

      .text {
        position: relative;
        display: inline-block;
        padding: 10px * 0.7 0 20px * 0.7 0;
        font-size: 38px * 0.7;
        line-height: 54px * 0.7;
        color: #a83a01;
        text-align: center;
        user-select: none;
      }
    }
  }
}
</style>