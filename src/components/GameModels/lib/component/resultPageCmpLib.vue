<template>
  <div class="result-page">
    <div class="content">
      <div class="main">
        <img class="content-bg" src="/static/game_assets/common/score_bg.png">
        <div class="top">
          <span class="number">{{store}}</span>
          <span class="text">分</span>
        </div>
        <div class="bottom">
          <p class="info" v-for="(item, index) in info" :key="index">
            {{item.key}}：{{item.value}}
          </p>
        </div>
      </div>
    </div>
    <div class="footer">
      <img class="left" src="/static/game_assets/common/again.png" @click="again">
      <img class="right" src="/static/game_assets/common/next.png" @click="next">
    </div>
  </div>
</template>

<script>
import api from '../../utils/common.js'

export default {
  name: 'resultPage',

  props: {
    store: {
      type: Number,
      default: 0
    },

    info: {
      type: Array,
      default: () => []
    },

    params: {
      type: Object,
      default: () => { }
    }
  },

  data () {
    return {

    }
  },

  methods: {
    again () {
      this.$emit('again')
    },

    next () {
      api.save(this.params).then(res => {
        if (res.result && res.result.id) {
          window.location.href = window.location.origin + `/games/${res.result.nextPath}?id=${res.result.id}`
          // this.$router.replace(`/games/${res.result.nextPath}?id=${res.result.id}`)
        } else {
          this.$router.push({
            name: 'dashboard'
          })
          this.$notification['success']({
            message: '训练完成',
            description: '训练游戏已完成'
          })
        }
      })
      // this.$router.go(-1)
    }
  },

  watch: {
    info: {
      handler (newValue) {
        newValue.forEach(item => {
          if (item.key === '训练时长') {
            if (typeof item.value === 'string') return
            let m = parseInt(item.value / 60)
            let s = item.value % 60

            item.value = m ? m + '分' + s + '秒' : s + '秒'
          }
        })
      },

      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.result-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .content {
    flex: 1;
    display: inline-flex;
    align-items: center;
    padding-top: 60px * 0.7;

    .main {
      position: relative;
      width: 1148px * 0.7;
      height: 683px * 0.7;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1148px * 0.7;
        height: 683px * 0.7;
      }

      .top {
        position: relative;
        padding-top: 90px * 0.7;
        text-align: center;

        .number {
          display: inline-block;
          font-size: 170px * 0.7;
          font-weight: 600;
          line-height: 238px * 0.7;
          color: transparent;
          background: radial-gradient(circle at -80px * 0.7 -50px * 0.7, #ff7b00 0%, #e82f2f 100%);
          -webkit-background-clip: text;
          user-select: none;
        }

        .text {
          position: relative;
          top: -10px * 0.7;
          display: inline-block;
          padding-left: 17px * 0.7;
          font-size: 48px * 0.7;
          line-height: 67px * 0.7;
          color: transparent;
          background: radial-gradient(circle at 20px * 0.7 30px * 0.7, #ffc500 0%, #ff5353 100%);
          -webkit-background-clip: text;
          user-select: none;
        }
      }

      .bottom {
        position: relative;
        padding: 60px * 0.7 240px * 0.7 0 268px * 0.7;
        display: flex;
        flex-wrap: wrap;

        .info {
          width: 320px * 0.7;
          padding-left: 20px * 0.7;
          padding-bottom: 16px * 0.7;
          margin: 0;
          font-size: 32px * 0.7;
          line-height: 45px * 0.7;
          font-weight: 600;
          color: #5e381f;
          user-select: none;
        }
      }
    }
  }

  .footer {
    width: 800px * 0.7;
    display: flex;
    justify-content: space-between;
    padding-bottom: 100px * 0.7;

    img {
      width: 270px * 0.7;
      height: 115px * 0.7;
      cursor: pointer;
    }
  }
}
</style>