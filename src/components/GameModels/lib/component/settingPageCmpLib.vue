<template>
  <div class="setting-page">
    <template v-if="!show">
      <p class="title">{{title}}</p>
      <slot></slot>
      <div class="footer">
        <img class="img" src="/static/game_assets/common/quit.png" @click="quit">
        <img class="img" src="/static/game_assets/common/start.png" @click="start">
      </div>
    </template>
    <countdown v-model:show="show" />
  </div>
</template>

<script>
import countdown from './countdown.vue'

export default {
  name: 'settingPage',

  components: {
    [countdown.name]: countdown
  },

  props: {
    title: {
      type: String,
      default: ''
    },

    showAnimation: {
      type: Boolean,
      default: true
    }
  },

  data () {
    return {
      show: false
    }
  },

  methods: {
    quit () {
      this.$router.go(-1)
    },

    start () {
      if (this.showAnimation) this.show = true
      this.$emit('start')
    }
  },

  watch: {
    show (newValue) {
      if (!newValue) {
        this.$emit('challenge')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.setting-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .title {
    position: absolute;
    top: 77px * 0.7;
    margin: 0;
    font-size: 50px * 0.7;
    line-height: 70px * 0.7;
    font-weight: 600;
    color: #5e381f;
    text-align: center;
    user-select: none;
  }

  .footer {
    position: absolute;
    bottom: 40px * 0.7;
    width: 734px * 0.7;
    display: inline-flex;
    justify-content: space-between;

    .img {
      width: 268px * 0.7;
      height: 115px * 0.7;
      cursor: pointer;
    }
  }
}
</style>