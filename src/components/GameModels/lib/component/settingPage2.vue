<template>
  <div class="setting-page">
    <template v-if="!show">
      <div class="mark">
        <img class="bg" src="/static/game_assets/game8/mark.png">
        <span class="text">？</span>
      </div>

      <div class="content-warp">
        <div class="content">
          <img class="bg" src="/static/game_assets/game8/content_bg.png">
          <div class="main">
            <p class="title">训练说明</p>
            <slot></slot>
          </div>
        </div>
      </div>

      <div class="footer-warp">
        <div class="footer">
          <div class="btn" @click="quit">
            <img class="img" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">训练主页</span>
          </div>

          <div class="btn" @click="start">
            <img class="img" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">开始训练</span>
          </div>
        </div>
      </div>
    </template>
    <countdown :show.sync="show" />
  </div>
</template>

<script>
import countdown from './countdown.vue'

export default {
  name: 'settingPage',

  components: {
    [countdown.name]: countdown
  },

  props: {
    title: {
      type: String,
      default: ''
    }
  },

  data () {
    return {
      show: false
    }
  },

  methods: {
    quit () {
      this.$router.go(-1)
    },

    start () {
      this.show = true
      this.$emit('start')
    }
  },

  watch: {
    show (newValue) {
      if (!newValue) {
        this.$emit('challenge')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.setting-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // align-items: center;

  .mark {
    position: relative;
    padding-top: 45px * 0.7;
    padding-right: 60px * 0.7;
    text-align: right;

    .bg {
      position: absolute;
      top: 45px * 0.7;
      right: 60px * 0.7;
      width: 126px * 0.7;
      height: 88px * 0.7;
    }

    .text {
      position: relative;
      display: inline-block;
      padding: 10px * 0.7 18px * 0.7 18px * 0.7 0;
      font-size: 60px * 0.7;
      line-height: 60px * 0.7;
      font-weight: 500;
      color: #a83a01;
      text-align: center;
      user-select: none;
    }
  }

  .content-warp {
    width: 100%;
    height: 708px * 0.7;
    padding-bottom: 32px * 0.7;

    .content {
      position: relative;
      width: 1200px * 0.7;
      height: 678px * 0.7;
      margin: 0 auto;

      .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1200px * 0.7;
        height: 678px * 0.7;
      }

      .main {
        position: relative;
        padding: 35px * 0.7 82px * 0.7 0 71px * 0.7;

        .title {
          padding-bottom: 26px * 0.7;
          font-size: 60px * 0.7;
          line-height: 60px * 0.7;
          font-weight: bold;
          text-align: center;
          color: #a83a01;
          user-select: none;
        }
      }
    }
  }

  .footer-warp {
    width: 100%;
    padding-bottom: 45px * 0.7;
    text-align: center;

    .footer {
      width: 682px * 0.7;
      display: inline-flex;
      justify-content: space-between;
      margin: 0 auto;

      .btn {
        position: relative;
        width: 294px * 0.7;
        height: 83px * 0.7;
        cursor: pointer;

        .img {
          position: absolute;
          top: 0;
          left: 0;
          width: 294px * 0.7;
          height: 83px * 0.7;
        }

        .text {
          position: relative;
          display: inline-block;
          padding: 10px * 0.7 0 20px * 0.7 0;
          font-size: 38px * 0.7;
          line-height: 54px * 0.7;
          color: #a83a01;
          text-align: center;
          user-select: none;
        }
      }
    }
  }
}
</style>