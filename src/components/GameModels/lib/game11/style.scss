
.game11-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    inset: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    background-image: url('/static/game_assets/game11/bg.png');
  }

  .game-synopsis {
    width: 1200px * 0.7;
    height: 580px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border: 2px * 0.7 solid #7bbd41;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .img1 {
      position: absolute;
      top: 55px * 0.7;
      left: 35px * 0.7;
      width: 270px * 0.7;
      height: 115px * 0.7;
      cursor: pointer;
    }

    .title {
      position: relative;
      padding: 20px * 0.7 0;
      width: 864px * 0.7;

      .title-bg {
        position: absolute;
        top: 20px * 0.7;
        left: 0;
        width: 864px * 0.7;
      }

      .title-text {
        position: relative;
        padding: 74px * 0.7 0 74px * 0.7 85px * 0.7;
        font-size: 52px * 0.7;
        line-height: 73px * 0.7;
        font-weight: 500;
        color: #5e381f;
        user-select: none;

        img {
          width: 73px * 0.7;
        }
      }
    }

    .content {
      position: relative;
      width: 1064px * 0.7;
      height: 526px * 0.7;
      padding-top: 25px * 0.7;
      display: flex;
      flex-direction: column;
      align-items: center;

      .content-input {
        position: relative;
        width: 312px * 0.7;
        height: 128px * 0.7;

        .img {
          position: absolute;
          top: 0;
          left: 0;
          width: 313px * 0.7;
          height: 129px * 0.7;
        }

        .clear {
          position: absolute;
          top: 29px * 0.7;
          right: 24px * 0.7;
          width: 57px * 0.7;
          height: 58px * 0.7;
          cursor: pointer;
        }

        .number {
          position: absolute;
          top: 20px * 0.7;
          left: 80px * 0.7;
          width: 145px * 0.7;
          height: 74px * 0.7;
          font-size: 52px * 0.7;
          line-height: 74px * 0.7;
          text-align: center;
          font-family: Impact;
          color: #fff;
          user-select: none;
        }
      }

      .content-main {
        height: 355px * 0.7;
        padding-top: 55px * 0.7;
        display: flex;
        flex-wrap: wrap;
        place-content: space-between space-between;

        .content-item {
          width: 180px * 0.7;
          height: 127px * 0.7;
          cursor: pointer;
        }
      }
    }

    .footer {
      position: relative;
      width: 312px * 0.7;
      padding-bottom: 20px * 0.7;

      .img {
        width: 312px * 0.7;
        cursor: pointer;
      }
    }

    .content-position {
      position: relative;
      width: 100%;
      height: 900px * 0.7;

      .position-img {
        position: absolute;
        width: 105px * 0.7;
        height: 105px * 0.7;
      }

      .img-1 {
        top: 400px * 0.7;
        left: 100px * 0.7;
      }

      .img-2 {
        top: 800px * 0.7;
        left: 1400px * 0.7;
      }

      .img-3 {
        top: 150px * 0.7;
        left: 400px * 0.7;
      }

      .img-4 {
        top: 80px * 0.7;
        left: 1000px * 0.7;
      }

      .img-5 {
        top: 10px * 0.7;
        left: 1500px * 0.7;
      }

      .img-6 {
        top: 500px * 0.7;
        left: 600px * 0.7;
      }

      .img-7 {
        top: 750px * 0.7;
        left: 300px * 0.7;
      }

      .img-8 {
        top: 600px * 0.7;
        left: 1700px * 0.7;
      }

      .img-9 {
        top: 800px * 0.7;
        left: 680px * 0.7;
      }

      .img-10 {
        top: 450px * 0.7;
        left: 850px * 0.7;
      }

      .img-11 {
        top: 320px * 0.7;
        left: 1270px * 0.7;
      }

      .img-0 {
        top: 280px * 0.7;
        left: 1700px * 0.7;
      }
    }

    .center-icon {
      position: absolute;
      top: 300px * 0.7;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
      z-index: 99;
    }
  }
}