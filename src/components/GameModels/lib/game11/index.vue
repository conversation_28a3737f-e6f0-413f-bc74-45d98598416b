<template>
  <div class="game11-page">
    <div class="page-bg"></div>
    <settingPageLib title="视觉广度-方式二" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、注意的广度又称注意的范围，是指在同一时间内一个人能看清楚地把握注意对象的数量。</p>
        <p class="synopsis-content">2、本组训练将有助于扩大您的注意范围，进而有助于提高学习和工作的效率。</p>
        <p class="synopsis-content">3、显示屏上将快速出现一些不同形状的图形或字母，请注意看，然后说出其中某个图形的个数。</p>
        <p class="synopsis-content">4、训练等级：依据注意的数量和目标呈现的时间长短分为六个等级。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <template v-if="isShowKey">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="title">
          <img class="title-bg" src="/static/game_assets/game11/title_bg.png" />
          <div class="title-text">
            <span>您刚才看到了几个"</span>
            <img :src="`/static/game_assets/game11/item_${chooseItem}.png`" />
            <span>"图形？</span>
          </div>
        </div>

        <div class="content">
          <div class="content-input">
            <img class="img" src="/static/game_assets/game35/input.png" />
            <img class="clear" src="/static/game_assets/game35/clear.png" @click="choose = ''" />
            <span class="number">{{ choose }}</span>
          </div>
          <div class="content-main">
            <img v-for="item in 10" :key="item" class="content-item" :src="`/static/game_assets/game35/btn_${item}.png`" @click="select(item)" />
          </div>
        </div>

        <div class="footer">
          <img class="img" src="/static/game_assets/game35/confirm.png" @click="confirm" />
        </div>
      </template>

      <template v-else>
        <div class="content-position">
          <img :class="['position-img', 'img-' + index]" v-for="(item, index) in questionList" :key="index + 'img'" :src="`/static/game_assets/game11/item_${item}.png`">
        </div>
      </template>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>

    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 游戏分为6个等级，每轮游戏3个回合
// 1级：1-3个图形 5个选项 1.5s
// 2级：1-3 6 1.5s
// 3级：2-4 7 1s
// 4级：2-4 8 1s
// 5级：3-5 9 0.5s
// 6级：3-5 10 0.5s

export default {
  name: 'game11',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [bgMusic.name]: bgMusic,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowKey: false,
      questionList: [],
      chooseItem: 0,
      answer: 0,
      choose: '',

      isShowCorrect: false,
      isShowError: false,
      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.startProcess()
      this.status = 3
      // this.timing()
    },

    startProcess () {
      this.isShowKey = false
      const all = [1, 2, 3]

      const start = parseInt((this.level - 1) / 2) + 1
      const end = start + 2
      const length = this.level + 4
      this.answer = api.randomNum(start, end) // 答案个数
      this.chooseItem = api.randomNum(1, 3) // 答案图形
      const list = all.filter(item => item !== this.chooseItem)
      for (let i = 0; i < length; i++) {
        if (i < this.answer) {
          this.questionList.push(this.chooseItem)
        } else {
          this.questionList.push(api.getRandomArray(list, 1)[0])
        }
      }
      this.questionList = api.shuffle(this.questionList)

      setTimeout(() => {
        this.isShowKey = true
        this.number++
      }, (6 - parseInt((this.level - 1) / 2)) * 500)
    },

    select (item) {
      if (this.choose.length >= 2 || this.isShowCorrect || this.isShowError) return
      this.choose = this.choose + (item === 10 ? 0 : item).toString()
    },

    confirm () {
      if (!this.choose || this.isShowCorrect || this.isShowError) return
      if (Number(this.choose) === this.answer) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }

      setTimeout(() => {
        this.isShowCorrect = false
        this.isShowError = false

        if (this.number < 3) {
          this.questionList = []
          this.answer = 0
          this.choose = ''
          this.startProcess()
        } else {
          this.submit()
        }
      }, 800)
    },

    stop () {
      if (!this.isShowKey || this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.store = this.succesNum ? this.succesNum * 30 + 10 : 0
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.isStop = false
      this.answer = 0
      this.choose = ''
      this.chooseItem = 0
      this.questionList = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
@import './style.scss';
</style>