<template>
  <div class="game109-page">
    <div class="page-bg"></div>
    <settingPageLib @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、通过本训练的教学，提高训练者如何分类垃圾的认知。</p>
        <p class="synopsis-content">2、请点击垃圾桶进行垃圾分类。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content1" v-if="type === 1">
        <img v-if="showItem" :class="['content-top', itemClass()]" :src="`/static/game_assets/game109/item_${currect.index}.png`" />

        <div class="content-bottom">
          <img class="bottom-item" v-for="item in 3" :key="item + 'img'" :src="`/static/game_assets/game109/img${item}.png`" @click="chooseItem(item)" />
        </div>
      </div>

      <div class="content2" v-if="type === 2">
        <div class="content-top">
          <img :class="['content-item', itemClass(item)]" v-for="item in 6" :key="item + 'item'" :src="`/static/game_assets/game109/item_${item}.png`" @click="chooseItem2(item)" />
        </div>

        <img class="content-bottom" :src="`/static/game_assets/game109/img${index}.png`" />
      </div>

      <div class="btn-group">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 每轮游戏3个回合

export default {
  name: 'game109',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      show: false,
      status: 1,
      isStop: false,
      showItem: true,
      canClick: true,

      type: 1,
      index: 0,
      currect: {},
      choose: 0,
      questions: [],
      answer: [],

      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    itemClass (item) {
      if (item) {
        if (this.answer.includes(item)) return 'rotate-item' + item
        return ''
      }
      if (!this.choose) return ''
      return 'rotate-item' + this.choose
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.status = 3
      const list = [
        {
          index: 1,
          answer: 3
        },
        {
          index: 2,
          answer: 2
        },
        {
          index: 3,
          answer: 3
        },
        {
          index: 4,
          answer: 1
        },
        {
          index: 5,
          answer: 1
        },
        {
          index: 6,
          answer: 2
        }
      ]
      this.questions = api.shuffle(api.getRandomArray(list, 2).concat(list))
      this.index = api.randomNum(1, 3)
      this.startProcess()
      // this.timing()
    },

    // 开始流程
    startProcess () {
      this.currect = this.questions[this.number]
      this.number++
    },

    playAudio (item) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve(true)
        }, item * 1000 + 1500)
      })
    },

    chooseItem (item) {
      if (!this.canClick) return
      this.choose = item

      this.canClick = false
      if (this.choose === this.currect.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      setTimeout(() => {
        this.showItem = false
      }, 1500)

      setTimeout(() => {
        this.canClick = true
        this.showItem = true
        if (this.number < 8) {
          this.choose = 0
          this.startProcess()
        } else {
          this.type = 2
        }
      }, 1700)
    },

    chooseItem2 (item) {
      if (!this.canClick) return
      this.answer.push(item)
      this.canClick = false

      setTimeout(() => {
        this.canClick = true
        if (this.answer.length >= 2) {
          let list = []
          if (this.index === 1) {
            list = [4, 5]
          } else if (this.index === 2) {
            list = [2, 6]
          } else if (this.index === 3) {
            list = [1, 3]
          }

          this.answer.forEach(item => {
            if (list.includes(item)) {
              this.succesNum++
            } else {
              this.errorNum++
            }
          })
          this.submit()
        }
      }, 1700)
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.store = 10 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.choose = 0
      this.type = 1
      this.index = 0
      this.answer = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game109-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game109/bg.png');
  }

  .game-synopsis {
    .synopsis-content {
      padding-top: 20px * 0.7;
      margin: 0;
      font-size: 30px * 0.7;
      line-height: 42px * 0.7;
      font-weight: 400;
      color: #a83a01;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .content1 {
      position: relative;
      width: 1096px * 0.7;
      height: 800px * 0.7;
      margin-bottom: 120px * 0.7;

      .content-top {
        position: absolute;
        top: 0;
        left: 398px * 0.7;
        width: 300px * 0.7;
        height: 300px * 0.7;
        transition: All 1.5s ease-in-out;
        z-index: 1;
      }

      .rotate-item1 {
        transform: translate3d(-410px * 0.7, 270px * 0.7, 0) scale(0);
      }

      .rotate-item2 {
        transform: translate3d(0px * 0.7, 270px * 0.7, 0) scale(0);
      }

      .rotate-item3 {
        transform: translate3d(410px * 0.7, 270px * 0.7, 0) scale(0);
      }

      .content-bottom {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 436px * 0.7;
        display: flex;
        justify-content: space-between;

        .bottom-item {
          width: 276px * 0.7;
          height: 436px * 0.7;
          cursor: pointer;
        }
      }
    }

    .content2 {
      position: relative;
      width: 1590px * 0.7;
      height: 625px * 0.7;
      margin-top: 280px * 0.7;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .content-top {
        width: 100%;
        display: flex;
        justify-content: space-between;

        .content-item {
          width: 240px * 0.7;
          height: 240px * 0.7;
          cursor: pointer;
          transition: All 1.5s ease-in-out;
          z-index: 1;
        }

        .rotate-item1 {
          transform: translate3d(1390px * 0.7, 283px * 0.7, 0) scale(0);
        }

        .rotate-item2 {
          transform: translate3d(1120px * 0.7, 283px * 0.7, 0) scale(0);
        }

        .rotate-item3 {
          transform: translate3d(850px * 0.7, 283px * 0.7, 0) scale(0);
        }

        .rotate-item4 {
          transform: translate3d(590px * 0.7, 283px * 0.7, 0) scale(0);
        }

        .rotate-item5 {
          transform: translate3d(320px * 0.7, 283px * 0.7, 0) scale(0);
        }

        .rotate-item6 {
          transform: translate3d(50px * 0.7, 283px * 0.7, 0) scale(0);
        }
      }

      .content-bottom {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 157px * 0.7;
        height: 251px * 0.7;
      }
    }

    .btn-group {
      position: absolute;
      bottom: 82px * 0.7;
      width: 1672px * 0.7;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px * 0.7;
        height: 84px * 0.7;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px * 0.7;
          height: 84px * 0.7;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px * 0.7 0 21px * 0.7 0;
          font-size: 38px * 0.7;
          line-height: 53px * 0.7;
          text-align: center;
          color: #a83a01;
        }
      }
    }
  }
}
</style>