<template>
  <div class="game11-page">
    <div class="page-bg"></div>
    <settingPageLib title="视觉广度-方式一" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、注意的广度又称注意的范围，是指在同一时间内一个人能看清楚地把握注意对象的数量。</p>
        <p class="synopsis-content">2、本组训练将有助于扩大您的注意范围，进而有助于提高学习和工作的效率。</p>
        <p class="synopsis-content">3、显示屏上将快速出现一些不同形状的图形或字母，请注意看，然后说出其中某个图形的个数。</p>
        <p class="synopsis-content">4、训练等级：依据注意的数量和目标呈现的时间长短分为六个等级。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <template v-if="isShowKey">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="title">
          <img class="title-bg" src="/static/game_assets/game11/title_bg.png" />
          <div class="title-text">
            <span>您刚才看到了几个"</span>
            <img :src="`/static/game_assets/game11/item_${chooseItem}.png`" />
            <span>"图形？</span>
          </div>
        </div>

        <div class="content">
          <div class="content-input">
            <img class="img" src="/static/game_assets/game35/input.png" />
            <img class="clear" src="/static/game_assets/game35/clear.png" @click="choose = ''" />
            <span class="number">{{ choose }}</span>
          </div>
          <div class="content-main">
            <img v-for="(item,index) in selectList" :key="index+'-'" class="content-item" :src="(item.type ? item.is : item.un)" @click="select(item,index)" />
          </div>
        </div>

        <div class="footer">
          <img class="img" src="/static/game_assets/game35/confirm.png" @click="confirm" />
        </div>
      </template>

      <template v-else>
        <div class="content-position">
          <img :class="['position-img', 'img-' + index]" v-for="(item, index) in questionList" :key="index + 'img'" :src="`/static/game_assets/game11/item_${item}.png`">
        </div>
      </template>
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
// 游戏分为6个等级，每轮游戏3个回合
// 1级：1-3个图形 5个选项 1.5s
// 2级：1-3 6 1.5s
// 3级：2-4 7 1s
// 4级：2-4 8 1s
// 5级：3-5 9 0.5s
// 6级：3-5 10 0.5s

export default {
  name: 'game10',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      selectList: [],
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowKey: false,
      questionList: [],
      chooseItem: 0,
      answer: 0,
      choose: '',
      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },
  mounted () {
    this.timing()
    this.img()
  },
  methods: {
    img () {
      let arr = []
      for (let i = 0; i < 10; i++) {
        arr.push({
          "un": "/static/game_assets/game10/images/icon0" + i + ".png",
          "is": "/static/game_assets/game10/images/icon1" + i + ".png",
          "type": false
        })
      }
      this.selectList = arr
    },

    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.startProcess()
      this.status = 3
    },

    startProcess () {
      this.isShowKey = false
      const all = [1, 2, 3]

      const start = parseInt((this.level - 1) / 2) + 1
      const end = start + 2
      const length = this.level + 4
      this.answer = api.randomNum(start, end) // 答案个数
      this.chooseItem = api.randomNum(1, 3) // 答案图形
      const list = all.filter(item => item !== this.chooseItem)
      for (let i = 0; i < length; i++) {
        if (i < this.answer) {
          this.questionList.push(this.chooseItem)
        } else {
          this.questionList.push(api.getRandomArray(list, 1)[0])
        }
      }
      this.questionList = api.shuffle(this.questionList)

      setTimeout(() => {
        this.isShowKey = true
        this.number++
      }, (6 - parseInt((this.level - 1) / 2)) * 500)
    },

    select (item, index) {
      console.log(item)
      if (this.choose.length >= 2) return
      item.type = true
      setTimeout(() => {
        item.type = false

      }, 500)
      this.choose = this.choose + (index === 10 ? 0 : index).toString()
    },

    confirm () {
      if (!this.choose) {
        return false
      }
      if (Number(this.choose) === this.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      if (this.number < 3) {
        this.questionList = []
        this.answer = 0
        this.choose = ''
        this.startProcess()
      } else {
        this.submit()
      }
    },

    stop () {
      if (!this.isShowKey) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.store = this.succesNum > 0 ? (this.succesNum === 1 ? 33 : (this.succesNum - 1) * 33 + 34) : 0
      this.infos[0].value = this.level
      this.infos[1].value = api.getTime(this.second)
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.isStop = false
      this.answer = 0
      this.choose = ''
      this.chooseItem = 0
      this.questionList = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game11-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game11/bg.png');
  }

  .game-synopsis {
    width: 1200px * 0.7;
    height: 580px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border: 2px * 0.7 solid #7bbd41;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-weight: 600;
      font-size: 2rem;
      line-height: 3rem;
      color: #5e381f;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 2rem;
      line-height: 3rem;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .img1 {
      position: absolute;
      top: 55px * 0.7;
      left: 35px * 0.7;
      width: 270px * 0.7;
      height: 115px * 0.7;
      cursor: pointer;
    }

    .title {
      position: relative;
      padding: 55px * 0.7 0;
      width: 975px * 0.7;

      .title-bg {
        position: absolute;
        top: 55px * 0.7;
        left: 0;
        width: 865px * 0.7;
      }

      .title-text {
        position: relative;
        padding: 74px * 0.7 0 74px * 0.7 85px * 0.7;
        font-size: 3rem;
        line-height: 4rem;
        font-weight: 500;
        color: #5e381f;

        img {
          width: 73px * 0.7;
        }
      }
    }

    .content {
      position: relative;
      width: 1064px * 0.7;
      height: 566px * 0.7;
      padding-top: 65px * 0.7;
      display: flex;
      flex-direction: column;
      align-items: center;

      .content-input {
        position: relative;
        width: 312px * 0.7;
        height: 128px * 0.7;

        .img {
          position: absolute;
          top: 0;
          left: 0;
          width: 313px * 0.7;
          height: 129px * 0.7;
        }

        .clear {
          position: absolute;
          top: 29px * 0.7;
          right: 24px * 0.7;
          width: 57px * 0.7;
          height: 58px * 0.7;
          cursor: pointer;
        }

        .number {
          position: absolute;
          top: 20px * 0.7;
          left: 80px * 0.7;
          width: 145px * 0.7;
          height: 74px * 0.7;
          font-size: 3rem;
          line-height: 4rem;
          text-align: center;
          font-family: Impact;
          color: #fff;
        }
      }

      .content-main {
        height: 355px * 0.7;
        padding-top: 55px * 0.7;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;

        .content-item {
          width: 180px * 0.7;
          height: 127px * 0.7;
          cursor: pointer;
        }
      }
    }

    .footer {
      position: relative;
      width: 312px * 0.7;
      padding-bottom: 64px * 0.7;

      .img {
        width: 312px * 0.7;
        cursor: pointer;
      }
    }

    .content-position {
      position: relative;
      width: 100%;
      height: 900px * 0.7;

      .position-img {
        position: absolute;
        width: 105px * 0.7;
        height: 105px * 0.7;
      }

      .img-1 {
        top: 400px * 0.7;
        left: 100px * 0.7;
      }

      .img-2 {
        top: 800px * 0.7;
        left: 1400px * 0.7;
      }

      .img-3 {
        top: 150px * 0.7;
        left: 400px * 0.7;
      }

      .img-4 {
        top: 80px * 0.7;
        left: 1000px * 0.7;
      }

      .img-5 {
        top: 10px * 0.7;
        left: 1500px * 0.7;
      }

      .img-6 {
        top: 500px * 0.7;
        left: 600px * 0.7;
      }

      .img-7 {
        top: 750px * 0.7;
        left: 300px * 0.7;
      }

      .img-8 {
        top: 600px * 0.7;
        left: 1700px * 0.7;
      }

      .img-9 {
        top: 800px * 0.7;
        left: 680px * 0.7;
      }

      .img-10 {
        top: 450px * 0.7;
        left: 850px * 0.7;
      }

      .img-11 {
        top: 320px * 0.7;
        left: 1270px * 0.7;
      }

      .img-0 {
        top: 280px * 0.7;
        left: 1700px * 0.7;
      }
    }
  }
}
</style>