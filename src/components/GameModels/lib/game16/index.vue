<template>
  <div class="game17-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="找字训练-混合" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本组练习是在键盘上迅速找到目标数字或字母，用以训练选择性注意和手眼协调。</p>
        <p class="synopsis-content">2、请按照屏幕上出现的数字或字母在键盘上按下相同的数字或字母。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">

      <div class="content">
        <img class="bg" src="/static/game_assets/game16/btn_bg.png">
        <p class="text">{{currentText}}</p>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
      </div>
    </div>

    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'

export default {
  name: 'game16',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/error_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      questions: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
      currentText: '',
      isCorrect: true,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.getText()
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    getText () {
      const that = this
      window.onkeypress = function (e) {
        if (that.isStop) return
        const key = e.key.toUpperCase()
        if (key === that.currentText) {
          that.isCorrect ? that.succesNum++ : (that.isCorrect = true)
          that.startProcess()
        } else {
          that.errorNum++
          that.isCorrect = false
          that.play()
        }
      }
    },

    play () {
      this.$refs.music.play()
    },

    pause () {
      this.$refs.music.pause()
    },

    start () {
      this.status = 3
      // this.timing()
      this.questions = api.shuffle(this.questions)
      this.startProcess()
    },

    startProcess () {
      this.currentText = this.questions[this.number]
      this.number++
      if (this.number > this.questions.length) {
        this.pause()
        this.isStop = true
        this.store = parseInt(100 / (this.succesNum + this.errorNum) * this.succesNum)
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: '',
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    stop () {
      this.pause()
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.currentText = ''
      this.isCorrect = true
      this.isStop = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game17-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game12/bg.png');
  }

  .game-synopsis {
    width: 787px * 0.7;
    height: 484px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border: 2px * 0.7 solid #7bbd41;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    padding-top: 130px * 0.7;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .content {
      position: relative;
      height: 751px * 0.7;
      width: 751px * 0.7;

      .bg {
        position: absolute;
        top: 0;
        left: 0;
        height: 751px * 0.7;
        width: 751px * 0.7;
      }

      .text {
        position: relative;
        margin: 0;
        font-size: 371px * 0.7;
        line-height: 751px * 0.7;
        text-align: center;
        color: #93621e;
        font-family: Impact;
        user-select: none;
      }
    }

    .footer {
      position: relative;
      display: flex;
      justify-content: space-between;
      width: 1509px * 0.7;
      padding-bottom: 47px * 0.7;
      margin: 0 auto;

      .left {
        width: 566px * 0.7;
        display: inline-flex;
        justify-content: space-between;
      }

      .right {
        width: 564px * 0.7;
        display: inline-flex;
        justify-content: space-between;
      }

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img2 {
        width: 268px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img3 {
        width: 267px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }
}
</style>