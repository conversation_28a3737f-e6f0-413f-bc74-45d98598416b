<template>
  <div class="game64-page">
    <div class="page-bg"></div>
    <settingPageLib title="语言排序——连句成段" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">语言排序-连词成句</p>
        <div class="synopsis-content">
          <p>1、将呈现的词按要求的正确顺序拖到相应位置</p>
        </div>
      </div>
    </settingPageLib>
    <div class="page-content" v-if="status == 3">
      <div class="answer">
        <div class="answer-label">{{title}}</div>
        <div class="answer-value">
          <template v-for="(item,index) of question" :key="index">
            <div class="_item">
              <div v-if="!item.isMove" class="_item_contanier" :draggable="true" @dragstart="(e)=>{dragstart(item,e)}" @dragend="(e)=>{dragend(item,e)}" @drag="(e)=>{drag(item,e)}">
                {{item.label}}
              </div>
              <div v-else-if="item.isMove" class="_item_contanier isMove" :draggable="false">
                {{item.label}}
              </div>
            </div>

          </template>
        </div>
        <div class="answer-value">
          <template v-for="(item,index) of move" :key="index">
            <div class="move_item" @dragenter="(e)=>{dragenter(item,e)}" @dragover="(e)=>{dragover(item,e)}" @dragleave="(e)=>{dragleave(item,e)}" @drop="(e)=>{drop(item,e)}">
              {{item.label}}
            </div>
          </template>
        </div>
      </div>
      <div class="_actions">
        <img src="/static/game_assets/game64/icon2.png" class="btn" alt="" @click="stop">
        <img src="/static/game_assets/game65/cx.png" class="btn" alt="" @click="undo" v-if="sartArr.length > 0">
      </div>
      <div class="check_answer" v-if="isSelect">
        <img class="check_answer_icon" src="/static/game_assets/game78/right.png" v-if="isAnswer == 0" alt=""></img>
        <img class="check_answer_icon" src="/static/game_assets/game78/false.png" v-if="isAnswer == 1" alt=""></img>
      </div>
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again">
    </resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
export default {
  name: 'game65',
  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm
  },
  data () {
    return {
      formData: {
        title: {
          1: '把下面的词语组成句子',
          2: '按照顺序从小到大把下面的词语排列好',
          3: '选择范围从小到大,把以下词语排列成正确顺序',
        },
        question: {
          1: [
            { id: "1", label: "可爱" },
            { id: "2", label: "小兔子" },
            { id: "3", label: "真" }
          ],
          2: [
            { id: "1", label: "爷爷" },
            { id: "2", label: "儿子" },
            { id: "3", label: "爸爸" }
          ],
          3: [
            { id: "1", label: "铅笔" },
            { id: "2", label: "彩色铅笔" },
            { id: "3", label: "笔" }
          ]
        },
        answer: {
          1: "2,3,1",
          2: "2,3,1",
          3: "2,1,3"
        },
      },
      total: 0,
      status: 1,
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ],
      level: 1,
      unit: 1,
      title: '',
      question: [],
      move: [],
      oldData: {},
      newData: {},
      answer: null,
      number: 0, //得分
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      params: {},
      isStop: false,
      show: false,
      isSelect: false,
      isAnswer: -1,
      startData: {},//拖拽开始
      endData: {},//拖拽结束
      sartArr: [],//已经拖动过
    }
  },
  mounted () {
    this.timing()
  },
  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },
    start () {
      this.level = Number(this.info.level) || 1
      this.startProcess()
      this.status = 3
      // this.timing()
    },
    startProcess () {
      const { title, question, answer } = this.formData
      let unit = this.unit
      this.title = title[unit]
      this.question = question[unit]
      this.answer = answer[unit]
      this.total = Object.keys(question).length
      for (let item of this.question) {
        item.isMove = false
      }
      for (let i = 0; i < this.question.length; i++) {
        this.move.push({ id: i + 1, label: ' ' })
      }

    },

    dragstart (item, e) {
      // 开始拖拽
      this.startData = item
    },
    drag (item, e) {
      // 拖拽中
    },
    // 拖拽结束
    dragend (item, e) {
    },
    // 拖放进入目标元素
    dragenter (item, e) {
      e.preventDefault()
      this.endData = item

    },
    dragover (item, e) { //在拖放目标元素内移动
      e.preventDefault()

    },
    dragleave (item, e) { // 拖放离开目标元素
      this.endData = {}

    },
    drop (item, e) {
      // 将拖放元素放到目标元素中
      event.preventDefault()

      this.endData = item
      let sartIndex = this.question.indexOf(this.startData)
      let endIndex = this.move.indexOf(this.endData)

      if (this.sartArr.indexOf(endIndex) == -1) {
        this.move[endIndex] = this.question[sartIndex]
        this.question[sartIndex].isMove = true
        this.sartArr.push(endIndex)
      }
      if (this.sartArr.length == 3) { // 完成
        this.next()
      }
    },
    undo () {
      let i = this.sartArr.length - 1
      let moveIndex = this.sartArr[i]
      let moveLabel = this.move[moveIndex].label
      this.move[moveIndex] = {
        id: '',
        label: " "
      }
      for (let item of this.question) {
        if (item.label == moveLabel) {
          item.isMove = false
          this.sartArr.splice(i, 1)
        }
      }
    },
    next () { // 完成
      let _str = ""
      let _i = 0
      for (let item of this.move) {
        if (_i == this.move.length - 1) {
          _str += item.id
        } else {
          _str += item.id + ','
        }
        _i++
      }

      let unitPoint = Math.floor(100 / this.total)
      this.isSelect = true
      if (_str == this.answer) {
        this.number += (unitPoint - 0)
        this.isAnswer = 0
        this.succesNum++
      } else {
        this.errorNum++
        this.isAnswer = 1
      }
      setTimeout(() => {
        this.isAnswer = -1
        this.isSelect = false
        this.sartArr = []
        if (this.unit < this.total) {

          this.unit++
          this.answer = []
          this.question = []
          this.move = []

          this.startProcess()
        } else {
          this.submit()
        }
      }, 1000)

    },
    submit () {
      if (this.succesNum == this.total) {
        this.number = 100
      }
      this.isStop = true
      this.store = this.number
      this.infos[0].value = this.level
      this.infos[1].value = api.getTime(this.second)
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },
    again () {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.unit = 1
      this.answer = []
      this.question = []
      this.move = []
      this.isAnswer = -1
      this.isSelect = false
      this.isStop = false
      this.start()
      this.timing()
    },
    stop () {
      this.isStop = true
      this.show = true
    },
    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    }
  },
}
</script>

<style lang="scss">
.game64-page {
  width: 100%;
  height: 100%;
  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game64/bg.png');
  }
  .game-synopsis {
    width: 707px * 0.7;
    background: #fffef3;
    border-radius: 36px * 0.7;
    border: 2px * 0.7 solid #014747;
    display: flex;
    flex-direction: column;
    padding: 33px * 0.7 31px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 2rem;
      line-height: 3rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #014747;
    }

    .synopsis-content {
      padding-top: 25px * 0.7;

      p {
        margin: 0;
        font-size: 2rem;
        line-height: 3rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #014747;
      }
    }
  }
  .page-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    .answer {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 1300px * 0.7;
      max-height: 70%;
      &-label {
        font-size: 3rem;
        line-height: 4rem;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #014747;
        padding: 90px * 0.7 0;
      }
      &-value {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        overflow: hidden;
        justify-content: space-around;
        ._item {
          width: 346px * 0.7;
          height: 149px * 0.7;
          margin-bottom: 57px * 0.7;
          &_contanier {
            width: 346px * 0.7;
            height: 149px * 0.7;
            background-image: url('/static/game_assets/game65/unbg.png');
            background-size: 100% 100%;

            font-size: 3rem;
            line-height: 3rem;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #643f0c;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .isMove {
            background-image: url('/static/game_assets/game65/isbg.png');
          }
        }

        .move_item {
          width: 346px * 0.7;
          height: 149px * 0.7;
          background-image: url('/static/game_assets/game65/move.png');
          background-size: 100% 100%;
          font-size: 3rem;
          line-height: 3rem;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #643f0c;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
    ._actions {
      width: 1300px * 0.7;
      position: fixed;
      bottom: 64px * 0.7;
      display: flex;
      justify-content: space-between;
      .btn {
        width: 268px * 0.7;
        height: 115px * 0.7;
        margin-right: 28px * 0.7;
        &:last-child {
          margin-right: 0;
        }
      }
      .stop-btn {
        width: 268px * 0.7;
        height: 115px * 0.7;
      }
    }
    .check_answer {
      width: 100%;
      height: 100%;
      position: fixed;
      z-index: 999;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba(0, 0, 0, 0.3);
      // top: 40%;
      &_icon {
        width: 460px * 0.7;
        height: 460px * 0.7;
      }
    }
  }
}
</style>
