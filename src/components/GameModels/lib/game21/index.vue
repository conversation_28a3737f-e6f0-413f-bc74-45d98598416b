<template>
  <div class="game21-page">
    <div class="page-bg" :class="status === 1 || status === 4 ? 'page-bg1': 'page-bg2'"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="计算转换" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本训练为两列计算题，两列题轮换做。</p>
        <p class="synopsis-content">2、第一列出题规则为:将分别位于上、下的两个数相加。然后把和的个位数写在右上方，而把位于上方的数字原封不动的移到右下方，按此方法做下去。</p>
        <p class="synopsis-content">3、第二列出题规则为:将分别位于上、下的两个数相加，然后把和的个位数写在右下方，而把位于下方的数字原封不动的移到右上方，按此方法做下去。</p>
        <p class="synopsis-content">4、计算机不定时发出'敲钟’的声音，提示立即改做另一列题。</p>
        <p class="synopsis-content">5、要求:当填完数字后请按回车键继续填下一个数字，并且尽可能准确而迅速地完成作业。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="content-top">
          <!-- :disabled="(errorTopIndex !== 0 && errorTopIndex !== item) || (Math.ceil(item / 2) !== Math.ceil(topIndex / 2))" -->
          <a-input :class="['input-item', errorTopIndex.includes(item) ? 'error-item' : '']" :disabled="!type" v-for="item in 30" :key="item + '-top'" v-model="questionTop[item - 1]" @change="setNum($event, 1, item - 1)" @pressEnter="setNum($event, 1, item - 1)"></a-input>
        </div>

        <div class="content-bottom">
          <!-- :disabled="(errorBottomIndex !== 0 && errorBottomIndex !== item) || (Math.ceil(item / 2) !== Math.ceil(bottomIndex / 2))" -->
          <a-input :class="['input-item', errorBottomIndex.includes(item) ? 'error-item' : '']" :disabled="!!type" v-for="item in 30" :key="item + '-bottom'" v-model="questionBottom[item - 1]" @change="setNum($event, 2, item - 1)" @pressEnter="setNum($event, 2, item - 1)"></a-input>
        </div>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <img class="img2" src="/static/game_assets/common/reset.png" @click="reset">
      </div>
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'

export default {
  name: 'game21',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      type: 1, // 1 -- 把个位数写在右上方  0 -- 把个位数写下右下方
      topIndex: 0,
      errorTopIndex: [],
      bottomIndex: 0,
      errorBottomIndex: [],
      questionTop: [],
      questionBottom: [],
      musicUrl: '/static/game_assets/audio/bell.mp3',
      timer: null,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    playAudio () {
      if (this.isStop) return

      this.$refs.music.play()
      this.isPlay = true
    },

    pauseAudio () {
      this.$refs.music.pause()
    },

    handleEnded () {
      this.isPlay = false

      if (this.type) {
        this.type = 0
      } else {
        this.type = 1
      }
      this.setIndex()
    },

    start () {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      this.questionTop.push(api.randomNum(0, 9))
      this.questionTop.push(api.randomNum(0, 9))
      this.questionBottom.push(api.randomNum(0, 9))
      this.questionBottom.push(api.randomNum(0, 9))

      this.setIndex()
      this.setTime()
    },

    setIndex () {
      if (this.type === 1) {
        const list = this.questionTop.filter(item => item)
        // this.topIndex = this.errorTopIndex ? list.length : list.length + 1
        this.topIndex = list.length + 1
        this.bottomIndex = 0
      } else {
        const list = this.questionBottom.filter(item => item)
        // this.bottomIndex = this.errorBottomIndex ? list.length : list.length + 1
        this.bottomIndex = list.length + 1
        this.topIndex = 0
      }
    },

    setTime () {
      if (this.isStop) return

      const time = api.randomNum(15, 17)
      this.timer = setTimeout(() => {
        this.playAudio()
        this.setTime()
      }, time * 1000)
    },

    setNum (e, part, index) {
      const num = Number(e.target.value)

      if (this.type && part === 1) {
        if (!(index % 2) && num === (Number(this.questionTop[index - 1]) + Number(this.questionTop[index - 2])) % 10) {
          this.succesNum++
          // this.errorTopIndex = 0
          this.errorTopIndex = this.errorTopIndex.filter(item => item != index + 1)
          this.setIndex()
        } else if (index % 2 && num === Number(this.questionTop[index - 3])) {
          this.succesNum++
          // this.errorTopIndex = 0
          this.errorTopIndex = this.errorTopIndex.filter(item => item != index + 1)
          this.setIndex()
        } else {
          this.errorNum++
          this.errorTopIndex.push(index + 1)
          // this.errorTopIndex = index + 1
        }
      } else if (!this.type && part === 2) {
        if (index % 2 && num === (Number(this.questionBottom[index - 2]) + Number(this.questionBottom[index - 3])) % 10) {
          this.succesNum++
          // this.errorBottomIndex = 0
          this.errorBottomIndex = this.errorBottomIndex.filter(item => item != index + 1)
          this.setIndex()
        } else if (!(index % 2) && num === Number(this.questionBottom[index - 1])) {
          this.succesNum++
          // this.errorBottomIndex = 0
          this.errorBottomIndex = this.errorBottomIndex.filter(item => item != index + 1)
          this.setIndex()
        } else {
          this.errorNum++
          // this.errorBottomIndex = index + 1
          this.errorBottomIndex.push(index + 1)
        }
      } else {
        this.errorNum++
        if (part === 1) {
          this.errorTopIndex.push(index + 1)
        } else {
          this.errorBottomIndex.push(index + 1)
        }
        // part === 1 ? (this.errorTopIndex = index + 1) : (this.errorBottomIndex = index + 1)
      }

      if (this.questionTop.length + this.questionBottom.length >= 60) {
        this.submit()
      }
    },

    stop () {
      this.isStop = true
      this.show = true
      this.isPlay && this.pauseAudio()
      clearTimeout(this.timer)
    },

    reset () {
      this.isPlay && this.pauseAudio()
      clearTimeout(this.timer)
      this.questionTop.splice(2)
      this.questionBottom.splice(2)
      this.second = 0
      this.errorTopIndex = []
      this.errorBottomIndex = []
      this.errorNum = 0
      this.succesNum = 0
      this.type = 1
      this.setTime()
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()

      if (this.isPlay) {
        this.$refs.music.play()
      } else {
        this.setTime()
      }
    },

    submit () {
      clearTimeout(this.timer)
      this.isStop = true
      this.store = parseInt(100 / (this.succesNum + this.errorNum) * this.succesNum)
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isPlay = false
      this.questionTop = []
      this.questionBottom = []
      this.topIndex = 0
      this.errorTopIndex = 0
      this.bottomIndex = []
      this.errorBottomIndex = []
      this.type = 1
      this.timer = null
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game21-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background-image: url('/static/game_assets/game22/bg_1.png');
  }
  .page-bg2 {
    background: url('/static/game_assets/game21/bg2.png') center center no-repeat;
    background-size: cover;
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 20px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 28px * 0.7;
      line-height: 38px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1362px * 0.7;
      height: 774px * 0.7;
      padding-bottom: 80px * 0.7;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .content-top {
        height: 314px * 0.7;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        align-content: space-between;

        .input-item {
          width: 88px * 0.7;
          height: 147px * 0.7;
          margin-bottom: 10px * 0.7;
          background: transparent;
          border-width: 0.5px * 0.7;
          border-radius: 2px * 0.7;

          font-size: 96px * 0.7;
          font-weight: 600;
          line-height: 147px * 0.7;
          text-align: center;
          font-family: PingFang SC;
          color: #fff;
          user-select: none;

          &:focus {
            border-color: #fec100;
          }

          &:hover {
            border-color: #fec100;
          }
        }

        .error-item {
          color: #ff7b00;
        }
      }

      .content-bottom {
        height: 314px * 0.7;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        align-content: space-between;

        .input-item {
          width: 88px * 0.7;
          height: 147px * 0.7;
          margin-top: 10px * 0.7;
          background: transparent;
          border-width: 0.5px * 0.7;
          border-radius: 2px * 0.7;

          font-size: 96px * 0.7;
          font-weight: 600;
          line-height: 147px * 0.7;
          text-align: center;
          font-family: PingFang SC;
          color: #fff;
          user-select: none;

          &:focus {
            border-color: #fec100;
          }

          &:hover {
            border-color: #fec100;
          }
        }

        .error-item {
          color: #ff7b00;
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 73px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1469px * 0.7;

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img2 {
        width: 268px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }
}
</style>