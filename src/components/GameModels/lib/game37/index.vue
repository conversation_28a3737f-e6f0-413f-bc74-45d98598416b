<template>
  <div class="game37-page">
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>

    <audio ref="music2" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
    <settingPageLib @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、该任务是一项+视空间记忆的双重任务训练。</p>
        <p class="synopsis-content">2、每项任务均会有成对的声音出现，要求您注意听任务中播放的声音，并记住那两个位置的声音相同，声音结束后判断哪两个位置的声音相同。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="top">
        <div class="top-item">
          <span class="text">正确数：</span>
          <span class="success">{{succesNum}}</span>
        </div>

        <div class="top-item">
          <span class="text">错误数：</span>
          <span class="error">{{errorNum}}</span>
        </div>

        <div class="top-item">
          <span class="text">用时{{second}}秒</span>
        </div>
      </div>

      <div class="content">
        <div class="content-item" v-for="item in 4" :key="item">
          <div :class="['item', itemClass(item)]" @click="chooseItem(item)">
            <img class="bg" src="/static/game_assets/game37/item_bg1.png">
          </div>
          <div :class="['item', itemClass(item)]">
            <img class="bg" src="/static/game_assets/game37/item_bg2.png">
            <img class="main" src="/static/game_assets/game37/item.png">
          </div>
        </div>
      </div>

      <div class="btn-group">
        <div class="btn" @click="stop" v-if="answerStatus !== 'play'">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <div class="btn" @click="startGame" v-if="answerStatus === 'playWait' || answerStatus === 'answerWait' || answerStatus === 'continue'">
          <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
          <span class="text">{{ (answerStatus === 'playWait' && '开始记忆') || (answerStatus === 'answerWait' && '开始选择') || (answerStatus === 'continue' && '继续训练') }}</span>
        </div>
      </div>
    </div>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
// 每轮游戏3个回合

export default {
  name: 'game37',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      musicUrl: '',
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      questions: [],
      answerStatus: 'playWait', // play -- 题目 answer -- 回答 playWait -- 播放前等待 answerWait -- 回答前等待 continue -- 继续训练
      index: 0,
      show: false,
      isPlay: false,
      isContinue: false,
      status: 1,
      isStop: false,
      chooseArr: [],
      indexArr: [],
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play (url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$nextTick(() => {
        this.$refs.music.play()
        this.isPlay = true
      })
    },

    playError () {
      this.$refs.music2.play()
    },

    pause () {
      this.$refs.music2.pause()
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnd () {
      this.isPlay = false
      if (this.answerStatus !== 'play') return

      this.index++
      if (this.index <= this.questions.length) {
        this.play(`/static/game_assets/audio/game37/audio_${this.questions[this.index - 1]}.mp3`)
      } else {
        this.index = 0
        this.answerStatus = 'answerWait'
      }
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.status = 3
    },

    startGame () {
      if (this.answerStatus === 'playWait') {
        this.answerStatus = 'play'
        this.startProcess()
      } else if (this.answerStatus === 'answerWait') {
        this.answerStatus = 'answer'
      } else if (this.answerStatus === 'continue') {
        this.isStop = true
        this.answerStatus = ''
        this.index = 0
        this.indexArr = []
        this.chooseArr = []
        setTimeout(() => {
          this.answerStatus = 'playWait'
        }, 2000)
      }
    },

    // 开始流程
    async startProcess () {
      this.number++
      let list = [1, 2, 3, 4, 5]
      this.questions = api.getRandomArray(list, 2)
      this.questions = this.questions.concat(this.questions)
      this.questions = api.shuffle(this.questions)

      this.index++
      this.play(`/static/game_assets/audio/game37/audio_${this.questions[this.index - 1]}.mp3`)
    },

    itemClass (item) {
      if (this.index >= item) {
        return 'rotate-item'
      }

      if (this.indexArr.indexOf(item) !== -1) {
        return 'rotate-item'
      }
    },

    chooseItem (item) {
      if (this.answerStatus !== 'answer') return

      if (!(this.chooseArr.length % 2)) {
        this.chooseArr.push(this.questions[item - 1])
        this.indexArr.push(item)
      } else {
        const choose = this.questions[item - 1]
        const answer = this.chooseArr[this.chooseArr.length - 1]

        if (choose !== answer) {
          this.errorNum++
          this.playError()
        } else {
          this.succesNum++
          this.indexArr.push(item)
          this.chooseArr.push(this.questions[item - 1])
        }

        if (choose === answer && this.chooseArr.length === 4) {
          if (this.number < 3) {
            this.answerStatus = 'continue'
          } else {
            setTimeout(() => {
              this.pause()
              this.store = 100 - this.errorNum * 10
              this.isStop = true
              this.infos[0].value = this.level
              this.infos[1].value = this.second
              this.infos[2].value = this.succesNum
              this.infos[3].value = this.errorNum
              this.status = 4
              this.params = {
                id: this.info.id,
                grade: this.level,
                time: this.second,
                totalPoints: this.store
              }
            }, 2000)
          }
        }
      }
    },

    stop () {
      this.isStop = true
      this.show = true
      if (this.isPlay) {
        this.pause()
        this.isContinue = true
      }
    },

    reset () {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.answerStatus = 'playWait'
      this.index = 0
      this.indexArr = []
      this.chooseArr = []
    },

    // 继续游戏, 继续计时
    cancel () {
      if (this.answerStatus !== 'answer' && this.answerStatus !== 'continue') return
      this.isStop = false
      this.timing()
      if (this.isContinue) {
        this.play()
        this.isContinue = false
      }
    },

    again () {
      this.isStop = false
      this.status = 3
      this.reset()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game37-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game37/bg.png');
  }

  .game-synopsis {
    .synopsis-content {
      padding-top: 20px * 0.7;
      margin: 0;
      font-size: 30px * 0.7;
      line-height: 42px * 0.7;
      font-weight: 400;
      color: #a83a01;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .top {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      padding: 12px * 0.7 25px * 0.7 2px * 0.7 0;

      .top-item {
        margin-left: 10px * 0.7;
        padding: 8px * 0.7 16px * 0.7;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 8px * 0.7;

        .text {
          font-size: 24px * 0.7;
          line-height: 32px * 0.7;
          color: #fff;
          user-select: none;
        }

        .success {
          font-size: 24px * 0.7;
          line-height: 32px * 0.7;
          color: #10c517;
          user-select: none;
        }

        .error {
          font-size: 24px * 0.7;
          line-height: 32px * 0.7;
          color: #ff0a0a;
          user-select: none;
        }
      }
    }

    .content {
      width: 778px * 0.7;
      height: 850px * 0.7;
      padding-bottom: 22px * 0.7;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .content-item > div:first-child {
        z-index: 1;
        backface-visibility: hidden;
      }

      .rotate-item {
        transform: rotateY(180deg);
      }

      .reverse-rotate-item {
        transform: rotateY(0deg);
      }

      .content-item {
        position: relative;
        width: 369px * 0.7;
        height: 374px * 0.7;
        margin-bottom: 40px * 0.7;
        cursor: pointer;

        .item {
          position: absolute;
          top: 0;
          left: 0;
          width: 369px * 0.7;
          height: 374px * 0.7;
          transition: all 2s;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 369px * 0.7;
            height: 374px * 0.7;
          }

          .main {
            position: absolute;
            top: 103px * 0.7;
            left: 48px * 0.7;
            width: 256px * 0.7;
            height: 168px * 0.7;
          }
        }
      }
    }

    .btn-group {
      position: relative;
      width: 1072px * 0.7;
      height: 166px * 0.7;
      display: flex;
      justify-content: space-between;
      padding-bottom: 82px * 0.7;
      margin: 0 auto;

      .btn {
        position: relative;
        width: 295px * 0.7;
        height: 84px * 0.7;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px * 0.7;
          height: 84px * 0.7;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px * 0.7 0 21px * 0.7 0;
          font-size: 38px * 0.7;
          line-height: 53px * 0.7;
          text-align: center;
          color: #a83a01;
          user-select: none;
        }
      }
    }
  }
}
</style>