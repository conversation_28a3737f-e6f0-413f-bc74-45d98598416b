<template>
  <div class="game67-page">
    <div class="page-bg"></div>
    <settingPageLib title="挑战游戏说明" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">数字配对</p>
        <p class="synopsis-content">请在呈现的所有图片中找出其对应的动作顺序，按顺序用眼定位图片</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="title">将下面数字进行配对</div>

      <div class="content">

        <div class="top-list">
          <draggable class="top-item" v-model="ddragItemTop[index]" v-for="(item, index) in downAnswers" :key="item + 'number'" :sort="false" :group="groupTop[index]" @change="putChange(index)">
            <div class="number">{{item}}</div>
            <div class="draggable-item-top" v-for="it in ddragItemTop[index]" :key="it + 'top'">
              <img class="draggable-img" src="/static/game_assets/game141/banana_small_right.png" alt="" v-if="item == it.name">
              <img class="draggable-img" src="/static/game_assets/game141/banana_small_warning.png" alt="" v-else>
              <div class="top-number">{{it.name}}</div>
            </div>
          </draggable>
        </div>
        <div class="bottom-list">
          <draggable class="bottom-item" v-model="dragItemBottom[index]" v-for="(item, index) in correctAnswer" :key="item" :sort="false" :group="group2">
            <div class="draggable-item-bottom" v-for="it in dragItemBottom[index]" :key="it + 'bottom'">
              <div class="bottom-number">{{item}}</div>
            </div>
          </draggable>
        </div>
      </div>

      <div class="footer">
        <div class="left">
          <img class="img3" src="/static/game_assets/common/submit.png" @click="submit" v-if="answerNumber == downAnswers.length">
        </div>
        <div class="right">
          <div class="store right-item">
            题目分数：{{store}}
          </div>
          <div class="number right-item">
            题目数量：{{number}}
          </div>
          <div class="time right-item">
            题目用时：{{timeer}}
          </div>
        </div>
      </div>
    </div>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'

export default {
  name: 'game67',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    draggable
  },

  data () {
    return {
      // 答题页数,设为5页，每页4题，每题5分
      number: 0,
      second: 0,
      timeer: '00:00',
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      dragItemBottom: [],
      ddragItemTop: [[], [], [], []],
      correctAnswer: [],
      downAnswers: [],
      groupTop: [],
      group2: {
        name: 'itemList',
        pull: true,
        put: false
      },
      // 本页答题次数
      answerNumber: 0,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s
      this.timeer = m + ' : ' + s;
      return m + ' : ' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    putChange (index) {
      this.groupTop[index].put = false;
      this.answerNumber += 1;

      if (this.ddragItemTop[index][0].name === this.downAnswers[index]) {
        this.succesNum++
      } else {
        this.errorNum++
      }
      this.store = this.succesNum * 5
    },
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
        this.time()
      }, 1000)
    },

    start () {
      this.startProcess()
      this.status = 3
      // this.timing()
    },
    setTopGroup () {
      this.groupTop = []
      this.downAnswers.forEach((item, index) => {
        this.groupTop.push({
          name: 'itemList',
          pull: false,
          put: true
        })
      })
    },

    startProcess () {

      let randomNum = Math.floor(Math.random() * 5) + 1;
      let list = [1 + randomNum, 2 + randomNum, 3 + randomNum, 4 + randomNum];
      this.downAnswers = [1 + randomNum, 2 + randomNum, 3 + randomNum, 4 + randomNum];
      this.correctAnswer = api.shuffle(list)
      this.correctAnswer.forEach((item, index) => {
        this.dragItemBottom.push([{
          name: item,
          startIndex: index,
        }])
      })

      this.setTopGroup()
      this.answerNumber = 0;
      this.number++
    },

    resetDragItem (index) {
      const item = this.ddragItemTop[index].pop()
      this.dragItemBottom[item.startIndex].push(item)
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    goOn () {
      this.isStop = false
      this.timing()
    },

    submit () {

      if (this.number < 5) {
        this.dragItemBottom = []
        this.ddragItemTop = [[], [], [], []]
        this.startProcess()
      } else {
        this.isStop = true
        this.store = this.succesNum * 5
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: 1,
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    reset () {
      this.dragItemBottom = []
      this.ddragItemTop = [[], [], [], []]
      this.correctAnswer.forEach((item, index) => {
        this.dragItemBottom.push([{
          name: item,
          startIndex: index,
        }])
      })

      if (this.isStop) {
        this.isStop = false
        this.timing()
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.dragItemBottom = []
      this.ddragItemTop = [[], [], [], []]
      this.correctAnswer = []
      this.downAnswers = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game67-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg {
    background-image: url('/static/game_assets/game141/bg.png');
  }
  .page-bg1 {
    background-image: url('/static/game_assets/game1/images/bg1.png');
  }
  .page-bg2 {
    background-image: url('/static/game_assets/game1/images/bg2.png');
  }

  .game-synopsis {
    width: 707px * 0.7;
    height: 444px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border: 2px * 0.7 solid #7bbd41;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-content: center;

    .title {
      position: relative;
      padding: 47px * 0.7 0;
      font-size: 42px * 0.7;
      line-height: 59px * 0.7;
      text-align: center;
      font-weight: 500;
    }

    .content {
      position: relative;
      width: 1866px * 0.7;
      height: 738px * 0.7;
      padding-left: 70px * 0.7;
      padding-right: 77px * 0.7;
      padding-bottom: 5px * 0.7;
      display: flex;
      flex-direction: column;

      .reset-icon {
        position: absolute;
        top: 340px * 0.7;
        width: 67px * 0.7;
        height: 67px * 0.7;
        border-radius: 50%;
        background: #f1ffc7;
        cursor: pointer;
      }

      .reset-icon-1 {
        left: 446px * 0.7;
      }

      .reset-icon-2 {
        left: 897px * 0.7;
      }

      .reset-icon-3 {
        left: 1348px * 0.7;
      }

      .reset-icon-4 {
        right: 0;
      }

      .bottom-list {
        width: 100%;
        display: flex;
        justify-content: space-between;

        .bottom-item {
          position: relative;
          width: 320px * 0.7;
          height: 320px * 0.7;

          .draggable-item-bottom {
            width: 320px * 0.7;
            height: 320px * 0.7;
            background: #fff;
            border-radius: 160px * 0.7;
            justify-content: center;
            align-items: center;
            display: flex;
            background-image: url('/static/game_assets/game141/banana.png');
            background-size: 100% 100%;
            .item-img {
              position: absolute;
              width: 320px * 0.7;
              height: 320px * 0.7;
            }
            .bottom-number {
              width: 100%;
              height: 100%;
              font-size: 151px * 0.7;
              font-family: PingFangSC-Semibold, PingFang SC;
              font-weight: 600;
              color: #000000;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }

      .top-list {
        width: 100%;
        padding-top: 14px * 0.7;
        display: flex;
        justify-content: space-between;

        .top-item {
          position: relative;
          width: 320px * 0.7;
          height: 320px * 0.7;
          border-radius: 160px * 0.7;
          background-color: #fff;
          z-index: 1;
          justify-content: center;
          align-items: center;
          display: flex;
          background-image: url('/static/game_assets/game141/monkey.png');
          background-size: 100% 100%;

          .draggable-item-bottom {
            display: none;
          }

          .number {
            position: absolute;
            width: 100%;
            height: 100%;
            font-size: 151px * 0.7;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #000000;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .draggable-item-top {
            position: relative;
            width: 136px * 0.7;
            height: 136px * 0.7;
            overflow: hidden;
            z-index: 1;
            margin-top: 240px * 0.7;
            margin-left: -240px * 0.7;
            display: flex;
            align-items: center;
            justify-content: center;

            .draggable-img {
              position: absolute;
              width: 136px * 0.7;
              height: 136px * 0.7;
              z-index: -1;
            }
            .top-number {
              font-size: 63px * 0.7;
              font-family: PingFangSC-Semibold, PingFang SC;
              font-weight: 600;
              color: #000000;
              z-index: 1;
            }
          }
        }
      }
    }

    .footer {
      position: relative;
      display: flex;
      justify-content: space-between;
      width: 1509px * 0.7;
      min-height: 162px * 0.7;
      padding-bottom: 47px * 0.7;
      margin: 0 auto;

      .left {
        width: 566px * 0.7;
        min-height: 115px * 0.7;
        display: inline-flex;
        justify-content: space-between;
      }

      .right {
        display: inline-flex;
        justify-content: space-between;

        .right-item {
          width: auto;
          max-height: 76px * 0.7;
          border: 1px * 0.7 solid #fff;
          border-radius: 4px * 0.7;
          color: #fff;
          font-size: 24px * 0.7;
          padding: 21px * 0.7 43px * 0.7;
          margin-left: 20px * 0.7;
        }
        .store {
        }
        .number {
        }
        .time {
        }
      }

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img2 {
        width: 268px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img3 {
        width: 267px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }
}
</style>