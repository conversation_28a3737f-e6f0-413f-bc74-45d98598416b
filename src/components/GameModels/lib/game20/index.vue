<template>
  <div class="game20-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="大小转换" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、屏幕上显示汉字’大’、‘小’，字号不同并随机排列。(逐字出现)练习者按照要求说出当前字。</p>
        <p class="synopsis-content">2、两种朗读方式:①按字义读，即字是’大’读大;字为‘小’读小。②按字号读，即大号字读大;小号字读小。</p>
        <p class="synopsis-content">3、计算机不定时发出‘敲钟’的声音，提示立即转换为另一朗读规则。</p>
        <p class="synopsis-content">4、要求:尽可能准确迅速的读出当前字。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <img class="content-bg" src="/static/game_assets/game21/content_bg.png" />
        <img class="left-icon" src="/static/game_assets/game21/left_icon.png" />
        <img class="right-icon" src="/static/game_assets/game21/right_icon.png" />

        <div class="content-title">
          <img class="title-bg" src="/static/game_assets/game21/title_bg.png" />
          <p class="title-text">{{ type ? '按字义读，即字是’大‘读大；字为‘小’读小。' : '按字号读，即大号字读大；小号字读小。' }}</p>
        </div>

        <div class="content-main">
          <div class="content-item" v-for="(item, index) in questions" :key="index">
            <img v-if="chooseIndex === index" class="item-bg" src="/static/game_assets/game21/btn_choose_bg.png" />
            <img v-else class="item-bg" src="/static/game_assets/game21/btn_bg.png" />
            <span :class="['item-text', item.isBig ? 'text-big' : '']">{{item.text}}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="btn-group">
          <div class="btn" @click="clickBig" @mousedown="isBigClick = true" @mouseup="isBigClick = false">
            <img v-if="isBigClick" class="img" src="/static/game_assets/game21/big_choose.png">
            <img v-else class="img" src="/static/game_assets/game21/big.png">
          </div>

          <div class="btn" @click="clickSmall" @mousedown="isSmallClick = true" @mouseup="isSmallClick = false">
            <img v-if="isSmallClick" class="img" src="/static/game_assets/game21/small_choose.png">
            <img v-else class="img" src="/static/game_assets/game21/small.png">
          </div>
        </div>
      </div>
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'

export default {
  name: 'game20',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      type: 1, // 1 -- 按字义读  0 -- 按大小读
      chooseIndex: null,
      oldChooseIndex: null,
      questions: [],
      musicUrl: '/static/game_assets/audio/bell.mp3',
      isBigClick: false,
      isSmallClick: false,
      timer: null,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    playAudio () {
      if (this.isStop) return

      this.$refs.music.play()
      this.isPlay = true
    },

    pauseAudio () {
      this.$refs.music.pause()
    },

    handleEnded () {
      this.isPlay = false

      if (this.type) {
        this.type = 0
      } else {
        this.type = 1
      }
    },

    start () {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      for (let i = 0; i < 40; i++) {
        const isBig = api.randomNum(0, 1) // 1 -- 大 0 -- 小
        const text = api.randomNum(0, 1) // 1 -- 大 0 -- 小
        this.questions.push({
          text: text ? '大' : '小',
          isBig
        })
      }

      this.setIndex()
      this.setTime()
    },

    setIndex () {
      if (this.chooseIndex !== null) this.oldChooseIndex = this.chooseIndex
      this.chooseIndex = api.randomNum(0, 39)
      if (this.chooseIndex === this.oldChooseIndex) this.chooseIndex = this.chooseIndex === 39 ? (this.chooseIndex - 1) : (this.chooseIndex + 1)
    },

    setTime () {
      if (this.isStop) return

      const time = api.randomNum(5, 8)
      this.timer = setTimeout(() => {
        this.playAudio()
        this.setTime()
      }, time * 1000)
    },

    clickBig () {
      const text = this.questions[this.chooseIndex].text
      const isBig = this.questions[this.chooseIndex].isBig
      if ((this.type && text === '大') || (!this.type && isBig)) {
        this.succesNum++
      } else if ((this.type && text !== '大') || (!this.type && !isBig)) {
        this.errorNum++
      }

      if ((this.succesNum + this.errorNum) >= 20) {
        this.submit()
      } else {
        this.setIndex()
      }
    },

    clickSmall () {
      const text = this.questions[this.chooseIndex].text
      const isBig = this.questions[this.chooseIndex].isBig
      if ((this.type && text === '小') || (!this.type && !isBig)) {
        this.succesNum++
      } else if ((this.type && text !== '小') || (!this.type && isBig)) {
        this.errorNum++
      }

      if ((this.succesNum + this.errorNum) >= 20) {
        this.submit()
      } else {
        this.setIndex()
      }
    },

    stop () {
      this.isStop = true
      this.show = true
      this.isPlay && this.pauseAudio()
      clearTimeout(this.timer)
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()

      if (this.isPlay) {
        this.$refs.music.play()
      } else {
        this.setTime()
      }
    },

    submit () {
      clearTimeout(this.timer)
      this.isStop = true
      this.store = 5 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isPlay = false
      this.questions = []
      this.type = 1
      this.chooseIndex = null
      this.oldChooseIndex = null
      this.timer = null
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game20-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/bg_1.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1920px * 0.7;
      height: 826px * 0.7;
      padding-top: 49px * 0.7;
      padding-left: 250px * 0.7;
      padding-right: 270px * 0.7;

      .content-bg {
        position: absolute;
        top: 0;
        left: 181px * 0.7;
        width: 1524px * 0.7;
        height: 807px * 0.7;
      }

      .left-icon {
        position: absolute;
        top: 54px * 0.7;
        left: 0;
        height: 680px * 0.7;
        z-index: 1;
      }

      .right-icon {
        position: absolute;
        top: 108px * 0.7;
        right: 0;
        width: 298px * 0.7;
        height: 718px * 0.7;
        z-index: 1;
      }

      .content-title {
        position: relative;
        padding-left: 23px * 0.7;
        padding-bottom: 21px * 0.7;
        width: 1354px * 0.7;
        height: 141px * 0.7;

        .title-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 1354px * 0.7;
        }

        .title-text {
          position: relative;
          margin: 0;
          padding-left: 120px * 0.7;
          font-size: 36px * 0.7;
          line-height: 120px * 0.7;
          color: #6f7685;
          user-select: none;
        }
      }

      .content-main {
        display: flex;
        flex-wrap: wrap;

        .content-item {
          position: relative;
          width: 169px * 0.7;
          height: 169px * 0.7;
          margin: -16px * 0.7;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 169px * 0.7;
            height: 169px * 0.7;
          }

          .item-text {
            position: relative;
            display: block;
            font-size: 48px * 0.7;
            font-weight: 500;
            text-align: center;
            line-height: 169px * 0.7;
            color: #353637;
            user-select: none;
          }

          .text-big {
            font-size: 72px * 0.7;
            user-select: none;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 60px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1469px * 0.7;
      padding-right: 38px * 0.7;

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        margin: 9px * 0.7 0;
        cursor: pointer;
      }

      .btn-group {
        width: 588px * 0.7;
        height: 133px * 0.7;
        display: flex;
        justify-content: space-between;

        .img {
          width: 274px * 0.7;
          height: 133px * 0.7;
          cursor: pointer;
        }
      }
    }
  }
}
</style>