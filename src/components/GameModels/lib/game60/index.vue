<template>
  <div class="game60-page">
    <div class="page-bg"></div>
    <settingPageLib title="区分训练" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">请把不同类的词找出来。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <img class="left-icon" src="/static/game_assets/game59/icon.png">

      <div class="content">
        <div class="content-top">
          <img class="top-bg" src="/static/game_assets/game59/question_bg.png">
          <div class="item" v-for="item in question.answerList" :key="item" @click="chooseItem(item)">
            <img class="item-bg" src="/static/game_assets/game59/default_bg.png" />
            <span class="item-text">{{item}}</span>
          </div>
        </div>

        <div class="content-bottom">
          <p class="question">不同的一类是：</p>
          <div class="answer">
            <img class="bg" src="/static/game_assets/game59/default_bg.png" />
            <img v-if="isShowCorrect" class="bg" src="/static/game_assets/game59/correct_bg.png" />
            <img v-if="isShowError" class="bg" src="/static/game_assets/game59/error_bg.png" />

            <span class="text">{{answer}}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="btn-group">
          <img class="img" src="/static/game_assets/common/reset.png" @click="reset">
          <img v-if="answer" class="img" src="/static/game_assets/common/submit.png" @click="submit">
        </div>
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game60',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      isShowCorrect: false,
      isShowError: false,
      questionList: [],
      question: {},
      answer: '',

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      this.questionList = api.shuffle(data.questions)
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      this.question = this.questionList[this.number]
      this.question.answerList = api.shuffle(this.question.answerList)
      this.number++
    },

    chooseItem (item) {
      if (this.isShowCorrect || this.isShowError) return
      this.answer = item
    },

    stop () {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    reset () {
      if (this.isShowCorrect || this.isShowError) return
      this.answer = ''
    },

    submit () {
      if (this.question.answer === this.answer) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }

      if (this.number < 10) {
        setTimeout(() => {
          this.isShowCorrect = false
          this.isShowError = false
          this.question = {}
          this.answer = ''
          this.startProcess()
        }, 800)
      } else {
        setTimeout(() => {
          this.isShowCorrect = false
          this.isShowError = false
          this.isStop = true
          this.store = 10 * this.succesNum
          this.infos[0].value = this.second
          this.infos[1].value = this.succesNum
          this.infos[2].value = this.errorNum
          this.status = 4
          this.params = {
            id: this.info.id,
            grade: '',
            time: this.second,
            totalPoints: this.store
          }
        }, 800)
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isShowCorrect = false
      this.isShowError = false
      this.question = {}
      this.answer = ''
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game60-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game59/bg.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .left-icon {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 956px * 0.7;
    }

    .content {
      position: relative;
      width: 1800px * 0.7;
      height: 748px * 0.7;
      padding-left: 78px * 0.7;
      padding-bottom: 72px * 0.7;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .content-top {
        position: relative;
        width: 1722px * 0.7;
        padding: 80px * 0.7 135px * 0.7 85px * 0.7 294px * 0.7;
        display: flex;
        justify-content: space-between;

        .top-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 1722px * 0.7;
        }

        .item {
          position: relative;
          width: 299px * 0.7;
          height: 127px * 0.7;
          cursor: pointer;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 299px * 0.7;
            height: 127px * 0.7;
          }

          .item-text {
            position: relative;
            display: block;
            padding: 32px * 0.7 0 45px * 0.7 0;
            font-size: 50px * 0.7;
            text-align: center;
            line-height: 50px * 0.7;
            font-weight: 600;
            color: #f8f9ff;
            user-select: none;
          }
        }
      }

      .content-bottom {
        position: relative;
        width: 1290px * 0.7;
        height: 342px * 0.7;
        padding: 45px * 0.7 200px * 0.7;
        margin-left: 300px * 0.7;
        background: #f8f9ff;
        border-radius: 50px * 0.7;

        .question {
          margin: 0;
          font-size: 50px * 0.7;
          line-height: 50px * 0.7;
          font-weight: 600;
          color: #2c2f60;
          user-select: none;
        }

        .answer {
          position: relative;
          width: 299px * 0.7;
          height: 127px * 0.7;
          margin-top: 35px * 0.7;
          margin-left: 274px * 0.7;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 299px * 0.7;
            height: 127px * 0.7;
          }

          .text {
            position: relative;
            display: block;
            padding: 32px * 0.7 0 45px * 0.7 0;
            font-size: 50px * 0.7;
            text-align: center;
            line-height: 50px * 0.7;
            font-weight: 600;
            color: #f8f9ff;
            user-select: none;
          }
        }
      }

      .content-line1 {
        position: absolute;
        bottom: 325px * 0.7;
        right: 518px * 0.7;
        width: 775px * 0.7;
        height: 2px * 0.7;
        border: 1px * 0.7 dashed #4474da;
      }

      .content-line2 {
        position: absolute;
        bottom: 185px * 0.7;
        right: 518px * 0.7;
        width: 775px * 0.7;
        height: 2px * 0.7;
        border: 1px * 0.7 dashed #4474da;
      }
    }

    .footer {
      position: absolute;
      bottom: 75px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1470px * 0.7;
      padding-right: 38px * 0.7;

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }

      .btn-group {
        width: 583px * 0.7;
        height: 115px * 0.7;
        display: flex;
        justify-content: space-between;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
    }
  }
}
</style>