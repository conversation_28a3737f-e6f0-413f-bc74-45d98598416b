<template>
  <div class="game17-page">
    <div class="page-bg"></div>
    <settingPageLib title="不同图片" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、查找不同训练的目的是培养被训练者的观察力和提高对所关注信息的选择能力。</p>
        <p class="synopsis-content">2、训练内容丰富，比对的两组图片几乎一样。但有几处不同，请您尽快把它们找出来。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="title">
        <div class="left">总个数： 3</div>
        <div class="right">
          <template v-for="item in 5">
            <img v-if="answer.length < item" class="img" :key="item + '-'" :src="`/static/game_assets/game17/btn_${item}.png`">
            <img v-else class="img" :key="item+'-'" :src="`/static/game_assets/game17/btn_choose_${item}.png`">
          </template>
        </div>
      </div>

      <div class="content">
        <template v-for="item in imgList">
          <div class="item" v-if="item === questionNum" :key="item + '-1'">
            <img class="img" />
            <div :ref="`ring-${questionNum}-${item}`" :class="`ring-${questionNum}-${item}`" v-for="item in 5" :key="item" @click="chooseItem(item)"></div>
          </div>

          <div :class="['item', 'item-' + questionNum]" v-if="item === questionNum" :key="item + '-2'">
            <img class="img" />
            <div :ref="`ring-${questionNum}-${item}`" :class="`ring-${questionNum}-${item}`" v-for="item in 5" :key="item" @click="chooseItem(item)"></div>
          </div>
        </template>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <img class="img3" src="/static/game_assets/common/submit.png" @click="submit">
      </div>
    </div>

    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'

export default {
  name: 'game17',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      imgList: [1, 2, 3],
      questionNum: 0,
      answer: [],

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      // this.timing()
      this.imgList = api.shuffle(this.imgList)
      this.startProcess()
    },

    startProcess () {
      this.questionNum = this.imgList[this.number]
    },

    chooseItem (item) {
      const list = this.answer.filter(it => it === item)
      if (list.length) return
      this.answer.push(item)
      const refs = this.$refs[`ring-${this.questionNum}-${item}`]
      refs.forEach(ref => {
        ref.style.opacity = 1
      })
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    submit () {
      this.succesNum = this.succesNum + this.answer.length

      this.number++
      if (this.number >= 3) {
        this.isStop = true
        this.errorNum = 15 - this.succesNum
        this.store = parseInt(100 / (this.succesNum + this.errorNum) * this.succesNum)
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: '',
          time: this.second,
          totalPoints: this.store
        }
      } else {
        this.answer = []
        this.startProcess()
      }
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    again () {
      this.number = 0
      this.answer = []
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.questionNum = 0
      this.isStop = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game17-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game12/bg.png');
  }

  .game-synopsis {
    width: 787px * 0.7;
    height: 484px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border: 2px * 0.7 solid #7bbd41;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .title {
      display: flex;
      justify-content: space-between;
      position: relative;
      padding: 40px * 0.7 155px * 0.7 0 190px * 0.7;

      .left {
        padding: 15px * 0.7 35px * 0.7 15px * 0.7 20px * 0.7;
        border: 1px * 0.7 solid #37b982;
        border-radius: 38px * 0.7;

        font-size: 32px * 0.7;
        line-height: 45px * 0.7;
        color: #37b982;
        user-select: none;
      }

      .right {
        display: inline-flex;
        justify-content: space-between;
        width: 338px * 0.7;
        height: 76px * 0.7;
        padding: 7px * 0.7 0;

        .img {
          width: 62px * 0.7;
          height: 62px * 0.7;
        }
      }
    }

    .content {
      width: 1100px * 0.7;
      height: 708px * 0.7;
      padding-top: 44px * 0.7;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;

      .item {
        position: relative;
        width: 512px * 0.7;
        height: 664px * 0.7;

        .img {
          width: 512px * 0.7;
          height: 664px * 0.7;
        }

        .ring-1-1 {
          position: absolute;
          top: 21px * 0.7;
          right: 25px * 0.7;
          width: 45px * 0.7;
          height: 45px * 0.7;
          border: 6px * 0.7 solid #f5ff39;
          border-radius: 50%;
          opacity: 0;
        }

        .ring-1-2 {
          position: absolute;
          top: 75px * 0.7;
          right: 28px * 0.7;
          width: 45px * 0.7;
          height: 45px * 0.7;
          border: 6px * 0.7 solid #f5ff39;
          border-radius: 50%;
          opacity: 0;
        }

        .ring-1-3 {
          position: absolute;
          top: 460px * 0.7;
          right: 70px * 0.7;
          width: 104px * 0.7;
          height: 104px * 0.7;
          border: 6px * 0.7 solid #f5ff39;
          border-radius: 50%;
          opacity: 0;
        }

        .ring-1-4 {
          position: absolute;
          top: 189px * 0.7;
          right: 12px * 0.7;
          width: 76px * 0.7;
          height: 76px * 0.7;
          border: 6px * 0.7 solid #f5ff39;
          border-radius: 50%;
          opacity: 0;
        }

        .ring-1-5 {
          position: absolute;
          top: 537px * 0.7;
          right: 223px * 0.7;
          width: 55px * 0.7;
          height: 55px * 0.7;
          border: 6px * 0.7 solid #f5ff39;
          border-radius: 50%;
          opacity: 0;
        }
      }

      .img {
        width: 366px * 0.7;
        height: 371px * 0.7;
        background: #fff;
        border-radius: 37px * 0.7;
        cursor: pointer;
      }

      .number {
        width: 366px * 0.7;
        height: 348px * 0.7;
        background: #fff;
        border-radius: 37px * 0.7;
      }
    }

    .footer {
      position: relative;
      display: flex;
      justify-content: space-between;
      width: 1509px * 0.7;
      padding-bottom: 47px * 0.7;
      margin: 0 auto;

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img3 {
        width: 267px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }
}
</style>