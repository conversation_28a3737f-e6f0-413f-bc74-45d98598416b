<template>
  <div class="game185-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <audio ref="music2" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPageLib title="听写数字" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、该训练提高您的数字加工能力。</p>
        <p class="synopsis-content">2、听写阿拉伯数字。</p>
        <p class="synopsis-content">3、随着编码转换能力的提高，训练难度将不断提高。</p>
        <p class="synopsis-content">4、该题共有五个等级，每4道题为一等级，4道题全答对将升级，正确不足2道题将降级。</p>
        <p class="synopsis-content">5、同一等级未做完4道题就停止训练时，不记录成绩。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="content-top">
          <img class="top-img" src="/static/game_assets/game185/icon.png" @click="play" />

          <div class="top-num">
            <img class="num-bg" src="/static/game_assets/game185/btn_bg.png" />
            <span class="num">{{answer}}</span>
          </div>

          <img v-if="isShowCorrect" class="top-icon" src="/static/game_assets/game185/correct.png" />
          <img v-if="isShowError" class="top-icon" src="/static/game_assets/game185/error.png" />
        </div>

        <div class="content-bottom">
          <img class="btn" v-for="item in 10" :key="item + 'btn'" :src="`/static/game_assets/game182/btn_${item - 1}.png`" @click="chooseItem(item - 1)" />
        </div>
      </div>

      <div class="footer">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop">

        <img v-if="isShowCorrect || isShowError" class="img" src="/static/game_assets/common/next.png" @click="toNext">
        <img v-else class="img" src="/static/game_assets/common/confirm.png" @click="confirm" />
      </div>
    </div>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
// 20个题 5个等级
// 1级 0-5
// 2级 5-10
// 3级 11-20
// 4级 21-30
// 5级 31-50

export default {
  name: 'game185',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/error_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      level: 1,
      status: 1,
      show: false,
      isStop: false,
      isShowCorrect: false,
      isShowError: false,

      currect: 0,
      index: 0, // 连续正确数
      answer: '',

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play () {
      if (this.isPlay) return
      this.musicUrl = `/static/game_assets/audio/game22/audio_${this.currect}.mp3`
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    playError () {
      this.$refs.music2.play()
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnd () {
      this.isPlay = false
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.startProcess()
      this.status = 3
      // this.timing()
    },

    startProcess () {
      let num = 0
      if (this.level === 1) {
        num = api.randomNum(0, 5)
      } else if (this.level === 2) {
        num = api.randomNum(6, 10)
      } else if (this.level === 3) {
        num = api.randomNum(11, 20)
      } else if (this.level === 4) {
        num = api.randomNum(21, 30)
      } else {
        num = api.randomNum(31, 50)
      }

      if (num === this.currect) {
        this.currect = num + 1
      } else {
        this.currect = num
      }
      this.play()
    },

    chooseItem (item) {
      if (this.answer.length >= 2) return
      this.answer = this.answer + (item === 10 ? 0 : item).toString()
    },

    confirm () {
      this.pause()
      if (!this.answer) {
        this.playError()
        return
      }
      if (this.currect === Number(this.answer)) {
        this.succesNum++
        this.isShowCorrect = true
        if (this.index >= 0) {
          this.index++
        } else {
          this.index = 1
        }
      } else {
        this.errorNum++
        this.isShowError = true
        if (this.index > 0) {
          this.index = -1
        } else {
          this.index--
        }
      }
    },

    toNext () {
      this.number++
      this.isShowCorrect = false
      this.isShowError = false
      this.answer = ''
      if (this.index >= 4 && this.level < 5) {
        this.level++
        this.index = 0
      } else if (this.index <= -2 && this.level > 1) {
        this.level--
        this.index = 0
      }

      if (this.number < 20) {
        this.startProcess()
      } else {
        this.isStop = true
        this.store = 5 * this.succesNum
        this.infos[0].value = this.level
        this.infos[1].value = this.second
        this.infos[2].value = this.succesNum
        this.infos[3].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: this.level,
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    stop () {
      this.isStop = true
      this.show = true
      this.pause()
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
      this.play()
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.isStop = false
      this.store = 0
      this.index = 0
      this.answer = ''
      this.currect = 0
      this.index = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game185-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game185/bg.png');
  }

  .game-synopsis {
    width: 857px * 0.7;
    height: 594px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      width: 1294px * 0.7;
      height: 623px * 0.7;
      margin-left: 70px * 0.7;
      margin-bottom: 130px * 0.7;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;

      .content-top {
        position: relative;
        width: 100%;
        padding-right: 260px * 0.7;
        display: flex;
        justify-content: center;
        align-items: center;

        .top-img {
          width: 170px * 0.7;
          height: 170px * 0.7;
          margin-right: 89px * 0.7;
          cursor: pointer;
        }

        .top-num {
          position: relative;
          width: 178px * 0.7;
          height: 178px * 0.7;

          .num-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 178px * 0.7;
          }

          .num {
            position: relative;
            display: block;
            font-size: 78px * 0.7;
            line-height: 168px * 0.7;
            text-align: center;
            color: #f9a85c;
            font-family: Impact;
          }
        }

        .top-icon {
          position: absolute;
          right: 373px * 0.7;
          top: 27px * 0.7;
          width: 112px * 0.7;
        }
      }

      .content-bottom {
        width: 100%;
        height: 386px * 0.7;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;

        .btn {
          width: 178px * 0.7;
          height: 178px * 0.7;
          margin: 0 40px * 0.7;
          cursor: pointer;
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 88px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1620px * 0.7;

      .footer-right {
        width: 576px * 0.7;
        display: flex;
        justify-content: space-between;
      }

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
      z-index: 99;
    }
  }
}
</style>
