<template>
  <div class="game55-page">
    <div class="page-bg"></div>
    <settingPageLib title="词段补笔" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、此项训练着重加强您的记忆能力。</p>
        <p class="synopsis-content">2、页面会循环播放一组词，25个词为一组，呈现时间为2秒，间隔时间为1s，播放完后，点击继续再次循环播放。</p>
        <p class="synopsis-content">3、点击测验按钮，左边呈现一个词的第一个字，根据您的学习记忆，在右边输入框中填入另一个字，来加强记忆。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content1" v-if="!isShowBtn">
        <img class="content-bg" src="/static/game_assets/game55/content_bg_1.png" />

        <div class="content-text" v-if="current">
          <img class="text-bg" src="/static/game_assets/game55/item_bg_1.png" />
          <span class="text">{{isTest ? current[0] : current}}</span>
        </div>

        <div class="content-text input-item" v-if="isTest">
          <img class="text-bg" src="/static/game_assets/game55/item_bg_2.png" />
          <a-input class="text-input" v-model="text"></a-input>
        </div>
      </div>

      <div class="content2" v-if="isShowBtn">
        <img class="content-bg" src="/static/game_assets/game55/content_bg_2.png" />

        <img class="img" src="/static/game_assets/game55/goon.png" @click="goon">
        <img class="img" src="/static/game_assets/game55/test.png" @click="test">
        <img class="img" src="/static/game_assets/common/quit.png" @click="quit">
      </div>

      <div class="footer" v-if="!isShowBtn">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
        <img class="img" v-if="isTest && text" src="/static/game_assets/common/confirm.png" @click="confirm">
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game55',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isTest: false,
      isShowBtn: false,
      questionList: [],
      current: '',
      text: '',

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      this.questionList = api.shuffle(data.questions)
      this.startProcess()
    },

    startProcess () {
      if (this.number >= 25) {
        this.isShowBtn = true
        return
      }

      this.current = this.questionList[this.number]
      this.number++
      this.setTime()
    },

    setTime () {
      if (this.isStop) return
      setTimeout(() => {
        this.current = ''
      }, 2000)

      setTimeout(() => {
        this.startProcess()
      }, 3000)
    },

    // 重新循环播放
    goon () {
      this.number = 0
      this.isShowBtn = false
      this.startProcess()
    },

    test () {
      this.isStop = false
      // this.timing()
      this.isShowBtn = false
      this.isTest = true
      this.number = 0
      this.questionList = api.shuffle(this.questionList)
      this.current = this.questionList[this.number]
    },

    quit () {
      this.$router.go(-1)
    },

    confirm () {
      console.log(this.number)
      if (this.text === this.current[1]) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      if (this.number >= 24) {
        this.submit()
        return
      }

      this.number++
      this.current = this.questionList[this.number]
      this.text = ''
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      if (!this.isTest) {
        this.setTime()
      } else {
        this.timing()
      }
    },

    submit () {
      this.isStop = true
      this.store = 4 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isTest = false
      this.isShowBtn = false
      this.current = ''
      this.text = ''
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game55-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game126/bg.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content1 {
      position: relative;
      width: 1804px * 0.7;
      height: 968px * 0.7;
      margin-left: 116px * 0.7;
      margin-top: 38px * 0.7;
      padding: 338px * 0.7 605px * 0.7 488px * 0.7 473px * 0.7;
      display: flex;
      justify-content: center;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1804px * 0.7;
      }

      .content-text {
        position: relative;
        width: 335px * 0.7;
        height: 142px * 0.7;

        .text-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 335px * 0.7;
          height: 142px * 0.7;
        }

        .text {
          display: block;
          position: relative;
          padding: 34px * 0.7 0 52px * 0.7 0;
          font-size: 56px * 0.7;
          line-height: 56px * 0.7;
          text-align: center;
          font-weight: 600;
          color: #fff;
          user-select: none;
        }

        .text-input {
          width: 335px * 0.7;
          height: 142px * 0.7;
          padding: 34px * 0.7 30px * 0.7 52px * 0.7 30px * 0.7;
          font-size: 56px * 0.7;
          line-height: 56px * 0.7;
          text-align: center;
          font-weight: 600;
          color: #fff;
          background: transparent;
          border: none;
          box-shadow: none;
          user-select: none;
        }
      }

      .input-item {
        margin-left: 56px * 0.7;
      }
    }

    .content2 {
      position: relative;
      width: 1619px * 0.7;
      height: 1007px * 0.7;
      margin-left: 57px * 0.7;
      margin-bottom: 73px * 0.7;
      padding: 321px * 0.7 714px * 0.7 287px * 0.7 637px * 0.7;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1619px * 0.7;
        height: 1007px * 0.7;
      }

      .img {
        position: relative;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }

    .footer {
      position: absolute;
      bottom: 75px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1470px * 0.7;
      padding-right: 38px * 0.7;

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img1 {
        margin-left: 603px * 0.7;
      }

      .btn-group {
        width: 872px * 0.7;
        height: 115px * 0.7;
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
</style>