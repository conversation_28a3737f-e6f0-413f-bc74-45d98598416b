<template>
  <div class="game161-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="比较数字" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请根据提示选择符合提示的选项；</p>
        <p class="synopsis-content">2、训练结束后会出现成绩单；</p>
        <p class="synopsis-content">3、本训练难度为A（简单）。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />

        <div class="top-text">那个数{{type === 1 ? '最大' : '最小'}}?</div>
      </div>

      <div class="content">
        <div :class="['content-item', currect.question.length === 2 ? 'item1' : 'item2']" v-for="item in currect.question" :key="item + 'item'" @click="chooseItem(item)">
          <template v-if="currect.question.length === 2">
            <img class="item-bg" src="/static/game_assets/game161/content_bg_1.png" />
            <img class="item-bg" v-if="answer === item" src="/static/game_assets/game161/content_bg_2.png" />
          </template>
          <template v-else>
            <img class="item-bg" src="/static/game_assets/game161/content_bg_3.png" />
            <img class="item-bg" v-if="answer === item" src="/static/game_assets/game161/content_bg_4.png" />
          </template>

          <span class="item-text">{{item}}</span>
        </div>
      </div>

      <div class="footer">
        <img class="img" :style="{'opacity': answer ? 1 : 0}" src="/static/game_assets/common/submit.png" @click="submit">

        <div class="footer-right">
          <p class="item">题目分数：{{store}}</p>
          <p class="item">题目数量：{{number}}</p>
          <p class="item">用时：{{time}}</p>
        </div>
      </div>
    </div>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import api from '../../utils/common.js'

export default {
  name: 'game161',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    draggable
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      level: 1,
      status: 1,
      isPlay: false,
      isStop: false,

      type: 1, // 1-最大 2-最小
      answer: 0,
      currect: {
        question: [],
        answer: 0
      },

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.startProcess()
      this.status = 3
      // this.timing()
      this.play()
    },

    startProcess () {
      this.type = api.randomNum(1, 2)
      const length = api.randomNum(2, 3)
      this.currect.question = []
      while (this.currect.question.length < length) {
        const num = api.randomNum(1, 10)
        if (!this.currect.question.includes(num)) {
          this.currect.question.push(num)
        }
      }
      if (length === 2) {
        this.currect.answer = this.type === 1 ? Math.max(this.currect.question[0], this.currect.question[1]) : Math.min(this.currect.question[0], this.currect.question[1])
      } else {
        this.currect.answer = this.type === 1 ? Math.max(this.currect.question[0], this.currect.question[1], this.currect.question[2]) : Math.min(this.currect.question[0], this.currect.question[1], this.currect.question[2])
      }

      this.currect.question = api.shuffle(this.currect.question)
      this.number++
    },

    chooseItem (item) {
      this.answer = item
    },

    submit () {
      if (!this.answer) return
      if (this.answer === this.currect.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }
      this.store = 10 * this.succesNum

      if (this.number < 10) {
        this.answer = 0
        this.startProcess()
      } else {
        this.pause()
        this.isStop = true
        this.infos[0].value = this.level
        this.infos[1].value = this.second
        this.infos[2].value = this.succesNum
        this.infos[3].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: this.level,
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.isStop = false
      this.store = 0
      this.answer = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game161-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game161/bg.png');
  }

  .game-synopsis {
    width: 707px * 0.7;
    height: 444px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border: 2px * 0.7 solid #d88956;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 40px * 0.7;
      left: 0;
      width: 100%;
      padding: 0 54px * 0.7;

      .top-icon {
        position: absolute;
        top: 0;
        left: 50px * 0.7;
        width: 80px * 0.7;
        cursor: pointer;
        z-index: 1;
      }

      .top-text {
        position: relative;
        font-size: 50px * 0.7;
        line-height: 82px * 0.7;
        text-align: center;
        font-weight: 600;
        color: #d88856;
      }
    }

    .content {
      position: relative;
      width: 1428px * 0.7;
      height: 654px * 0.7;
      // margin-bottom: 230px * 0.7;
      display: flex;
      justify-content: center;

      .content-item {
        position: relative;
        height: 614px * 0.7;
        cursor: pointer;

        .item-bg {
          position: absolute;
          height: 614px * 0.7;
        }

        .item-text {
          position: relative;
          display: block;
          font-size: 211px * 0.7;
          font-weight: 500;
          line-height: 614px * 0.7;
          text-align: center;
          color: #d78855;
        }
      }

      .item1 {
        width: 556px * 0.7;
        margin: 0 32px * 0.7;
      }

      .item2 {
        width: 428px * 0.7;
        margin: 0 24px * 0.7;
      }
    }

    .footer {
      position: absolute;
      bottom: 20px * 0.7;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      width: 1620px * 0.7;

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }

      .footer-right {
        width: 690px * 0.7;
        display: inline-flex;
        justify-content: space-between;

        .item {
          margin: 0;
          width: 210px * 0.7;
          height: 76px * 0.7;
          border-radius: 4px * 0.7;
          border: 1px * 0.7 solid #d78855;

          font-size: 24px * 0.7;
          text-align: center;
          line-height: 74px * 0.7;
          color: #d78855;
        }
      }
    }
  }
}
</style>