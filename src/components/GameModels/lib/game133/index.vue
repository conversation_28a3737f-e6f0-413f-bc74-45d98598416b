<template>
  <div class="game133-page">
    <div class="page-bg"></div>
    <settingPageLib title="文字转换" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、{{titleText[mode - 1].first}}数字转为{{titleText[mode - 1].second}}数字。</p>
        <p class="synopsis-content">2、随着编码能力提高，训练难度将不断提高。</p>
        <p class="synopsis-content">3、该题共有三个等级，每做五道题将记录一个等级，在五道题之内全对将升级，正确不足两道题将降级。</p>
      </div>

      <div class="choose-content">
        <div class="choose-item" v-for="item in 6" :key="item + 'choose'">
          <div class="icon" @click="chooseWay(item)">
            <img class="bg" src="/static/game_assets/game32/icon.png" />
            <img class="img" v-if="mode === item" src="/static/game_assets/game32/correct.png" />
          </div>
          <span class="text">方式{{modeText[item - 1]}}</span>
        </div>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="content-top">
          <p class="top-num">{{question}}</p>
          <div class="top-text">
            <div class="text-item" v-for="item in total" :key="item + 'bg'">
              <img class="text-bg" src="/static/game_assets/game133/title_bg.png" />
              <span class="text">{{answer[item - 1]}}</span>
            </div>
          </div>
        </div>

        <div :class="['content-bottom', (mode === 2 || mode === 3) && 'small-bottom']">
          <div class="bottom-item" v-for="item in answerList" :key="item + 'text'" @click="chooseItem(item)">
            <img class="item-bg" src="/static/game_assets/game133/text_bg.png" />
            <img class="item-bg" v-if="answer.includes(item)" src="/static/game_assets/game133/correct_bg.png" />
            <span class="item-text">{{item}}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop" />

        <div class="footer-right">
          <img class="img" src="/static/game_assets/game133/reset.png" @click="reset" />
          <img v-if="answer.length" class="img" src="/static/game_assets/common/confirm.png" @click="confirm" />
        </div>
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 一共10道题
// 1级 个位
// 2级 两位
// 3位 三位

export default {
  name: 'game133',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isShowCorrect: false,
      isShowError: false,
      show: false,

      mode: 1,
      modeText: ['一', '二', '三', '四', '五', '六'],
      titleText: [
        { first: '阿拉伯', second: '简体中文' },
        { first: '简体中文', second: '阿拉伯' },
        { first: '繁体中文', second: '阿拉伯' },
        { first: '简体中文', second: '简体中文' },
        { first: '阿拉伯', second: '繁体中文' },
        { first: '繁体中文', second: '简体中文' }
      ],
      answer: [],
      answerList: [],
      index: 0,
      question: '',
      correct: '',
      total: 0,
      list1: ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '百', '千', '万'],
      list2: ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾', '佰', '仟', '万'],
      list3: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.status = 3
      if (this.mode === 1 || this.mode === 6) {
        this.answerList = this.list1
      } else if (this.mode === 2 || this.mode === 3) {
        this.answerList = this.list3
      } else if (this.mode === 4 || this.mode === 5) {
        this.answerList = this.list2
      }

      this.startProcess()
      // this.timing()
    },

    startProcess () {
      if (this.mode === 1 || this.mode === 5) {
        if (this.level === 1) {
          this.question = api.randomNum(0, 9)
          this.correct = this.mode === 1 ? this.numConvert(this.question, 1) : this.numConvert(this.question, 2)
          this.total = 1
        } else if (this.level === 2) {
          this.question = api.randomNum(20, 99)
          if (!(this.question % 10)) this.question = this.question + 2
          this.correct = this.mode === 1 ? this.numConvert(this.question, 1) : this.numConvert(this.question, 2)
          this.total = 3
        } else {
          this.question = api.randomNum(101, 999)
          if (!(this.question % 10)) this.question = this.question + 2
          this.correct = this.mode === 1 ? this.numConvert(this.question, 1) : this.numConvert(this.question, 2)
          this.total = 5
        }
      }

      if (this.mode === 2 || this.mode === 4) {
        if (this.level === 1) {
          let num = api.randomNum(0, 9)
          if (!(num % 10)) num = num + 1
          this.question = this.numConvert(num, 1)
          this.correct = this.mode === 2 ? num : this.numConvert(num, 2)
          this.total = 1
        } else if (this.level === 2) {
          let num = api.randomNum(20, 99)
          if (!(num % 10)) num = num + 1
          this.question = this.numConvert(num, 1)
          this.correct = this.mode === 2 ? num : this.numConvert(num, 2)
          this.total = this.mode === 2 ? 2 : 3
        } else {
          let num = api.randomNum(100, 999)
          if (!(num % 10)) num = num + 1
          this.question = this.numConvert(num, 1)
          this.correct = this.mode === 2 ? num : this.numConvert(num, 2)
          this.total = this.mode === 2 ? 3 : 5
        }
      }

      if (this.mode === 3 || this.mode === 6) {
        if (this.level === 1) {
          let num = api.randomNum(0, 9)
          if (!(num % 10)) num = num + 3
          this.question = this.numConvert(num, 2)
          this.correct = this.mode === 3 ? num : this.numConvert(num, 1)
          this.total = 1
        } else if (this.level === 2) {
          let num = api.randomNum(20, 99)
          if (!(num % 10)) num = num + 3
          this.question = this.numConvert(num, 2)
          this.correct = this.mode === 3 ? num : this.numConvert(num, 1)
          this.total = this.mode === 3 ? 2 : 3
        } else {
          let num = api.randomNum(100, 999)
          if (!(num % 10)) num = num + 3
          this.question = this.numConvert(num, 2)
          this.correct = this.mode === 3 ? num : this.numConvert(num, 1)
          this.total = this.mode === 3 ? 3 : 5
        }
      }
      this.number++
    },

    numConvert (num, type) {
      let text = ''
      const numArr = num.toString().split('')
      const index1 = this.list3.indexOf(Number(numArr[0]))
      text += type === 1 ? this.list1[index1] : this.list2[index1]
      if (numArr.length > 2) {
        text += type === 1 ? '百' : '佰'
        const index3 = this.list3.indexOf(Number(numArr[1]))
        text += type === 1 ? this.list1[index3] : this.list2[index3]

        text += type === 1 ? '十' : '拾'
        const index2 = this.list3.indexOf(Number(numArr[2]))
        text += type === 1 ? this.list1[index2] : this.list2[index2]
      } else if (numArr.length > 1) {
        text += type === 1 ? '十' : '拾'
        const index2 = this.list3.indexOf(Number(numArr[1]))
        text += type === 1 ? this.list1[index2] : this.list2[index2]
      }

      return text
    },

    chooseWay (item) {
      this.mode = item
    },

    chooseItem (item) {
      if (this.isShowCorrect || this.isShowError) return
      this.answer.push(item)
    },

    reset () {
      if (this.isShowCorrect || this.isShowError) return
      this.answer = []
    },

    confirm () {
      if (this.isShowCorrect || this.isShowError) return
      const answer = this.answer.join('')
      if (answer === this.correct.toString()) {
        this.succesNum++
        this.isShowCorrect = true
        if (this.index >= 0) {
          this.index++
        } else {
          this.index = 1
        }
      } else {
        this.errorNum++
        this.isShowError = true
        if (this.index <= 0) {
          this.index--
        } else {
          this.index = -1
        }
      }

      if (this.index >= 5 && this.level < 3) {
        this.level++
        this.index = 0
      } else if (this.index <= -2 && this.level > 1) {
        this.level--
        this.index = 0
      }

      setTimeout(() => {
        this.isShowCorrect = false
        this.isShowError = false
        this.answer = []

        if (this.number < 10) {
          this.startProcess()
          return
        } else {
          this.submit()
        }
      }, 800)
    },

    stop () {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.store = this.succesNum * 10
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = []
      this.index = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game133-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game133/bg.png');
  }

  .game-synopsis {
    width: 850px * 0.7;
    height: 444px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fffef3;
    border: 2px * 0.7 solid #014747;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .choose-content {
    width: 1150px * 0.7;
    display: flex;
    justify-content: space-between;

    .choose-item {
      display: inline-flex;
      align-content: center;

      .icon {
        position: relative;
        width: 41px * 0.7;
        height: 35px * 0.7;
        display: inline-flex;
        justify-content: center;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 41px * 0.7;
        }

        .img {
          position: absolute;
          width: 41px * 0.7;
          height: 35px * 0.7;
        }
      }

      .text {
        position: relative;
        display: block;
        padding-left: 15px * 0.7;
        font-size: 36px * 0.7;
        line-height: 41px * 0.7;
        font-weight: 600;
        color: #8c4f27;
      }
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1190px * 0.7;
      height: 750px * 0.7;
      margin-bottom: 198px * 0.7;
      display: flex;
      flex-direction: column;
      align-items: center;

      .content-top {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;

        .top-num {
          margin: 0;
          font-size: 113px * 0.7;
          line-height: 158px * 0.7;
          font-weight: 600;
          text-align: center;
          color: #014a5c;
        }

        .top-text {
          display: inline-flex;
          justify-content: center;

          .text-item {
            position: relative;
            width: 241px * 0.7;
            height: 221px * 0.7;
            padding: 0 10px * 0.7;

            .text-bg {
              position: absolute;
              top: 0;
              left: 10px * 0.7;
              width: 221px * 0.7;
            }

            .text {
              display: block;
              position: relative;
              font-size: 85px * 0.7;
              line-height: 221px * 0.7;
              text-align: center;
              font-weight: 800;
              color: #118961;
            }
          }
        }
      }

      .small-bottom {
        width: 850px * 0.7;
      }

      .content-bottom {
        position: relative;
        padding-top: 17px * 0.7;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;

        .bottom-item {
          position: relative;
          width: 170px * 0.7;
          height: 170px * 0.7;
          padding: 20px * 0.7;
          cursor: pointer;

          .item-bg {
            position: absolute;
            top: 20px * 0.7;
            left: 20px * 0.7;
            width: 130px * 0.7;
          }

          .item-text {
            position: relative;
            display: block;
            font-size: 56px * 0.7;
            line-height: 130px * 0.7;
            text-align: center;
            font-weight: 800;
            color: #5e381f;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 50px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1790px * 0.7;

      .footer-right {
        width: 566px * 0.7;
        display: inline-flex;
        justify-content: space-between;
      }

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
      z-index: 99;
    }
  }
}
</style>