<script>
import { postTrainResultComplete } from '/@/api/index';
export default {
  data () {
    return {
      saveRequest: null
    }
  },
  methods: {
    save (params) {
      /*
      id: 记录id
      grade: 游戏等级（level）
      time: 游戏时间
      totalPoints: 得分
      */
      let request = postTrainResultComplete(params)
      this.saveRequest = request
      return request
    },
    nextPage () {
      if (this.saveRequest) {
        this.saveRequest.then((res) => {
          if (res.result && res.result.id) {

            window.location.href = window.location.origin + `/games/${res.result.nextPath}?id=${res.result.id}`
            // this.$router.replace(`/games/${res.result.nextPath}?id=${res.result.id}`)
          } else {
            this.$router.push({
              name: 'dashboard'
            })
            this.$notification['success']({
              message: '训练完成',
              description: '训练游戏已完成'
            })
          }
        }).finally(() => {
        })
      }
    }
  }
}
</script>