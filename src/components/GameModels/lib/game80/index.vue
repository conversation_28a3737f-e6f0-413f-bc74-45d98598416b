<template>
  <div class="game80-page">
    <div class="page-bg"></div>
    <settingPageLib title="语句解释" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、此项训练为语言解解释能力。</p>
        <p class="synopsis-content">2、根据已给出的语句 在输入框中输入自己的理解，每组为10题，每组结束后，可以选择继续做题。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <img class="content-bg" src="/static/game_assets/game80/info_bg.png" />
        <div class="content-top">{{question}}</div>
        <a-textarea class="content-bottom" :rows="3" :maxLength="45" v-model="answer"></a-textarea>
      </div>

      <div class="footer">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="btn-group">
          <img v-if="isShowNext" class="img img1" src="/static/game_assets/common/next.png" @click="next">

          <template v-else>
            <img class="img" src="/static/game_assets/game59/correct.png" @click="handleClick(1)" />
            <img class="img" src="/static/game_assets/game59/partially_correct.png" @click="handleClick(2)" />
            <img class="img" src="/static/game_assets/game59/error.png" @click="handleClick(3)" />
          </template>
        </div>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game80',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      partCorrectNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      isShowNext: false,
      questionList: [],
      question: [],
      answer: '',

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '部分正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      this.questionList = api.shuffle(data.questions)
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      this.question = this.questionList[this.number]
      this.number++
    },

    handleClick (type) {
      if (!this.answer) return
      if (type === 1) {
        this.succesNum++
      } else if (type === 2) {
        this.partCorrectNum++
      } else {
        this.errorNum++
      }

      this.isShowNext = true
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    next () {
      if (this.number < 10) {
        this.answer = ''
        this.startProcess()
        this.isShowNext = false
      } else {
        this.submit()
      }
    },

    submit () {
      this.isStop = true
      this.store = 10 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.partCorrectNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isShowNext = false
      this.question = []
      this.answer = ''
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game80-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game80/bg.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1712px * 0.7;
      height: 896px * 0.7;
      padding-left: 32px * 0.7;
      padding-bottom: 39px * 0.7;

      .content-bg {
        position: absolute;
        top: 0;
        left: 32px * 0.7;
        width: 1680px * 0.7;
      }

      .content-top {
        position: absolute;
        top: 95px * 0.7;
        left: 525px * 0.7;
        right: 313px * 0.7;
        width: 1000px * 0.7;

        font-size: 50px * 0.7;
        line-height: 100px * 0.7;
        text-align: center;
        color: #0e7058;
        user-select: none;
      }

      .content-bottom {
        position: absolute;
        bottom: 170px * 0.7;
        right: 518px * 0.7;
        width: 775px * 0.7;
        height: 310px * 0.7;
        border: none;
        outline: none;
        resize: none;
        background: transparent;

        font-size: 50px * 0.7;
        line-height: 105px * 0.7;
        font-weight: 600;
        color: #1e2257;
        user-select: none;

        &:focus {
          box-shadow: none;
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 75px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1470px * 0.7;
      padding-right: 38px * 0.7;

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img1 {
        margin-left: 603px * 0.7;
      }

      .btn-group {
        width: 872px * 0.7;
        height: 115px * 0.7;
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
</style>