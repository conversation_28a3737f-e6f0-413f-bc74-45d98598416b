<template>
  <div class="game184-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPageLib title="数的组成" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本训练提高您的数字推理能力。</p>
        <p class="synopsis-content">2、题型为填空题。</p>
        <p class="synopsis-content">3、训练共分为5个等级。答题有错误者，可复习答案后再联系1-2次，第3次仍然错误时可终止该题练习而选择新的题目。</p>
        <p class="synopsis-content">4、随着推理能力的提高，训练难度会不断提高。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="top">填空题</div>

      <div class="content" v-if="!isShowCorrect && !isShowError">
        <div class="content-top">
          <div :class="['item', currect.number1 === null && 'empty']">{{currect.number1 !== null ? currect.number1 : answer !== '' ? answer : ''}}</div>
          <span class="text">和</span>
          <div :class="['item', currect.number2 === null && 'empty']">{{currect.number2 !== null ? currect.number2 : answer !== '' ? answer : ''}}</div>
          <span class="text">组成</span>
          <div :class="['item', currect.number3 === null && 'empty']">{{currect.number3 !== null ? currect.number3 : answer !== '' ? answer : ''}}</div>
        </div>

        <div class="content-bottom">
          <div class="left">
            <img v-for="item in 10" :key="item" class="btn" :src="`/static/game_assets/game35/btn_${item}.png`" @click="chooseItem(item)" />
          </div>
          <div class="right">
            <div class="input">
              <img class="img" src="/static/game_assets/game35/input.png" />
              <img class="clear" src="/static/game_assets/game35/clear.png" @click="answer = ''" />
              <span class="number">{{ answer }}</span>
            </div>
            <img class="img" src="/static/game_assets/game35/confirm.png" @click="submit" />
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="footer-left">
          <img class="img" src="/static/game_assets/common/stop.png" @click="stop" />
          <img class="img" src="/static/game_assets/game184/question.png" @click="toQuestion" />
        </div>

        <div class="footer-right">
          <p class="item">题目分数：{{store}}</p>
          <p class="item">题目数量：{{number}}</p>
          <p class="item">用时：{{time}}</p>
        </div>
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 10个题 5个等级 连续答对3题升一级 连续打错2题降一级
// 1级 0-10
// 2级 11-20
// 3级 21-30
// 4级 31-50
// 5级 51-100

export default {
  name: 'game184',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      level: 1,
      status: 1,
      show: false,
      isStop: false,
      isShowCorrect: false,
      isShowError: false,

      index: 0, // 连续正确数
      answer: '',
      currect: {
        number1: null,
        number2: null,
        number3: null,
        answer: null
      },

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play () {
      this.$refs.music.play()
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.startProcess()
      this.status = 3
      // this.timing()
    },

    startProcess () {
      let num1 = 0, num2 = 0
      const type = api.randomNum(1, 3)
      if (this.level === 1) {
        num1 = api.randomNum(1, 10)
        num2 = api.randomNum(1, 10)
      } else if (this.level === 2) {
        num1 = api.randomNum(11, 20)
        num2 = api.randomNum(1, 20)
      } else if (this.level === 3) {
        num1 = api.randomNum(21, 30)
        num2 = api.randomNum(1, 30)
      } else if (this.level === 4) {
        num1 = api.randomNum(31, 50)
        num2 = api.randomNum(1, 50)
      } else {
        num1 = api.randomNum(51, 100)
        num2 = api.randomNum(31, 100)
      }


      if (type === 1) {
        if (num1 >= num2) {
          this.currect.number1 = null
          this.currect.number2 = num1 - num2
          this.currect.number3 = num1
          this.currect.answer = num2
        } else {
          this.currect.number1 = null
          this.currect.number2 = num2 - num1
          this.currect.number3 = num2
          this.currect.answer = num1
        }
      } else if (type === 2) {
        if (num1 >= num2) {
          this.currect.number1 = num1 - num2
          this.currect.number2 = null
          this.currect.number3 = num1
          this.currect.answer = num2
        } else {
          this.currect.number1 = num2 - num1
          this.currect.number2 = null
          this.currect.number3 = num2
          this.currect.answer = num1
        }
      } else {
        if (num1 >= num2) {
          const flag = api.randomNum(0, 1)
          this.currect.number1 = flag ? num1 - num2 : num2
          this.currect.number2 = flag ? num2 : num1 - num2
          this.currect.number3 = null
          this.currect.answer = num1
        } else {
          const flag = api.randomNum(0, 1)
          this.currect.number1 = flag ? num2 - num1 : num1
          this.currect.number2 = flag ? num1 : num2 - num1
          this.currect.number3 = null
          this.currect.answer = num2
        }
      }
    },

    chooseItem (item) {
      if (this.answer.length >= 3) return
      this.answer = this.answer + (item === 10 ? 0 : item).toString()
    },

    toQuestion () {
      this.number++
      this.isShowCorrect = false
      this.isShowError = false
      if (this.index >= 3 && this.level < 5) {
        this.level++
        this.index = 0
      } else if (this.index <= -2 && this.level > 1) {
        this.level--
        this.index = 0
      }
      if (this.number < 10) {
        this.answer = ''
        this.startProcess()
      } else {
        this.isStop = true
        this.infos[0].value = this.level
        this.infos[1].value = this.second
        this.infos[2].value = this.succesNum
        this.infos[3].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: this.level,
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    submit () {
      if (!this.answer) {
        this.play()
        return
      }
      if (Number(this.answer) === this.currect.answer) {
        this.succesNum++
        this.isShowCorrect = true
        if (this.index >= 0) {
          this.index++
        } else {
          this.index = 1
        }
      } else {
        this.errorNum++
        this.isShowError = true
        if (this.index > 0) {
          this.index = -1
        } else {
          this.index--
        }
      }
      this.store = 10 * this.succesNum
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.isStop = false
      this.store = 0
      this.answer = ''
      this.currect = {
        number1: null,
        number2: null,
        number3: null,
        answer: null
      }
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game184-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game184/bg.png');
  }

  .game-synopsis {
    width: 807px * 0.7;
    height: 544px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 20px * 0.7;
      left: 0;
      width: 100%;
      font-size: 50px * 0.7;
      line-height: 82px * 0.7;
      text-align: center;
      font-weight: 600;
      color: #5e381f;
    }

    .content {
      position: relative;
      width: 1434px * 0.7;
      height: 638px * 0.7;
      margin-bottom: 40px * 0.7;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;

      .content-top {
        width: 962px * 0.7;
        height: 88px * 0.7;
        display: flex;
        justify-content: space-between;

        .item {
          width: 217px * 0.7;
          height: 88px * 0.7;
          font-size: 50px * 0.7;
          line-height: 88px * 0.7;
          text-align: center;
          font-weight: 600;
          color: #5e381f;
        }

        .empty {
          border-radius: 4px * 0.7;
          background: #fff1e1;
        }

        .text {
          display: inline-block;
          font-size: 50px * 0.7;
          line-height: 88px * 0.7;
          font-weight: 600;
          color: #5e381f;
        }
      }

      .content-bottom {
        width: 1434px * 0.7;
        height: 309px * 0.7;
        display: flex;

        .left {
          display: flex;
          flex-wrap: wrap;
          align-content: space-between;
          width: 1083px * 0.7;
          height: 309px * 0.7;

          img {
            width: 180px * 0.7;
            height: 127px * 0.7;
            margin: 0 18px * 0.7;
            cursor: pointer;
          }
        }

        .right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: flex-end;
          width: 350px * 0.7;
          height: 309px * 0.7;
          padding-right: 10px * 0.7;

          .input {
            position: relative;
            width: 313px * 0.7;
            height: 129px * 0.7;

            .img {
              position: absolute;
              top: 0;
              left: 0;
              width: 313px * 0.7;
              height: 127px * 0.7;
            }

            .clear {
              position: absolute;
              top: 29px * 0.7;
              right: 24px * 0.7;
              width: 57px * 0.7;
              height: 58px * 0.7;
              cursor: pointer;
            }

            .number {
              position: absolute;
              top: 20px * 0.7;
              left: 80px * 0.7;
              width: 145px * 0.7;
              height: 74px * 0.7;
              font-size: 52px * 0.7;
              line-height: 74px * 0.7;
              text-align: center;
              font-family: Impact;
              color: #fff;
            }
          }

          .img {
            width: 313px * 0.7;
            height: 127px * 0.7;
            cursor: pointer;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 20px * 0.7;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      width: 1620px * 0.7;

      .footer-left {
        width: 585px * 0.7;
        display: inline-flex;
        justify-content: space-between;

        .img {
          height: 115px * 0.7;
          cursor: pointer;
        }
      }

      .footer-right {
        width: 690px * 0.7;
        display: inline-flex;
        justify-content: space-between;

        .item {
          margin: 0;
          width: 210px * 0.7;
          height: 76px * 0.7;
          border-radius: 4px * 0.7;
          border: 1px * 0.7 solid #efd4b7;
          background: #fff1e2;

          font-size: 24px * 0.7;
          text-align: center;
          line-height: 74px * 0.7;
          color: #ac3e0f;
        }
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
      z-index: 99;
    }
  }
}
</style>