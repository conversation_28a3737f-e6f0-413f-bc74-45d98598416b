<template>
  <div class="game135-page">
    <div class="page-bg"></div>
    <settingPageLib title="比较大小" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本训练提高您的数字理解能力。</p>
        <p class="synopsis-content">2、请您判断两个数字的关系，即哪个大、哪个小、还是一样大。</p>
        <p class="synopsis-content">3、本训练共分为3个等级，随着比较判断能力的提高，训练难度易将不断提高。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="content-top">
          <div class="top-left">
            <img class="text-bg" src="/static/game_assets/game133/title_bg.png" />
            <span class="text">{{current[0]}}</span>
          </div>

          <div class="top-middle">
            <img class="text-bg" src="/static/game_assets/game133/title_bg.png" />
            <span class="text">{{choose}}</span>
          </div>

          <div class="top-right">
            <img class="text-bg" src="/static/game_assets/game133/title_bg.png" />
            <span class="text">{{current[1]}}</span>
          </div>
        </div>

        <div class="content-bottom">
          <div class="bottom-item">
            <img class="item-img" v-for="item in current[0]" :key="item + 'img1'" src="/static/game_assets/game135/item.png" />
          </div>

          <div class="bottom-middle">
            <p class="middle-text" @click="chooseItem(1)">=</p>
            <p class="middle-text" @click="chooseItem(2)">&gt;</p>
            <p class="middle-text" @click="chooseItem(3)">&lt;</p>
          </div>

          <div class="bottom-item">
            <img class="item-img" v-for="item in current[1]" :key="item + 'img2'" src="/static/game_assets/game135/item.png" />
          </div>
        </div>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">

        <div class="right">
          <span class="right-item">等级：{{level}}</span>
          <span class="right-item">正确：{{succesNum}}</span>
          <span class="right-item">错误：{{errorNum}}</span>
        </div>
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 一共10道题
// 1级 两位数与一位数相比
// 2级 个位数与个位数相比
// 3级 两位数与两位数相比
// 连续答对3题会升级

export default {
  name: 'game135',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,

      index: 0, // 连续正确数
      choose: '',
      current: [],

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.status = 3
      this.startProcess()
      // this.timing()
    },

    startProcess () {
      const flag = api.randomNum(0, 1)
      if (this.level === 1 && flag) {
        this.current.push(api.randomNum(1, 10))
        this.current.push(api.randomNum(11, 20))
      } else if (this.level === 1 && !flag) {
        this.current.push(api.randomNum(11, 20))
        this.current.push(api.randomNum(1, 10))
      }

      if (this.level === 2) {
        this.current.push(api.randomNum(1, 10))
        this.current.push(api.randomNum(1, 10))
      }

      if (this.level === 3) {
        this.current.push(api.randomNum(11, 20))
        this.current.push(api.randomNum(11, 20))
      }
      this.number++
    },

    chooseItem (type) {
      if (this.isShowCorrect || this.isShowError) return
      this.choose = type === 1 ? '=' : type === 2 ? '>' : '<'
      if ((type === 1 && this.current[0] === this.current[1])
        || (type === 2 && this.current[0] > this.current[1])
        || (type === 3 && this.current[0] < this.current[1])) {
        this.succesNum++
        this.isShowCorrect = true
        this.index++
      } else {
        this.errorNum++
        this.isShowError = true
        this.index = 0
      }

      if (this.index >= 3 && this.level < 3) {
        this.level++
        this.index = 0
      }

      setTimeout(() => {
        this.isShowCorrect = false
        this.isShowError = false
        this.current = []
        this.choose = ''

        if (this.number < 10) {
          this.startProcess()
        } else {
          this.submit()
        }
      }, 800)
    },

    stop () {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.store = 10 * this.succesNum
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.current = []
      this.choose = ''
      this.index = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game135-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game133/bg.png');
  }

  .game-synopsis {
    width: 760px * 0.7;
    height: 500px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fffef3;
    border: 2px * 0.7 solid #014747;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1674px * 0.7;
      height: 702px * 0.7;
      margin-bottom: 158px * 0.7;
      display: flex;
      flex-direction: column;

      .content-top {
        height: 221px * 0.7;
        display: flex;

        .text {
          position: relative;
          display: block;
          font-size: 113px * 0.7;
          line-height: 221px * 0.7;
          text-align: center;
          font-weight: 600;
          color: #8a5c1c;
          font-family: PingFangSC-Semibold, PingFang SC;
        }

        .top-left {
          position: relative;
          flex: 1;
          display: inline-flex;
          justify-content: center;
        }

        .top-middle {
          position: relative;
          width: 221px * 0.7;

          .text {
            line-height: 200px * 0.7;
          }
        }

        .top-right {
          position: relative;
          flex: 1;
          display: inline-flex;
          justify-content: center;
        }

        .text-bg {
          position: absolute;
          top: 0;
          width: 221px * 0.7;
        }
      }

      .content-bottom {
        display: flex;
        height: 100%;
        padding-top: 20px * 0.7;

        .bottom-item {
          flex: 1;
          display: inline-flex;
          flex-wrap: wrap;
          align-content: flex-start;
          justify-content: center;

          .item-img {
            width: 98px * 0.7;
            height: 98px * 0.7;
            margin-top: 30px * 0.7;
            margin-right: 15px * 0.7;
          }
        }

        .bottom-middle {
          width: 221px * 0.7;
          display: inline-flex;
          flex-direction: column;

          .middle-text {
            margin-bottom: 30px * 0.7;
            font-size: 125px * 0.7;
            line-height: 150px * 0.7;
            font-weight: 600;
            text-align: center;
            color: #8a5c1c;
            font-family: PingFangSC-Semibold, PingFang SC;
            cursor: pointer;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 20px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1790px * 0.7;

      .right {
        width: 790px * 0.7;
        padding: 20px * 0.7 0;
        display: inline-flex;
        justify-content: space-between;

        .right-item {
          display: block;
          width: 245px * 0.7;
          height: 75px * 0.7;
          border: 1px * 0.7 solid #42aed7;
          border-radius: 4px * 0.7;
          background: #cbedf4;

          font-size: 24px * 0.7;
          line-height: 70px * 0.7;
          text-align: center;
          color: #42aed7;
        }
      }

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
      z-index: 99;
    }
  }
}
</style>