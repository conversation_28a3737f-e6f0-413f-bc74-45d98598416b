<template>
  <div class="game113-page">
    <audio ref="music" muted controls="controls" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
    <settingPageLib @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">本训练用于提高训练者的各种身体部位认知。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <img class="content-icon" src="/static/game_assets/game113/icon.png" />
        <div class="content-left">
          <div class="left-item" v-for="item in 5" :key="item + 'item'">
            <draggable class="left-item-wapper" v-model="dragItemTop[item-1]" :group="group1" @start="dragStart(item)">
              <img v-for="it in dragItemTop[item-1]" :key="it.name + 'img'" :class="['left-img', 'left-img-' + it.name]" :src="`/static/game_assets/game113/item_${it.name}.png`" />
            </draggable>
          </div>
        </div>

        <div class="content-right">
          <img class="right-img-1" src="/static/game_assets/game113/img_1.png" />
          <img class="right-img-2" src="/static/game_assets/game113/img_2.png" />
          <img class="right-img-3" src="/static/game_assets/game113/img_3.png" />
          <draggable :class="['right-item', 'right-item-' + item]" :style="{'z-index': choose === 1 && item === 1 ? 6 : choose === item ? 4 : item === 2 ? 5 : item === 4 ? 1 : 0}" v-for="item in 5" :key="item + 'item'" v-model="dragItemBottom[item-1]" :group="group2" @add="dragAdd(item)">
            <img :src="`/static/game_assets/game113/item_wapper_${item}.png`" />
            <img v-for="it in dragItemBottom[item-1]" :key="it.name + 'img'" :src="`/static/game_assets/game113/item_${it.name}.png`" />
          </draggable>
        </div>
      </div>

      <div class="footer">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>
      </div>
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'

export default {
  name: 'game113',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    draggable
  },

  data () {
    return {
      musicUrl: '',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,

      choose: 0,
      answer: [],
      dragItemTop: [],
      dragItemBottom: [[], [], [], [], []],
      group1: {
        name: 'itemList',
        pull: true,
        put: false,
        sort: false
      },
      group2: {
        name: 'itemList',
        pull: false,
        put: true,
        sort: false
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      this.startProcess()
      // this.timing()
      this.play('/static/game_assets/audio/game113/title.mp3')
    },

    startProcess () {
      for (let i = 0; i < 5; i++) {
        this.dragItemTop.push([{
          name: i + 1,
          startIndex: i,
        }])
      }
    },

    play (url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    dragStart (item) {
      this.choose = this.dragItemTop[item - 1][0].name
    },

    dragAdd (index) {
      if (this.choose !== index) {
        if (this.dragItemBottom[index - 1].length > 1) {
          const item = this.dragItemBottom[index - 1].filter(item => item.name !== index)[0]
          this.dragItemBottom[index - 1] = this.dragItemBottom[index - 1].filter(item => item.name === index)
          this.dragItemTop[item.startIndex].push(item)
        } else {
          const item = this.dragItemBottom[index - 1].pop()
          this.dragItemTop[item.startIndex].push(item)
        }
        this.errorNum++
      } else {
        this.succesNum++
      }

      this.choose = 0
      const list = this.dragItemBottom.filter(item => item.length)
      if (list.length >= 5) {
        this.submit()
      }
    },

    chooseItem (item) {
      if (!this.number) return

      this.choose = item
      this.isShowCorrect = true
      if (this.choose === this.current.index) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      this.goNext()
    },


    stop () {
      this.isStop = true
      this.show = true
      this.pause()
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
      this.play('/static/game_assets/audio/game113/title.mp3')
    },

    submit () {
      this.pause()
      this.isStop = true
      this.store = 100 - 10 * this.errorNum > 0 ? 100 - 10 * this.errorNum : 0
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.choose = 0
      this.dragItemTop = []
      this.dragItemBottom = [[], [], [], [], []]
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game113-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game113/bg.png');
  }

  .game-synopsis {
    .synopsis-content {
      padding-top: 20px * 0.7;
      margin: 0;
      font-size: 30px * 0.7;
      line-height: 42px * 0.7;
      font-weight: 400;
      color: #a83a01;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    .content-icon {
      position: absolute;
      left: 40px * 0.7;
      bottom: 125px * 0.7;
      width: 158px * 0.7;
    }

    .content {
      position: relative;
      width: 1554px * 0.7;
      height: 883px * 0.7;
      margin-top: 128px * 0.7;
      margin-right: 80px * 0.7;
      display: flex;
      justify-content: center;

      .content-left {
        width: 1026px * 0.7;
        height: 620px * 0.7;
        display: flex;
        flex-wrap: wrap;

        .left-item {
          width: 264px * 0.7;
          height: 264px * 0.7;
          margin-right: 78px * 0.7;
          margin-bottom: 46px * 0.7;
          border: 5px * 0.7 solid #fff;
          background: #8eb5e1;
          display: inline-flex;
          justify-content: center;
          align-items: center;

          .left-item-wapper {
            .left-img {
              cursor: pointer;
            }

            .left-img-1 {
              width: 142px * 0.7;
            }

            .left-img-2 {
              width: 92px * 0.7;
            }

            .left-img-3 {
              width: 63px * 0.7;
            }

            .left-img-4 {
              width: 100px * 0.7;
            }

            .left-img-5 {
              width: 90px * 0.7;
            }
          }
        }
      }

      .content-right {
        position: relative;
        width: 295px * 0.7;
        height: 692px * 0.7;
        margin-top: 190px * 0.7;

        .right-img-1 {
          position: absolute;
          top: 81px * 0.7;
          left: 23px * 0.7;
          width: 249px * 0.7;
          z-index: 4;
        }

        .right-img-2 {
          position: absolute;
          top: 359px * 0.7;
          left: 80px * 0.7;
          width: 143px * 0.7;
        }

        .right-img-3 {
          position: absolute;
          top: 542px * 0.7;
          left: 120px * 0.7;
          width: 83px * 0.7;
        }

        .right-item {
          position: absolute;

          img {
            position: absolute;
            width: 100%;
            height: 100%;
          }
        }

        .right-item-1 {
          top: 0;
          left: 0;
          width: 295px * 0.7;
          height: 289px * 0.7;
        }

        .right-item-2 {
          top: 133px * 0.7;
          left: 55px * 0.7;
          width: 184px * 0.7;
          height: 79px * 0.7;
        }

        .right-item-3 {
          top: 276px * 0.7;
          left: 96px * 0.7;
          width: 110px * 0.7;
          height: 278px * 0.7;
        }

        .right-item-4 {
          top: 280px * 0.7;
          left: 82px * 0.7;
          width: 140px * 0.7;
          height: 130px * 0.7;
        }

        .right-item-5 {
          bottom: 0;
          left: 116px * 0.7;
          width: 93px * 0.7;
          height: 51px * 0.7;
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 82px * 0.7;
      width: 1072px * 0.7;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px * 0.7;
        height: 84px * 0.7;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px * 0.7;
          height: 84px * 0.7;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px * 0.7 0 21px * 0.7 0;
          font-size: 38px * 0.7;
          line-height: 53px * 0.7;
          text-align: center;
          color: #a83a01;
        }
      }
    }
  }
}
</style>