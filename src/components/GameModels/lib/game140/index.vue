<template>
  <div class="game140-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPageLib title="学习数字" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">根据训练提示，进行对应数量物件选择。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="content-left">
          <img class="left-icon" src="/static/game_assets/game140/icon.png" />
          <div :class="['left-text', 'flash-item']">
            <img class="text-bg" src="/static/game_assets/game140/text_bg.png" />
            <img class="item-img" :src="`/static/game_assets/game140/item_${currect.index}_1.png`" />
            <span class="text">x{{currect.answer}}</span>
          </div>
        </div>

        <div class="content-right">
          <img class="right-bg" src="/static/game_assets/game140/content_bg.png" />
          <div class="right-item" v-for="(item, index) in questions" :key="index + 'item'">
            <img :class="['item-img', 'item-img-' + index]" :src="`/static/game_assets/game140/item_${item}_2.png`" />
            <img :class="['item-img', 'item-img-' + index, itemClass(index)]" :src="`/static/game_assets/game140/item_${item}_1.png`" @click="chooseItem(item, index)" />
          </div>
        </div>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'

export default {
  name: 'game140',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      level: 3,
      status: 1,
      isStop: false,
      isCanClick: true,
      isCorrect: true,

      choose: [],
      currect: {
        index: 0,
        answer: 0
      },
      questions: [],

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    itemClass (index) {
      if (this.choose.includes(index)) {
        return 'rotate-img'
      }
    },

    play () {
      this.$refs.music.play()
    },

    pause () {
      this.$refs.music.pause()
    },

    start () {
      this.level = this.info.level || 3
      this.startProcess()
      this.status = 3
      // this.timing()
    },

    startProcess () {
      this.currect.answer = api.randomNum(3, 9)
      const list = [1, 2, 3, 4, 5, 6, 7, 8, 9]
      const type = api.randomNum(2, 4)
      const imgList = api.getRandomArray(list, type)
      this.choose = []
      this.questions = []
      this.isCorrect = true
      for (let i = 0; i < this.currect.answer + type - 1; i++) {
        if (i < this.currect.answer) {
          this.questions.push(imgList[0])
        } else {
          this.questions.push(imgList[this.currect.answer + type - i - 1])
        }
      }
      this.questions = api.shuffle(this.questions)
      this.currect.index = imgList[0]
      this.number++
    },

    chooseItem (item, index) {
      if (!this.isCanClick || this.choose.includes(index)) return
      if (item !== this.currect.index) {
        this.isCorrect = false
        this.play()
      } else {
        this.choose.push(index)
      }

      if (this.choose.length >= this.currect.answer) {
        this.isCanClick = false
        if (this.isCorrect) {
          this.succesNum++
        } else {
          this.errorNum++
        }

        setTimeout(() => {
          this.isCanClick = true
          if (this.number >= 5) {
            this.submit()
          } else {
            this.startProcess()
          }
        }, 2000)
      }
    },

    submit () {
      const num = parseInt(100 / this.succesNum)
      this.store = 20 * this.succesNum

      this.pause()
      this.isStop = true
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game140-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game140/bg.png');
  }

  .setting-page {
    .title {
      color: #fff;
    }
  }

  .game-synopsis {
    width: 707px * 0.7;
    height: 444px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1785px * 0.7;
      height: 1006px * 0.7;
      margin-right: 33px * 0.7;
      margin-top: 22px * 0.7;
      display: flex;

      .content-left {
        position: relative;
        flex: 1;

        .left-icon {
          position: absolute;
          left: 0;
          bottom: 51px * 0.7;
          width: 352px * 0.7;
        }

        .left-text {
          position: absolute;
          left: 170px * 0.7;
          bottom: 364px * 0.7;
          width: 258px * 0.7;
          height: 258px * 0.7;

          .text-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 258px * 0.7;
          }

          .item-img {
            position: absolute;
            top: 73px * 0.7;
            left: 20px * 0.7;
            width: 80px * 0.7;
          }

          .text {
            position: relative;
            display: inline-block;
            padding-top: 60px * 0.7;
            padding-left: 105px * 0.7;
            font-size: 72px * 0.7;
            line-height: 100px * 0.7;
            font-weight: 600;
            color: #000;
          }
        }

        .flash-item {
          animation: flash 2s linear infinite;
        }
      }

      .content-right {
        position: relative;
        width: 1145px * 0.7;
        height: 1006px * 0.7;
        padding: 267px * 0.7 167px * 0.7;

        .right-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 1145px * 0.7;
        }

        .item-img {
          position: absolute;
          width: 160px * 0.7;
          height: 160px * 0.7;
          cursor: pointer;
        }

        .item-img-0 {
          top: 167px * 0.7;
          left: 167px * 0.7;
        }

        .item-img-1 {
          top: 167px * 0.7;
          left: 377px * 0.7;
        }

        .item-img-2 {
          top: 167px * 0.7;
          left: 587px * 0.7;
        }

        .item-img-3 {
          top: 167px * 0.7;
          left: 797px * 0.7;
        }

        .item-img-4 {
          top: 377px * 0.7;
          left: 167px * 0.7;
        }

        .item-img-5 {
          top: 377px * 0.7;
          left: 377px * 0.7;
        }

        .item-img-6 {
          top: 377px * 0.7;
          left: 587px * 0.7;
        }

        .item-img-7 {
          top: 377px * 0.7;
          left: 797px * 0.7;
        }

        .item-img-8 {
          top: 588px * 0.7;
          left: 167px * 0.7;
        }

        .item-img-9 {
          top: 588px * 0.7;
          left: 377px * 0.7;
        }

        .item-img-10 {
          top: 588px * 0.7;
          left: 587px * 0.7;
        }

        .item-img-11 {
          top: 588px * 0.7;
          left: 797px * 0.7;
        }

        .rotate-img {
          animation: rotate 1.5s 0.15s linear infinite;
          animation-iteration-count: 1;
          animation-fill-mode: forwards;
        }
      }
    }
  }
}

@keyframes flash {
  0% {
    transform: scale(1);
  }
  60% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes rotate {
  100% {
    transform: scale(0);
    left: -390px * 0.7;
    top: 420px * 0.7;
  }
}
</style>