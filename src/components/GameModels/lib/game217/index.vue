<template>
  <div class="game217-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="`/static/game_assets/audio/game217/question_${number}.mp3`" type="audio/mpeg" />
    </audio>
    <settingPageLib title="居家运动" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">选择有关居家运动的相关问题</p>
      </div>
    </settingPageLib>

    <div class="game-content" v-if="status === 3">
      <div class="btn-right">
        <div class="icon-img">
          <img src="/static/game_assets/common/clock.png" />
          <span>时间：{{ time }}</span>
        </div>
        <div class="icon-img">
          <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
          <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
          <span>语音读题</span>
        </div>
      </div>

      <div class="top">
        <span class="top-text">{{ currect.question }}</span>
      </div>

      <div :class="[number > 1 ? 'content' : 'content-big']">
        <div class="content-item" v-for="(answer, index) in currect.answerList" :key="answer.value + 'item'" @click="clickItem(answer, index)">
          <img class="item-icon" v-if="isCorrect && chooseItem.index === index" src="/static/game_assets/common/correct.png" />
          <img class="item-icon" v-if="!isCorrect && chooseItem.index === index" src="/static/game_assets/common/error.png" />
          <img class="item-img" :src="answer.img" />
          <span class="item-text">{{ answer.label }}</span>
        </div>
      </div>
    </div>
    <!-- <bgMusic :status="status"></bgMusic> -->
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <div class="game-modal" v-if="show">
      <div class="modal-text">
        <div class="modal-top">
          <img class="item-icon" v-if="isCorrect" src="/static/game_assets/common/correct.png" />
          <img class="item-icon" v-else src="/static/game_assets/common/error.png" />
          <span>{{ isCorrect ? '答对了' : '答错了' }}</span>
        </div>

        <p class="modal-main">{{ chooseItem.content }}</p>

        <div class="modal-btn">
          <img v-if="number < questionList.length" class="btn" src="/static/game_assets/common/next.png" @click="next" />
          <img v-else class="btn" src="/static/game_assets/common/submit.png" @click="submit" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common'
import data from './data/data.json'

export default {
  name: 'game217',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,

      questionList: [],
      currect: {},
      chooseItem: {},
      isCorrect: false,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play () {
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnd () {
      this.isPlay = false
    },

    start () {
      this.status = 3
      // this.timing()

      this.questionList = data.data
      this.startProcess()
    },

    next () {
      this.show = false
      this.startProcess()
    },

    startProcess () {
      this.isCorrect = false
      this.chooseItem = {}
      this.currect = this.questionList[this.number]
      this.currect.answerList = api.shuffle(this.currect.answerList)
      this.number++
      this.play()
    },

    clickItem (answer, index) {
      this.chooseItem = answer
      this.chooseItem.index = index
      if (this.currect.answer === answer.value && this.chooseItem.value === answer.value) {
        this.isCorrect = true
        this.succesNum++
      } else {
        this.errorNum++
      }
      this.show = true

      this.store = 25 * this.succesNum
    },

    submit () {
      this.pause()
      this.show = false
      this.isStop = true
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.show = false
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.isStop = false
      this.store = 0
      this.start()
      this.timing()
    },
  }
}
</script>

<style lang="scss">
.game217-page {
  position: relative;
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game217/bg.png');
  }

  .setting-page {
    .title {
      color: #0055a6;
    }
  }

  .game-synopsis {
    width: 854px * 0.7;
    height: 672px * 0.7;
    margin: 34px * 0.7;
    padding: 70px * 0.7 90px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game217/text_bg.png');

    .synopsis-title {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #0055a6;
      padding-bottom: 24px * 0.7;
    }

    .synopsis-content {
      padding-bottom: 18px * 0.7;
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #000000;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .btn-right {
      position: absolute;
      top: 40px * 0.7;
      right: 100px * 0.7;
      height: 76px * 0.7;
      display: flex;
    }

    .icon-img {
      padding: 4px * 0.7 15px * 0.7;
      // border: 1px * 0.7 solid #37B982;
      border-radius: 38px * 0.7;
      font-size: 0;

      img {
        width: 50px * 0.7;
        height: 50px * 0.7;
        vertical-align: middle;
        cursor: pointer;
      }

      span {
        display: inline-block;
        padding-left: 8px * 0.7;
        font-size: 28px * 0.7;
        line-height: 50px * 0.7;
        vertical-align: middle;
        user-select: none;
      }
    }

    .top {
      position: absolute;
      top: 128px * 0.7;
      left: 0;
      width: 100%;
      height: 82px * 0.7;
      padding: 0 140px * 0.7;

      .top-icon {
        position: absolute;
        top: 0;
        left: 140px * 0.7;
        width: 82px * 0.7;
        cursor: pointer;
      }

      .top-text {
        display: block;
        font-size: 40px * 0.7;
        line-height: 56px * 0.7;
        text-align: center;
        font-weight: 600;
        color: #0055a6;
      }
    }

    .content {
      position: relative;
      width: 1551px * 0.7;
      height: 326px * 0.7;
      margin-bottom: 35px * 0.7;
      display: flex;
      justify-content: space-between;
    }

    .content-big {
      position: relative;
      width: 1175px * 0.7;
      height: 695px * 0.7;
      margin-bottom: 35px * 0.7;
      display: flex;
      justify-content: space-between;
      align-content: space-between;
      flex-wrap: wrap;
    }

    .content-item {
      position: relative;
      width: 356px * 0.7;
      height: 324px * 0.7;
      display: inline-flex;
      flex-direction: column;
      justify-content: space-between;
      cursor: pointer;

      .item-icon {
        position: absolute;
        top: 25px * 0.7;
        right: 40px * 0.7;
        width: 70px * 0.7;
        // height: 70px * 0.7;
      }

      .item-img {
        width: 345px * 0.7;
        height: 267px * 0.7;
      }

      .item-text {
        display: block;
        padding-top: 14px * 0.7;
        font-size: 28px * 0.7;
        line-height: 44px * 0.7;
        text-align: center;
        color: #0055a6;
      }
    }

    .content-right {
      position: absolute;
      top: 226px * 0.7;
      right: 88px * 0.7;
      width: 250px * 0.7;
      height: 268px * 0.7;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .item {
        width: 250px * 0.7;
        height: 76px * 0.7;
        border: 2px * 0.7 solid #fff;
        border-radius: 4px * 0.7;

        font-size: 24px * 0.7;
        line-height: 72px * 0.7;
        text-align: center;
        color: #fff;
      }
    }

    .footer {
      position: absolute;
      bottom: 30px * 0.7;
      width: 582px * 0.7;
      display: flex;
      justify-content: space-between;

      img {
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }

  .game-modal {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.3);

    .modal-text {
      position: relative;
      width: 854px * 0.7;
      height: 642px * 0.7;
      padding: 72px * 0.7 99px * 0.7 88px * 0.7 92px * 0.7;
      background-repeat: no-repeat;
      background-size: cover;
      background-image: url('/static/game_assets/game217/text_bg.png');

      .modal-top {
        display: flex;
        justify-content: center;
        align-items: center;

        .item-icon {
          height: 70px * 0.7;
        }

        span {
          padding-left: 30px * 0.7;
          font-size: 48px * 0.7;
          line-height: 70px * 0.7;
          font-weight: 600;
        }
      }

      .modal-main {
        position: relative;
        padding-top: 50px * 0.7;
        margin: 0;
        font-size: 28px * 0.7;
        line-height: 48px * 0.7;
      }

      .modal-btn {
        position: absolute;
        bottom: 48px * 0.7;
        left: 0;
        right: 0;
        height: 115px * 0.7;
        display: flex;
        justify-content: center;

        .btn {
          height: 115px * 0.7;
          cursor: pointer;
        }
      }
    }
  }
}
</style>