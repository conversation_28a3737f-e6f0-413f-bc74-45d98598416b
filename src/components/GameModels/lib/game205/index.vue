<template>
  <div class="game205-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <audio ref="music2" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPageLib title="心算练习" :showAnimation="false" @start="status = 2" v-if="status < 2">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本项目考查算术法则。</p>
        <p class="synopsis-content">2、题型是填空题。</p>
        <p class="synopsis-content">3、训练对象:儿童或算术法则确实/提取困难者。</p>
        <p class="synopsis-content">4、一次10题全部答对的您可以终止这一项目训练。</p>
        <p class="synopsis-content">5、有错误者复习一下答案，再训练1-2次。第三次练习后仍有错误，也不再重新练习，而是做其他项目，次日接着训练本一项目。</p>

        <div class="synopsis-choose">
          <p class="choose-title">训练方式:</p>

          <div class="item">
            <div class="item-icon" @click="setChoose(1, 1)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 1" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">加法</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(1, 2)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 2" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">减法</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(1, 3)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 3" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">乘法</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(1, 4)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 4" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">除法</span>
          </div>
        </div>
      </div>
    </settingPageLib>

    <settingPageLib title="心算练习" @start="status = 2" @challenge="start" v-if="status < 4">
      <div class="game-synopsis">
        <p class="synopsis-title">选择题目类型</p>
        <div class="synopsis-choose choose2">
          <div class="item" v-for="item in 4" :key="item + 'type'">
            <div class="item-icon" @click="setChoose(2, item)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="law === item" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">{{lawList[type - 1][item - 1]}}</span>
          </div>
        </div>
      </div>
    </settingPageLib>

    <div class="game-content" v-if="status === 4">
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game206/title_bg.png" />
        <span class="title-text">心算-加法</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game205/content_bg.png" />
        <img class="left-icon" src="/static/game_assets/game205/img1.png" />
        <img class="right-icon" src="/static/game_assets/game205/img2.png" />

        <div class="content-title">不进位的二位数和一位数的加法</div>

        <div class="content-top">
          <span class="top-text">{{currect.num1}}</span>

          <span class="top-text">{{type === 1 ? '+' : type === 2 ? '-' : type === 3 ? '×' : '÷'}}</span>

          <span class="top-text">{{currect.num2}}</span>

          <span class="top-text">=</span>

          <div class="num-item">
            <img class="num-bg" src="/static/game_assets/game209/text_bg.png" />
            <span :class="[answer !== '' ? 'num' : 'symbol']">{{answer !== '' ? answer : '?'}}</span>
          </div>
        </div>

        <div class="content-bottom" v-if="!isShowCorrect && !isShowError">
          <div class="bottom-left">
            <div class="left-item" v-for="item in 10" :key="item + 'btn'" @click="chooseItem(item)">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_1.png" />
              <span class="item-num">{{item % 10}}</span>
            </div>
          </div>

          <div class="bottom-right">
            <div class="right-item item1">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_2.png" />
              <span class="item-num">{{answer}}</span>
            </div>

            <div class="right-item item2" @click="answer = ''">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_3.png" />
              <span class="item-num">X</span>
            </div>

            <div class="right-item item3" @click="confirm">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_4.png" />
              <span class="item-num">确定</span>
            </div>
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="footer-left">
          <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
          <img v-show="isShowCorrect || isShowError" class="img" src="/static/game_assets/common/next.png" @click="toNext" />
        </div>

        <img v-show="isShowCorrect || isShowError" class="img" src="/static/game_assets/common/reset.png" @click="reset">
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status" :isStep="true"></bgMusic>
    <resultPageLib v-if="status === 5" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 10道题 

export default {
  name: 'game205',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,

      type: 1, // 训练模式
      law: 1, // 题目类型
      lawList: [
        ['不进位的两位数和一位数的加法', '不进位的两位数和两位数的加法', '进位的两位数和一位数的加法', '进位的两位数和两位数的加法'],
        ['不借位的两位数和一位数的减法', '不借位的两位数和两位数的减法', '借位的两位数和一位数的减法', '借位的两位数和两位数的减法'],
        ['不进位的一位数和一位数的乘法', '不进位的两位数和一位数的乘法', '进位的一位数和一位数的乘法', '进位的两位数和一位数的乘法'],
        ['不借位的两位数和一位数的除法', '不借位的两位数和两位数的除法', '借位的两位数和一位数的除法', '借位的两位数和两位数的除法']
      ],
      high: true,
      answer: '',
      currect: {
        num1: 0,
        num2: 0,
        answer: 0,
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    playError () {
      this.$refs.music2.play()
    },

    start () {
      this.status = 4
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      if (this.type === 1) { // 加法
        if (this.law === 1) {
          this.currect.num2 = api.randomNum(0, 9)
          const num1 = api.randomNum(0, 9 - this.currect.num2)
          const num2 = api.randomNum(1, 9)
          this.currect.num1 = Number(num2.toString() + num1.toString())
        } else if (this.law === 2) {
          // 十位
          const num1 = api.randomNum(1, 9)
          const num2 = api.randomNum(1, 9 - num1)
          // 个位
          const num3 = api.randomNum(0, 9)
          const num4 = api.randomNum(0, 9 - num3)
          this.currect.num1 = Number(num1.toString() + num3.toString())
          this.currect.num2 = Number(num2.toString() + num4.toString())
        } else if (this.law === 3) {
          this.currect.num2 = api.randomNum(1, 9)
          const num1 = api.randomNum(10 - this.currect.num2, 9)
          const num2 = api.randomNum(1, 9)
          this.currect.num1 = Number(num2.toString() + num1.toString())
        } else {
          // 十位
          const num1 = api.randomNum(1, 9)
          const num2 = api.randomNum(1, 9 - num1)
          // 个位
          const num3 = api.randomNum(10 - num1, 9)
          const num4 = api.randomNum(10 - num3, 9)
          this.currect.num1 = Number(num1.toString() + num3.toString())
          this.currect.num2 = Number(num2.toString() + num4.toString())
        }
        this.currect.answer = this.currect.num1 + this.currect.num2
      } else if (this.type === 2) { // 减法
        if (this.law === 1) {
          this.currect.num2 = api.randomNum(0, 9)
          const num1 = api.randomNum(this.currect.num2, 9)
          const num2 = api.randomNum(1, 9)
          this.currect.num1 = Number(num2.toString() + num1.toString())
        } else if (this.law === 2) {
          // 十位
          const num1 = api.randomNum(1, 9)
          const num2 = api.randomNum(num1, 9)
          // 个位
          const num3 = api.randomNum(0, 9)
          const num4 = api.randomNum(num3, 9)
          this.currect.num1 = Number(num2.toString() + num4.toString())
          this.currect.num2 = Number(num1.toString() + num3.toString())
        } else if (this.law === 3) {
          this.currect.num2 = api.randomNum(1, 9)
          const num1 = api.randomNum(0, this.currect.num2 - 1)
          const num2 = api.randomNum(1, 9)
          this.currect.num1 = Number(num2.toString() + num1.toString())
        } else {
          // 十位
          const num1 = api.randomNum(1, 8)
          const num2 = api.randomNum(num1 + 1, 9)
          // 个位
          const num3 = api.randomNum(0, 9)
          const num4 = api.randomNum(0, num3 - 1)
          this.currect.num1 = Number(num2.toString() + num4.toString())
          this.currect.num2 = Number(num1.toString() + num3.toString())
        }
        this.currect.answer = this.currect.num1 - this.currect.num2
      } else if (this.type === 3) { // 乘法
        if (this.law === 1) {
          this.currect.num1 = api.randomNum(1, 9)
          this.currect.num2 = api.randomNum(1, parseInt(9 / this.currect.num1))
        } else if (this.law === 2) {
          // 个位
          const num1 = api.randomNum(1, 9)
          const num2 = api.randomNum(1, parseInt(9 / num1))
          // 十位
          const num3 = api.randomNum(1, parseInt(9 / num1))
          this.currect.num1 = Number(num2.toString() + num3.toString())
          this.currect.num2 = num1
        } else if (this.law === 3) {
          this.currect.num1 = api.randomNum(1, 9)
          this.currect.num2 = api.randomNum(Math.ceil(9 / this.currect.num1), 9)
        } else {
          // 个位
          const num1 = api.randomNum(1, 9)
          const num2 = api.randomNum(parseInt(9 / num1) + 1, 9)
          // 十位
          const num3 = api.randomNum(1, 9)
          this.currect.num1 = Number(num3.toString() + num2.toString())
          this.currect.num2 = num1
        }
        this.currect.answer = this.currect.num1 * this.currect.num2
      } else { // 除法
        if (this.law === 1) {
          const num1 = api.randomNum(1, 9)
          const num2 = api.randomNum(10, parseInt(99 / num1))
          this.currect.num1 = num2 * num1
          this.currect.num2 = num1
        } else if (this.law === 2) {
          const num1 = api.randomNum(10, 20)
          const num2 = api.randomNum(10, parseInt(99 / num1))
          this.currect.num1 = num2 * num1
          this.currect.num2 = num1
        } else if (this.law === 3) {
          const num1 = api.randomNum(2, 9)
          const num2 = api.randomNum(parseInt(10 / num1) + 1, 9)
          this.currect.num1 = num2 * num1
          this.currect.num2 = num1
        } else {
          const num1 = api.randomNum(10, 50)
          const num2 = api.randomNum(1, 9)
          this.currect.num1 = num2 * num1
          this.currect.num2 = num1
        }
        this.currect.answer = this.currect.num1 / this.currect.num2
      }
      this.number++
    },

    setChoose (type, item) {
      if (type === 1) {
        this.type = item
      } else {
        this.law = item
      }
    },

    chooseItem (item) {
      if (this.answer.length >= 3) return
      this.answer = this.answer + (item === 10 ? 0 : item).toString()
    },

    confirm () {
      if (!this.answer) {
        this.playError()
        return
      }
      if (Number(this.answer) === this.currect.answer) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }
      this.is
    },

    toNext () {
      this.isShowCorrect = false
      this.isShowError = false

      if (this.number < 10) {
        this.answer = ''
        this.startProcess()
      } else {
        this.submit()
      }
    },

    reset () {
      if (this.isShowCorrect) {
        this.succesNum--
      } else {
        this.errorNum--
      }

      this.isShowCorrect = false
      this.isShowError = false
      this.answer = ''
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.store = this.succesNum * 10
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 5
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = ''
      this.currect = {
        num1: 0,
        num2: 0,
        answer: 0,
      },
        this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game205-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game126/bg.png');
  }

  .game-synopsis {
    width: 1605px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
    }

    .synopsis-choose {
      display: flex;
      flex-wrap: wrap;
      margin-top: 25px * 0.7;
      padding: 13px * 0.7 30px * 0.7;
      border: 1px * 0.7 solid #c49e68;
      border-radius: 10px * 0.7;
      background: #fff6e3;

      .choose-title {
        margin: 0;
        font-size: 36px * 0.7;
        line-height: 41px * 0.7;
        font-weight: 600;
        color: #414043;
      }

      .item {
        position: relative;
        display: inline-flex;
        align-items: center;
        padding-left: 25px * 0.7;

        .item-icon {
          position: relative;
          width: 41px * 0.7;
          height: 35px * 0.7;
          display: inline-flex;
          justify-content: center;
          cursor: pointer;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 41px * 0.7;
          }

          .icon1 {
            position: absolute;
            width: 41px * 0.7;
          }
        }

        .item-text {
          position: relative;
          top: 2px * 0.7;
          display: inline-block;
          padding-left: 10px * 0.7;
          font-size: 36px * 0.7;
          line-height: 41px * 0.7;
          font-weight: 600;
          color: #414043;
        }
      }
    }

    .choose2 {
      .item {
        padding-bottom: 20px * 0.7;
      }
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      position: absolute;
      top: 0;
      width: 932px * 0.7;
      height: 125px * 0.7;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px * 0.7;
        height: 125px * 0.7;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 28px * 0.7 0 56px * 0.7 0;
        font-size: 36px * 0.7;
        line-height: 36px * 0.7;
        text-align: center;
        color: #1b1d2d;
      }
    }

    .content {
      position: relative;
      width: 1920px * 0.7;
      height: 824px * 0.7;
      margin-top: 80px * 0.7;
      padding: 194px * 0.7 480px * 0.7 145px * 0.7 459px * 0.7;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;

      .content-bg {
        position: absolute;
        top: 0;
        left: 28px * 0.7;
        width: 1864px * 0.7;
      }

      .left-icon {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 474px * 0.7;
      }

      .right-icon {
        position: absolute;
        bottom: 44px * 0.7;
        right: 0;
        width: 434px * 0.7;
      }

      .content-title {
        position: absolute;
        top: 95px * 0.7;
        font-size: 30px * 0.7;
        line-height: 30px * 0.7;
        color: #312b4f;
      }

      .content-top {
        position: relative;
        width: 100%;
        padding: 0 200px * 0.7;
        display: flex;
        justify-content: space-between;

        .top-text {
          font-size: 96px * 0.7;
          line-height: 166px * 0.7;
          font-family: Impact;
          color: #312b4f;
        }

        .num-item {
          position: relative;
          width: 188px * 0.7;
          height: 206px * 0.7;

          .num-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 188px * 0.7;
          }

          .num {
            position: relative;
            display: block;
            width: 100%;
            font-size: 96px * 0.7;
            line-height: 166px * 0.7;
            text-align: center;
            font-family: Impact;
            color: #312b4f;
          }

          .symbol {
            position: relative;
            display: block;
            width: 100%;
            font-size: 96px * 0.7;
            line-height: 166px * 0.7;
            text-align: center;
            font-weight: 500;
            color: #312b4f;
          }
        }
      }

      .content-bottom {
        position: relative;
        width: 100%;
        display: flex;

        .bottom-left {
          width: 557px * 0.7;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;
          margin-right: -10px * 0.7;

          .left-item {
            position: relative;
            width: 135px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7 -15px * 0.7;
            cursor: pointer;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 135px * 0.7;
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px * 0.7;
              line-height: 136px * 0.7;
              text-align: center;
              color: #312b4f;
            }
          }
        }

        .bottom-right {
          width: 450px * 0.7;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;

          .right-item {
            position: relative;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px * 0.7;
              line-height: 136px * 0.7;
              text-align: center;
              color: #312b4f;
            }
          }

          .item1 {
            width: 291px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7 -12px * 0.7 -12px * 0.7 0;
          }

          .item2 {
            width: 190px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7;
            cursor: pointer;
          }

          .item3 {
            width: 462px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7 -12px * 0.7 -12px * 0.7 0;
            cursor: pointer;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 72px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1480px * 0.7;

      .footer-left {
        width: 620px * 0.7;
        display: inline-flex;
        justify-content: space-between;
      }

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
    }
  }
}
</style>