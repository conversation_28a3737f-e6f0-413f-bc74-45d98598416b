<template>
  <div class="game206-page">
    <div class="page-bg"></div>
    <audio ref="music2" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <img class="left-icon" src="/static/game_assets/game206/icon1.png" />
    <img class="right-icon" src="/static/game_assets/game206/icon2.png" />
    <settingPageLib title="算术法则" :showAnimation="false" @start="start2" v-if="status < 2">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本项目考查算术法则。</p>
        <p class="synopsis-content">2、题型是填空题。</p>
        <p class="synopsis-content">3、训练对象:儿童或算术法则缺失/提取困难者。</p>
        <p class="synopsis-content">4、一组10题答对9-10题的您可以终止这一项目训练。</p>
        <p class="synopsis-content">5、有错误者复习一下答案，再训练1-2次。您第三次练习后仍有错误，也不再重新练习，而是做其他项目，次日接着训练本一项目。</p>

        <div class="synopsis-choose">
          <p class="choose-title">训练方式:</p>

          <div class="item">
            <div class="item-icon" @click="setChoose(1, 1)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 1" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">加法</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(1, 2)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 2" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">减法</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(1, 3)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 3" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">乘法</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(1, 4)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 4" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">除法</span>
          </div>
        </div>
      </div>
    </settingPageLib>

    <settingPageLib title="算术法则" @start="status = 2" @challenge="start" v-if="status < 4">
      <div class="game-synopsis">
        <p class="synopsis-title">选择考察的法则</p>
        <div class="synopsis-choose choose2">
          <div class="item" v-if="type !== 4">
            <div class="item-icon" @click="setChoose(2, 1)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="law === 1" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">0的法则</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(2, 2)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="law === 2" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">1的法则</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(2, 3)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="law === 3" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">9的法则</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(2, 4)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="law === 4" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">10的法则</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(2, 5)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="law === 5" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">结合法则</span>
          </div>
        </div>
      </div>
    </settingPageLib>

    <div class="game-content" v-if="status === 4">
      <img class="left-icon" src="/static/game_assets/game209/icon1.png" />
      <img class="right-icon" src="/static/game_assets/game209/icon2.png" />

      <div class="title">
        <img class="title-bg" src="/static/game_assets/game206/title_bg.png" />
        <span class="title-text">请输入下列所有钟表的总数</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game209/content_bg.png" />
        <div class="content-top">
          <div class="num-item">
            <img class="num-bg" src="/static/game_assets/game209/text_bg.png" />
            <span v-if="currect.num1 > 12" class="num">{{currect.num1}}</span>
            <img v-else class="num-img" v-for="item in currect.num1" :key="item + 'img1'" src="/static/game_assets/game209/item_1.png" />
          </div>

          <img class="symbol1" :src="`/static/game_assets/game209/symbol${type}.png`" />

          <div class="num-item">
            <img class="num-bg" src="/static/game_assets/game209/text_bg.png" />
            <span v-if="currect.num1 > 12" class="num">{{currect.num2}}</span>
            <img v-else class="num-img" v-for="item in currect.num2" :key="item + 'img2'" src="/static/game_assets/game209/item_1.png" />
          </div>

          <img class="symbol2" src="/static/game_assets/game209/symbol5.png" />

          <div class="num-item">
            <img class="num-bg" src="/static/game_assets/game209/text_bg.png" />
            <span :class="[answer !== '' ? 'num' : 'symbol']">{{answer !== '' ? answer : '?'}}</span>
          </div>
        </div>

        <div class="content-bottom">
          <div class="bottom-left">
            <div class="left-item" v-for="item in 10" :key="item + 'btn'" @click="chooseItem(item)">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_1.png" />
              <span class="item-num">{{item % 10}}</span>
            </div>
          </div>

          <div class="bottom-right">
            <div class="right-item item1">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_2.png" />
              <span class="item-num">{{answer}}</span>
            </div>

            <div class="right-item item2" @click="reset">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_3.png" />
              <span class="item-num">X</span>
            </div>

            <div class="right-item item3" @click="confirm">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_4.png" />
              <span class="item-num">确定</span>
            </div>
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="footer-left">
          <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
        </div>

      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status" :isStep="true"></bgMusic>
    <resultPageLib v-if="status === 5" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 10道题 

export default {
  name: 'game206',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,

      type: 1, // 训练模式
      law: 1, // 考查法则
      answer: '',
      currect: {
        num1: 0,
        num2: 0,
        answer: 0,
        answerList: []
      },


      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start2 () {
      this.status = 2
      if (this.type === 4) this.law = 2
    },

    start () {
      this.status = 4
      // this.timing()      
      this.startProcess()
    },

    playError () {
      this.$refs.music2.play()
    },

    startProcess () {
      this.currect.answerList = []
      if (this.type === 1) {
        this.currect.num1 = api.randomNum(1, 30)
        this.currect.num2 = this.law === 1 ? 0 : this.law === 2 ? 1 : this.law === 3 ? 9 : this.law === 4 ? 10 : api.getRandomArray([0, 1, 9, 10], 1)[0]

        this.currect.answer = this.currect.num1 + this.currect.num2
      } else if (this.type === 2) {
        this.currect.num1 = api.randomNum(1, 30)
        this.currect.num2 = this.law === 1 ? 0 : this.law === 2 ? 1 : this.law === 3 ? 9 : this.law === 4 ? 10 : api.getRandomArray([0, 1, 9, 10], 1)[0]

        this.currect.answer = this.currect.num1 - this.currect.num2
      } else if (this.type === 3) {
        this.currect.num1 = api.randomNum(1, 20)
        this.currect.num2 = this.law === 1 ? 0 : this.law === 2 ? 1 : this.law === 3 ? 9 : this.law === 4 ? 10 : api.getRandomArray([0, 1, 9, 10], 1)[0]

        this.currect.answer = this.currect.num1 * this.currect.num2
      } else {
        this.currect.num2 = this.law === 2 ? 1 : this.law === 3 ? 9 : this.law === 4 ? 10 : api.getRandomArray([0, 1, 9, 10], 1)[0]
        this.currect.num1 = api.randomNum(1, 15) * this.currect.num2

        this.currect.answer = this.currect.num1 / this.currect.num2
      }
    },

    setChoose (type, item) {
      if (type === 1) {
        this.type = item
      } else {
        this.law = item
      }
    },

    chooseItem (item) {
      if (this.answer.length >= 3) return
      this.answer = this.answer + (item === 10 ? 0 : item).toString()
    },

    confirm () {
      if (!this.answer) {
        this.playError()
        return
      }
      if (Number(this.answer) === this.currect.answer) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }

      setTimeout(() => {
        this.isShowCorrect = false
        this.isShowError = false
        this.answer = ''
        this.number++
        if (this.number >= 10) {
          this.submit()
        } else {
          this.startProcess()
        }
      }, 800)
    },

    reset () {
      this.answer = ''
    },

    stop () {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.store = this.succesNum * 10
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 5
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = ''
      this.currect = {
        num1: 0,
        num2: 0,
        answer: 0,
        answerList: []
      },
        this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game206-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game126/bg.png');
  }

  .left-icon {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 413px * 0.7;
  }

  .right-icon {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 426px * 0.7;
  }

  .game-synopsis {
    width: 1605px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
    }

    .synopsis-choose {
      display: flex;
      flex-wrap: wrap;
      margin-top: 25px * 0.7;
      padding: 13px * 0.7 30px * 0.7;
      border: 1px * 0.7 solid #c49e68;
      border-radius: 10px * 0.7;
      background: #fff6e3;

      .choose-title {
        margin: 0;
        font-size: 36px * 0.7;
        line-height: 41px * 0.7;
        font-weight: 600;
        color: #414043;
      }

      .item {
        position: relative;
        display: inline-flex;
        align-items: center;
        padding-left: 25px * 0.7;

        .item-icon {
          position: relative;
          width: 41px * 0.7;
          height: 35px * 0.7;
          display: inline-flex;
          justify-content: center;
          cursor: pointer;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 41px * 0.7;
          }

          .icon1 {
            position: absolute;
            width: 41px * 0.7;
          }
        }

        .item-text {
          position: relative;
          top: 2px * 0.7;
          display: inline-block;
          padding-left: 10px * 0.7;
          font-size: 36px * 0.7;
          line-height: 41px * 0.7;
          font-weight: 600;
          color: #414043;
        }
      }
    }

    .choose2 {
      .item {
        padding-bottom: 20px * 0.7;
      }
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .left-icon {
      position: absolute;
      left: 0;
      bottom: 45px * 0.7;
      width: 708px * 0.7;
    }

    .right-icon {
      position: absolute;
      right: 0;
      bottom: 73px * 0.7;
      width: 405px * 0.7;
    }

    .title {
      position: absolute;
      top: 0;
      width: 932px * 0.7;
      height: 125px * 0.7;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px * 0.7;
        height: 125px * 0.7;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 28px * 0.7 0 56px * 0.7 0;
        font-size: 36px * 0.7;
        line-height: 36px * 0.7;
        text-align: center;
        color: #1b1d2d;
      }
    }

    .content {
      position: relative;
      width: 1680px * 0.7;
      height: 860px * 0.7;
      margin-right: 20px * 0.7;
      margin-top: 70px * 0.7;
      padding: 106px * 0.7 348px * 0.7 158px * 0.7 348px * 0.7;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1680px * 0.7;
      }

      .content-top {
        position: relative;
        padding: 0 16px * 0.7;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .num-item {
          position: relative;
          width: 224px * 0.7;
          height: 305px * 0.7;
          padding: 10px * 0.7 22px * 0.7;
          display: flex;
          flex-wrap: wrap;
          align-content: flex-start;

          .num-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 224px * 0.7;
            height: 305px * 0.7;
          }

          .num-img {
            position: relative;
            width: 60px * 0.7;
            height: 60px * 0.7;
          }

          .num {
            position: relative;
            display: block;
            width: 100%;
            font-size: 92px * 0.7;
            line-height: 240px * 0.7;
            text-align: center;
            color: #312b4f;
            font-family: Impact;
          }

          .symbol {
            position: relative;
            display: block;
            width: 100%;
            font-size: 92px * 0.7;
            line-height: 240px * 0.7;
            text-align: center;
            color: #312b4f;
          }
        }

        .symbol1 {
          margin-bottom: 40px * 0.7;
          width: 62px * 0.7;
          height: 100%;
        }

        .symbol2 {
          margin-bottom: 40px * 0.7;
          width: 63px * 0.7;
          height: 33px * 0.7;
        }
      }

      .content-bottom {
        position: relative;
        width: 100%;
        padding-top: 45px * 0.7;
        display: flex;

        .bottom-left {
          width: 557px * 0.7;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;
          margin-right: -10px * 0.7;

          .left-item {
            position: relative;
            width: 135px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7 -15px * 0.7;
            cursor: pointer;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 135px * 0.7;
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px * 0.7;
              line-height: 136px * 0.7;
              text-align: center;
              color: #312b4f;
            }
          }
        }

        .bottom-right {
          width: 450px * 0.7;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;

          .right-item {
            position: relative;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px * 0.7;
              line-height: 136px * 0.7;
              text-align: center;
              color: #312b4f;
            }
          }

          .item1 {
            width: 291px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7 -12px * 0.7 -12px * 0.7 0;
          }

          .item2 {
            width: 190px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7;
            cursor: pointer;
          }

          .item3 {
            width: 462px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7 -12px * 0.7 -12px * 0.7 0;
            cursor: pointer;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 72px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1480px * 0.7;

      .footer-left {
        width: 620px * 0.7;
        display: inline-flex;
        justify-content: space-between;
      }

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
    }
  }
}
</style>