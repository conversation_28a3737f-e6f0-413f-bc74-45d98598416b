<template>
  <div class="game108-page">
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
    <settingPageLib title="学习天气" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、通过本训练的教学，提高训练者如何判断天气的认知;</p>
        <p class="synopsis-content">2、一共七关，请根据提示进行操作。</p>
      </div>
    </settingPageLib>
    <div :class="['game-content', (number === 0 || number === 1 || number === 5 || number === 7) && 'black-bg']" v-if="status === 3">
      <div class="top">
        <img v-if="isPlay" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>

      <div class="content">
        <img class="left-btn" v-if="number > 0" @click="toPrevious" src="/static/game_assets/game104/left_btn.png" />
        <img class="right-btn" v-if="number < 7" @click="toNext" src="/static/game_assets/game104/right_btn.png" />

        <!-- 学习环节+训练1 -->
        <div class="content-1" v-if="number === 0 || number === 1">
          <div class="content-title">
            <template v-if="number === 1">
              <img class="title-icon" src="/static/game_assets/game104/img7.png" />
              <img class="title-bg" src="/static/game_assets/game104/text_bg.png" />
              <span class="title-text">{{current.name}}</span>
            </template>
          </div>

          <div class="content-main">
            <div :class="['content-item', itemClass(1, index)]" v-for="(item, index) in question" :key="index + 'item'" @click="chooseItem(item.index)">
              <img class="item-img" :src="`/static/game_assets/game108/item_${item.index}.png`" />

              <template v-if="number === 1">
                <img class="item-icon" src="/static/game_assets/game104/img6.png" />
                <img class="icon-bg" src="/static/game_assets/game104/icon_bg.png" />
              </template>

              <img v-if="number === 1 && choose === item.index && isShowCorrect" class="icon" src="/static/game_assets/game104/icon.png" />
            </div>
          </div>
        </div>

        <!-- 训练2 -->
        <div class="content-2" v-if="number === 2">
          <div class="content-left">
            <img class="left-bg" src="/static/game_assets/game104/content_bg3.png" />
            <div class="left-content">
              <div class="left-item" v-for="item in 9" :key="item + 'item'">
                <draggable class="left-item-wapper" v-model="dragItemTop[item-1]" :group="group1" @start="dragStart1(item)">
                  <img v-for="it in dragItemTop[item-1]" :key="it.name + 'img'" :class="['left-img', 'left-img-' + it.name]" :src="`/static/game_assets/game108/item_${current.index}_${it.name}.png`" />
                </draggable>
              </div>
            </div>
          </div>

          <div class="content-right">
            <img class="right-img" :src="`/static/game_assets/game108/item_${current.index}.png`" />
            <img class="right-bg" src="/static/game_assets/game104/content_bg4.png" />

            <div class="right-content">
              <img class="right-content-bg" src="/static/game_assets/game104/content_bg2.png" />
              <draggable :class="['right-item', 'right-item-' + item]" :style="{'z-index': choose === item ? 99 : 0}" v-for="item in 9" :key="item + 'item'" v-model="dragItemBottom[item-1]" :group="group2" @add="dragAdd(item)">
                <img :class="['right-img', 'right-img-' + item]" :src="`/static/game_assets/game104/puzzle_${item}.png`" />
                <img v-for="it in dragItemBottom[item-1]" :key="it.name + 'img'" :class="['left-img', 'left-img-' + it.name]" :src="`/static/game_assets/game108/item_${current.index}_${it.name}.png`" />
              </draggable>
            </div>
          </div>
        </div>

        <!-- 训练3 -->
        <div class="content-3" v-if="number === 3">
          <img class="content-bg" src="/static/game_assets/game104/content_bg1.png" />

          <div class="content-left">
            <div class="left-item" v-for="item in question" :key="item.index + 'item'">
              <div class="item-left">
                <img class="item-bg" src="/static/game_assets/game104/text_bg.png" />
                <span class="item-text">{{item.name}}</span>
              </div>

              <img class="item-right" src="/static/game_assets/game104/img5.png">
            </div>
          </div>

          <div class="content-middle">
            <draggable class="middle-item" v-for="(item, index) in question" :key="item.index + 'item2'" v-model="dragItemLeft[index]" :group="group2" @add="dragAdd2(index)">
              <img class="item-icon" src="/static/game_assets/game104/img6.png" />
              <img v-for="it in dragItemLeft[index]" :key="it.name + 'img1'" class="right-img" :src="`/static/game_assets/game108/item_${it.name}.png`" />
            </draggable>
          </div>

          <div class="content-right">
            <div class="right-item" v-for="item in 3" :key="item + 'item3'">
              <draggable class="right-item-wapper" :group="group1" v-model="dragItemRight[item-1]" @start="dragStart2(item)">
                <img v-for="it in dragItemRight[item-1]" :key="it.name + 'img2'" class="right-img" :src="`/static/game_assets/game108/item_${it.name}.png`" />
              </draggable>
            </div>
          </div>
        </div>

        <!-- 训练4 -->
        <div class="content-4" v-if="number === 4">
          <img class="content-bg" src="/static/game_assets/game104/content_bg1.png" />

          <div class="content-top">
            <img class="top-icon1" src="/static/game_assets/game104/img4.png" />
            <img class="top-icon2" src="/static/game_assets/game104/img3.png" />
            <img class="top-img" src="/static/game_assets/game104/img2.png" />
          </div>

          <div class="content-bottom">
            <div class="bottom-item" v-for="item in question" :key="item.index + 'item4'" @click="chooseItem(item.index)">
              <img class="item-img" :src="`/static/game_assets/game108/item_${item.index}.png`" />

              <img class="icon-bg" src="/static/game_assets/game104/icon_bg.png" />
              <img v-if="choose === item.index && isShowCorrect" class="icon" src="/static/game_assets/game104/icon.png" />
            </div>
          </div>
        </div>

        <!-- 训练5 + 训练7 -->
        <div class="content-5" v-if="number === 5 || number === 7">
          <div class="content-item" v-for="(item, index) in question" :key="index + 'item5'" @click="openItem(item, index)">
            <div :class="['item', itemClass(2, item, index)]">
              <img class="bg" src="/static/game_assets/game104/item_bg1.png" />
              <img class="icon1" src="/static/game_assets/game104/img6.png" />
            </div>
            <div :class="['item', itemClass(2, item, index)]">
              <img v-if="number === 5" class="main1" :src="`/static/game_assets/game108/item_${item.index}.png`" />

              <template v-if="number === 7">
                <img class="bg" src="/static/game_assets/game104/item_bg1.png" />
                <img class="main2" src="/static/game_assets/game104/img7.png" />
              </template>
            </div>
          </div>
        </div>

        <!-- 训练6 -->
        <div class="content-6" v-if="number === 6">
          <img class="content-bg" src="/static/game_assets/game104/content_bg1.png" />

          <div class="content-top">
            <img class="top-icon1" src="/static/game_assets/game104/img4.png" />
            <img class="top-icon2" src="/static/game_assets/game104/img7.png" />
            <div class="top-title">
              <img class="title-bg" src="/static/game_assets/game104/text_bg.png" />
              <span class="title-text">{{current.name}}</span>
            </div>
            <img class="top-img" src="/static/game_assets/game104/img1.png" />
          </div>

          <div class="content-bottom">
            <div class="bottom-item" v-for="item in question" :key="item.index + 'item'" @click="chooseItem(item.index)">
              <img class="item-img" :src="`/static/game_assets/game108/item_${item.index}.png`" />

              <img class="icon-bg" src="/static/game_assets/game104/icon_bg.png" />
              <img v-if="choose === item.index && isShowCorrect" class="icon" src="/static/game_assets/game104/icon.png" />
            </div>
          </div>
        </div>
      </div>

      <div :class="['sucess', isShowCorrect ? 'translate-top' : '']" :style="{'opacity': isShowCorrect ? 1 : 0}">
        <img class="img" src="/static/game_assets/game29/hot_balloon.png">
      </div>
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import api from '../../utils/common.js'

export default {
  name: 'game108',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    draggable
  },

  data () {
    return {
      musicUrl: '',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      isShowCorrect: false,
      isCanOpen: true,

      timer: null,
      index: 0,
      question: [],
      current: {},
      choose: 0,
      answer: [],
      dragItemTop: [],
      dragItemBottom: [[], [], [], [], [], [], [], [], []],
      dragItemLeft: [[], [], []],
      dragItemRight: [],
      group1: {
        name: 'itemList',
        pull: true,
        put: false,
        sort: false
      },
      group2: {
        name: 'itemList',
        pull: false,
        put: true,
        sort: false
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    itemClass (type, item, index) {
      if (type === 1 && this.number === 0) {
        if (this.index > item) {
          return 'rotate-item'
        }
      } else {
        const list1 = this.answer.filter(it => it.index === item.index)
        const list2 = this.answer.filter(it => it.sort === index)
        if (list1.length && list2.length) {
          return 'flip-item'
        }
      }
    },

    // TODO: 返回首页
    goHome () {
      this.pause()
      this.$router.go(-1)
    },

    play (url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnded () {
      this.isPlay = false
      this.index++
      setTimeout(() => {
        if (this.number) return

        if (this.index < 6) {
          this.play(`/static/game_assets/audio/game108/audio_${this.question[this.index].index}.mp3`)
        } else {
          this.number++
          this.startProcess()
        }
      }, 1200)
    },

    start () {
      this.status = 3
      this.startProcess()
      // this.timing()
    },

    startProcess () {
      const list = [
        {
          name: '阴天',
          index: 1
        },
        {
          name: '打闪',
          index: 2
        },
        {
          name: '刮风',
          index: 3
        },
        {
          name: '晴天',
          index: 4
        },
        {
          name: '大雨',
          index: 5
        },
        {
          name: '大雪',
          index: 6
        }
      ]

      this.index = 0
      if (this.number === 1 || this.number === 2) {
        this.current = api.getRandomArray(list, 1)[0]
      }
      if (this.number === 0 || this.number === 1) {
        this.question = api.shuffle(list)
      }
      if (this.number === 0) {
        this.play(`/static/game_assets/audio/game108/audio_${this.question[this.index].index}.mp3`)
      }
      if (this.number === 2) {
        let li = [1, 2, 3, 4, 5, 6, 7, 8, 9]
        li = api.shuffle(li)

        for (let i = 0; i < 9; i++) {
          const item = li[i]
          this.dragItemTop.push([{
            name: item,
            startIndex: i,
          }])
        }
      }
      if (this.number === 3 || this.number === 4 || this.number === 5 || this.number === 6 || this.number === 7) {
        this.question = api.getRandomArray(list, 3)
      }
      if (this.number === 3) {
        const li = api.shuffle([0, 1, 2])
        for (let i = 0; i < 3; i++) {
          const item = this.question[li[i]]
          this.dragItemRight.push([{
            name: item.index,
            index: li[i],
            startIndex: i,
          }])
        }
      }
      if (this.number === 4 || this.number === 6) {
        this.current = api.getRandomArray(this.question, 1)[0]
      }
      if (this.number === 5 || this.number === 7) {
        this.question = this.question.concat(this.question)
        this.question = api.shuffle(this.question)
      }
      if (this.number === 1 || this.number === 2 || this.number === 3 || this.number === 5 || this.number === 7) {
        this.play(`/static/game_assets/audio/game104/title_${this.number}.mp3`)
      }
      if (this.number === 4 || this.number === 6) {
        this.play(`/static/game_assets/audio/game108/audio_${this.current.index}.mp3`)
      }
    },

    toPrevious () {
      if (this.isShowCorrect) return
      clearTimeout(this.timer)
      this.choose = 0
      this.answer = []
      this.dragItemTop = []
      this.dragItemBottom = [[], [], [], [], [], [], [], [], []]
      this.dragItemLeft = [[], [], []]
      this.dragItemRight = []
      this.isShowCorrect = false
      this.number--
      this.startProcess()
    },

    toNext () {
      if (this.isShowCorrect) return
      clearTimeout(this.timer)
      this.choose = 0
      this.answer = []
      this.isShowCorrect = false
      this.number++
      if (this.number === 1 || this.number === 4 || this.number === 6) {
        this.errorNum++
      }
      if (this.number === 2) {
        const list = this.dragItemBottom.filter(item => item.length)
        this.errorNum = this.errorNum + 9 - list.length
      }
      if (this.number === 3) {
        const list = this.dragItemLeft.filter(item => item.length)
        this.errorNum = this.errorNum + 3 - list.length
      }
      if (this.number === 5) {
        const choose = this.answer.filter(it => it.sort === index)
        this.errorNum = this.errorNum + 3 - parseInt(choose.length / 2)
      }
      this.startProcess()
    },

    dragStart1 (item) {
      this.choose = this.dragItemTop[item - 1][0].name
    },

    dragStart2 (item) {
      this.choose = this.dragItemRight[item - 1][0].index
    },

    dragAdd (index) {
      if (this.choose !== index) {
        if (this.dragItemBottom[index - 1].length > 1) {
          const item = this.dragItemBottom[index - 1].filter(item => item.name !== index)[0]
          this.dragItemBottom[index - 1] = this.dragItemBottom[index - 1].filter(item => item.name === index)
          this.dragItemTop[item.startIndex].push(item)
        } else {
          const item = this.dragItemBottom[index - 1].pop()
          this.dragItemTop[item.startIndex].push(item)
        }
        this.errorNum++
        this.play('/static/game_assets/audio/error_audio.mp3')
      } else {
        this.succesNum++
      }

      const list = this.dragItemBottom.filter(item => item.length)
      if (list.length >= 9) this.goNext()
    },

    dragAdd2 (index) {
      if (this.choose !== index) {
        if (this.dragItemLeft[index].length > 1) {
          const item = this.dragItemLeft[index].filter(item => item.index !== index)[0]
          this.dragItemLeft[index] = this.dragItemLeft[index].filter(item => item.index === index)
          this.dragItemRight[item.startIndex].push(item)
        } else {
          const item = this.dragItemLeft[index].pop()
          this.dragItemRight[item.startIndex].push(item)
        }
        this.errorNum++
        this.play('/static/game_assets/audio/error_audio.mp3')
      } else {
        this.succesNum++
      }

      const list = this.dragItemLeft.filter(item => item.length)
      if (list.length >= 3) this.goNext()
    },

    chooseItem (item) {
      if (!this.number) return

      this.choose = item
      if (this.choose === this.current.index) {
        this.succesNum++
        this.goNext()
      } else {
        this.errorNum++
        this.play('/static/game_assets/audio/error_audio.mp3')
      }
    },

    openItem (item, index) {
      const choose = this.answer.filter(it => it.sort === index)
      if (!this.isCanOpen || choose.length) return

      this.answer.push({
        ...item,
        sort: index
      })
      if (this.number === 7) this.play(`/static/game_assets/audio/game108/audio_${item.index}.mp3`)
      if (this.answer.length && !(this.answer.length % 2)) {
        const it = this.answer[this.answer.length - 2]
        if (it.index !== item.index) {
          this.errorNum++
          this.isCanOpen = false
          setTimeout(() => {
            this.answer.pop()
            this.answer.pop()
            this.isCanOpen = true

            if (this.answer.length >= 6) this.goNext()
          }, 500)
        } else {
          this.succesNum++
          if (this.answer.length >= 6) this.goNext()
        }
      }
    },

    goNext () {
      this.isShowCorrect = true
      this.timer = setTimeout(() => {
        this.timer = null
        this.choose = 0
        this.answer = []
        this.isShowCorrect = false
        this.number++

        if (this.number > 7) {
          this.submit()
        } else {
          this.startProcess()
        }
      }, 2200)
    },

    submit () {
      this.isStop = true
      this.store = parseInt(100 / (this.succesNum + this.errorNum) * this.succesNum)
      this.infos[0].value = this.second
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.choose = 0
      this.answer = []
      this.dragItemTop = []
      this.dragItemBottom = [[], [], [], [], [], [], [], [], []]
      this.dragItemLeft = [[], [], []]
      this.dragItemRight = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game108-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game104/bg.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    .top {
      position: absolute;
      top: 40px * 0.7;
      left: 105px * 0.7;
      width: 175px * 0.7;
      display: flex;
      justify-content: space-between;

      .top-icon {
        width: 80px * 0.7;
        cursor: pointer;
      }
    }

    .content {
      position: relative;
      width: 1920px * 0.7;
      height: 1080px * 0.7;
      display: flex;
      justify-content: center;
      align-items: center;

      .left-btn {
        position: absolute;
        top: 525px * 0.7;
        left: 80px * 0.7;
        width: 131px * 0.7;
        cursor: pointer;
      }

      .right-btn {
        position: absolute;
        top: 525px * 0.7;
        right: 80px * 0.7;
        width: 131px * 0.7;
        cursor: pointer;
      }

      .content-1 {
        position: relative;
        width: 1047px * 0.7;
        height: 843px * 0.7;
        margin-bottom: 47px * 0.7;

        .content-title {
          position: relative;
          width: 380px * 0.7;
          height: 122px * 0.7;
          margin-left: 308px * 0.7;

          .title-icon {
            position: absolute;
            top: 0;
            left: 0;
            width: 114px * 0.7;
            z-index: 1;
          }

          .title-bg {
            position: absolute;
            top: 0;
            right: 0;
            width: 314px * 0.7;
          }

          .title-text {
            position: relative;
            display: block;
            padding: 29px * 0.7 0 44px * 0.7 68px * 0.7;
            font-size: 47px * 0.7;
            line-height: 47px * 0.7;
            text-align: center;
            font-weight: 500;
            color: #fff;
          }
        }

        .content-main {
          height: 721px * 0.7;
          padding-top: 30px * 0.7;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;

          .rotate-item {
            transform: rotate(1080deg) scale(0);
          }

          .content-item {
            position: relative;
            width: 334px * 0.7;
            height: 335px * 0.7;
            transition: All 1s ease-in-out;
            cursor: pointer;

            .item-img {
              position: absolute;
              top: 0;
              left: 0;
              width: 334px * 0.7;
            }

            .item-icon {
              position: absolute;
              top: 93px * 0.7;
              left: 118px * 0.7;
              width: 98px * 0.7;
            }

            .icon-bg {
              position: absolute;
              right: 16px * 0.7;
              bottom: 12px * 0.7;
              width: 78px * 0.7;
            }

            .icon {
              position: absolute;
              right: 34px * 0.7;
              bottom: 38px * 0.7;
              width: 44px * 0.7;
            }
          }
        }
      }

      .content-2 {
        position: relative;
        width: 1251px * 0.7;
        height: 891px * 0.7;
        margin-top: 47px * 0.7;
        margin-left: 15px * 0.7;

        .content-left {
          position: absolute;
          left: 0;
          top: 0;
          width: 976px * 0.7;
          height: 891px * 0.7;

          .left-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 976px * 0.7;
          }

          .left-content {
            position: relative;
            padding: 80px * 0.7 250px * 0.7 185px * 0.7 85px * 0.7;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-content: space-between;

            .left-item {
              width: 200px * 0.7;
              height: 200px * 0.7;
              display: inline-flex;
              justify-content: center;
              align-items: center;

              .left-item-wapper {
                .left-img {
                  cursor: pointer;
                }

                .left-img-1 {
                  width: 160px * 0.7;
                  height: 122px * 0.7;
                }

                .left-img-2 {
                  width: 121px * 0.7;
                  height: 161px * 0.7;
                }

                .left-img-3 {
                  width: 159px * 0.7;
                  height: 122px * 0.7;
                }

                .left-img-4 {
                  width: 122px * 0.7;
                  height: 200px * 0.7;
                }

                .left-img-5 {
                  width: 199px * 0.7;
                  height: 122px * 0.7;
                }

                .left-img-6 {
                  width: 121px * 0.7;
                  height: 200px * 0.7;
                }

                .left-img-7 {
                  width: 160px * 0.7;
                  height: 121px * 0.7;
                }

                .left-img-8 {
                  width: 122px * 0.7;
                  height: 160px * 0.7;
                }

                .left-img-9 {
                  width: 160px * 0.7;
                  height: 121px * 0.7;
                }
              }
            }
          }
        }

        .content-right {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 519px * 0.7;
          height: 818px * 0.7;
          padding: 286px * 0.7 50px * 0.7 122px * 0.7 59px * 0.7;

          .right-img {
            position: absolute;
            top: 0;
            right: 50px * 0.7;
            width: 210px * 0.7;
            z-index: 1;
          }

          .right-bg {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 519px * 0.7;
          }

          .right-content {
            position: relative;

            .right-content-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 410px * 0.7;
            }

            .right-item {
              position: absolute;
              display: flex;
              justify-content: center;
              align-items: center;

              .right-img {
                position: absolute;
                top: 0;
                left: 0;
              }

              .left-img {
                position: relative;
                z-index: 2;
              }

              .right-img-1,
              .right-img-7,
              .right-img-9 {
                width: 195px * 0.7;
              }

              .right-img-2,
              .right-img-6 {
                width: 156px * 0.7;
              }

              .right-img-3 {
                width: 194px * 0.7;
              }

              .right-img-4,
              .right-img-8 {
                width: 157px * 0.7;
              }

              .right-img-5 {
                width: 234px * 0.7;
              }

              .left-img-1,
              .left-img-7,
              .left-img-9 {
                width: 160px * 0.7;
              }

              .left-img-2,
              .left-img-6 {
                width: 121px * 0.7;
              }

              .left-img-3 {
                width: 159px * 0.7;
              }

              .left-img-4,
              .left-img-8 {
                width: 122px * 0.7;
              }

              .left-img-5 {
                width: 199px * 0.7;
              }
            }

            .right-item-1 {
              top: 0;
              left: 0;
              width: 195px * 0.7;
              height: 157px * 0.7;
            }

            .right-item-2 {
              top: 0;
              left: 122px * 0.7;
              width: 156px * 0.7;
              height: 196px * 0.7;
            }

            .right-item-3 {
              top: 0;
              left: 206px * 0.7;
              width: 194px * 0.7;
              height: 157px * 0.7;
            }

            .right-item-4 {
              top: 83px * 0.7;
              left: 0;
              width: 157px * 0.7;
              height: 235px * 0.7;
            }

            .right-item-5 {
              top: 122px * 0.7;
              left: 83px * 0.7;
              width: 234px * 0.7;
              height: 157px * 0.7;
            }

            .right-item-6 {
              top: 84px * 0.7;
              left: 244px * 0.7;
              width: 156px * 0.7;
              height: 235px * 0.7;
            }

            .right-item-7 {
              top: 245px * 0.7;
              left: 0;
              width: 195px * 0.7;
              height: 156px * 0.7;
            }

            .right-item-8 {
              top: 206px * 0.7;
              left: 121px * 0.7;
              width: 157px * 0.7;
              height: 195px * 0.7;
            }

            .right-item-9 {
              top: 245px * 0.7;
              left: 206px * 0.7;
              width: 195px * 0.7;
              height: 156px * 0.7;
            }
          }
        }
      }

      .content-3 {
        position: relative;
        width: 1446px * 0.7;
        height: 899px * 0.7;
        margin-top: 95px * 0.7;
        padding: 117px * 0.7 272px * 0.7 122px * 0.7 252px * 0.7;
        display: flex;
        justify-content: space-between;

        .content-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 1446px * 0.7;
        }

        .content-left {
          display: flex;
          flex-direction: column;
          width: 416px * 0.7;
          justify-content: space-between;

          .left-item {
            display: inline-flex;
            justify-content: space-between;
            align-items: center;
            height: 209px * 0.7;

            .item-left {
              position: relative;
              width: 314px * 0.7;
              height: 118px * 0.7;

              .item-bg {
                position: absolute;
                top: 0;
                left: 0;
                width: 314px * 0.7;
              }

              .item-text {
                position: relative;
                display: block;
                padding: 27px * 0.7 0 44px * 0.7 0;
                font-size: 47px * 0.7;
                line-height: 47px * 0.7;
                font-weight: 500;
                text-align: center;
                color: #fff;
              }
            }

            .item-right {
              position: relative;
              width: 62px * 0.7;
              height: 43px * 0.7;
            }
          }
        }

        .content-middle {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          width: 208px * 0.7;

          .middle-item {
            position: relative;
            width: 208px * 0.7;
            height: 209px * 0.7;
            background: #c9c9c9;
            border-radius: 48px * 0.7;

            .item-icon {
              position: absolute;
              top: 32px * 0.7;
              left: 53px * 0.7;
              width: 98px * 0.7;
            }
          }
        }

        .content-right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          width: 208px * 0.7;
          height: 100%;

          .right-item {
            position: relative;
            width: 208px * 0.7;
            height: 209px * 0.7;

            .right-item-wapper {
              width: 208px * 0.7;
              height: 209px * 0.7;

              .right-img {
                width: 208px * 0.7;
                height: 209px * 0.7;
                cursor: pointer;
              }
            }
          }
        }

        .right-img {
          position: relative;
          width: 208px * 0.7;
          height: 209px * 0.7;
        }
      }

      .content-4 {
        position: relative;
        width: 1446px * 0.7;
        height: 970px * 0.7;
        margin-top: 20px * 0.7;
        padding: 480px * 0.7 206px * 0.7 0 193px * 0.7;
        display: flex;
        flex-direction: column;
        align-items: center;

        .content-bg {
          position: absolute;
          top: 71px * 0.7;
          left: 0;
          width: 1446px * 0.7;
        }

        .content-top {
          position: absolute;
          top: 0;
          width: 740px * 0.7;
          height: 567px * 0.7;
          margin-right: 25px * 0.7;

          .top-icon1 {
            position: absolute;
            top: 0;
            left: 253px * 0.7;
            width: 172px * 0.7;
            z-index: 2;
          }

          .top-icon2 {
            position: absolute;
            top: 178px * 0.7;
            left: 201px * 0.7;
            width: 355px * 0.7;
            z-index: 1;
          }

          .top-img {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 740px * 0.7;
            z-index: 0;
          }
        }

        .content-bottom {
          position: relative;
          width: 100%;
          display: flex;
          justify-content: space-between;
          z-index: 1;

          .bottom-item {
            position: relative;
            width: 334px * 0.7;
            height: 335px * 0.7;
            cursor: pointer;

            .item-img {
              width: 334px * 0.7;
              height: 335px * 0.7;
            }

            .icon-bg {
              position: absolute;
              right: 22px * 0.7;
              bottom: 14px * 0.7;
              width: 78px * 0.7;
            }

            .icon {
              position: absolute;
              right: 38px * 0.7;
              bottom: 42px * 0.7;
              width: 44px * 0.7;
            }
          }
        }
      }

      .content-5 {
        position: relative;
        width: 1083px * 0.7;
        height: 724px * 0.7;
        margin-top: 100px * 0.7;
        display: flex;
        flex-wrap: wrap;

        .content-item > div:first-child {
          z-index: 1;
          backface-visibility: hidden;
        }

        .flip-item {
          transform: rotateY(180deg);
        }

        .content-item {
          position: relative;
          width: 361px * 0.7;
          height: 362px * 0.7;
          cursor: pointer;

          .item {
            position: absolute;
            top: 0;
            left: 0;
            width: 361px * 0.7;
            height: 362px * 0.7;
            transition: all 0.5s;
            display: flex;
            justify-content: center;
            align-items: center;

            .bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 361px * 0.7;
              height: 362px * 0.7;
            }

            .icon1 {
              position: relative;
              width: 98px * 0.7;
            }

            .main1 {
              position: relative;
              width: 334px * 0.7;
            }

            .main2 {
              position: relative;
              width: 182px * 0.7;
              margin-left: 20px * 0.7;
            }
          }
        }
      }

      .content-6 {
        position: relative;
        width: 1446px * 0.7;
        height: 970px * 0.7;
        margin-top: 20px * 0.7;
        padding: 0 206px * 0.7 0 193px * 0.7;
        display: flex;
        flex-direction: column;
        align-items: center;

        .content-bg {
          position: absolute;
          top: 71px * 0.7;
          left: 0;
          width: 1446px * 0.7;
        }

        .content-top {
          position: relative;
          width: 702px * 0.7;
          height: 446px * 0.7;
          margin-right: 22px * 0.7;

          .top-icon1 {
            position: absolute;
            top: 0;
            left: 253px * 0.7;
            width: 172px * 0.7;
            z-index: 2;
          }

          .top-icon2 {
            position: absolute;
            top: 178px * 0.7;
            left: 126px * 0.7;
            width: 114px * 0.7;
            z-index: 1;
          }

          .top-title {
            position: absolute;
            top: 181px * 0.7;
            left: 194px * 0.7;
            width: 314px * 0.7;
            height: 118px * 0.7;

            .title-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 314px * 0.7;
            }

            .title-text {
              position: relative;
              display: block;
              padding: 27px * 0.7 0 44px * 0.7 0;
              font-size: 47px * 0.7;
              line-height: 47px * 0.7;
              text-align: center;
              font-weight: 500;
              color: #fff;
            }
          }

          .top-img {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 702px * 0.7;
          }
        }

        .content-bottom {
          width: 100%;
          padding-top: 34px * 0.7;
          display: flex;
          justify-content: space-between;

          .bottom-item {
            position: relative;
            width: 334px * 0.7;
            height: 335px * 0.7;
            cursor: pointer;

            .item-img {
              width: 334px * 0.7;
              height: 335px * 0.7;
            }

            .icon-bg {
              position: absolute;
              right: 22px * 0.7;
              bottom: 14px * 0.7;
              width: 78px * 0.7;
            }

            .icon {
              position: absolute;
              right: 38px * 0.7;
              bottom: 42px * 0.7;
              width: 44px * 0.7;
            }
          }
        }
      }
    }

    .sucess {
      position: absolute;
      bottom: -762px * 0.7;
      width: 774px * 0.7;
      height: 762px * 0.7;
      transition: transform 2s ease-in;
      z-index: 100;

      .img {
        width: 774px * 0.7;
        height: 762px * 0.7;
      }
    }

    .translate-top {
      transform: translateY(-2442px * 0.7);
    }
  }

  .black-bg {
    background: rgba(0, 0, 0, 0.33);
  }
}
</style>