<template>
  <div class="game59-page">
    <div class="page-bg"></div>
    <settingPageLib title="差异性测试" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">请指出这对词语的不同之处。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <img class="content-bg" src="/static/game_assets/game59/content_bg.png" />

        <div class="content-top">
          <div class="item1">
            <img class="item-bg" src="/static/game_assets/game59/item_bg.png" />
            <span class="item-text">{{question[0]}}</span>
          </div>

          <div class="item2">
            <img class="item-bg" src="/static/game_assets/game59/item_bg1.png" />
            <span class="item-text">{{question[1]}}</span>
          </div>
        </div>

        <a-textarea class="content-bottom" :rows="2" v-model="answer"></a-textarea>
        <div class="content-line1"></div>
        <div class="content-line2"></div>
      </div>

      <div class="footer">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="btn-group">
          <img v-if="isShowNext" class="img img1" src="/static/game_assets/common/next.png" @click="next">

          <template v-if="answer && !isShowNext">
            <img class="img" src="/static/game_assets/game59/correct.png" @click="handleClick(1)" />
            <img class="img" src="/static/game_assets/game59/partially_correct.png" @click="handleClick(2)" />
            <img class="img" src="/static/game_assets/game59/error.png" @click="handleClick(3)" />
          </template>
        </div>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game59',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      partCorrectNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      isShowNext: false,
      questionList: [],
      question: [],
      answer: '',

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '部分正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      this.questionList = api.shuffle(data.questions)
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      this.question = this.questionList[this.number]
      this.number++
    },

    handleClick (type) {
      if (type === 1) {
        this.succesNum++
      } else if (type === 2) {
        this.partCorrectNum++
      } else {
        this.errorNum++
      }

      this.isShowNext = true
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    next () {
      if (this.number < 10) {
        this.answer = ''
        this.startProcess()
        this.isShowNext = false
      } else {
        this.submit()
      }
    },

    submit () {
      this.isStop = true
      this.store = 10 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.partCorrectNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isShowNext = false
      this.question = []
      this.answer = ''
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game59-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game59/bg.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1712px * 0.7;
      height: 896px * 0.7;
      padding-left: 32px * 0.7;
      padding-bottom: 39px * 0.7;

      .content-bg {
        position: absolute;
        top: 0;
        left: 32px * 0.7;
        width: 1680px * 0.7;
      }

      .content-top {
        position: absolute;
        top: 88px * 0.7;
        right: 313px * 0.7;
        width: 821px * 0.7;
        display: flex;
        justify-content: space-between;

        .item1 {
          position: relative;
          width: 395px * 0.7;
          height: 132px * 0.7;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 395px * 0.7;
            height: 132px * 0.7;
          }

          .item-text {
            position: relative;
            display: block;
            padding: 34px * 0.7 0 48px * 0.7 185px * 0.7;
            font-size: 50px * 0.7;
            line-height: 50px * 0.7;
            font-weight: 600;
            color: #f8f9ff;
            user-select: none;
          }
        }

        .item2 {
          position: relative;
          width: 373px * 0.7;
          height: 132px * 0.7;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 373px * 0.7;
            height: 132px * 0.7;
          }

          .item-text {
            position: relative;
            display: block;
            padding: 34px * 0.7 0 48px * 0.7 168px * 0.7;
            font-size: 50px * 0.7;
            line-height: 50px * 0.7;
            font-weight: 600;
            color: #f8f9ff;
            user-select: none;
          }
        }
      }

      .content-bottom {
        position: absolute;
        bottom: 176px * 0.7;
        right: 518px * 0.7;
        width: 775px * 0.7;
        height: 280px * 0.7;
        border: none;
        outline: none;
        resize: none;
        background: transparent;

        font-size: 50px * 0.7;
        line-height: 145px * 0.7;
        font-weight: 600;
        color: #1e2257;
        user-select: none;

        &:focus {
          box-shadow: none;
        }
      }

      .content-line1 {
        position: absolute;
        bottom: 325px * 0.7;
        right: 518px * 0.7;
        width: 775px * 0.7;
        height: 2px * 0.7;
        border: 1px * 0.7 dashed #4474da;
      }

      .content-line2 {
        position: absolute;
        bottom: 185px * 0.7;
        right: 518px * 0.7;
        width: 775px * 0.7;
        height: 2px * 0.7;
        border: 1px * 0.7 dashed #4474da;
      }
    }

    .footer {
      position: absolute;
      bottom: 75px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1470px * 0.7;
      padding-right: 38px * 0.7;

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img1 {
        margin-left: 603px * 0.7;
      }

      .btn-group {
        width: 872px * 0.7;
        height: 115px * 0.7;
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
</style>