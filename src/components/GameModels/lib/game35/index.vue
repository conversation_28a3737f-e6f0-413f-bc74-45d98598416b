<template>
  <div class="game35-page">
    <div class="page-bg" :class="status > 1 ? 'page-bg2': 'page-bg1'"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <audio ref="music2" muted controls="controls" style="display:none" loop="loop">
      <source src="/static/game_assets/audio/dida.mp3" type="audio/mpeg" />
    </audio>
    <settingPageLib title="时钟转换--简单" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <div class="synopsis-content">
          <p>1、这是一项注意力保持的训练任务。</p>
          <p>2、要求您计数录音里钟表报时的次数，声音播放完毕时，录音中会有“几点”的提问，这是请回答几点钟了（即数了几次报时声音）。</p>
          <p>3、钟表运行时有“嗒嗒”的指针的声音，这是用来干扰您注意力的，注意不要计数这个声音。只数钟表报时声音-“铛”的声音</p>
          <p>4、任务中的报时声音数不会超过12，也不会低于1.</p>
          <p>5、分级：1-4点为初级，5-8点为中级，9-12点为高级。</p>
        </div>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="clock-content">
        <div class="clock" v-for="item in 3" :key="item">
          <img class="clock-shadow" src="/static/game_assets/game34/shadow.png" />
          <img class="clock-bg" src="/static/game_assets/game34/yellow_bg.png" />
          <img class="clock-point" src="/static/game_assets/game34/yellow_point.png" />
          <img class="hour-hand" src="/static/game_assets/game34/hour_hand.png" />
          <img class="minute-hand" src="/static/game_assets/game34/minute_hand.png" />
        </div>
      </div>

      <div class="keyboard" v-if="answerStatus === 'answer'">
        <div class="left">
          <img v-for="item in 10" :key="item" class="btn" :src="`/static/game_assets/game35/btn_${item}.png`" @click="select(item)" />
        </div>
        <div class="right">
          <div class="input">
            <img class="img" src="/static/game_assets/game35/input.png" />
            <img class="clear" src="/static/game_assets/game35/clear.png" @click="time = ''" />
            <span class="number">{{ time }}</span>
          </div>
          <img class="img" src="/static/game_assets/game35/confirm.png" @click="submit" />
        </div>
      </div>

      <div class="btn-group">
        <img class="left" src="/static/game_assets/game34/stop.png" @click="stop" />
        <div class="right">
          <img v-if="answerStatus === 'wait'" class="start" src="/static/game_assets/game34/start.png" @click="startProcess" />
          <img v-if="answerStatus === 'result'" class="reset" src="/static/game_assets/game34/reset.png" @click="reset" />
          <img v-if="answerStatus === 'result'" class="continue" src="/static/game_assets/game34/continue.png" @click="continueTrain" />
        </div>
      </div>
    </div>

    <maskPage v-if="answerStatus === 'judge'">
      <div class="mask-content">
        <img v-if="!isCorrect" class="img" src="/static/game_assets/game34/error.png" />
        <img v-else class="img" src="/static/game_assets/game34/success.png" />
      </div>
    </maskPage>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import maskPage from '../component/mask.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
// 游戏分为3个等级，每轮游戏3个回合
// 初级：1-4点
// 中级：5-8点
// 高级：9-12点

export default {
  name: 'game35',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    [maskPage.name]: maskPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      musicUrl: '',
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isStop: false,
      show: false,
      isPlay: false,
      isContinue: false,
      answerStatus: 'wait', // wait--等待状态，play--播报题目，answer--答题，judge--判定对错，result--结果
      randomNumber: 0, // 几点
      bellNum: 0, // 报时数量
      time: '',
      isCorrect: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    playAudio () {
      setTimeout(() => {
        this.answerStatus = 'answer'
        this.isStop = false
        this.timing()
      }, 3000)
    },

    play (url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$nextTick(() => {
        this.$refs.music.play()
        this.isPlay = true
      })
    },

    playBg () {
      this.$refs.music2.play()
    },

    pause () {
      this.$refs.music.pause()
      this.$refs.music2.pause()
      this.isPlay = false
    },

    handleEnd () {
      this.isPlay = false
      if (this.answerStatus !== 'play') return

      this.bellNum++
      if (this.bellNum < this.randomNumber) {
        this.play('/static/game_assets/audio/bell.mp3')
      } else {
        this.answerStatus = 'answer'
        this.pause()
        this.play('/static/game_assets/audio/game34/audio1.mp3')
      }
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.status = 3
    },

    select (item) {
      if (this.time.length >= 2) return
      this.time = this.time + (item === 10 ? 0 : item).toString()
    },

    startProcess () {
      this.answerStatus = 'play'
      this.bellNum = 0
      this.randomNumber = api.randomNum((this.level - 1) * 4 + 1, this.level * 4)
      this.play('/static/game_assets/audio/bell.mp3')
      this.playBg()
    },

    submit () {
      if (!this.time) return
      this.isStop = true
      this.answerStatus = 'judge'
      this.isCorrect = (this.randomNumber === Number(this.time))
      if (this.isCorrect) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      setTimeout(() => {
        this.number++
        if (this.number >= 3) {
          this.answerStatus = 'wait'
          this.store = this.succesNum ? (this.succesNum * 30 + 10) : 0
          this.infos[0].value = this.level
          this.infos[1].value = this.second
          this.infos[2].value = this.succesNum
          this.infos[3].value = this.errorNum
          this.status = 4
          this.params = {
            id: this.info.id,
            grade: this.level,
            time: this.second,
            totalPoints: this.store
          }
        } else {
          this.answerStatus = 'result'
        }
      }, 1000)
    },

    stop () {
      this.isStop = true
      this.show = true
      if (this.isPlay) {
        this.pause()
        this.isContinue = true
      }
    },

    reset () {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.timeNum = 0
      this.answerStatus = 'wait'
      this.randomNumber = 0
      this.time = ''
    },

    // 继续游戏, 继续计时
    cancel () {
      if (this.answerStatus === 'answer') {
        this.isStop = false
        this.timing()
      }
      if (this.isContinue) {
        this.play()
        if (this.answerStatus === 'play') this.playBg()
        this.isContinue = false
      }
    },

    again () {
      this.isStop = false
      this.status = 3
      this.reset()
      this.timing()
    },

    continueTrain () {
      this.answerStatus = 'wait'
      this.randomNumber = 0
      this.time = ''
    }
  }
}
</script>

<style lang="scss">
.game35-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background-image: url('/static/game_assets/game34/bg_1.png');
  }
  .page-bg2 {
    background-image: url('/static/game_assets/game34/bg_2.png');
  }

  .game-synopsis {
    width: 1000px * 0.7;
    height: 504px * 0.7;
    padding-left: 200px * 0.7;
    padding-top: 50px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #333;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 25px * 0.7;

      p {
        margin: 0;
        font-size: 25px * 0.7;
        line-height: 40px * 0.7;
        font-weight: 400;
        color: #333;
        user-select: none;
      }
    }
  }

  .game-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative;
    width: 100%;
    height: 100%;
    padding: 30px * 0.7 0 15px * 0.7 0;

    .clock-content {
      display: flex;
      justify-content: space-between;
      width: 1330px * 0.7;

      .clock {
        position: relative;
        width: 373px * 0.7;
        height: 455px * 0.7;

        .clock-bg {
          position: absolute;
          top: 0;
          left: 20px * 0.7;
          width: 332px * 0.7;
          height: 455px * 0.7;
        }

        .clock-shadow {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 373px * 0.7;
          height: 53px * 0.7;
          opacity: 0.2;
        }

        .clock-point {
          position: absolute;
          top: 251px * 0.7;
          left: 167px * 0.7;
          width: 49px * 0.7;
          height: 47px * 0.7;
          z-index: 1;
        }

        .hour-hand {
          position: absolute;
          top: 264px * 0.7;
          left: 185px * 0.7;
          width: 95px * 0.7;
          height: 58px * 0.7;
          animation: rotate 120s infinite linear;
          transform-origin: 3px * 0.7 5px * 0.7;
        }

        .minute-hand {
          position: absolute;
          top: 174px * 0.7;
          left: 143px * 0.7;
          width: 55px * 0.7;
          height: 110px * 0.7;
          animation: rotate 20s infinite linear;
          transform-origin: 48px * 0.7 100px * 0.7;
        }
      }
    }

    .keyboard {
      display: flex;
      width: 1300px * 0.7;
      padding: 15px * 0.7 0 10px * 0.7 0;

      .left {
        display: flex;
        flex-wrap: wrap;
        width: 975px * 0.7;
        height: 282px * 0.7;

        img {
          width: 180px * 0.7;
          height: 127px * 0.7;
          margin-right: 15px * 0.7;
          margin-bottom: 13px * 0.7;
          margin-top: 1px * 0.7;
          cursor: pointer;
        }
      }

      .right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 325px * 0.7;
        height: 282px * 0.7;
        padding-left: 12px * 0.7;
        padding-bottom: 12px * 0.7;

        .input {
          position: relative;
          width: 313px * 0.7;
          height: 129px * 0.7;

          .img {
            position: absolute;
            top: 0;
            left: 0;
            width: 313px * 0.7;
            height: 129px * 0.7;
          }

          .clear {
            position: absolute;
            top: 29px * 0.7;
            right: 24px * 0.7;
            width: 57px * 0.7;
            height: 58px * 0.7;
            cursor: pointer;
          }

          .number {
            position: absolute;
            top: 20px * 0.7;
            left: 80px * 0.7;
            width: 145px * 0.7;
            height: 74px * 0.7;
            font-size: 52px * 0.7;
            line-height: 74px * 0.7;
            text-align: center;
            font-family: Impact;
            color: #fff;
            user-select: none;
          }
        }

        .img {
          width: 313px * 0.7;
          height: 129px * 0.7;
          cursor: pointer;
        }
      }
    }

    .btn-group {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding: 0 123px * 0.7 30px * 0.7 123px * 0.7;

      .left {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .right {
        .start,
        .continue,
        .reset {
          width: 268px * 0.7;
          height: 115px * 0.7;
          margin-left: 40px * 0.7;
          cursor: pointer;
        }

        .submit {
          width: 270px * 0.7;
          height: 115px * 0.7;
          cursor: pointer;
        }
      }
    }
  }

  .mask-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;

    .img {
      width: 287px * 0.7;
      height: 310px * 0.7;
      margin-top: 320px * 0.7;
    }
  }
}
@keyframes rotate {
  100% {
    transform: rotateZ(360deg);
  }
}
</style>