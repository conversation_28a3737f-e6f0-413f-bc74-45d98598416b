<template>
  <div class="game8-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPageLib @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、注意的广度又称注意的范围，是指在同一时间内一个人能看清楚地把握注意对象的数量。</p>
        <p class="synopsis-content">2、本组训练将有助于扩大您的注意范围，进而有助于提高学习和工作的效率。</p>
        <p class="synopsis-content">3、训练方法：显示屏上将快速出现一些相同的图形，请注意看，然后回答问题。</p>
        <p class="synopsis-content">4、训练等级：依据注意的数量和目标呈现的。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="items" v-if="answerStatus === 'topic'">
          <img class="item-img" v-for="item in 10" :key="item" :src="`/static/game_assets/game8/${chooseArr.includes(item) ? 'yellow' : 'blue'}_item.png`">
        </div>

        <div class="answer-content" v-if="answerStatus === 'answer' || answerStatus === 'wait'">
          <div class="title">
            <img class="bg" src="/static/game_assets/game8/brown_bg2.png">
            <p class="text">您刚才看到几个黄色窗帘？</p>
          </div>

          <div class="keyboard" v-if="answerStatus === 'answer'">
            <img class="bg" src="/static/game_assets/game8/info_bg.png">
            <div class="keyboard-btn">
              <div :class="['input', answer ? '' : 'placeholder']">{{answer || '请输入您的答案'}}</div>
              <div class="delete-btn" @click="deleteItem">
                <img class="img" src="/static/game_assets/game8/delete.png">
              </div>
              <div class="btn" v-for="item in 10" :key="item" @click="chooseItem(item === 10 ? 0 : item)">{{item === 10 ? 0 : item}}</div>
              <div class="confirm" @click="confirm">确定</div>
            </div>
          </div>
        </div>

        <div class="judge" v-if="answerStatus === 'judge'">
          <img class="img" :src="`/static/game_assets/game8/${isCorrect ? 'success' : 'error'}.png`">
        </div>
      </div>

      <div :class="['btn-group', answerStatus !== 'wait' ? 'big-width' : '']">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>
        <template v-if="answerStatus === 'wait'">
          <div class="btn" @click="goOn">
            <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">继续训练</span>
          </div>
          <div class="btn" @click="reset">
            <img class="bg" src="/static/game_assets/game8/brown_bg.png">
            <span class="text">重做</span>
          </div>
        </template>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 游戏分为6个等级，每轮游戏3个回合
// 1级：1-4个窗帘，时间1.5s
// 2级：1-4个窗帘，时间1s
// 3级：5-7个窗帘，时间1.5s
// 4级：5-7个窗帘，时间1s
// 5级：8-10个窗帘，时间1.5s
// 6级：8-10个窗帘，时间1s

export default {
  name: 'game8',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      answerStatus: 'topic', // topic -- 题目 answer -- 回答 judge -- 判定 wait -- 等待
      show: false,
      status: 1,
      isStop: false,
      answer: '',
      isCorrect: false,
      chooseArr: [],
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    playError () {
      this.$refs.music.play()
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    chooseItem (item) {
      this.answer = this.answer + item.toString()
    },

    deleteItem () {
      this.answer = this.answer.substring(0, this.answer.length - 1)
    },

    confirm () {
      if (!this.answer) {
        this.playError()
        return
      }
      this.answerStatus = 'judge'
      this.isCorrect = Number(this.answer) === this.chooseArr.length
      if (this.isCorrect) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      setTimeout(() => {
        this.number++
        if (this.number >= 3) {
          this.store = this.succesNum ? this.succesNum * 30 + 10 : 0
          this.isStop = true
          this.infos[0].value = this.level
          this.infos[1].value = this.second
          this.infos[2].value = this.succesNum
          this.infos[3].value = this.errorNum
          this.status = 4
          this.params = {
            id: this.info.id,
            grade: this.level,
            time: this.second,
            totalPoints: this.store
          }
        } else {
          this.answerStatus = 'wait'
        }
      }, 1000)
    },

    stop () {
      if (this.answerStatus === 'topic' || this.answerStatus === 'judge') return
      this.isStop = true
      this.show = true
    },

    goOn () {
      this.answerStatus = 'topic'
      this.answer = ''
      this.chooseArr = []
      this.startProcess()
    },

    reset () {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.answerStatus = 'topic'
      this.answer = ''
      this.chooseArr = []
      this.start()
      this.timing()
    },

    submit () {
      this.isStop = true
      this.status = 4
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    again () {
      this.isStop = false
      this.reset()
    },

    // 窗帘个数计算
    startProcess () {
      const list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

      switch (this.level) {
        case 1:
        case 2: {
          const randomNumber = api.randomNum(1, 4)
          this.chooseArr = api.getRandomArray(list, randomNumber)
          break
        }
        case 3:
        case 4: {
          const randomNumber = api.randomNum(5, 7)
          this.chooseArr = api.getRandomArray(list, randomNumber)
          break
        }
        case 5:
        case 6: {
          const randomNumber = api.randomNum(8, 10)
          this.chooseArr = api.getRandomArray(list, randomNumber)
          break
        }
        default:
          break
      }

      const time = this.level % 2 ? 1500 : 1000
      setTimeout(() => {
        this.answerStatus = 'answer'
      }, time)
    }
  }
}
</script>

<style lang="scss">
.game8-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game8/bg.png');
  }

  .game-synopsis {
    .synopsis-content {
      padding-top: 20px * 0.7;
      margin: 0;
      font-size: 30px * 0.7;
      line-height: 42px * 0.7;
      font-weight: 400;
      color: #a83a01;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .content {
      flex: 1;
      display: flex;
      align-items: center;

      .items {
        width: 1647px * 0.7;
        height: 670px * 0.7;
        padding: 0 0 19px * 0.7 42px * 0.7;
        display: flex;
        flex-wrap: wrap;

        .item-img {
          width: 279px * 0.7;
          height: 264px * 0.7;
          margin-right: 42px * 0.7;
          margin-bottom: 47px * 0.7;
        }
      }

      .answer-content {
        width: 936px * 0.7;
        height: 872px * 0.7;
        padding-bottom: 104px * 0.7;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;

        .title {
          position: relative;
          width: 852px * 0.7;
          height: 222px * 0.7;

          .bg {
            position: absolute;
            left: 0;
            width: 852px * 0.7;
            height: 118px * 0.7;
          }

          .text {
            position: relative;
            padding: 17px * 0.7 0 34px * 0.7 0;
            font-size: 48px * 0.7;
            line-height: 67px * 0.7;
            text-align: center;
            color: #a83a01;
            user-select: none;
          }
        }

        .keyboard {
          position: relative;
          width: 936px * 0.7;
          height: 616px * 0.7;
          padding: 36px * 0.7 33px * 0.7 40px * 0.7 35px * 0.7;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 936px * 0.7;
            height: 616px * 0.7;
          }

          .keyboard-btn {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            user-select: none;

            .input {
              width: 646px * 0.7;
              height: 118px * 0.7;
              margin: 0 18px * 0.7 14px * 0.7 0;
              border-radius: 10px * 0.7;
              background: #f8dd84;

              padding: 27px * 0.7 34px * 0.7 31px * 0.7 34px * 0.7;
              font-size: 60px * 0.7;
              line-height: 60px * 0.7;
              color: #a83a01;
              overflow: hidden;
              text-overflow: ellipsis;
              user-select: none;
            }

            .placeholder {
              color: rgba(168, 58, 1, 0.2);
            }

            .delete-btn {
              position: relative;
              width: 187px * 0.7;
              height: 118px * 0.7;
              margin: 0 15px * 0.7 14px * 0.7 0;
              border-radius: 10px * 0.7;
              background: #f8dd84;
              cursor: pointer;

              &:active {
                background: #fff;
              }

              .img {
                position: absolute;
                top: 42px * 0.7;
                left: 56px * 0.7;
                width: 72px * 0.7;
                height: 34px * 0.7;
              }
            }

            .btn {
              width: 274px * 0.7;
              height: 88px * 0.7;
              margin: 0 15px * 0.7 14px * 0.7 0;
              border-radius: 10px * 0.7;
              background: #f8dd84;

              font-size: 60px * 0.7;
              line-height: 88px * 0.7;
              text-align: center;
              color: #a83a01;
              cursor: pointer;

              &:active {
                background: #fff;
              }
            }

            .confirm {
              width: 564px * 0.7;
              height: 88px * 0.7;
              margin: 0 15px * 0.7 14px * 0.7 0;
              border-radius: 10px * 0.7;
              background: #f8dd84;

              font-size: 60px * 0.7;
              line-height: 88px * 0.7;
              text-align: center;
              color: #a83a01;
              cursor: pointer;

              &:active {
                background: #fff;
              }
            }
          }
        }
      }

      .judge {
        width: 460px * 0.7;
        height: 506px * 0.7;
        padding-bottom: 46px * 0.7;

        .img {
          width: 460px * 0.7;
          height: 460px * 0.7;
        }
      }
    }

    .btn-group {
      position: relative;
      width: 1072px * 0.7;
      display: flex;
      justify-content: space-between;
      padding-bottom: 82px * 0.7;
      margin: 0 auto;

      .btn {
        position: relative;
        width: 295px * 0.7;
        height: 84px * 0.7;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px * 0.7;
          height: 84px * 0.7;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px * 0.7 0 21px * 0.7 0;
          font-size: 38px * 0.7;
          line-height: 53px * 0.7;
          text-align: center;
          color: #a83a01;
          user-select: none;
        }
      }
    }

    .big-width {
      width: 1554px * 0.7;
    }
  }
}
</style>