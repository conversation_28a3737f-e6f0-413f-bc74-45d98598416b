<template>
  <div class="game56-page">
    <div class="page-bg"></div>
    <settingPageLib title="图文概括" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、抽象是众多事物中抽取出共同的，本质性的特征 而舍弃其非本质的特征。例如苹果、香蕉、生梨、葡萄、桃子等 它们共同的特性，就是水果。概括是在头脑中把个种事物抽象出来的共同特征 联合起来的过程。</p>
        <p class="synopsis-content">2、这是一组训练抽象与概括能力的练习。请根据出示的图片显示。 图片总结出它们之间的共同特征。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <img class="left-icon" src="/static/game_assets/game56/left_icon.png" />
      <img class="right-icon" src="/static/game_assets/game56/right_icon.png" />

      <div class="title">
        <img class="title-bg" src="/static/game_assets/game56/title_bg.png" />
        <span class="title-text">请指出呈现物品的类别</span>
      </div>

      <div class="content">
        <div class="content-top">
          <img class="item" v-for="item in 2" :key="item" :src="`/static/game_assets/game56/item_${current.question}_${item}.png`" />
        </div>

        <div class="content-bottom">
          <div class="item" v-for="(item, index) in current.answerList" :key="item + index" @click="chooseItem(item)">
            <img class="bg" v-if="choose === item" src="/static/game_assets/game56/correct_bg.png" />
            <img class="bg" v-else src="/static/game_assets/game56/default_bg.png" />

            <span class="text">{{item}}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <img class="img3" v-if="choose && !isShowCorrect && !isShowError" src="/static/game_assets/common/submit.png" @click="submit">
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'

export default {
  name: 'game56',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,
      current: {},
      choose: '',
      answerList: ['动物', '餐具', '家具', '电器', '衣服', '文具', '植物'],
      questions: [
      ],

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      this.questions = api.shuffle(this.questions)
      const list = api.getRandomArray(this.answerList, 5)
      for (let i = 0; i < 5; i++) {
        const li = this.answerList.filter(item => item !== list[i])
        const answerList = api.shuffle(api.getRandomArray(li, 3).concat(list[i]))
        this.questions.push({
          question: this.answerList.indexOf(list[i]) + 1,
          answerList: answerList,
          answer: list[i]
        })
      }
      this.current = this.questions[this.number]
      this.current.answerList = api.shuffle(this.current.answerList)
    },

    chooseItem (item) {
      this.choose = item
    },

    stop () {
      if (this.isShowError || this.isShowCorrect) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      if (this.choose === this.current.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      if (this.number >= 4) {
        this.isStop = true
        this.store = 20 * this.succesNum
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: '',
          time: this.second,
          totalPoints: this.store
        }
      } else {
        if (this.choose === this.current.answer) {
          this.isShowCorrect = true
        } else {
          this.isShowError = true
        }

        setTimeout(() => {
          this.isShowCorrect = false
          this.isShowError = false
          this.number++
          this.current = this.questions[this.number]
          this.current.answerList = api.shuffle(this.current.answerList)
          this.choose = ''
        }, 800)
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.current = {}
      this.choose = ''
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game56-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game56/bg.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .left-icon {
      position: absolute;
      bottom: 10px * 0.7;
      left: 0;
      width: 372px * 0.7;
    }

    .right-icon {
      position: absolute;
      right: 28px * 0.7;
      bottom: 12px * 0.7;
      width: 380px * 0.7;
    }

    .title {
      position: absolute;
      top: 0;
      width: 932px * 0.7;
      height: 125px * 0.7;
      margin-left: 148px * 0.7;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px * 0.7;
        height: 125px * 0.7;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 33px * 0.7 0 56px * 0.7 0;
        font-size: 36px * 0.7;
        line-height: 36px * 0.7;
        text-align: center;
        user-select: none;
      }
    }

    .content {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 1304px * 0.7;
      height: 640px * 0.7;
      padding-left: 10px * 0.7;
      padding-bottom: 100px * 0.7;

      .content-top {
        width: 1225px * 0.7;
        margin: 0 auto;
        display: inline-flex;
        justify-content: space-between;

        .item {
          width: 580px * 0.7;
          height: 320px * 0.7;
          background: #fff;
          border-radius: 54px * 0.7;
        }
      }

      .content-bottom {
        width: 100%;
        display: inline-flex;
        justify-content: space-between;

        .item {
          position: relative;
          width: 299px * 0.7;
          cursor: pointer;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 299px * 0.7;
          }

          .text {
            position: relative;
            display: block;
            padding: 32px * 0.7 0 45px * 0.7 0;
            font-size: 50px * 0.7;
            line-height: 50px * 0.7;
            text-align: center;
            font-weight: 600;
            color: #fff;
            user-select: none;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 72px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1528px * 0.7;
      padding-right: 30px * 0.7;

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        margin: 9px * 0.7 0;
        cursor: pointer;
      }

      .img3 {
        width: 267px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
    }
  }
}
</style>