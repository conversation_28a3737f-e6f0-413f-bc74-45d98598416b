<template>
  <div class="game45-page">
    <audio ref="music" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
    <settingPageLib @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、请在短时间内记住图片，记忆结束后，找出刚刚记住的图片并选择它。</p>
        <p class="synopsis-content">2、选择错误，会提示错误。</p>
        <p class="synopsis-content">3、关卡挑战性逐步升级。</p>
        <p class="synopsis-content">4、错误数、正确数、做题总数和训练用时将作为成绩记录下来。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="content-wapper">
          <div class="content-item" v-for="item in 4" :key="item">
            <div :class="['item', itemClass(item)]" @click="chooseItem(item)">
              <div class="bg"></div>
            </div>
            <div :class="['item', itemClass(item)]">
              <div :class="['bg', !questions.includes(item) && 'error-bg']"></div>
              <img v-if="questions.includes(item)" class="main" src="/static/game_assets/game45/item.png">
            </div>
          </div>
        </div>
      </div>

      <div class="btn-group">
        <div class="btn" @click="stop" v-if="answerStatus !== 'question'">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <div class="btn" @click="startGame" v-if="answerStatus === 'wait' || answerStatus === 'answerWait' || answerStatus === 'continue'">
          <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
          <span class="text">{{ (answerStatus === 'wait' && '开始记忆') || (answerStatus === 'answerWait' && '开始选择') || (answerStatus === 'continue' && '继续训练') }}</span>
        </div>
      </div>

      <div class="judge" v-if="answerStatus === 'judge'">
        <img class="img" :src="`/static/game_assets/game8/${isCorrect ? 'success' : 'error'}.png`">
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 每轮游戏4个回合
// 1级：1张图片
// 2级：2张图片
// 3级：3张图片

export default {
  name: 'game45',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      questions: [],
      itemClassArr: [],
      answerStatus: 'wait', // question -- 题目 answer -- 回答 wait -- 题目前等待 answerWait -- 回答前等待 judge -- 判定
      index: 0,
      show: false,
      status: 1,
      isStop: false,
      chooseArr: [],
      errorChoose: 0,
      isCorrect: false,

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.status = 3
    },

    startGame () {
      if (this.answerStatus === 'wait') {
        this.answerStatus = 'question'
        this.startProcess()
      } else if (this.answerStatus === 'answerWait') {
        this.answerStatus = 'answer'
      } else if (this.answerStatus === 'continue') {
        this.isStop = true
        this.answerStatus = ''
        this.index = 0
        this.itemClassArr = []
        this.chooseArr = []
        this.errorChoose = 0
        setTimeout(() => {
          this.answerStatus = 'wait'
        }, 1500)
      }
    },

    // 开始流程
    async startProcess () {
      this.number++
      this.questions = api.getRandomArray([1, 2, 3, 4], this.level)

      this.itemClassArr.push(this.questions[0])
      await this.playAnimation()
      if (this.level === 1) {
        this.itemClassArr = []
        this.answerStatus = 'answerWait'
        return
      }

      this.itemClassArr.push(this.questions[1])
      await this.playAnimation()
      if (this.level === 2) {
        this.itemClassArr = []
        this.answerStatus = 'answerWait'
        return
      }

      this.itemClassArr.push(this.questions[2])
      await this.playAnimation()
      this.itemClassArr = []

      this.answerStatus = 'answerWait'
    },

    playAnimation () {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve(true)
        }, 2000)
      })
    },

    itemClass (item) {
      if (this.itemClassArr.indexOf(item) !== -1) {
        return 'rotate-item'
      }

      if (this.chooseArr.indexOf(item) !== -1) {
        return 'rotate-item'
      }

      if (this.errorChoose) {
        if (this.errorChoose === item || this.questions.indexOf(item) !== -1) {
          return 'rotate-item'
        }
      }
    },

    chooseItem (item) {
      if (this.answerStatus !== 'answer' || this.chooseArr.includes(item) || this.errorChoose === item) return

      if (this.questions.includes(item)) {
        this.chooseArr.push(item)
        if (this.chooseArr.length !== this.questions.length) return
        this.succesNum++
        this.isCorrect = true
      } else {
        this.errorChoose = item
        this.errorNum++
        this.isCorrect = false
      }
      setTimeout(() => {
        this.answerStatus = 'judge'
      }, 1800)

      setTimeout(() => {
        if (this.number < 4) {
          this.answerStatus = 'continue'
        } else {
          this.store = 25 * this.succesNum
          this.isStop = true
          this.infos[0].value = this.level
          this.infos[1].value = this.second
          this.infos[2].value = this.succesNum
          this.infos[3].value = this.errorNum
          this.status = 4
          this.params = {
            id: this.info.id,
            grade: this.level,
            time: this.second,
            totalPoints: this.store
          }
        }
      }, 2600)
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    reset () {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.answerStatus = 'wait'
      this.index = 0
      this.itemClassArr = []
      this.chooseArr = []
      this.errorChoose = 0
    },

    // 继续游戏, 继续计时
    cancel () {
      if (this.answerStatus !== 'answer' && this.answerStatus !== 'continue') return
      this.isStop = false
      this.timing()
    },

    again () {
      this.isStop = false
      this.status = 3
      this.reset()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game45-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game45/bg.png');
  }

  .game-synopsis {
    .synopsis-content {
      padding-top: 20px * 0.7;
      margin: 0;
      font-size: 30px * 0.7;
      line-height: 42px * 0.7;
      font-weight: 400;
      color: #a83a01;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .content {
      width: 794px * 0.7;
      height: 794px * 0.7;
      margin-bottom: 130px * 0.7;
      padding: 20px * 0.7;
      background: rgba(0, 0, 0, 0.3);

      .content-wapper {
        width: 100%;
        height: 100%;
        background: #c2e40a;
        padding: 10px * 0.7;
        display: flex;
        flex-wrap: wrap;

        .content-item > div:first-child {
          z-index: 1;
          backface-visibility: hidden;
        }

        .rotate-item {
          transform: rotateY(180deg);
        }

        .content-item {
          position: relative;
          width: 346px * 0.7;
          height: 346px * 0.7;
          margin: 10px * 0.7;
          cursor: pointer;

          .item {
            position: absolute;
            top: 0;
            left: 0;
            width: 346px * 0.7;
            height: 346px * 0.7;
            transition: all 1.5s;

            .bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 346px * 0.7;
              height: 346px * 0.7;
              background: #fff;
            }

            .error-bg {
              background: #ff6a38;
            }

            .main {
              position: absolute;
              top: 60px * 0.7;
              left: 74px * 0.7;
              width: 196px * 0.7;
              height: 236px * 0.7;
            }
          }
        }
      }
    }

    .btn-group {
      position: absolute;
      bottom: 62px * 0.7;
      width: 1072px * 0.7;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px * 0.7;
        height: 84px * 0.7;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px * 0.7;
          height: 84px * 0.7;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px * 0.7 0 21px * 0.7 0;
          font-size: 38px * 0.7;
          line-height: 53px * 0.7;
          text-align: center;
          color: #a83a01;
        }
      }
    }

    .judge {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding-top: 10px * 0.7;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 99;

      .img {
        width: 460px * 0.7;
        height: 460px * 0.7;
      }
    }
  }
}
</style>