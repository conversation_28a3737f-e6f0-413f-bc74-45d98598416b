<template>
  <div class="game92-page">
    <audio ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
    <settingPageLib title="" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <img class="img1" src="/static/game_assets/game92/img1.png" />
        <img class="img2" src="/static/game_assets/game92/img2.png" />
      </div>
    </settingPageLib>
    <div :class="['game-content']" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" class="top-icon" src="/static/game_assets/common/play.png" @click="play" />
        <img v-else class="top-icon" src="/static/game_assets/common/pause.png" @click="pause" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>

      <div class="content">
        <!-- 菜单页 -->
        <div class="content-1" v-if="number === 0">
          <div class="content-item" v-for="item in 12" :key="item + 'img1'" @click="chooseItem(item)">
            <img class="item-bg" src="/static/game_assets/game92/item_bg.png" />
            <img :class="['item-img', 'item-img-' + item]" :src="`/static/game_assets/game92/item_${item}.png`" />
            <img :class="['item-num', item > 9 && 'item-num-right']" :src="`/static/game_assets/game92/num_${item}.png`" />
          </div>
        </div>

        <!-- 训练 -->
        <div class="content-2" v-if="number === 1">
          <img class="content-bg" src="/static/game_assets/game92/content_bg.png" />

          <img class="content-btn1" src="/static/game_assets/game92/btn1.png" @click="goback" />
          <img class="content-btn2" src="/static/game_assets/game92/btn2.png" @click="reset" />

          <div class="content-left">
            <div :class="['left-item', 'left-item-' + current]">
              <img :class="[`left-item-bg`]" :src="`/static/game_assets/game92/item_bg_${current}.png`" />
              <draggable :class="[`left-main`, `left-main-${item}`]" :style="{'z-index': choose === item ? 99 : 0}" v-for="item in 7" :key="item + 'item1'" v-model="dragItemLeft[item-1]" :group="group2" @add="dragAdd(item)">
                <img v-for="it in dragItemLeft[item-1]" :key="it.name + 'bg1'" :class="[`left-img`, `left-img-${item}`]" :src="`/static/game_assets/game92/item_${current}_${it.name}.png`" />
              </draggable>
            </div>
          </div>

          <div class="content-right">
            <div :class="['right-item', 'right-item-' + current]">
              <draggable :class="['right-img', 'right-img-' + item]" :style="{'z-index': choose === item ? 99 : 0}" v-for="item in 9" :key="item + 'item'" v-model="dragItemRight[item-1]" :group="group1" @start="dragStart(item)">
                <img v-for="it in dragItemRight[item-1]" :key="it.name + 'img'" :src="`/static/game_assets/game92/item_${current}_${it.name}.png`" />
              </draggable>
            </div>
          </div>
        </div>
      </div>

      <div :class="['sucess', isShowCorrect ? 'translate-top' : '']" :style="{'opacity': isShowCorrect ? 1 : 0}">
        <img class="img" src="/static/game_assets/game29/hot_balloon.png">
      </div>
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'

export default {
  name: 'game92',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    draggable
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isPlay: false,
      isShowCorrect: false,
      isCanOpen: true,

      timer: null,
      current: 0,
      choose: 0,
      dragItemLeft: [[], [], [], [], [], [], []],
      dragItemRight: [],
      group1: {
        name: 'itemList',
        pull: true,
        put: false,
        sort: false
      },
      group2: {
        name: 'itemList',
        pull: false,
        put: true,
        sort: false
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
    goHome () {
      this.pause()
      this.$router.go(-1)
    },

    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start () {
      this.status = 3
      // this.timing()
      this.play()
    },

    startProcess () {
      for (let i = 0; i < 7; i++) {
        this.dragItemRight.push([{
          name: i + 1,
          startIndex: i,
        }])
      }
    },

    dragStart (item) {
      this.choose = this.dragItemRight[item - 1][0].name
    },

    goback () {
      if (this.isShowCorrect) return
      this.number = 0
      this.choose = 0
      this.current = 0
      this.dragItemLeft = [[], [], [], [], [], [], []]
      this.dragItemRight = []
    },

    reset () {
      if (this.isShowCorrect) return
      this.succesNum = 0
      this.errorNum = 0
      this.choose = 0
      this.dragItemLeft = [[], [], [], [], [], [], []]
      this.dragItemRight = []
      this.startProcess()
    },

    dragAdd (index) {
      if (this.choose !== index) {
        if (this.dragItemLeft[index - 1].length > 1) {
          const item = this.dragItemLeft[index - 1].filter(item => item.name !== index)[0]
          this.dragItemLeft[index - 1] = this.dragItemLeft[index - 1].filter(item => item.name === index)
          this.dragItemRight[item.startIndex].push(item)
        } else {
          const item = this.dragItemLeft[index - 1].pop()
          this.dragItemRight[item.startIndex].push(item)
        }
        this.errorNum++
      } else {
        this.succesNum++
      }

      const list = this.dragItemLeft.filter(item => item.length)
      if (list.length >= 7) {
        this.isShowCorrect = true
        this.timer = setTimeout(() => {
          this.isShowCorrect = false
          this.submit()
        }, 2200)
      }
    },

    chooseItem (item) {
      this.startProcess()
      this.current = item
      this.number = 1
    },

    submit () {
      this.pause()
      this.isStop = true
      this.store = 100 - 5 * this.errorNum > 0 ? 100 - 5 * this.errorNum : 0
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.choose = 0
      this.current = 0
      this.dragItemLeft = [[], [], [], [], [], [], []]
      this.dragItemRight = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game92-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game126/bg.png');
  }

  .game-synopsis {
    position: relative;
    width: 1823px * 0.7;
    height: 644px * 0.7;
    margin-top: 40px * 0.7;
    margin-left: 15px * 0.7;

    .img1 {
      position: absolute;
      top: 0;
      left: 0;
      width: 1823px * 0.7;
    }

    .img2 {
      position: absolute;
      top: 18px * 0.7;
      right: 283px * 0.7;
      width: 187px * 0.7;
    }
  }

  .setting-page {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .footer {
      position: absolute;
      bottom: 40px * 0.7;
      width: 734px * 0.7;
      display: inline-flex;
      justify-content: space-between;

      .img {
        width: 268px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    .top {
      position: absolute;
      top: 40px * 0.7;
      left: 105px * 0.7;
      width: 175px * 0.7;
      display: flex;
      justify-content: space-between;
      z-index: 1;

      .top-icon {
        width: 80px * 0.7;
        cursor: pointer;
      }
    }

    .content {
      position: relative;
      width: 1920px * 0.7;
      height: 1080px * 0.7;
      display: flex;
      justify-content: center;
      align-items: center;

      .content-1 {
        width: 1795px * 0.7;
        height: 835px * 0.7;
        margin-top: 65px * 0.7;
        margin-right: 20px * 0.7;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;

        .content-item {
          position: relative;
          width: 466px * 0.7;
          height: 242px * 0.7;
          margin-left: -23px * 0.7;
          padding-top: 10px * 0.7;
          padding-right: 60px * 0.7;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 466px * 0.7;
          }

          .item-img {
            position: relative;
          }

          .item-img-1,
          .item-img-2,
          .item-img-3 {
            width: 102px * 0.7;
          }

          .item-img-4 {
            width: 122px * 0.7;
          }

          .item-img-5 {
            width: 174px * 0.7;
          }

          .item-img-6 {
            width: 154px * 0.7;
          }

          .item-img-7 {
            width: 130px * 0.7;
          }

          .item-img-8 {
            width: 155px * 0.7;
          }

          .item-img-9 {
            width: 159px * 0.7;
          }

          .item-img-10 {
            width: 97px * 0.7;
          }

          .item-img-11 {
            width: 73px * 0.7;
          }

          .item-img-12 {
            width: 151px * 0.7;
          }

          .item-num {
            position: absolute;
            bottom: 73px * 0.7;
            right: 50px * 0.7;
            height: 69px * 0.7;
          }

          .item-num-right {
            right: 25px * 0.7;
          }
        }
      }

      .content-2 {
        position: relative;
        width: 1849px * 0.7;
        height: 916px * 0.7;
        margin-bottom: 54px * 0.7;
        margin-left: 17px * 0.7;
        padding: 220px * 0.7 235px * 0.7 56px * 0.7 255px * 0.7;
        display: flex;

        .content-bg {
          position: absolute;
          top: 84px * 0.7;
          left: 0;
          width: 1849px * 0.7;
        }

        .content-btn1 {
          position: absolute;
          top: 0;
          right: 50px * 0.7;
          width: 110px * 0.7;
          cursor: pointer;
        }

        .content-btn2 {
          position: absolute;
          top: 122px * 0.7;
          right: 50px * 0.7;
          width: 110px * 0.7;
          cursor: pointer;
        }

        .content-left {
          position: relative;
          width: 955px * 0.7;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;

          .left-item {
            position: relative;

            .left-item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
            }

            .left-main {
              position: absolute;

              img {
                width: calc(100% - 3px * 0.7);
              }
            }
          }

          .left-item-1 {
            width: 337px * 0.7;
            height: 580px * 0.7;

            .left-main-1 {
              position: absolute;
              top: 2px * 0.7;
              left: 155px * 0.7;
              width: 112px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-2 {
              position: absolute;
              top: 125px * 0.7;
              left: 94px * 0.7;
              width: 80px * 0.7;
              height: 159px * 0.7;
            }

            .left-main-3 {
              position: absolute;
              top: 120px * 0.7;
              left: 176px * 0.7;
              width: 159px * 0.7;
              height: 160px * 0.7;
            }

            .left-main-4 {
              position: absolute;
              top: 240px * 0.7;
              left: 66px * 0.7;
              width: 159px * 0.7;
              height: 159px * 0.7;
            }

            .left-main-5 {
              position: absolute;
              top: 373px * 0.7;
              left: 2px * 0.7;
              width: 79px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-6 {
              position: absolute;
              top: 468px * 0.7;
              left: 170px * 0.7;
              width: 56px * 0.7;
              height: 112px * 0.7;
            }

            .left-main-7 {
              position: absolute;
              top: 342px * 0.7;
              left: 229px * 0.7;
              width: 80px * 0.7;
              height: 159px * 0.7;
            }
          }

          .left-item-2 {
            width: 334px * 0.7;
            height: 328px * 0.7;

            .left-main-1 {
              position: absolute;
              top: 45px * 0.7;
              left: 1.5px * 0.7;
              width: 80px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-2 {
              position: absolute;
              top: 130px * 0.7;
              left: 2px * 0.7;
              width: 80px * 0.7;
              height: 81px * 0.7;
            }

            .left-main-3 {
              position: absolute;
              top: 215px * 0.7;
              left: 2px * 0.7;
              width: 80px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-4 {
              position: absolute;
              top: 44px * 0.7;
              left: 88px * 0.7;
              width: 80px * 0.7;
              height: 159px * 0.7;
            }

            .left-main-5 {
              position: absolute;
              top: 131px * 0.7;
              left: 88px * 0.7;
              width: 80px * 0.7;
              height: 159px * 0.7;
            }

            .left-main-6 {
              position: absolute;
              top: 3px * 0.7;
              left: 173px * 0.7;
              width: 160px * 0.7;
              height: 159px * 0.7;
            }

            .left-main-7 {
              position: absolute;
              top: 169px * 0.7;
              left: 173px * 0.7;
              width: 160px * 0.7;
              height: 159px * 0.7;
            }
          }

          .left-item-3 {
            width: 330px * 0.7;
            height: 429px * 0.7;

            .left-main-1 {
              position: absolute;
              top: 2px * 0.7;
              left: 23px * 0.7;
              width: 57px * 0.7;
              height: 112px * 0.7;
            }

            .left-main-2 {
              position: absolute;
              top: 2px * 0.7;
              left: 85px * 0.7;
              width: 57px * 0.7;
              height: 112px * 0.7;
            }

            .left-main-3 {
              position: absolute;
              top: 66px * 0.7;
              left: 28px * 0.7;
              width: 112px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-4 {
              position: absolute;
              top: 185px * 0.7;
              left: 2px * 0.7;
              width: 159px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-5 {
              position: absolute;
              top: 266px * 0.7;
              left: 2px * 0.7;
              width: 159px * 0.7;
              height: 159px * 0.7;
            }

            .left-main-6 {
              position: absolute;
              top: 270px * 0.7;
              right: 163px * 0.7;
              width: 159px * 0.7;
              height: 159px * 0.7;
            }

            .left-main-7 {
              position: absolute;
              bottom: 1px * 0.7;
              right: 1px * 0.7;
              width: 159px * 0.7;
              height: 80px * 0.7;
            }
          }

          .left-item-4 {
            width: 397px * 0.7;
            height: 231px * 0.7;

            .left-main-1 {
              position: absolute;
              top: 2px * 0.7;
              left: 2px * 0.7;
              width: 159px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-2 {
              position: absolute;
              top: 2px * 0.7;
              left: 86px * 0.7;
              width: 225px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-3 {
              position: absolute;
              top: 5px * 0.7;
              left: 235px * 0.7;
              width: 79px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-4 {
              position: absolute;
              top: 2px * 0.7;
              right: 1px * 0.7;
              width: 80px * 0.7;
              height: 159px * 0.7;
            }

            .left-main-5 {
              position: absolute;
              top: 85px * 0.7;
              left: 2px * 0.7;
              width: 80px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-6 {
              position: absolute;
              top: 119px * 0.7;
              left: 86px * 0.7;
              width: 225px * 0.7;
              height: 112px * 0.7;
            }

            .left-main-7 {
              position: absolute;
              top: 86px * 0.7;
              right: 2px * 0.7;
              width: 80px * 0.7;
              height: 80px * 0.7;
            }
          }

          .left-item-5 {
            width: 567px * 0.7;
            height: 344px * 0.7;

            .left-main-1 {
              position: absolute;
              top: 3px * 0.7;
              left: 2px * 0.7;
              width: 57px * 0.7;
              height: 112px * 0.7;
            }

            .left-main-2 {
              position: absolute;
              top: 3px * 0.7;
              left: 66px * 0.7;
              width: 56px * 0.7;
              height: 112px * 0.7;
            }

            .left-main-3 {
              position: absolute;
              top: 67px * 0.7;
              right: 449px * 0.7;
              width: 112px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-4 {
              position: absolute;
              top: 184px * 0.7;
              left: 60px * 0.7;
              width: 160px * 0.7;
              height: 159px * 0.7;
            }

            .left-main-5 {
              position: absolute;
              top: 184px * 0.7;
              left: 152px * 0.7;
              width: 159px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-6 {
              position: absolute;
              top: 185px * 0.7;
              left: 244px * 0.7;
              width: 160px * 0.7;
              height: 159px * 0.7;
            }

            .left-main-7 {
              position: absolute;
              top: 104px * 0.7;
              right: -1px * 0.7;
              width: 159px * 0.7;
              height: 80px * 0.7;
            }
          }

          .left-item-6 {
            width: 499px * 0.7;
            height: 384px * 0.7;

            .left-main-1 {
              position: absolute;
              top: 3px * 0.7;
              left: 223px * 0.7;
              width: 113px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-2 {
              position: absolute;
              top: 106px * 0.7;
              left: 2px * 0.7;
              width: 80px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-3 {
              position: absolute;
              top: 109px * 0.7;
              left: 185px * 0.7;
              width: 112px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-4 {
              position: absolute;
              top: 142px * 0.7;
              left: 85px * 0.7;
              width: 160px * 0.7;
              height: 159px * 0.7;
            }

            .left-main-5 {
              position: absolute;
              bottom: 0;
              left: 86px * 0.7;
              width: 80px * 0.7;
              height: 79px * 0.7;
            }

            .left-main-6 {
              position: absolute;
              bottom: 0;
              right: 161px * 0.7;
              width: 159px * 0.7;
              height: 159px * 0.7;
            }

            .left-main-7 {
              position: absolute;
              bottom: 80px * 0.7;
              right: -1px * 0.7;
              width: 159px * 0.7;
              height: 80px * 0.7;
            }
          }

          .left-item-7 {
            width: 423px * 0.7;
            height: 352px * 0.7;

            .left-main-1 {
              position: absolute;
              top: 3px * 0.7;
              left: 3px * 0.7;
              width: 113px * 0.7;
              height: 112px * 0.7;
            }

            .left-main-2 {
              position: absolute;
              top: 59px * 0.7;
              left: 145px * 0.7;
              width: 113px * 0.7;
              height: 56px * 0.7;
            }

            .left-main-3 {
              position: absolute;
              top: 125px * 0.7;
              left: 59px * 0.7;
              width: 112px * 0.7;
              height: 225px * 0.7;
            }

            .left-main-4 {
              position: absolute;
              top: 119px * 0.7;
              left: 63px * 0.7;
              width: 159px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-5 {
              position: absolute;
              bottom: 61px * 0.7;
              right: 74px * 0.7;
              width: 225px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-6 {
              position: absolute;
              bottom: 1px * 0.7;
              left: 62px * 0.7;
              width: 169px * 0.7;
              height: 57px * 0.7;
            }

            .left-main-7 {
              position: absolute;
              bottom: 0;
              right: 0;
              width: 112px * 0.7;
              height: 57px * 0.7;
            }
          }

          .left-item-8 {
            width: 505px * 0.7;
            height: 469px * 0.7;

            .left-main-1 {
              position: absolute;
              top: 3px * 0.7;
              right: 189px * 0.7;
              width: 113px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-2 {
              position: absolute;
              top: 119px * 0.7;
              right: 133px * 0.7;
              width: 225px * 0.7;
              height: 114px * 0.7;
            }

            .left-main-3 {
              position: absolute;
              top: 236px * 0.7;
              right: 132px * 0.7;
              width: 225px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-4 {
              position: absolute;
              bottom: 60px * 0.7;
              left: 86px * 0.7;
              width: 169px * 0.7;
              height: 57px * 0.7;
            }

            .left-main-5 {
              position: absolute;
              bottom: 37px * 0.7;
              right: 81px * 0.7;
              width: 159px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-6 {
              position: absolute;
              top: 288px * 0.7;
              right: 0;
              width: 112px * 0.7;
              height: 57px * 0.7;
            }

            .left-main-7 {
              position: absolute;
              bottom: 0;
              left: 2px * 0.7;
              width: 112px * 0.7;
              height: 57px * 0.7;
            }
          }

          .left-item-9 {
            width: 518px * 0.7;
            height: 297px * 0.7;

            .left-main-1 {
              position: absolute;
              top: 3px * 0.7;
              left: 174px * 0.7;
              width: 112px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-2 {
              position: absolute;
              top: 118px * 0.7;
              left: 174px * 0.7;
              width: 112px * 0.7;
              height: 57px * 0.7;
            }

            .left-main-3 {
              position: absolute;
              bottom: 0;
              left: 2px * 0.7;
              width: 159px * 0.7;
              height: 81px * 0.7;
            }

            .left-main-4 {
              position: absolute;
              bottom: 0;
              left: 91px * 0.7;
              width: 79px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-5 {
              position: absolute;
              top: 181px * 0.7;
              left: 173px * 0.7;
              width: 112px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-6 {
              position: absolute;
              bottom: 0;
              right: 116px * 0.7;
              width: 225px * 0.7;
              height: 114px * 0.7;
            }

            .left-main-7 {
              position: absolute;
              top: 180px * 0.7;
              right: 0;
              width: 225px * 0.7;
              height: 113px * 0.7;
            }
          }

          .left-item-10 {
            width: 317px * 0.7;
            height: 290px * 0.7;

            .left-main-1 {
              position: absolute;
              top: 3px * 0.7;
              left: 83px * 0.7;
              width: 80px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-2 {
              position: absolute;
              top: 89px * 0.7;
              left: 3px * 0.7;
              width: 159px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-3 {
              position: absolute;
              top: 56px * 0.7;
              right: -1px * 0.7;
              width: 225px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-4 {
              position: absolute;
              bottom: 0;
              left: 35px * 0.7;
              width: 112px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-5 {
              position: absolute;
              bottom: 0;
              left: 45px * 0.7;
              width: 225px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-6 {
              position: absolute;
              right: 40px * 0.7;
              bottom: 56px * 0.7;
              width: 113px * 0.7;
              height: 57px * 0.7;
            }

            .left-main-7 {
              position: absolute;
              bottom: -2px * 0.7;
              right: 36px * 0.7;
              width: 57px * 0.7;
              height: 112px * 0.7;
            }
          }

          .left-item-11 {
            width: 238px * 0.7;
            height: 242px * 0.7;

            .left-main-1 {
              position: absolute;
              top: 2px * 0.7;
              left: 4px * 0.7;
              width: 225px * 0.7;
              height: 112px * 0.7;
            }

            .left-main-2 {
              position: absolute;
              top: 6px * 0.7;
              left: 2px * 0.7;
              width: 113px * 0.7;
              height: 225px * 0.7;
            }

            .left-main-3 {
              position: absolute;
              bottom: 0;
              left: 2px * 0.7;
              width: 112px * 0.7;
              height: 57px * 0.7;
            }

            .left-main-4 {
              position: absolute;
              top: 125px * 0.7;
              left: 63px * 0.7;
              width: 113px * 0.7;
              height: 113px * 0.7;
            }

            .left-main-5 {
              position: absolute;
              top: 70px * 0.7;
              right: 58px * 0.7;
              width: 57px * 0.7;
              height: 112px * 0.7;
            }

            .left-main-6 {
              position: absolute;
              top: 7px * 0.7;
              right: -1px * 0.7;
              width: 57px * 0.7;
              height: 169px * 0.7;
            }

            .left-main-7 {
              position: absolute;
              bottom: 1px * 0.7;
              right: 0;
              width: 113px * 0.7;
              height: 112px * 0.7;
            }
          }

          .left-item-12 {
            width: 496px * 0.7;
            height: 296px * 0.7;

            .left-main-1 {
              position: absolute;
              top: 3.5px * 0.7;
              left: 3.5px * 0.7;
              width: 79px * 0.7;
              height: 79px * 0.7;
            }

            .left-main-2 {
              position: absolute;
              top: 48px * 0.7;
              left: 89px * 0.7;
              width: 80px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-3 {
              position: absolute;
              top: 3px * 0.7;
              left: 175px * 0.7;
              width: 80px * 0.7;
              height: 79px * 0.7;
            }

            .left-main-4 {
              position: absolute;
              bottom: 0;
              left: 88px * 0.7;
              width: 160px * 0.7;
              height: 160px * 0.7;
            }

            .left-main-5 {
              position: absolute;
              bottom: 80px * 0.7;
              left: 181px * 0.7;
              width: 159px * 0.7;
              height: 80px * 0.7;
            }

            .left-main-6 {
              position: absolute;
              bottom: 0;
              right: 65px * 0.7;
              width: 159px * 0.7;
              height: 160px * 0.7;
            }

            .left-main-7 {
              position: absolute;
              bottom: 79px * 0.7;
              right: 0;
              width: 57px * 0.7;
              height: 169px * 0.7;
            }
          }
        }

        .content-right {
          flex: 1;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;

          .right-item {
            position: relative;

            .right-img {
              position: absolute;
              cursor: pointer;

              img {
                width: 100%;
              }
            }
          }

          .right-item-1 {
            width: 250px * 0.7;
            height: 365px * 0.7;

            .right-img-1 {
              bottom: 14px * 0.7;
              left: 87px * 0.7;
              width: 112px * 0.7;
            }

            .right-img-2 {
              top: 68px * 0.7;
              right: 0;
              width: 80px * 0.7;
            }

            .right-img-3 {
              bottom: 0;
              left: 0;
              width: 159px * 0.7;
            }

            .right-img-4 {
              top: 32px * 0.7;
              left: 0;
              width: 159px * 0.7;
            }

            .right-img-5 {
              top: 32px * 0.7;
              right: 0;
              width: 79px * 0.7;
            }

            .right-img-6 {
              bottom: 70px * 0.7;
              right: 0;
              width: 56px * 0.7;
            }

            .right-img-7 {
              top: 0;
              left: 0;
              width: 80px * 0.7;
            }
          }

          .right-item-2 {
            width: 265px * 0.7;
            height: 383px * 0.7;

            .right-img-1 {
              bottom: 0;
              left: 72px * 0.7;
              width: 80px * 0.7;
            }

            .right-img-2 {
              bottom: 102px * 0.7;
              right: 0;
              width: 80px * 0.7;
            }

            .right-img-3 {
              top: 99px * 0.7;
              right: 0;
              width: 80px * 0.7;
            }

            .right-img-4 {
              top: 217px * 0.7;
              left: 94px * 0.7;
              width: 80px * 0.7;
            }

            .right-img-5 {
              top: 0;
              left: 63px * 0.7;
              width: 80px * 0.7;
            }

            .right-img-6 {
              top: 34px * 0.7;
              left: 0;
              width: 160px * 0.7;
            }

            .right-img-7 {
              top: 203px * 0.7;
              left: 0;
              width: 160px * 0.7;
            }
          }

          .right-item-3 {
            width: 226px * 0.7;
            height: 435px * 0.7;

            .right-img-1 {
              top: 144px * 0.7;
              left: 0;
              width: 57px * 0.7;
            }

            .right-img-2 {
              bottom: 59px * 0.7;
              right: 0;
              width: 57px * 0.7;
            }

            .right-img-3 {
              top: 95px * 0.7;
              left: 33px * 0.7;
              width: 112px * 0.7;
            }

            .right-img-4 {
              top: 0;
              left: 36px * 0.7;
              width: 159px * 0.7;
            }

            .right-img-5 {
              left: 0;
              bottom: 0;
              width: 159px * 0.7;
            }

            .right-img-6 {
              bottom: 180px * 0.7;
              right: 0;
              width: 159px * 0.7;
            }

            .right-img-7 {
              bottom: 0;
              left: 28px * 0.7;
              width: 159px * 0.7;
            }
          }

          .right-item-4 {
            width: 225px * 0.7;
            height: 390px * 0.7;

            .right-img-1 {
              top: 125px * 0.7;
              left: 30px * 0.7;
              width: 159px * 0.7;
            }

            .right-img-2 {
              top: 0;
              left: 0;
              width: 225px * 0.7;
            }

            .right-img-3 {
              right: 24px * 0.7;
              top: 38px * 0.7;
              width: 79px * 0.7;
            }

            .right-img-4 {
              bottom: 10px * 0.7;
              right: 0;
              width: 80px * 0.7;
            }

            .right-img-5 {
              bottom: 83px * 0.7;
              left: 0;
              width: 80px * 0.7;
            }

            .right-img-6 {
              left: 0;
              bottom: 0;
              width: 225px * 0.7;
            }

            .right-img-7 {
              top: 40px * 0.7;
              left: 14px * 0.7;
              width: 80px * 0.7;
            }
          }

          .right-item-5 {
            width: 263px * 0.7;
            height: 383px * 0.7;

            .right-img-1 {
              bottom: 26px * 0.7;
              right: 0;
              width: 57px * 0.7;
            }

            .right-img-2 {
              top: 89px * 0.7;
              right: 33px * 0.7;
              width: 56px * 0.7;
            }

            .right-img-3 {
              bottom: 30px * 0.7;
              left: 0;
              width: 112px * 0.7;
            }

            .right-img-4 {
              top: 0;
              left: 26px * 0.7;
              width: 160px * 0.7;
            }

            .right-img-5 {
              left: 50px * 0.7;
              bottom: 0;
              width: 159px * 0.7;
            }

            .right-img-6 {
              bottom: 48px * 0.7;
              left: 31px * 0.7;
              width: 160px * 0.7;
            }

            .right-img-7 {
              top: 88px * 0.7;
              left: 41px * 0.7;
              width: 159px * 0.7;
            }
          }

          .right-item-6 {
            width: 249px * 0.7;
            height: 418px * 0.7;

            .right-img-1 {
              top: 151px * 0.7;
              left: 26px * 0.7;
              width: 113px * 0.7;
            }

            .right-img-2 {
              top: 30px * 0.7;
              left: 0;
              width: 80px * 0.7;
            }

            .right-img-3 {
              top: 0;
              right: 27px * 0.7;
              width: 112px * 0.7;
            }

            .right-img-4 {
              left: 21px * 0.7;
              bottom: 0;
              width: 160px * 0.7;
            }

            .right-img-5 {
              top: 128px * 0.7;
              left: 0;
              width: 80px * 0.7;
            }

            .right-img-6 {
              bottom: 131px * 0.7;
              right: 0;
              width: 159px * 0.7;
            }

            .right-img-7 {
              bottom: 48px * 0.7;
              right: 10px * 0.7;
              width: 159px * 0.7;
            }
          }

          .right-item-7 {
            width: 244px * 0.7;
            height: 389px * 0.7;

            .right-img-1 {
              top: 209px * 0.7;
              right: 0;
              width: 113px * 0.7;
            }

            .right-img-2 {
              top: 0;
              right: 0;
              width: 113px * 0.7;
            }

            .right-img-3 {
              bottom: 0;
              left: 0;
              width: 112px * 0.7;
            }

            .right-img-4 {
              top: 155px * 0.7;
              left: 34px * 0.7;
              width: 159px * 0.7;
            }

            .right-img-5 {
              top: 14px * 0.7;
              right: 10px * 0.7;
              width: 225px * 0.7;
            }

            .right-img-6 {
              bottom: 0;
              left: 33px * 0.7;
              width: 169px * 0.7;
            }

            .right-img-7 {
              top: 0;
              left: 0;
              width: 112px * 0.7;
            }
          }

          .right-item-8 {
            width: 281px * 0.7;
            height: 420px * 0.7;

            .right-img-1 {
              bottom: 12px * 0.7;
              left: 0;
              width: 113px * 0.7;
            }

            .right-img-2 {
              bottom: 82px * 0.7;
              left: 16px * 0.7;
              width: 225px * 0.7;
            }

            .right-img-3 {
              top: 0;
              left: 23px * 0.7;
              width: 225px * 0.7;
            }

            .right-img-4 {
              top: 156px * 0.7;
              right: 0;
              width: 169px * 0.7;
            }

            .right-img-5 {
              top: 135px * 0.7;
              left: 0;
              width: 159px * 0.7;
            }

            .right-img-6 {
              bottom: 89px * 0.7;
              right: 19px * 0.7;
              width: 112px * 0.7;
            }

            .right-img-7 {
              bottom: 0;
              right: 29px * 0.7;
              width: 112px * 0.7;
            }
          }

          .right-item-9 {
            width: 284px * 0.7;
            height: 365px * 0.7;

            .right-img-1 {
              top: 90px * 0.7;
              right: 0;
              width: 112px * 0.7;
            }

            .right-img-2 {
              top: 39px * 0.7;
              left: 0;
              width: 112px * 0.7;
            }

            .right-img-3 {
              top: 0;
              left: 45px * 0.7;
              width: 159px * 0.7;
            }

            .right-img-4 {
              top: 0;
              right: 50px * 0.7;
              width: 79px * 0.7;
            }

            .right-img-5 {
              top: 108px * 0.7;
              left: 30px * 0.7;
              width: 112px * 0.7;
            }

            .right-img-6 {
              bottom: 125px * 0.7;
              left: 28px * 0.7;
              width: 225px * 0.7;
            }

            .right-img-7 {
              bottom: 0;
              left: 28px * 0.7;
              width: 225px * 0.7;
            }
          }

          .right-item-10 {
            width: 310px * 0.7;
            height: 385px * 0.7;

            .right-img-1 {
              bottom: 34px * 0.7;
              right: 33px * 0.7;
              width: 80px * 0.7;
            }

            .right-img-2 {
              bottom: 174px * 0.7;
              left: 0;
              width: 159px * 0.7;
            }

            .right-img-3 {
              bottom: 130px * 0.7;
              right: 27px * 0.7;
              width: 225px * 0.7;
            }

            .right-img-4 {
              bottom: 0;
              left: 72px * 0.7;
              width: 112px * 0.7;
            }

            .right-img-5 {
              top: 0;
              left: 49px * 0.7;
              width: 225px * 0.7;
            }

            .right-img-6 {
              top: 14px * 0.7;
              right: 0;
              width: 113px * 0.7;
            }

            .right-img-7 {
              top: 107px * 0.7;
              right: 30px * 0.7;
              width: 57px * 0.7;
            }
          }

          .right-item-11 {
            width: 255px * 0.7;
            height: 411px * 0.7;

            .right-img-1 {
              bottom: 0;
              right: 0;
              width: 225px * 0.7;
            }

            .right-img-2 {
              top: 0;
              left: 33px * 0.7;
              width: 113px * 0.7;
            }

            .right-img-3 {
              bottom: 0;
              left: 0;
              width: 112px * 0.7;
            }

            .right-img-4 {
              top: 0;
              left: 104px * 0.7;
              width: 113px * 0.7;
            }

            .right-img-5 {
              top: 72px * 0.7;
              right: 21px * 0.7;
              width: 57px * 0.7;
            }

            .right-img-6 {
              left: 105px * 0.7;
              bottom: 117px * 0.7;
              width: 57px * 0.7;
            }

            .right-img-7 {
              bottom: 116px * 0.7;
              right: 0;
              width: 113px * 0.7;
            }
          }

          .right-item-12 {
            width: 257px * 0.7;
            height: 363px * 0.7;

            .right-img-1 {
              top: 167px * 0.7;
              right: 0;
              width: 79px * 0.7;
            }

            .right-img-2 {
              bottom: 0;
              right: 0;
              width: 80px * 0.7;
            }

            .right-img-3 {
              top: 15px * 0.7;
              left: 0;
              width: 80px * 0.7;
            }

            .right-img-4 {
              bottom: 0;
              left: 28px * 0.7;
              width: 160px * 0.7;
            }

            .right-img-5 {
              top: 104px * 0.7;
              left: 0;
              width: 159px * 0.7;
            }

            .right-img-6 {
              top: 24px * 0.7;
              left: 31px * 0.7;
              width: 159px * 0.7;
            }

            .right-img-7 {
              top: 0;
              right: 0;
              width: 57px * 0.7;
            }
          }
        }
      }
    }

    .sucess {
      position: absolute;
      bottom: -762px * 0.7;
      width: 774px * 0.7;
      height: 762px * 0.7;
      transition: transform 2s ease-in;
      z-index: 100;

      .img {
        width: 774px * 0.7;
        height: 762px * 0.7;
      }
    }

    .translate-top {
      transform: translateY(-2442px * 0.7);
    }
  }

  .black-bg {
    background: rgba(0, 0, 0, 0.33);
  }
}
</style>