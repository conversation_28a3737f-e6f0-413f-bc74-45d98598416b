<template>
  <div class="game101-page">
    <div class="page-bg" :class="status === 1 ? 'page-bg1': 'page-bg2'"></div>
    <audio v-if="musicUrl" ref="music" muted style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib @start="status = 2" @challenge="start" v-if="status < 3">
      <img class="title-img" src="/static/game_assets/game101/text.png" />
      <img class="icon-img" src="/static/game_assets/game101/icon.png" />
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />

      <div class="content">
        <img v-if="number > 0" class="left-btn" src="/static/game_assets/game101/left_btn.png" @click="toPrevious">
        <img v-if="number < 4" class="right-btn" src="/static/game_assets/game101/right_btn.png" @click="toNext">

        <img class="content-bg" src="/static/game_assets/game101/content_bg.png" />
        <img class="item-bg" :src="`/static/game_assets/game101/item_${number + 1}.png`" />
        <img class="item-icon" src="/static/game_assets/game101/icon.png" />
        <div class="content-name">
          <img class="name-bg" src="/static/game_assets/game101/name_bg.png" />
          <span class="name-text">{{name}}</span>
        </div>
      </div>
    </div>
    <!-- 
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm> -->
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'

export default {
  name: 'game101',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/correct_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      // TODO: 动物音频
      audioList: [],
      questionNum: 0,
      answer: [],

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    name () {
      if (this.number === 0) {
        return '鹳科'
      }

      if (this.number === 1) {
        return '河狸'
      }

      if (this.number === 2) {
        return '美洲野牛'
      }

      if (this.number === 3) {
        return '水牛'
      }

      if (this.number === 4) {
        return '羚羊'
      }
    }
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
    goHome () {
      this.pause()
      this.$router.go(-1)
    },

    play () {
      this.$refs.music.load()
      this.$refs.music.play()
    },

    pause () {
      this.$refs.music.pause()
    },

    start () {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      this.musicUrl = this.audioList[this.number]
      this.play()
    },

    toPrevious () {
      this.number--
      // this.musicUrl = '/static/game_assets/audio/error_audio.mp3'
      this.play()
    },

    toNext () {
      this.number++
      // this.musicUrl = '/static/game_assets/audio/correct_audio.mp3'
      this.musicUrl = this.audioList[this.number]
      this.play()
    },
  }
}
</script>

<style lang="scss" scoped>
.game101-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background: url('/static/game_assets/game101/bg1.png') center center no-repeat;
    background-size: cover;
  }

  .page-bg2 {
    background: url('/static/game_assets/game101/bg2.png') center center no-repeat;
    background-size: cover;
  }

  .title-img {
    position: absolute;
    top: 278px * 0.7;
    left: 625px * 0.7;
    width: 606px * 0.7;
  }

  .icon-img {
    position: absolute;
    top: 501px * 0.7;
    left: 315px * 0.7;
    width: 1198px * 0.7;
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .top-icon {
      position: absolute;
      top: 40px * 0.7;
      left: 105px * 0.7;
      width: 80px * 0.7;
      cursor: pointer;
    }

    .content {
      position: relative;
      width: 100%;
      height: 833px * 0.7;
      margin-top: 117px * 0.7;
      padding-left: 371px * 0.7;
      padding-right: 421px * 0.7;

      .left-btn {
        position: absolute;
        top: 334px * 0.7;
        left: 140px * 0.7;
        width: 131px * 0.7;
        cursor: pointer;
      }

      .right-btn {
        position: absolute;
        top: 334px * 0.7;
        right: 190px * 0.7;
        width: 131px * 0.7;
        cursor: pointer;
      }

      .content-bg {
        position: absolute;
        top: 0;
        left: 371px * 0.7;
        width: 1128px * 0.7;
      }

      .item-bg {
        position: absolute;
        top: 97px * 0.7;
        left: 475px * 0.7;
        width: 922px * 0.7;
      }

      .item-icon {
        position: absolute;
        top: 626px * 0.7;
        left: 538px * 0.7;
        width: 702px * 0.7;
      }

      .content-name {
        position: absolute;
        top: 10px * 0.7;
        left: 1303px * 0.7;
        width: 136px * 0.7;
        height: 444px * 0.7;
        padding: 0 28px * 0.7 65px * 0.7 28px * 0.7;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .name-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 136px * 0.7;
        }

        .name-text {
          position: relative;
          display: inline-block;
          font-size: 76px * 0.7;
          line-height: 85px * 0.7;
          color: #fefefe;
        }
      }
    }
  }
}
</style>