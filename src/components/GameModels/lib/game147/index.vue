<template>
  <div class="game67-page">
    <div class="page-bg"></div>
    <settingPageLib title="挑战游戏说明" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">数字配对</p>
        <p class="synopsis-content">请根据提示说出对应物品，由训练者/治疗师判定并选择对应选项</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="title">第{{currentImage}}个物品是什么</div>

      <div class="content">

        <div class="image-item" v-for="it,index in imageArr" :key="index">
          <img class="img" :src="it" alt="">
        </div>
      </div>

      <div class="footer">
        <div class="left">
          <img class="img" src="/static/game_assets/game147/correct.png" @click="handleClick(true)" />
          <img class="img" src="/static/game_assets/game147/error.png" @click="handleClick(false)" />
        </div>
        <div class="right">
          <div class="store right-item">
            题目分数：{{store}}
          </div>
          <div class="number right-item">
            题目数量：{{number}}
          </div>
          <div class="time right-item">
            题目用时：{{timeer}}
          </div>
        </div>
      </div>
    </div>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'

export default {
  name: 'game67',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    draggable
  },

  data () {
    return {
      // 答题页数,设为5页，每页4题，每题5分
      number: 0,
      second: 0,
      timeer: '00:00',
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      level: 2,

      currect: 0,
      currentImage: 0,
      imageArr: [],
      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {

  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
        this.time()
      }, 1000)
    },
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s
      this.timeer = m + ' : ' + s;
      return m + ' : ' + s
    },

    start () {
      this.startProcess()
      this.status = 3
      // this.timing()
    },

    startProcess () {

      let num = api.randomNum(3, 10)
      if (num === this.currect) {
        this.startProcess()
      } else {
        this.currect = num
        this.currentImage = api.randomNum(1, num);
        this.imageArr = [];
        let arr = [];
        for (let i = 1; i <= num; i++) {
          arr.push('/static/game_assets/game147/147-' + i.toString() + '.png');
        }
        this.imageArr = api.shuffle(arr);
      }
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    handleClick (flag) {
      if (flag) {
        this.succesNum++
      } else {
        this.errorNum++
      }
      this.number++
      this.store = 25 * this.succesNum
      if (this.number > 3) {
        this.isStop = true
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: this.level,
          time: this.second,
          totalPoints: this.store
        }
        return
      }
      this.startProcess()
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game67-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg {
    background-image: url('/static/game_assets/game141/bg.png');
  }
  .page-bg1 {
    background-image: url('/static/game_assets/game1/images/bg1.png');
  }
  .page-bg2 {
    background-image: url('/static/game_assets/game1/images/bg2.png');
  }

  .game-synopsis {
    width: 707px * 0.7;
    height: 444px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border: 2px * 0.7 solid #7bbd41;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-content: center;

    .title {
      position: relative;
      padding: 47px * 0.7 0;
      font-size: 42px * 0.7;
      line-height: 59px * 0.7;
      text-align: center;
      font-weight: 500;
    }

    .content {
      position: relative;
      max-width: 1482px * 0.7;
      height: 738px * 0.7;
      // padding-left: 70px * 0.7;
      padding-bottom: 5px * 0.7;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      margin: 100px * 0.7 auto;
      align-items: center;

      .image-item {
        min-width: 122px * 0.7;
        min-height: 122px * 0.7;
        max-width: 237px * 0.7;
        max-height: 237px * 0.7;
        margin-right: 53px * 0.7;

        img {
          min-width: 122px * 0.7;
          min-height: 122px * 0.7;
          max-width: 237px * 0.7;
          max-height: 237px * 0.7;
        }

        &:nth-child(5n) {
          margin-right: 0;
        }
      }
    }

    .footer {
      position: relative;
      display: flex;
      justify-content: space-between;
      width: 1509px * 0.7;
      min-height: 162px * 0.7;
      padding-bottom: 47px * 0.7;
      margin: 0 auto;

      .left {
        width: 566px * 0.7;
        min-height: 115px * 0.7;
        display: inline-flex;
        justify-content: space-between;
      }

      .right {
        display: inline-flex;
        justify-content: space-between;

        .right-item {
          width: auto;
          max-height: 76px * 0.7;
          border: 1px * 0.7 solid #fff;
          border-radius: 4px * 0.7;
          color: #fff;
          font-size: 24px * 0.7;
          padding: 21px * 0.7 43px * 0.7;
          margin-left: 20px * 0.7;
        }
        .store {
        }
        .number {
        }
        .time {
        }
      }

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img2 {
        width: 268px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img3 {
        width: 267px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }
}
</style>