<template>
  <div class="game213-page">
    <div class="page-bg"></div>
    <audio ref="music2" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPageLib title="加法规则" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">本项目考察加法规则。</p>
      </div>
    </settingPageLib>

    <div class="game-content" v-if="status === 3">
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game206/title_bg.png" />
        <span class="title-text">加法规则</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game210/content_bg.png" />
        <div class="content-title">请输入所示手指的总数</div>

        <div class="content-top">
          <div class="num-item">
            <img v-if="currect.num1 <= 5" class="item-img" :src="`/static/game_assets/game210/item_${currect.num1}.png`" />
            <template v-else>
              <img class="item-img" src="/static/game_assets/game210/item_5.png" />
              <img class="item-img" :src="`/static/game_assets/game210/item_${currect.num1 - 5}.png`" />
            </template>
          </div>

          <img class="symbol1" src="/static/game_assets/game209/symbol1.png" />

          <div class="num-item">
            <img v-if="currect.num2 <= 5" class="item-img" :src="`/static/game_assets/game210/item_${currect.num2}.png`" />
            <template v-else>
              <img class="item-img" src="/static/game_assets/game210/item_5.png" />
              <img class="item-img" :src="`/static/game_assets/game210/item_${currect.num2 - 5}.png`" />
            </template>
          </div>

          <img class="symbol2" src="/static/game_assets/game209/symbol5.png" />

          <div class="num-item right-item">
            <img class="num-bg" src="/static/game_assets/game209/text_bg.png" />
            <span :class="[answer !== '' ? 'num' : 'symbol']">{{answer !== '' ? answer : '?'}}</span>
          </div>
        </div>

        <div class="content-bottom" v-if="!isShowCorrect && !isShowError">
          <div class="bottom-left">
            <div class="left-item" v-for="item in 10" :key="item + 'btn'" @click="chooseItem(item)">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_1.png" />
              <span class="item-num">{{item % 10}}</span>
            </div>
          </div>

          <div class="bottom-right">
            <div class="right-item item1">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_2.png" />
              <span class="item-num">{{answer}}</span>
            </div>

            <div class="right-item item2" @click="answer = ''">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_3.png" />
              <span class="item-num">X</span>
            </div>

            <div class="right-item item3" @click="confirm">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_4.png" />
              <span class="item-num">确定</span>
            </div>
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="footer-left">
          <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
          <img v-if="isShowCorrect || isShowError" class="img" src="/static/game_assets/common/next.png" @click="toNext">
        </div>

        <img v-if="isShowCorrect || isShowError" class="img" src="/static/game_assets/common/reset.png" @click="reset">
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 10道题 

export default {
  name: 'game213',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,

      answer: '',
      currect: {
        num1: 0,
        num2: 0,
        answer: 0,
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    playError () {
      this.$refs.music2.play()
    },

    start () {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      this.currect.num1 = api.randomNum(1, 10)
      this.currect.num2 = api.randomNum(1, 10)
      this.currect.answer = this.currect.num1 + this.currect.num2
    },

    chooseItem (item) {
      if (this.answer.length >= 2) return
      this.answer = this.answer + (item === 10 ? 0 : item).toString()
    },

    confirm () {
      if (!this.answer) {
        this.playError()
        return
      }
      if (Number(this.answer) === this.currect.answer) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }
    },

    reset () {
      if (this.isShowCorrect) {
        this.succesNum--
      } else {
        this.errorNum--
      }
      this.isShowCorrect = false
      this.isShowError = false
      this.answer = ''
    },

    toNext () {
      this.isShowCorrect = false
      this.isShowError = false
      this.answer = ''
      this.number++
      if (this.number >= 10) {
        this.submit()
      } else {
        this.startProcess()
      }
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.store = this.succesNum * 10
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = ''
      this.currect = {
        num1: 0,
        num2: 0,
        answer: 0,
      },
        this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game213-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game126/bg.png');
  }

  .game-synopsis {
    width: 1605px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
    }

    .synopsis-choose {
      display: flex;
      flex-wrap: wrap;
      margin-top: 25px * 0.7;
      padding: 13px * 0.7 30px * 0.7;
      border: 1px * 0.7 solid #c49e68;
      border-radius: 10px * 0.7;
      background: #fff6e3;

      .choose-title {
        margin: 0;
        font-size: 36px * 0.7;
        line-height: 41px * 0.7;
        font-weight: 600;
        color: #414043;
      }

      .item {
        position: relative;
        display: inline-flex;
        align-items: center;
        padding-left: 25px * 0.7;

        .item-icon {
          position: relative;
          width: 41px * 0.7;
          height: 35px * 0.7;
          display: inline-flex;
          justify-content: center;
          cursor: pointer;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 41px * 0.7;
          }

          .icon1 {
            position: absolute;
            width: 41px * 0.7;
          }
        }

        .item-text {
          position: relative;
          top: 2px * 0.7;
          display: inline-block;
          padding-left: 10px * 0.7;
          font-size: 36px * 0.7;
          line-height: 41px * 0.7;
          font-weight: 600;
          color: #414043;
        }
      }
    }

    .choose2 {
      .item {
        padding-bottom: 20px * 0.7;
      }
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      position: absolute;
      top: 0;
      width: 932px * 0.7;
      height: 125px * 0.7;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px * 0.7;
        height: 125px * 0.7;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 28px * 0.7 0 56px * 0.7 0;
        font-size: 36px * 0.7;
        line-height: 36px * 0.7;
        text-align: center;
        color: #1b1d2d;
      }
    }

    .content {
      position: relative;
      width: 1864px * 0.7;
      height: 828px * 0.7;
      margin-top: 80px * 0.7;
      padding: 168px * 0.7 452px * 0.7 149px * 0.7 431px * 0.7;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1864px * 0.7;
      }

      .content-title {
        position: absolute;
        top: 95px * 0.7;
        left: 800px * 0.7;
        font-size: 30px * 0.7;
        line-height: 30px * 0.7;
        text-align: center;
        color: #312b4f;
      }

      .content-top {
        position: relative;
        padding: 0 16px * 0.7;
        display: flex;
        justify-content: space-between;

        .num-item {
          position: relative;
          // width: 224px * 0.7;
          height: 244px * 0.7;
          display: flex;

          .item-img {
            width: 116px * 0.7;
          }

          .num-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 188px * 0.7;
            height: 206px * 0.7;
          }

          .num {
            position: relative;
            display: block;
            width: 100%;
            font-size: 92px * 0.7;
            line-height: 175px * 0.7;
            text-align: center;
            color: #312b4f;
            font-family: Impact;
          }

          .symbol {
            position: relative;
            display: block;
            width: 100%;
            font-size: 92px * 0.7;
            line-height: 175px * 0.7;
            text-align: center;
            text-align: center;
            color: #312b4f;
          }
        }

        .right-item {
          width: 188px * 0.7;
          height: 206px * 0.7;
          margin-top: 50px * 0.7;
        }

        .symbol1 {
          margin-top: 102px * 0.7;
          width: 62px * 0.7;
          height: 63px * 0.7;
        }

        .symbol2 {
          margin-top: 120px * 0.7;
          width: 63px * 0.7;
          height: 33px * 0.7;
        }
      }

      .content-bottom {
        position: relative;
        width: 100%;
        padding-top: 30px * 0.7;
        display: flex;

        .bottom-left {
          width: 557px * 0.7;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;
          margin-right: -10px * 0.7;

          .left-item {
            position: relative;
            width: 135px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7 -15px * 0.7;
            cursor: pointer;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 135px * 0.7;
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px * 0.7;
              line-height: 136px * 0.7;
              text-align: center;
              color: #312b4f;
            }
          }
        }

        .bottom-right {
          width: 450px * 0.7;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;

          .right-item {
            position: relative;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px * 0.7;
              line-height: 136px * 0.7;
              text-align: center;
              color: #312b4f;
            }
          }

          .item1 {
            width: 291px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7 -12px * 0.7 -12px * 0.7 0;
          }

          .item2 {
            width: 190px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7;
            cursor: pointer;
          }

          .item3 {
            width: 462px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7 -12px * 0.7 -12px * 0.7 0;
            cursor: pointer;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 72px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1480px * 0.7;

      .footer-left {
        width: 620px * 0.7;
        display: inline-flex;
        justify-content: space-between;
      }

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
    }
  }
}
</style>