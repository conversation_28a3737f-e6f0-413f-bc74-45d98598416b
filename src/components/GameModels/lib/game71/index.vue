<template>
  <div class="game71-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="图形推理" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、训练目的:训练识别规律和逻辑推理的能力。</p>
        <p class="synopsis-content">2、屏幕上呈现的图片是按一定顺序排列的，请按照这个顺序继续排列，从下面的图形中点击答案。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game71/title_bg.png" />
        <p class="title-text"> 形状 颜色 大小</p>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game71/content_bg.png" />

        <div class="content-top">
          <div class="top-item" v-for="(item, index) in questionList" :key="index + 'top'">
            <img class="item-bg" src="/static/game_assets/game71/item_bg.png" />
            <img :class="['item-img', item.type === 2 && 'middle', item.type === 3 && 'small']" :src="`/static/game_assets/game71/item_${item.first}_${item.second}.png`" />
          </div>

          <img class="top-icon1" src="/static/game_assets/game71/icon1.png" />
          <div class="top-icon2">
            <img class="item-bg" src="/static/game_assets/game71/item_bg.png" />
            <img class="icon" src="/static/game_assets/game71/icon2.png" />
          </div>
        </div>

        <div class="content-bottom">
          <div :class="['bottom-item', choose === (item.first.toString() + item.second.toString() + item.type.toString()) && 'choose-item']" v-for="(item, index) in answerList" :key="index + 'top'" @click="chooseItem(item.first, item.second, item.type)">
            <img class="item-bg" src="/static/game_assets/game71/item_bg.png" />
            <img :class="['item-img', item.type === 2 && 'middle', item.type === 3 && 'small']" :src="`/static/game_assets/game71/item_${item.first}_${item.second}.png`" />
          </div>
        </div>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="btn-group" v-if="choose">
          <img class="img2" src="/static/game_assets/common/continue.png" @click="goOn">
          <img class="img2" src="/static/game_assets/common/reset.png" @click="reset">
        </div>
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'

export default {
  name: 'game71',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      isShowCorrect: false,
      isShowError: false,
      questionList: [],
      answerList: [],
      answer: '',
      choose: '',
      musicUrl: '/static/game_assets/audio/correct_audio.mp3',

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    playAudio () {
      if (this.isStop) return

      this.index2++
      this.$refs.music.play()
      this.isPlay = true
    },

    pauseAudio () {
      this.$refs.music.pause()
    },

    handleEnded () {

    },

    start () {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      const list1 = [1, 2, 3, 4, 5]
      const list2 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
      const list3 = [1, 2, 3]
      const type = api.randomNum(1, 3) // 1 -- AAAAAAA  2 -- ABABABA  3 -- ABCABCA
      let it1 = 0
      let it2 = 0
      let it3 = 0

      if (type === 1) {
        const item1 = api.getRandomArray(list1, 1)[0]
        const item2 = api.getRandomArray(list2, 1)[0]
        const item3 = api.getRandomArray(list3, 1)[0]
        it1 = item1
        it2 = item2
        it3 = item3

        for (let i = 0; i < 7; i++) {
          this.questionList.push({
            first: item1,
            second: item2,
            type: item3
          })
        }
        this.answer = item1.toString() + item2.toString() + item3.toString()
      } else if (type === 2) {
        const item1 = api.getRandomArray(list1, 2)
        const item2 = api.getRandomArray(list2, 2)
        const item3 = api.getRandomArray(list3, 2)
        it1 = item1[1]
        it2 = item2[1]
        it3 = item3[1]

        for (let i = 0; i < 7; i++) {
          this.questionList.push({
            first: item1[i % 2],
            second: item2[i % 2],
            type: item3[i % 2]
          })
        }
        this.answer = item1[1].toString() + item2[1].toString() + item3[1].toString()
      } else if (type === 3) {
        const item1 = api.getRandomArray(list1, 3)
        const item2 = api.getRandomArray(list2, 3)
        const item3 = api.getRandomArray(list3, 3)
        it1 = item1[1]
        it2 = item2[1]
        it3 = item3[1]

        for (let i = 0; i < 7; i++) {
          this.questionList.push({
            first: item1[i % 3],
            second: item2[i % 3],
            type: item3[i % 3]
          })
        }
        this.answer = item1[1].toString() + item2[1].toString() + item3[1].toString()
      }

      this.answerList.push({
        first: it1,
        second: it2,
        type: it3
      })

      const l1 = api.getRandomArray(list1, 4)
      const l2 = api.getRandomArray(list2, 3)
      for (let i = 0; i < 11; i++) {
        const first = l1[parseInt(i / 3)]
        const second = l2[i % 3]
        if (first === it1 && second === it2) {
          const l3 = list3.filter(it => it !== it3)
          this.answerList.push({
            first,
            second,
            type: api.getRandomArray(l3, 1)[0]
          })
        } else {
          this.answerList.push({
            first,
            second,
            type: api.randomNum(1, 3)
          })
        }
      }
      this.answerList = api.shuffle(this.answerList)
      this.number++
    },

    chooseItem (first, second, type) {
      if (this.isShowCorrect || this.isShowError) return
      this.choose = first.toString() + second.toString() + type.toString()
    },

    goOn () {
      if (this.isShowCorrect || this.isShowError) return
      if (this.choose === this.answer) {
        this.isShowCorrect = true
        this.succesNum++
      } else {
        this.isShowError = true
        this.errorNum++
      }

      if (this.number < 5) {
        setTimeout(() => {
          this.isShowCorrect = false
          this.isShowError = false
          this.questionList = []
          this.answerList = []
          this.choose = ''
          this.answer = ''
          this.startProcess()
        }, 800)
      } else {
        setTimeout(() => {
          this.isShowCorrect = false
          this.isShowError = false
          this.isStop = true
          this.store = 20 * this.succesNum
          this.infos[0].value = this.second
          this.infos[1].value = this.succesNum
          this.infos[2].value = this.errorNum
          this.status = 4
          this.params = {
            id: this.info.id,
            grade: '',
            time: this.second,
            totalPoints: this.store
          }
        }, 800)
      }
    },

    reset () {
      this.choose = ''
    },

    stop () {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isPlay = false
      this.questionList = []
      this.answerList = []
      this.choose = ''
      this.answer = ''
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game71-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/bg_1.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      position: absolute;
      top: 0;
      width: 1047px * 0.7;
      height: 138px * 0.7;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1047px * 0.7;
      }

      .title-text {
        position: relative;
        font-size: 50px * 0.7;
        line-height: 50px * 0.7;
        padding-top: 27px * 0.7;
        margin: 0;
        text-align: center;
        font-weight: 600;
        color: #fff;
        user-select: none;
      }
    }

    .content {
      position: relative;
      width: 1640px * 0.7;
      height: 896px * 0.7;
      margin-top: 205px * 0.7;
      margin-right: 280px * 0.7;
      padding-left: 280px * 0.7;
      padding-bottom: 186px * 0.7;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .content-bg {
        position: absolute;
        top: 136px * 0.7;
        left: 0;
        width: 1494px * 0.7;
      }

      .content-top {
        display: flex;
        justify-content: space-between;

        .top-item {
          position: relative;
          width: 131px * 0.7;
          height: 141px * 0.7;
          display: inline-flex;
          justify-content: center;
          align-content: center;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 131px * 0.7;
            height: 141px * 0.7;
          }

          .item-img {
            position: absolute;
            top: 13px * 0.7;
            width: 100px * 0.7;
            height: 100px * 0.7;
          }

          .middle {
            position: absolute;
            top: 25px * 0.7;
            width: 76px * 0.7;
            height: 76px * 0.7;
          }

          .small {
            position: absolute;
            top: 36px * 0.7;
            width: 54px * 0.7;
            height: 54px * 0.7;
          }
        }

        .top-icon1 {
          width: 54px * 0.7;
          height: 80px * 0.7;
          margin-top: 30px * 0.7;
        }

        .top-icon2 {
          position: relative;
          width: 131px * 0.7;
          height: 141px * 0.7;
          display: inline-flex;
          justify-content: center;
          align-content: center;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 131px * 0.7;
            height: 141px * 0.7;
          }

          .icon {
            position: relative;
            width: 39px * 0.7;
            height: 65px * 0.7;
            margin-top: 30px * 0.7;
          }
        }
      }

      .content-bottom {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        padding: 0 365px * 0.7;
        height: 462px * 0.7;

        .bottom-item {
          position: relative;
          width: 141px * 0.7;
          height: 146px * 0.7;
          display: inline-flex;
          justify-content: center;
          align-content: center;
          border-radius: 54px * 0.7;
          cursor: pointer;

          .item-bg {
            position: absolute;
            top: 5px * 0.7;
            // left: 5px * 0.7;
            width: 131px * 0.7;
            height: 141px * 0.7;
          }

          .item-img {
            position: absolute;
            top: 20px * 0.7;
            width: 100px * 0.7;
            height: 100px * 0.7;
          }

          .middle {
            position: absolute;
            top: 32px * 0.7;
            width: 76px * 0.7;
            height: 76px * 0.7;
          }

          .small {
            position: absolute;
            top: 43px * 0.7;
            width: 54px * 0.7;
            height: 54px * 0.7;
          }
        }

        .choose-item {
          background: #017cfb;
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 20px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1752px * 0.7;
      padding-left: 105px * 0.7;

      .img1 {
        height: 115px * 0.7;
        cursor: pointer;
      }

      .btn-group {
        width: 569px * 0.7;
        height: 115px * 0.7;
        display: flex;
        justify-content: space-between;

        .img2 {
          height: 115px * 0.7;
          cursor: pointer;
        }
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
    }
  }
}
</style>