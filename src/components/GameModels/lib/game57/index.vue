<template>
  <div class="game57-page">
    <div class="page-bg" :class="status === 1 || status === 4 ? 'page-bg1': 'page-bg2'"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <audio ref="music2" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPageLib title="水果小分类" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">过关类训练，通过选择隶属于不同类别的水果进行训练。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>

      <div class="content">
        <div :class="['content-item', 'item-' + item]" v-for="item in 8" :key="item" @click="chooseItem(item)">
          <img class="img" v-if="item === randomNum" :src="`/static/game_assets/game57/item_${question[1]}.png`" />
          <img class="img" v-else :src="`/static/game_assets/game57/item_${question[0]}.png`" />
          <div v-if="chooseIndex === item && isShowCircle" class="circle"></div>
        </div>
      </div>
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import api from '../../utils/common.js'

export default {
  name: 'game57',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isPlay: false,
      isShowCircle: false,
      question: [],
      randomNum: 0,
      chooseIndex: 0,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
    goHome () {
      this.pause()
      this.$router.go(-1)
    },

    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.$refs.music2.pause()
      this.isPlay = false
    },

    playError () {
      this.$refs.music2.play()
    },

    start () {
      this.status = 3
      this.play()
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      this.number++
      const arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
      this.question = api.getRandomArray(arr, 2)
      this.randomNum = api.randomNum(1, 8)
    },

    chooseItem (item) {
      if (this.isShowCircle) return

      if (item !== this.randomNum) {
        this.errorNum++
        this.playError()
      } else if (item === this.randomNum) {
        if (!this.chooseIndex) this.succesNum++
        this.isShowCircle = true
        setTimeout(() => {
          if (this.number >= 5) {
            this.submit()
          } else {
            this.isShowCircle = false
            this.question = []
            this.chooseIndex = 0
            this.randomNum = 0
            this.startProcess()
          }
        }, 800)
      }
      this.chooseIndex = item
    },

    submit () {
      this.pause()
      this.isStop = true
      this.store = 20 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isShowCircle = false
      this.question = []
      this.chooseIndex = 0
      this.randomNum = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game57-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background-image: url('/static/game_assets/game57/bg.png');
  }
  .page-bg2 {
    background: url('/static/game_assets/game57/bg1.png') center center no-repeat;
    background-size: cover;
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 40px * 0.7;
      left: 105px * 0.7;
      width: 175px * 0.7;
      display: flex;
      justify-content: space-between;

      .top-icon {
        width: 80px * 0.7;
        cursor: pointer;
      }
    }

    .content {
      position: relative;
      width: 1520px * 0.7;
      height: 780px * 0.7;

      .content-item {
        position: absolute;
        width: 300px * 0.7;
        height: 300px * 0.7;
        cursor: pointer;

        .img {
          position: absolute;
          top: 0;
          left: 0;
          width: 300px * 0.7;
          height: 300px * 0.7;
        }

        .circle {
          position: absolute;
          top: 10px * 0.7;
          left: 10px * 0.7;
          width: 280px * 0.7;
          height: 280px * 0.7;
          border: 2px * 0.7 solid #7bbd41;
          border-radius: 50%;
        }
      }

      .item-1,
      .item-4,
      .item-8 {
        transform: rotate(20deg);
        animation: swing1 1.5s 0.15s linear infinite;
      }

      .item-2,
      .item-7 {
        transform: rotate(45deg);
        animation: swing2 1.5s 0.15s linear infinite;
      }

      .item-3,
      .item-5,
      .item-6 {
        transform: rotate(-20deg);
        animation: swing3 1.5s 0.15s linear infinite;
      }

      .item-1 {
        top: 20px * 0.7;
        left: 20px * 0.7;
      }

      .item-2 {
        top: 270px * 0.7;
        left: 230px * 0.7;
      }

      .item-3 {
        top: 480px * 0.7;
        left: 0px * 0.7;
      }

      .item-4 {
        top: 0px * 0.7;
        left: 420px * 0.7;
      }

      .item-5 {
        top: 80px * 0.7;
        left: 800px * 0.7;
      }

      .item-6 {
        top: 100px * 0.7;
        left: 1200px * 0.7;
      }

      .item-7 {
        top: 400px * 0.7;
        left: 600px * 0.7;
      }

      .item-8 {
        top: 380px * 0.7;
        left: 1000px * 0.7;
      }
    }
  }
}
@keyframes swing1 {
  0% {
    transform: rotate(20deg);
  }
  10% {
    transform: rotate(-10deg);
  }
  20% {
    transform: rotate(-30deg);
  }
  30% {
    transform: rotate(-10deg);
  }
  40% {
    transform: rotate(10deg);
  }
  50%,
  100% {
    transform: rotate(20deg);
  }
}

@keyframes swing2 {
  0% {
    transform: rotate(-40deg);
  }
  10% {
    transform: rotate(-15deg);
  }
  20% {
    transform: rotate(15deg);
  }
  30% {
    transform: rotate(25deg);
  }
  40% {
    transform: rotate(-10deg);
  }
  50%,
  100% {
    transform: rotate(-40deg);
  }
}

@keyframes swing3 {
  0% {
    transform: rotate(-20deg);
  }
  10% {
    transform: rotate(10deg);
  }
  20% {
    transform: rotate(30deg);
  }
  30% {
    transform: rotate(10deg);
  }
  40% {
    transform: rotate(-10deg);
  }
  50%,
  100% {
    transform: rotate(-20deg);
  }
}
</style>