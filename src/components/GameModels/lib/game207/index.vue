<template>
  <div class="game207-page">
    <div class="page-bg"></div>
    <audio ref="music2" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPageLib title="四舍五入" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本题是训练您对四则运算法则的掌握。</p>
        <p class="synopsis-content">2、您连续答对四道题将提升等级，连续答错两道题，则降低等级。</p>
        <p class="synopsis-content">3、缺省情况，由高位到低位输入结果。虚拟数字键盘中的'高'字表示由高位到低位输入结果;您若想由低位到高位输入结果，请点击'高'字，变成'低'字。除法题只能从高位到低位输入结果。</p>
      </div>
    </settingPageLib>

    <div class="game-content" v-if="status === 3">
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game206/title_bg.png" />
        <span class="title-text">估算-数的四舍五入</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game205/content_bg.png" />
        <img class="left-icon" src="/static/game_assets/game205/img1.png" />
        <img class="right-icon" src="/static/game_assets/game205/img2.png" />

        <div class="content-title">将&lt;{{currect.number}}&gt;四舍五入到&lt;{{currect.type}}&gt;位上得到的数字是？</div>

        <div class="content-top">
          <img class="num-bg" src="/static/game_assets/game209/text_bg.png" />
          <span :class="[answer !== '' ? 'num' : 'symbol']">{{answer !== '' ? answer : '?'}}</span>
        </div>

        <div class="content-bottom" v-if="!isShowCorrect && !isShowError">
          <div class="bottom-left">
            <div class="left-item" v-for="item in 10" :key="item + 'btn'" @click="chooseItem(item)">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_1.png" />
              <span class="item-num">{{item % 10}}</span>
            </div>
          </div>

          <div class="bottom-right">
            <div class="right-item item1">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_2.png" />
              <span class="item-num">{{answer}}</span>
            </div>

            <div class="right-item item2" @click="answer = ''">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_3.png" />
              <span class="item-num">X</span>
            </div>

            <div class="right-item item3" @click="confirm">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_4.png" />
              <span class="item-num">确定</span>
            </div>
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="footer-left">
          <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
          <img v-show="isShowCorrect || isShowError" class="img" src="/static/game_assets/common/next.png" @click="toNext" />
        </div>

        <img v-show="isShowCorrect || isShowError" class="img" src="/static/game_assets/common/reset.png" @click="reset">
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 10道题 

export default {
  name: 'game207',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,

      type: 1, // 训练模式
      index: 0, // 连续答对题目数
      high: true,
      answer: '',
      currect: {
        number: 0,
        type: '',
        answer: 0,
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    playError () {
      this.$refs.music2.play()
    },

    start () {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      const list = ['十', '百', '千']
      this.currect.number = api.randomNum(1000, 100000)
      this.currect.type = list[api.randomNum(0, 2)]
      const numList = this.currect.number.toString().split('')
      if (this.currect.type === '十') {
        this.currect.answer = (Number(numList[numList.length - 1]) > 4 ? (Number(numList[numList.length - 2]) + 1) : Number(numList[numList.length - 2])) % 10
      } else if (this.currect.type === '百') {
        this.currect.answer = (Number(numList[numList.length - 2]) > 4 ? (Number(numList[numList.length - 3]) + 1) : Number(numList[numList.length - 3])) % 10
      } else {
        this.currect.answer = (Number(numList[numList.length - 3]) > 4 ? (Number(numList[numList.length - 4]) + 1) : Number(numList[numList.length - 4])) % 10
      }
      this.number++
    },

    chooseItem (item) {
      if (this.answer.length >= 1) return
      this.answer = this.answer + (item === 10 ? 0 : item).toString()
    },

    confirm () {
      if (!this.answer) {
        this.playError()
        return
      }
      if (Number(this.answer) === this.currect.answer) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }
      this.is
    },

    toNext () {
      this.isShowCorrect = false
      this.isShowError = false

      if (this.number < 10) {
        this.answer = ''
        this.startProcess()
      } else {
        this.submit()
      }
    },

    reset () {
      if (this.isShowCorrect) {
        this.succesNum--
      } else {
        this.errorNum--
      }

      this.isShowCorrect = false
      this.isShowError = false
      this.answer = ''
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      this.isStop = true
      this.store = this.succesNum * 10
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = ''
      this.currect = {
        num1: 0,
        num2: 0,
        answer: 0,
      },
        this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game207-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game126/bg.png');
  }

  .game-synopsis {
    width: 1605px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      position: absolute;
      top: 0;
      width: 932px * 0.7;
      height: 125px * 0.7;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px * 0.7;
        height: 125px * 0.7;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 28px * 0.7 0 56px * 0.7 0;
        font-size: 36px * 0.7;
        line-height: 36px * 0.7;
        text-align: center;
        color: #1b1d2d;
      }
    }

    .content {
      position: relative;
      width: 1920px * 0.7;
      height: 824px * 0.7;
      margin-top: 80px * 0.7;
      padding: 194px * 0.7 480px * 0.7 145px * 0.7 459px * 0.7;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;

      .content-bg {
        position: absolute;
        top: 0;
        left: 28px * 0.7;
        width: 1864px * 0.7;
      }

      .left-icon {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 474px * 0.7;
      }

      .right-icon {
        position: absolute;
        bottom: 44px * 0.7;
        right: 0;
        width: 434px * 0.7;
      }

      .content-title {
        position: absolute;
        top: 95px * 0.7;
        font-size: 30px * 0.7;
        line-height: 30px * 0.7;
        color: #312b4f;
      }

      .content-top {
        position: relative;
        width: 188px * 0.7;
        height: 206px * 0.7;

        .num-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 188px * 0.7;
        }

        .num {
          position: relative;
          display: block;
          width: 100%;
          font-size: 96px * 0.7;
          line-height: 166px * 0.7;
          text-align: center;
          font-family: Impact;
          color: #312b4f;
        }

        .symbol {
          position: relative;
          display: block;
          width: 100%;
          font-size: 96px * 0.7;
          line-height: 166px * 0.7;
          text-align: center;
          font-weight: 500;
          color: #312b4f;
        }
      }

      .content-bottom {
        position: relative;
        width: 100%;
        display: flex;

        .bottom-left {
          width: 557px * 0.7;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;
          margin-right: -10px * 0.7;

          .left-item {
            position: relative;
            width: 135px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7 -15px * 0.7;
            cursor: pointer;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 135px * 0.7;
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px * 0.7;
              line-height: 136px * 0.7;
              text-align: center;
              color: #312b4f;
            }
          }
        }

        .bottom-right {
          width: 450px * 0.7;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;

          .right-item {
            position: relative;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px * 0.7;
              line-height: 136px * 0.7;
              text-align: center;
              color: #312b4f;
            }
          }

          .item1 {
            width: 291px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7 -12px * 0.7 -12px * 0.7 0;
          }

          .item2 {
            width: 190px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7;
            cursor: pointer;
          }

          .item3 {
            width: 462px * 0.7;
            height: 136px * 0.7;
            margin: -12px * 0.7 -12px * 0.7 -12px * 0.7 0;
            cursor: pointer;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 72px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1480px * 0.7;

      .footer-left {
        width: 620px * 0.7;
        display: inline-flex;
        justify-content: space-between;
      }

      .img {
        height: 115px * 0.7;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
    }
  }
}
</style>