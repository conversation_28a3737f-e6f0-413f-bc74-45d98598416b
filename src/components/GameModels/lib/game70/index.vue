<template>
  <div class="game70-page">
    <div class="page-bg" :class="status > 1 ? 'page-bg2': 'page-bg1'"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="我爱推理" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">请根据提示信息进行推理，找出右边图中对应选项。例如，左边的图是小狗，右边的图有四个选项，根据提示说喜欢吃什么，选择正确的选项。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="content-left">
          <img class="left-bg" src="/static/game_assets/game70/item_bg.png" />

          <img class="left-item" :src="`/static/game_assets/game70/item_${current}.png`" />
        </div>

        <div class="content-right1" v-if="current !== 3">
          <img class="right-bg" src="/static/game_assets/game70/content_bg.png" />

          <img class="right-item" v-for="item in answerList" :key="item" :src="`/static/game_assets/game70/item_${current}_${item}.png`" @click="chooseItem(item)" />
        </div>

        <div class="content-right2" v-else>
          <img class="right-bg" src="/static/game_assets/game70/content_bg.png" />

          <img class="right-item" v-for="item in answerList" :key="item" :src="`/static/game_assets/game70/item_3_${item}.png`" @click="chooseItem(item)" />
        </div>
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'

export default {
  name: 'game70',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
    draggable
  },

  data () {
    return {
      musicUrl: '',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      questionList: [],
      current: 0,
      answerList: [],
      answer: 0,
      choose: 0,
      isCanClick: false,

      isStop: false,
      isShowCorrect: false,
      isShowError: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    playAudio (url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$nextTick(() => {
        this.$refs.music.play()
      })
    },

    pauseAudio () {
      this.$refs.music.pause()
    },

    handleEnded () {
      this.isCanClick = true
    },

    start () {
      const list = [1, 2, 3]
      this.questionList = api.shuffle(list)
      this.startProcess()
      this.status = 3
      // this.timing()
    },

    startProcess () {
      this.isCanClick = false
      const list = [1, 2, 3, 4]
      this.answerList = api.shuffle(list)

      this.current = this.questionList[this.number]
      if (this.current === 1) {
        this.answer = 2
      } else if (this.current === 2) {
        this.answer = 2
      } else if (this.current === 3) {
        this.answer = 1
      }

      this.playAudio(`/static/game_assets/audio/game70/audio_${this.current}.mp3`)
      this.number++
    },

    chooseItem (item) {
      if (!this.isCanClick || this.isShowCorrect || this.isShowError) return

      this.choose = item
      if (this.choose === this.answer) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }

      setTimeout(() => {
        this.isShowCorrect = false
        this.isShowError = false
        this.submit()
      }, 800)
    },

    submit () {
      if (this.number < 3) {
        this.answerList = []
        this.answer = 0
        this.choose = 0
        this.current = 0
        this.startProcess()
      } else {
        this.pauseAudio()
        this.isStop = true
        this.store = this.succesNum ? this.succesNum * 30 + 10 : 0
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: '',
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.questionList = []
      this.answerList = []
      this.answer = 0
      this.choose = 0
      this.current = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game70-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background-image: url('/static/game_assets/game1/images/bg1.png');
  }
  .page-bg2 {
    background-image: url('/static/game_assets/game1/images/bg2.png');
  }

  .game-synopsis {
    width: 707px * 0.7;
    height: 444px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fffef3;
    border: 2px * 0.7 solid #014747;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1717px * 0.7;
      height: 1006px * 0.7;
      display: flex;
      justify-content: space-between;

      .content-left {
        position: relative;
        margin-top: 263px * 0.7;
        width: 491px * 0.7;
        height: 596px * 0.7;

        .left-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 491px * 0.7;
          height: 596px * 0.7;
        }

        .left-item {
          position: absolute;
          top: 45px * 0.7;
          left: 93px * 0.7;
          width: 294px * 0.7;
        }
      }

      .content-right1 {
        position: relative;
        width: 1145px * 0.7;
        height: 1006px * 0.7;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        padding: 187px * 0.7 230px * 0.7 225px * 0.7 230px * 0.7;

        .right-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 1145px * 0.7;
          height: 1006px * 0.7;
        }

        .right-item {
          position: relative;
          width: 257px * 0.7;
          height: 257px * 0.7;
          cursor: pointer;
        }
      }

      .content-right2 {
        position: relative;
        width: 1145px * 0.7;
        height: 1006px * 0.7;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        padding: 239px * 0.7 158px * 0.7 246px * 0.7 158px * 0.7;

        .right-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 1145px * 0.7;
          height: 1006px * 0.7;
        }

        .right-item {
          position: relative;
          width: 394px * 0.7;
          height: 221px * 0.7;
          cursor: pointer;
        }
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
    }
  }
}
</style>