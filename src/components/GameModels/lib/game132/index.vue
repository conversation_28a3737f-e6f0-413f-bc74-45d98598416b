<template>
  <div class="game132-page">
    <div class="page-bg"></div>
    <settingPageLib title="数的表示" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请根据提示选择正确的选项。</p>
        <p class="synopsis-content">2、训练结束后会出现成绩单。</p>
        <p class="synopsis-content">3、本训练难度为2（普通）。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <p class="top">哪个选项表示{{current.number}}个{{current.index === 1 ? '菱形' : current.index === 2 ? '正方形' : '三角形'}}</p>

      <div class="content">
        <div :class="['content-item', choose === item && 'choose-item']" v-for="item in answerList" :key="item + 'item'" @click="chooseItem(item)">
          <img class="item-img" v-for="num in item" :key="num + 'img'" :src="`/static/game_assets/game131/item_${current.index}.png`" />
        </div>
      </div>

      <div class="footer">
        <div class="left">
          <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
          <img v-if="choose" class="img3" src="/static/game_assets/common/finish.png" @click="submit">
        </div>
        <div class="right">
          <span class="right-item">题目分数：{{store}}</span>
          <span class="right-item">题目数量：{{number}}</span>
          <span class="right-item">训练用时：{{time}}</span>
        </div>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 一共10道题
// 数的范围是11-20

export default {
  name: 'game132',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      level: 2,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      choose: 0,
      answerList: [],
      current: {
        index: 0,
        number: 0
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time () {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ':' + s
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      this.startProcess()
      // this.timing()
    },

    startProcess () {
      const list = [11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
      this.current.index = api.randomNum(1, 3)
      this.current.number = api.randomNum(11, 20)
      const arr = list.filter(item => item !== this.current.number)
      this.answerList = api.getRandomArray(arr, 3).concat(this.current.number)
      this.answerList = api.shuffle(this.answerList)
      this.number++
    },

    chooseItem (item) {
      this.choose = item
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    submit () {
      if (this.choose === this.current.number) {
        this.succesNum++
      } else {
        this.errorNum++
      }
      this.store = this.succesNum * 10

      if (this.number < 10) {
        this.choose = 0
        this.startProcess()
        return
      }

      this.isStop = true
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.current = {
        index: 0,
        number: 0
      }
      this.choose = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game132-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game131/bg.png');
  }

  .game-synopsis {
    width: 707px * 0.7;
    height: 444px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fffef3;
    border: 2px * 0.7 solid #014747;
    border-radius: 36px * 0.7;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
    }

    .synopsis-content {
      padding-top: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .top {
      margin: 0;
      padding-top: 95px * 0.7;
      font-size: 50px * 0.7;
      line-height: 70px * 0.7;
      text-align: center;
      font-weight: 600;
      color: #014747;
    }

    .content {
      position: relative;
      width: 1750px * 0.7;
      height: 607px * 0.7;
      margin-bottom: 18px * 0.7;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .content-item {
        height: 127px * 0.7;
        padding: 0 30px * 0.7;
        display: inline-flex;
        align-items: center;
        border: 1px * 0.7 solid #31996d;
        border-radius: 16px * 0.7;
        background: #fff;
        cursor: pointer;

        .item-img {
          width: 74px * 0.7;
          margin-right: 10px * 0.7;
        }
      }

      .choose-item {
        background: #f0ffdc;
      }
    }

    .footer {
      position: relative;
      display: flex;
      justify-content: space-between;
      width: 1790px * 0.7;
      padding-bottom: 47px * 0.7;
      margin: 0 auto;

      .left {
        width: 564px * 0.7;
        display: inline-flex;
        justify-content: space-between;
      }

      .right {
        width: 790px * 0.7;
        padding: 20px * 0.7 0;
        display: inline-flex;
        justify-content: space-between;

        .right-item {
          display: block;
          width: 245px * 0.7;
          height: 75px * 0.7;
          border: 1px * 0.7 solid #31996d;
          border-radius: 4px * 0.7;
          background: #d0f89b;

          font-size: 24px * 0.7;
          line-height: 70px * 0.7;
          text-align: center;
          color: #31996d;
        }
      }

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img3 {
        width: 267px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }
  }
}
</style>