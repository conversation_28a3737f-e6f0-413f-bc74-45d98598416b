<template>
  <div class="game42-page">
    <!-- <audio ref="music" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio> -->
    <div class="page-bg"></div>
    <settingPageLib @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、屏幕中将顺序出现一些的线段，请记住这些线段的位置，随后凭记忆画出这些线段。</p>
        <p class="synopsis-content">2、线段出现的数量和线段呈现的时间将随着您的训练成绩而变化。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content" @mousedown="handleMousedown" @mousemove="handleMousemove" @mouseup="handleMouseup">
        <template v-if="answerStatus !== 'hint'">
          <canvas id="mycanvas" width="2000" height="2000"></canvas>
          <img :class="['content-item', 'content-item-' + item]" v-for="item in 8" :key="item + 'item'" :ref="`item${item}`" src="/static/game_assets/game42/item.png" />
        </template>

        <template v-if="answerStatus === 'hint'">
          <img class="content-bg" src="/static/game_assets/game8/content_bg.png">
          <p class="content-text">记忆结束，请开始连线。</p>
        </template>
      </div>

      <div class="footer" v-if="answerStatus !== 'demo' && answerStatus !== 'hint'">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <div v-if="answer.length" class="btn-group">
          <div class="btn" @click="reset">
            <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">重做</span>
          </div>

          <div class="btn" @click="finish">
            <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">完成</span>
          </div>
        </div>
      </div>

      <div class="judge" v-if="answerStatus === 'judge'">
        <img class="img" :src="`/static/game_assets/game8/${isCorrect ? 'success' : 'error'}.png`">
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 每轮游戏34个回合
// 1级 3个线段 每个1s
// 2   4      1
// 3   5      1

export default {
  name: 'game42',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      index: 0,
      show: false,
      isStop: false,
      isCorrect: false,
      answerStatus: 'demo', // demo -- 演示 hint -- 答题前等待 answer -- 答题 judge -- 判定
      questionList: [],
      current: [],
      answer: [],
      path: [],
      x1: 0,
      y1: 0,
      x2: 0,
      y2: 0,

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.status = 3
      setTimeout(() => {
        this.startProcess()
      }, 500)
    },

    // 开始流程
    async startProcess () {
      let list = []
      for (let i = 1; i < 9; i++) {
        for (let j = i + 1; j < 9; j++) {
          if (i === 1 && j === 7) continue
          if (i === 1 && j === 5) continue
          if (i === 3 && j === 7) continue
          if (i === 2 && j === 6) continue
          if (i === 2 && j === 8) continue
          list.push([i, j])
        }
      }
      this.questionList = api.getRandomArray(list, this.level + 2)
      console.log('questionList-=-=--=', this.questionList)
      this.number++
      this.setTime()
    },

    setTime () {
      if (this.index >= this.level + 2) {
        this.answerStatus = 'hint'
        setTimeout(() => {
          this.answerStatus = 'answer'
          this.index = 0
        }, 800)
        return
      }

      this.current = this.questionList[this.index]
      const ref1 = this.$refs[`item${this.current[0]}`][0]
      const x1 = Number(ref1.getBoundingClientRect().left) + Number(ref1.offsetHeight) / 2
      const y1 = Number(ref1.getBoundingClientRect().top) + Number(ref1.offsetWidth) / 2
      const ref2 = this.$refs[`item${this.current[1]}`][0]
      const x2 = Number(ref2.getBoundingClientRect().left) + Number(ref2.offsetHeight) / 2
      const y2 = Number(ref2.getBoundingClientRect().top) + Number(ref2.offsetWidth) / 2
      this.drawLine(x1, y1, x2, y2)
      this.index++
      setTimeout(() => {
        const canvas = document.getElementById('mycanvas')
        const ctx = canvas.getContext('2d')
        ctx.clearRect(0, 0, 1400, 800)
        this.setTime()
      }, 1500)
    },

    drawLine (x1, y1, x2, y2) {
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.save()
      ctx.beginPath() //不写每次都会重绘上次的线
      ctx.lineCap = "round"
      ctx.lineJoin = "round"

      ctx.moveTo(x1, y1)
      ctx.lineTo(x2, y2)
      ctx.closePath()
      ctx.strokeStyle = "#FF1616"
      ctx.lineWidth = 4
      ctx.stroke()
      ctx.restore()
    },

    isRange (x, y, type) {
      let finalX, finalY, index
      const ref1 = this.$refs['item1'][0]
      const item1x1 = parseInt(ref1.getBoundingClientRect().left)
      const item1y1 = parseInt(ref1.getBoundingClientRect().top)
      const item1x2 = parseInt(ref1.getBoundingClientRect().left) + parseInt(ref1.offsetHeight)
      const item1y2 = parseInt(ref1.getBoundingClientRect().top) + parseInt(ref1.offsetWidth)
      if (item1x1 < x && x < item1x2 && item1y1 < y && y < item1y2) {
        finalX = parseInt(ref1.getBoundingClientRect().left) + parseInt(ref1.offsetHeight) / 2
        finalY = parseInt(ref1.getBoundingClientRect().top) + parseInt(ref1.offsetWidth) / 2
        index = 1
      }

      const ref2 = this.$refs['item2'][0]
      const item2x1 = parseInt(ref2.getBoundingClientRect().left)
      const item2y1 = parseInt(ref2.getBoundingClientRect().top)
      const item2x2 = parseInt(ref2.getBoundingClientRect().left) + parseInt(ref2.offsetHeight)
      const item2y2 = parseInt(ref2.getBoundingClientRect().top) + parseInt(ref2.offsetWidth)
      if (item2x1 < x && x < item2x2 && item2y1 < y && y < item2y2) {
        finalX = parseInt(ref2.getBoundingClientRect().left) + parseInt(ref2.offsetHeight) / 2
        finalY = parseInt(ref2.getBoundingClientRect().top) + parseInt(ref2.offsetWidth) / 2
        index = 2
      }

      const ref3 = this.$refs['item3'][0]
      const item3x1 = parseInt(ref3.getBoundingClientRect().left)
      const item3y1 = parseInt(ref3.getBoundingClientRect().top)
      const item3x2 = parseInt(ref3.getBoundingClientRect().left) + parseInt(ref3.offsetHeight)
      const item3y2 = parseInt(ref3.getBoundingClientRect().top) + parseInt(ref3.offsetWidth)
      if (item3x1 < x && x < item3x2 && item3y1 < y && y < item3y2) {
        finalX = parseInt(ref3.getBoundingClientRect().left) + parseInt(ref3.offsetHeight) / 2
        finalY = parseInt(ref3.getBoundingClientRect().top) + parseInt(ref3.offsetWidth) / 2
        index = 3
      }

      const ref4 = this.$refs['item4'][0]
      const item4x1 = parseInt(ref4.getBoundingClientRect().left)
      const item4y1 = parseInt(ref4.getBoundingClientRect().top)
      const item4x2 = parseInt(ref4.getBoundingClientRect().left) + parseInt(ref4.offsetHeight)
      const item4y2 = parseInt(ref4.getBoundingClientRect().top) + parseInt(ref4.offsetWidth)
      if (item4x1 < x && x < item4x2 && item4y1 < y && y < item4y2) {
        finalX = parseInt(ref4.getBoundingClientRect().left) + parseInt(ref4.offsetHeight) / 2
        finalY = parseInt(ref4.getBoundingClientRect().top) + parseInt(ref4.offsetWidth) / 2
        index = 4
      }

      const ref5 = this.$refs['item5'][0]
      const item5x1 = parseInt(ref5.getBoundingClientRect().left)
      const item5y1 = parseInt(ref5.getBoundingClientRect().top)
      const item5x2 = parseInt(ref5.getBoundingClientRect().left) + parseInt(ref5.offsetHeight)
      const item5y2 = parseInt(ref5.getBoundingClientRect().top) + parseInt(ref5.offsetWidth)
      if (item5x1 < x && x < item5x2 && item5y1 < y && y < item5y2) {
        finalX = parseInt(ref5.getBoundingClientRect().left) + parseInt(ref5.offsetHeight) / 2
        finalY = parseInt(ref5.getBoundingClientRect().top) + parseInt(ref5.offsetWidth) / 2
        index = 5
      }

      const ref6 = this.$refs['item6'][0]
      const item6x1 = parseInt(ref6.getBoundingClientRect().left)
      const item6y1 = parseInt(ref6.getBoundingClientRect().top)
      const item6x2 = parseInt(ref6.getBoundingClientRect().left) + parseInt(ref6.offsetHeight)
      const item6y2 = parseInt(ref6.getBoundingClientRect().top) + parseInt(ref6.offsetWidth)
      if (item6x1 < x && x < item6x2 && item6y1 < y && y < item6y2) {
        finalX = parseInt(ref6.getBoundingClientRect().left) + parseInt(ref6.offsetHeight) / 2
        finalY = parseInt(ref6.getBoundingClientRect().top) + parseInt(ref6.offsetWidth) / 2
        index = 6
      }

      const ref7 = this.$refs['item7'][0]
      const item7x1 = parseInt(ref7.getBoundingClientRect().left)
      const item7y1 = parseInt(ref7.getBoundingClientRect().top)
      const item7x2 = parseInt(ref7.getBoundingClientRect().left) + parseInt(ref7.offsetHeight)
      const item7y2 = parseInt(ref7.getBoundingClientRect().top) + parseInt(ref7.offsetWidth)
      if (item7x1 < x && x < item7x2 && item7y1 < y && y < item7y2) {
        finalX = parseInt(ref7.getBoundingClientRect().left) + parseInt(ref7.offsetHeight) / 2
        finalY = parseInt(ref7.getBoundingClientRect().top) + parseInt(ref7.offsetWidth) / 2
        index = 7
      }

      const ref8 = this.$refs['item8'][0]
      const item8x1 = parseInt(ref8.getBoundingClientRect().left)
      const item8y1 = parseInt(ref8.getBoundingClientRect().top)
      const item8x2 = parseInt(ref8.getBoundingClientRect().left) + parseInt(ref8.offsetHeight)
      const item8y2 = parseInt(ref8.getBoundingClientRect().top) + parseInt(ref8.offsetWidth)
      if (item8x1 < x && x < item8x2 && item8y1 < y && y < item8y2) {
        finalX = parseInt(ref8.getBoundingClientRect().left) + parseInt(ref8.offsetHeight) / 2
        finalY = parseInt(ref8.getBoundingClientRect().top) + parseInt(ref8.offsetWidth) / 2
        index = 8
      }

      if (!finalX) {
        this.x1 = 0
        this.y1 = 0
        this.x2 = 0
        this.y2 = 0
        if (type === 2) this.answer.pop()
        return
      }
      if (type === 1) {
        this.x1 = finalX
        this.y1 = finalY
        this.answer.push([index])
      } else {
        if (this.answer[this.index][0] && index !== this.answer[this.index][0]) {
          this.x2 = finalX
          this.y2 = finalY
          this.answer.end = index
          this.answer[this.index].push(index)
        }
        if (index === this.answer[this.index][0]) {
          this.answer.pop()
        }
      }
    },

    handleMousedown (e) {
      if (this.answerStatus !== 'answer') return
      this.isRange(Number(e.clientX), Number(e.clientY), 1)
    },

    handleMousemove (e) {
      if (!this.x1) return

      const x2 = e.clientX
      const y2 = e.clientY
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, 2000, 2000)
      this.drawLine(this.drawLine(this.x1, this.y1, x2, y2))
      this.path.forEach(item => {
        this.drawLine(this.drawLine(item.x1, item.y1, item.x2, item.y2))
      })
    },

    handleMouseup (e) {
      this.isRange(Number(e.clientX), Number(e.clientY), 2)
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, 1400, 800)
      if (this.x2) {
        this.index++
        this.path.push({
          x1: this.x1,
          y1: this.y1,
          x2: this.x2,
          y2: this.y2
        })
      }

      this.path.forEach(item => {
        this.drawLine(this.drawLine(item.x1, item.y1, item.x2, item.y2))
      })
      this.x1 = 0
      this.y1 = 0
      this.x2 = 0
      this.y2 = 0
    },

    stop () {
      this.isStop = true
      this.show = true
    },

    reset () {
      this.isStop = true
      this.index = 0
      this.answer = []
      this.path = []
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, 1400, 800)
    },

    finish () {
      this.answerStatus = 'judge'
      if (this.questionList.length !== this.answer.length) {
        this.errorNum++
        this.isCorrect = false
      } else {
        let num = 0
        this.answer.forEach(item => {
          this.questionList.forEach(it => {
            if (it.includes(item[0]) && it.includes(item[1])) num++
          })
        })
        if (num === this.questionList.length) {
          this.isCorrect = true
          this.succesNum++
        } else {
          this.isCorrect = false
          this.errorNum++
        }
      }

      setTimeout(() => {
        this.answerStatus = 'demo'
        this.reset()
        if (this.number >= 4) {
          this.submit()
        } else {
          this.startProcess()
        }
      }, 1000)
    },

    submit () {
      this.isStop = true
      this.store = 25 * this.succesNum
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.current = []
      this.status = 3
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game42-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game42/bg.png');
  }

  .game-synopsis {
    .synopsis-content {
      padding-top: 20px * 0.7;
      margin: 0;
      font-size: 30px * 0.7;
      line-height: 42px * 0.7;
      font-weight: 400;
      color: #a83a01;
    }
  }

  .game-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
      // padding-top: 400px * 0.7;

      .content-item {
        position: absolute;
        width: 146px * 0.7;
        height: 146px * 0.7;
        user-select: none;
        -webkit-user-drag: none;
        cursor: pointer;
      }

      .content-item-1 {
        top: 67px * 0.7;
        left: 507px * 0.7;
      }

      .content-item-2 {
        top: 140px * 0.7;
        left: 880px * 0.7;
      }

      .content-item-3 {
        top: 52px * 0.7;
        left: 1144px * 0.7;
      }

      .content-item-4 {
        top: 300px * 0.7;
        left: 629px * 0.7;
      }

      .content-item-5 {
        top: 280px * 0.7;
        left: 1041px * 0.7;
      }

      .content-item-6 {
        top: 508px * 0.7;
        left: 516px * 0.7;
      }

      .content-item-7 {
        top: 625px * 0.7;
        left: 880px * 0.7;
      }

      .content-item-8 {
        top: 534px * 0.7;
        left: 1144px * 0.7;
      }

      .content-bg {
        position: absolute;
        top: 103px * 0.7;
        left: 340px * 0.7;
        width: 1253px * 0.7;
        height: 708px * 0.7;
      }

      .content-text {
        position: relative;
        margin: 0;
        padding-top: 400px * 0.7;
        font-size: 60px * 0.7;
        line-height: 60px * 0.7;
        text-align: center;
        font-weight: bold;
        color: #a83a01;
      }
    }

    .footer {
      position: absolute;
      bottom: 82px * 0.7;
      width: 1554px * 0.7;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px * 0.7;
        height: 84px * 0.7;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px * 0.7;
          height: 84px * 0.7;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px * 0.7 0 21px * 0.7 0;
          font-size: 38px * 0.7;
          line-height: 53px * 0.7;
          text-align: center;
          color: #a83a01;
        }
      }

      .btn-group {
        width: 680px * 0.7;
        display: flex;
        justify-content: space-between;
      }
    }

    .judge {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding-top: 10px * 0.7;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      justify-content: center;
      align-items: center;

      .img {
        width: 460px * 0.7;
        height: 460px * 0.7;
      }
    }
  }
}
</style>