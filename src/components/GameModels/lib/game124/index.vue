<template>
  <div class="game124-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" style="display:none" loop="loop">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="小松学颜色" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、小松系列，通过学习并对比来天掌握对各种颜色的认知，包括有红、黄、绿、蓝、黑、白六种颜色;</p>
        <p class="synopsis-content">2、本训练难度为A(简单)。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <div class="content1" v-if="number === 1">
        <img class="content-bg" src="/static/game_assets/game124/img5.png" />
        <img class="content-img" :src="`/static/game_assets/game124/item_${currect.index}.png`" />
        <img class="img1" src="/static/game_assets/game124/img4.png" />
        <img class="text-bg" src="/static/game_assets/game124/text_bg.png" />

        <p class="content-text">这是{{currect.value}}小松！现在该整理玩具了，{{currect.value}}小松负责{{currect.value}}的玩具。你能帮助他把{{currect.value}}的玩具收起来吗?</p>
      </div>

      <div class="content2" v-if="number === 2">
        <div class="content-title">
          <img class="title-bg" src="/static/game_assets/game124/title_bg.png" />
          <p class="title-text">请点击{{currect.value}}的玩具</p>
        </div>

        <div class="content-main">
          <img :class="['content-item', itemClass(2)]" :src="`/static/game_assets/game124/item_${currect.index}.png`" />
          <img class="content-wapper" src="/static/game_assets/game124/img1.png" />
        </div>

        <div class="content-bottom">
          <img :class="['bottom-img', 'bottom-img' + (index + 1), itemClass(1, item, index + 1)]" v-for="(item, index) in answerList1" :key="index + 'item'" :src="`/static/game_assets/game124/item_${item.index}_${index + 1}.png`" @click="chooseItem(item, index + 1)" />
        </div>
      </div>

      <div class="content3" v-if="number === 3">
        <img class="content-bg" src="/static/game_assets/game124/img5.png" />
        <img class="content-img" :src="`/static/game_assets/game124/item_${currect.index}.png`" />
        <img class="img2" src="/static/game_assets/game124/img3.png" />
        <img class="text-bg" src="/static/game_assets/game124/text_bg.png" />

        <p class="content-text">{{currect.value}}小松正在等待其他的{{currect.value}}伙伴，点击草地上{{currect.value}}的小伙伴们然后一起回家吧。</p>
      </div>

      <div class="content4" v-if="number === 4">
        <div class="content-title">
          <img class="title-bg" src="/static/game_assets/game124/title_bg.png" />
          <p class="title-text">请点击{{currect.value}}的小伙伴</p>
        </div>

        <div :class="['content-item', 'content-item' + item, itemClass(4, item)]" v-for="item in 5" :key="item + 'item1'">
          <img v-if="item === 1" :class="['item-img']" :src="`/static/game_assets/game124/item_${currect.index}.png`" />
          <img v-if="item === 4 && isRate4" :class="['item-img']" :src="`/static/game_assets/game124/item_${choose[0].index}.png`" />
          <img v-if="item === 2 && isRate2" :class="['item-img']" :src="`/static/game_assets/game124/item_${choose[1].index}.png`" />
          <img v-if="item === 3 && isRate3" :class="['item-img']" :src="`/static/game_assets/game124/item_${choose[2].index}.png`" />
          <img v-if="item === 5 && isRate5" :class="['item-img']" :src="`/static/game_assets/game124/item_${choose[3].index}.png`" />
          <img class="item-icon" src="/static/game_assets/game124/img2.png" />
        </div>

        <template v-for="(item, index) in answerList2">
          <img :class="['bottom-img', 'bottom-img' + (index + 1), itemClass(3, item, index + 1)]" :key="index + 'item2'" :src="`/static/game_assets/game124/item_${item.index}.png`" @click="chooseItem(item, index + 1)" v-if="showItem(item, index + 1)" />
        </template>
      </div>

      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>

      <div class="footer">
        <img v-if="number > 1" class="img1" src="/static/game_assets/game81/left_btn.png" @click="clickLeft">
        <img v-if="number < 4" class="img2" src="/static/game_assets/game81/right_btn.png" @click="clickRight">
      </div>
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import api from '../../utils/common.js'

export default {
  name: 'game124',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    draggable
  },

  data () {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      isCorrect: false,
      isRate2: false,
      isRate3: false,
      isRate4: false,
      isRate5: false,
      isClick: true,

      total: 0,
      timer: null,
      currect: {},
      choose: [],
      answerList1: [],
      answerList2: [],

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
    goHome () {
      this.pause()
      this.$router.go(-1)
    },

    play () {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start () {
      this.status = 3
      this.play()
      this.startProcess()
      // this.timing()
    },

    startProcess () {
      const list = [
        {
          index: 1,
          value: '白色'
        },
        {
          index: 2,
          value: '黑色'
        },
        {
          index: 3,
          value: '红色'
        },
        {
          index: 4,
          value: '黄色'
        },
        {
          index: 5,
          value: '绿色'
        },
        {
          index: 6,
          value: '蓝色'
        }
      ]

      const arr = api.getRandomArray(list, 2)
      this.currect = arr[0]

      if (this.number === 1 || this.number === 2) {
        const list1 = [1, 2, 3, 4, 5, 6]
        this.total = api.randomNum(2, 5)
        const indexArr = api.getRandomArray(list1, this.total)

        for (let i = 0; i < 6; i++) {
          if (indexArr.includes(i + 1)) {
            this.answerList1.push(arr[0])
          } else {
            this.answerList1.push(arr[1])
          }
        }
      }

      if (this.number === 3 || this.number === 4) {
        const list2 = [1, 2, 3, 4, 5, 6, 7, 8]
        const indexArr = api.getRandomArray(list2, 4)
        this.total = 4

        for (let i = 0; i < 8; i++) {
          if (indexArr.includes(i + 1)) {
            this.answerList2.push(arr[0])
          } else {
            this.answerList2.push(arr[1])
          }
        }
      }

      if (this.number === 1 || this.number === 3) {
        this.timer = setTimeout(() => {
          this.number++
        }, 6000)
      }
    },

    itemClass (type, item, index) {
      if (type === 1) {
        const list = this.choose.filter(it => it.position === index)
        if (list.length) return 'rotate-item' + index
      }

      if (type === 2 && this.isCorrect) {
        return 'swing'
      }

      if (type === 3) {
        const list = this.choose.filter(it => it.position === index)
        if (list.length) return 'rotate-item' + list[0].startIndex
      }

      if (type === 4) {
        if (item === 1) return 'swing'
        if (this.choose.length > 0 && item === 4 && this.isRate4) return 'swing'
        if (this.choose.length > 1 && item === 2 && this.isRate2) return 'swing'
        if (this.choose.length > 2 && item === 3 && this.isRate3) return 'swing'
        if (this.choose.length > 3 && item === 5 && this.isRate5) return 'swing'
      }
    },

    showItem (item, index) {
      const list = this.choose.filter(it => it.position === index)
      if (list.length && this.choose.length >= 4) {
        if (this.isRate5 || this.choose[this.choose.length - 1].position !== index) {
          return false
        } else {
          return true
        }
      } else if (list.length && this.choose.length >= 3) {
        if (this.isRate3 || this.choose[this.choose.length - 1].position !== index) {
          return false
        } else {
          return true
        }
      } else if (list.length && this.choose.length >= 2) {
        if (this.isRate2 || this.choose[this.choose.length - 1].position !== index) {
          return false
        } else {
          return true
        }
      } else if (list.length && this.choose.length >= 1) {
        if (this.isRate4) {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    },

    chooseItem (item, index) {
      const list = this.choose.filter(it => it.position === index)
      if (list.length || this.choose.length >= this.total || !this.isClick) return

      this.choose.push({
        ...item,
        position: index,
        startIndex: this.choose.length + 1
      })

      this.isClick = false
      this.isCorrect = true
      setTimeout(() => {
        this.isCorrect = false
        this.isClick = true

        if (this.choose.length >= 1 && this.number === 4) this.isRate4 = true
        if (this.choose.length >= 2 && this.number === 4) this.isRate2 = true
        if (this.choose.length >= 3 && this.number === 4) this.isRate3 = true
        if (this.choose.length >= 4 && this.number === 4) this.isRate5 = true

        if (this.choose.length >= this.total) {
          const list = this.choose.filter(it => it.value === this.currect.value)
          this.succesNum += list.length
          this.errorNum += this.total - list.length

          if (this.number >= 4) {
            this.submit()
          } else {
            this.choose = []
            this.number++
            this.startProcess()
          }
        }
      }, 1600)
    },

    clickLeft () {
      clearTimeout(this.timer)

      this.choose = []
      if (this.number % 2) {
        this.answerList1 = []
        this.answerList2 = []
        this.number -= 2
        this.startProcess()
      } else {
        this.number--
        this.timer = setTimeout(() => {
          this.number++
        }, 6000)
      }
    },

    clickRight () {
      clearTimeout(this.timer)

      this.choose = []
      if (this.number % 2) {
        this.number++
      } else {
        this.number++
        this.startProcess()
      }
    },

    submit () {
      this.isStop = true
      this.pause()
      this.store = this.succesNum >= 7 ? 100 - 10 * this.errorNum : 12 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 1
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.choose = []
      this.isCorrect = false
      this.isRate2 = false
      this.isRate3 = false
      this.isRate4 = false
      this.isRate5 = false
      this.timer = null
      this.answerList1 = []
      this.answerList2 = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game124-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game104/bg.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 40px * 0.7;
      left: 105px * 0.7;
      width: 175px * 0.7;
      display: flex;
      justify-content: space-between;

      .top-icon {
        width: 80px * 0.7;
        cursor: pointer;
      }
    }

    .content1,
    .content3 {
      position: relative;
      width: 1185px * 0.7;
      height: 868px * 0.7;
      margin-top: 98px * 0.7;
      margin-left: 189px * 0.7;
      padding: 87px * 0.7 110px * 0.7 0 486px * 0.7;

      .content-bg {
        position: absolute;
        top: 10px * 0.7;
        left: 0;
        width: 663px * 0.7;
      }

      .content-img {
        position: absolute;
        left: 100px * 0.7;
        bottom: 28px * 0.7;
        height: 290px * 0.7;
      }

      .img1 {
        position: absolute;
        bottom: 0;
        right: 250px * 0.7;
        width: 457px * 0.7;
      }

      .img2 {
        position: absolute;
        bottom: 28px * 0.7;
        right: 342px * 0.7;
        width: 413px * 0.7;
      }

      .text-bg {
        position: absolute;
        top: 0;
        right: 0;
        width: 833px * 0.7;
      }

      .content-text {
        position: relative;
        margin: 0;
        font-size: 48px * 0.7;
        line-height: 72px * 0.7;
        color: #1e1e1d;
      }
    }

    .content2 {
      position: relative;
      width: 1105px * 0.7;
      height: 100%;
      margin-left: 35px * 0.7;
      padding-bottom: 100px * 0.7;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;

      .content-title {
        position: relative;
        padding-right: 16px * 0.7;
        width: 100%;
        height: 120px * 0.7;

        .title-bg {
          position: absolute;
          top: 0;
          left: 73px * 0.7;
          width: 932px * 0.7;
        }

        .title-text {
          position: relative;
          padding-top: 27px * 0.7;
          margin: 0;
          text-align: center;
          font-size: 36px * 0.7;
          line-height: 36px * 0.7;
          color: #1b1d2d;
        }
      }

      .content-main {
        position: relative;
        width: 698px * 0.7;
        height: 307px * 0.7;
        margin-top: 154px * 0.7;

        .content-item {
          position: absolute;
          left: 0;
          bottom: 0;
          height: 290px * 0.7;
        }

        .swing {
          animation: swing 1.5s 0.15s linear infinite;
        }

        .content-wapper {
          position: absolute;
          right: 0;
          bottom: 14px * 0.7;
          width: 429px * 0.7;
          z-index: 2;
        }
      }

      .content-bottom {
        position: relative;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        z-index: 4;

        .bottom-img {
          transition: All 1.5s ease-in-out;
          cursor: pointer;
        }

        .rotate-item1 {
          transform: translate3d(632px * 0.7, -458px * 0.7, 0) rotate(1080deg) scale(0);
        }

        .rotate-item2 {
          transform: translate3d(462px * 0.7, -478px * 0.7, 0) rotate(1080deg) scale(0);
        }

        .rotate-item3 {
          transform: translate3d(272px * 0.7, -468px * 0.7, 0) rotate(1080deg) scale(0);
        }

        .rotate-item4 {
          transform: translate3d(60px * 0.7, -478px * 0.7, 0) rotate(1080deg) scale(0);
        }

        .rotate-item5 {
          transform: translate3d(-170px * 0.7, -458px * 0.7, 0) rotate(1080deg) scale(0);
        }

        .rotate-item6 {
          transform: translate3d(-336px * 0.7, -458px * 0.7, 0) rotate(1080deg) scale(0);
        }

        .bottom-img1 {
          width: 109px * 0.7;
        }

        .bottom-img2 {
          width: 208px * 0.7;
        }

        .bottom-img3 {
          width: 135px * 0.7;
        }

        .bottom-img4 {
          width: 245px * 0.7;
        }

        .bottom-img5 {
          width: 143px * 0.7;
        }

        .bottom-img6 {
          width: 133px * 0.7;
        }
      }
    }

    .content4 {
      position: relative;
      width: 1612px * 0.7;
      height: 100%;
      padding-bottom: 122px * 0.7;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;

      .content-title {
        position: relative;
        padding-left: 12px * 0.7;
        width: 100%;
        height: 120px * 0.7;

        .title-bg {
          position: absolute;
          top: 0;
          left: 332px * 0.7;
          width: 932px * 0.7;
        }

        .title-text {
          position: relative;
          padding-top: 27px * 0.7;
          margin: 0;
          text-align: center;
          font-size: 36px * 0.7;
          line-height: 36px * 0.7;
          color: #1b1d2d;
        }
      }

      .content-item {
        position: absolute;
        bottom: 462px * 0.7;
        width: 312px * 0.7;
        height: 363px * 0.7;
        display: flex;
        flex-direction: column;
        align-items: center;

        .item-img {
          position: absolute;
          bottom: 88px * 0.7;
          height: 275px * 0.7;
          z-index: 2;
        }

        .item-icon {
          position: absolute;
          bottom: 0;
          width: 222px * 0.7;
        }
      }

      .swing {
        animation: swing 1.5s 0.15s linear infinite;
      }

      .content-item1 {
        left: 0;
      }

      .content-item2 {
        left: 325px * 0.7;
      }

      .content-item3 {
        left: 650px * 0.7;
      }

      .content-item4 {
        left: 975px * 0.7;
      }

      .content-item5 {
        left: 1300px * 0.7;
      }

      .rotate-item1 {
        animation: rotate1 1.5s 0.15s linear infinite;
        animation-iteration-count: 1;
        animation-fill-mode: forwards;
      }

      .rotate-item2 {
        animation: rotate2 1.5s 0.15s linear infinite;
        animation-iteration-count: 1;
        animation-fill-mode: forwards;
      }

      .rotate-item3 {
        animation: rotate3 1.5s 0.15s linear infinite;
        animation-iteration-count: 1;
        animation-fill-mode: forwards;
      }

      .rotate-item4 {
        animation: rotate4 1.5s 0.15s linear infinite;
        animation-iteration-count: 1;
        animation-fill-mode: forwards;
      }

      .bottom-img {
        position: absolute;
        height: 205px * 0.7;
        cursor: pointer;
      }

      .bottom-img1 {
        left: 0;
        bottom: 193px * 0.7;
      }

      .bottom-img2 {
        left: 202px * 0.7;
        bottom: 165px * 0.7;
      }

      .bottom-img3 {
        left: 403px * 0.7;
        bottom: 132px * 0.7;
      }

      .bottom-img4 {
        left: 606px * 0.7;
        bottom: 122px * 0.7;
      }

      .bottom-img5 {
        left: 807px * 0.7;
        bottom: 122px * 0.7;
      }

      .bottom-img6 {
        left: 1009px * 0.7;
        bottom: 132px * 0.7;
      }

      .bottom-img7 {
        left: 1210px * 0.7;
        bottom: 165px * 0.7;
      }

      .bottom-img8 {
        left: 1412px * 0.7;
        right: 0;
        bottom: 193px * 0.7;
      }
    }

    .footer {
      position: absolute;
      bottom: 22px * 0.7;
      width: 1572px * 0.7;
      height: 141px * 0.7;
      z-index: 3;

      .img1 {
        position: absolute;
        top: 0;
        left: 0;
        height: 141px * 0.7;
        cursor: pointer;
      }

      .img2 {
        position: absolute;
        top: 0;
        right: 0;
        height: 141px * 0.7;
        cursor: pointer;
      }
    }
  }
}

@keyframes swing {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-40deg);
  }
  50% {
    transform: rotate(0deg);
  }
  75% {
    transform: rotate(40deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes rotate1 {
  80% {
    height: 205px * 0.7;
    left: 1025px * 0.7;
    bottom: 762px * 0.7;
  }
  100% {
    height: 275px * 0.7;
    left: 1010px * 0.7;
    bottom: 552px * 0.7;
  }
}

@keyframes rotate2 {
  80% {
    height: 205px * 0.7;
    left: 440px * 0.7;
    bottom: 762px * 0.7;
  }
  100% {
    height: 275px * 0.7;
    left: 360px * 0.7;
    bottom: 552px * 0.7;
  }
}

@keyframes rotate3 {
  80% {
    height: 205px * 0.7;
    left: 765px * 0.7;
    bottom: 762px * 0.7;
  }
  100% {
    height: 275px * 0.7;
    left: 685px * 0.7;
    bottom: 552px * 0.7;
  }
}

@keyframes rotate4 {
  80% {
    height: 205px * 0.7;
    left: 1415px * 0.7;
    bottom: 762px * 0.7;
  }
  100% {
    height: 275px * 0.7;
    left: 1335px * 0.7;
    bottom: 552px * 0.7;
  }
}
</style>