<template>
  <div class="game51-page">
    <div class="page-bg"></div>
    <settingPageLib title="一日三餐-图文再认" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、许多脑损伤的人都存在着如下的烦恼:“刚刚吃过饭就忘记吃的是什么”。本训练的目的旨在，让用户熟悉常见的一日三餐的内容，从而对现实生活中的食物加深记忆。</p>
        <p class="synopsis-content">2、本训练将分别呈现早餐、中餐和晚餐食品图片。请尽量记住它们。</p>
        <p class="synopsis-content">3、然后要求您在系统提供的词语中挑选出来食品图片的信息。</p>
      </div>
    </settingPageLib>
    <div class="game-content" v-if="status === 3">
      <img class="left-icon" :style="{opacity: step === 3 ? 1 : 0}" src="/static/game_assets/game50/left_2.png" />
      <img class="right-icon" :style="{opacity: step === 3 ? 1 : 0}" src="/static/game_assets/game50/right_2.png" />
      <div v-if="step === 1" :class="['question-img', number === 1 && 'question-img-1', number === 2 && 'question-img-2', number === 3 && 'question-img-3']"></div>

      <!-- 题目 -->
      <template v-if="step === 2">
        <img class="content-icon" src="/static/game_assets/game50/content_icon.png" />

        <div class="title">
          <img class="title-bg" src="/static/game_assets/game50/title_bg.png" />
          <p class="title-text">早餐：<span v-for="item in questionList" :key="item.name + 'title'">{{item.name}}</span></p>
        </div>

        <div :class="['content', questionList.length === 4 && 'content-1']">
          <div class="content-item" v-for="item in questionList" :key="item.name">
            <p class="text">{{item.name}}</p>
          </div>
        </div>
      </template>

      <!-- 选项 -->
      <template v-if="step === 3">
        <div class="content-answer">
          <div class="content-item" v-for="item in answerList" :key="item.name + 'img'" @click="chooseItem(item)">
            <img class="bg" src="/static/game_assets/game50/text_bg.png" />
            <p class="text">{{item.name}}</p>
            <div :class="[choose.includes(item.name) && 'choose-item']"></div>
          </div>
        </div>
      </template>

      <!-- 答案 -->
      <template v-if="step === 4">
        <img class="content-icon" src="/static/game_assets/game50/content_icon.png" />

        <div class="title">
          <img class="title-bg" src="/static/game_assets/game50/title_bg.png" />
          <p class="title-text">您的答案：<span v-for="item in choose" :key="item + 'answer'" :style="{'color' : !isHaveItem(item) ? '#E20505' : '#1B1D2D'}">{{item}}</span></p>
        </div>

        <div :class="['content', questionList.length === 4 && 'content-1']">
          <div class="content-item" v-for="item in questionList" :key="item.name">
            <p class="text">{{item.name}}</p>
          </div>
        </div>
      </template>

      <div class="footer">
        <div class="left-group">
          <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
          <img v-if="step === 1 || step === 4" class="img3" src="/static/game_assets/common/continue.png" @click="goon">
          <img v-if="step === 2" class="img3" src="/static/game_assets/common/next_step.png" @click="next">
        </div>
        <img v-if="step === 3 && choose.length === questionList.length" class="img1" src="/static/game_assets/common/finish.png" @click="finish">
        <!-- <img v-if="step === 4" class="img3" src="/static/game_assets/common/reset.png" @click="reset"> -->
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from '../game50/data/data.json'

export default {
  name: 'game51',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data () {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      step: 1, // 1-引导页 2-题目 3-选项 4-答案
      show: false,
      isShowCorrect: false,
      isShowError: false,
      choose: [],
      questionList: [],
      answerList: [],

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start () {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      if (this.number === 0) {
        const list1 = data.breakfast.filter(item => item.type === 1)
        const list2 = data.breakfast.filter(item => item.type === 2)
        const list3 = data.breakfast.filter(item => item.type === 3)
        this.questionList = api.getRandomArray(list1, 1).concat(api.getRandomArray(list2, 1)).concat(api.getRandomArray(list3, 1))
        this.answerList = api.shuffle(data.breakfast)
      } else if (this.number === 1) {
        const list1 = data.lunch.filter(item => item.type === 1)
        const list2 = data.lunch.filter(item => item.type === 2)
        const list3 = data.lunch.filter(item => item.type === 3)
        this.questionList = api.getRandomArray(list1, 2).concat(api.getRandomArray(list2, 1)).concat(api.getRandomArray(list3, 1))
        this.answerList = api.shuffle(data.lunch)
      } else if (this.number === 2) {
        const list1 = data.dinner.filter(item => item.type === 1)
        const list2 = data.dinner.filter(item => item.type === 2)
        const list3 = data.dinner.filter(item => item.type === 3)
        this.questionList = api.getRandomArray(list1, 1).concat(api.getRandomArray(list2, 1)).concat(api.getRandomArray(list3, 1))
        this.answerList = api.shuffle(data.dinner)
      }
      this.number++
    },

    chooseItem (item) {
      if (this.choose.includes(item.name)) {
        this.choose = this.choose.filter(it => item.name !== it)
        return
      }
      if (this.choose.length >= this.questionList.length) return
      this.choose.push(item.name)
      this.choose = Array.from(new Set(this.choose))
    },

    isHaveItem (item) {
      const list = this.questionList.map(item => item.name)
      return list.includes(item)
    },

    stop () {
      if (this.isShowError || this.isShowCorrect) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    },

    goon () {
      if (this.step === 1) {
        this.step = 2
        return
      }
      const list = this.questionList.map(item => item.name)
      this.choose.forEach(item => {
        if (list.includes(item)) {
          this.succesNum++
        } else {
          this.errorNum++
        }
      })

      if (this.number < 3) {
        this.questionList = []
        this.answerList = []
        this.choose = []
        this.step = 1
        this.startProcess()
      } else {
        this.isStop = true
        this.store = 10 * this.succesNum
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: '',
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    next () {
      this.step = 3
    },

    finish () {
      this.step = 4
      const list = this.questionList.map(item => item.name)
      for (let i = 0; i < this.choose.length; i++) {
        const item = this.choose[i]
        if (!list.includes(item)) {
          this.isShowError = true
          break
        }

        if (i === this.choose.length - 1) {
          this.isShowCorrect = true
        }
      }
      setTimeout(() => {
        this.isShowCorrect = false
        this.isShowError = false
      }, 800)
    },

    reset () {
      this.choose = []
      this.step = 1
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.step = 1
      this.isStop = false
      this.questionList = []
      this.answerList = []
      this.choose = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game51-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game50/bg.png');
  }

  .game-synopsis {
    width: 1576px * 0.7;
    height: 1066px * 0.7;
    margin-top: 14px * 0.7;
    padding: 300px * 0.7 354px * 0.7 0 440px * 0.7;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game22/info_bg.png');

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px * 0.7;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #1e1e1d;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px * 0.7;
      line-height: 45px * 0.7;
      font-weight: 400;
      color: #1e1e1d;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .question-img {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
    }

    .question-img-1 {
      background: url('/static/game_assets/game50/bg_1.png') center center no-repeat;
      background-size: cover;
    }

    .question-img-2 {
      background: url('/static/game_assets/game50/bg_2.png') center center no-repeat;
      background-size: cover;
    }

    .question-img-3 {
      background: url('/static/game_assets/game50/bg_3.png') center center no-repeat;
      background-size: cover;
    }

    .left-icon {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 427px * 0.7;
      z-index: 2;
    }

    .right-icon {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 524px * 0.7;
      z-index: 2;
    }

    .content-icon {
      position: absolute;
      width: 100%;
      left: 0;
      right: 0;
      bottom: 125px * 0.7;
      z-index: 3;
    }

    .title {
      position: absolute;
      top: 0;
      width: 1196px * 0.7;
      height: 122px * 0.7;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1196px * 0.7;
        height: 122px * 0.7;
      }

      .title-text {
        position: relative;
        margin: 0;
        padding: 30px * 0.7 0 54px * 0.7 0;
        font-size: 36px * 0.7;
        line-height: 38px * 0.7;
        text-align: center;
        color: #1b1d2d;
        user-select: none;

        span {
          display: inline-block;
          padding-right: 10px * 0.7;
        }
      }
    }

    .content {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      width: 1445px * 0.7;
      height: 772px * 0.7;
      padding: 235px * 0.7 156px * 0.7 288px * 0.7 156px * 0.7;
      background: rgba(255, 255, 255, 0.88);
      border-radius: 48px * 0.7;
      z-index: 2;

      .content-item {
        position: relative;
        width: 364px * 0.7;
        height: 252px * 0.7;
        border: 3px * 0.7 solid #d2d2d2;
        border-radius: 45px * 0.7;
        background: #fff;

        .text {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          margin: 0;
          padding: 16px * 0.7 0;
          font-size: 47px * 0.7;
          font-weight: 600;
          line-height: 47px * 0.7;
          text-align: center;
          color: #3f2b2a;
          background: rgba(255, 255, 255, 0.77);
          border-radius: 0 0 45px * 0.7 45px * 0.7;
          user-select: none;
        }
      }
    }

    .content-1 {
      padding: 90px * 0.7 350px * 0.7 122px * 0.7 350px * 0.7;
    }

    .content-answer {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      width: 1549px * 0.7;
      height: 894px * 0.7;
      padding: 140px * 0.7 124px * 0.7;
      background: rgba(255, 255, 255, 0.88);
      border-radius: 48px * 0.7;
      z-index: 2;

      .content-item {
        position: relative;
        width: 268px * 0.7;
        height: 120px * 0.7;
        z-index: 99;
        margin: -16px * 0.7 -5px * 0.7;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 268px * 0.7;
          height: 120px * 0.7;
        }

        .text {
          position: relative;
          margin: 0;
          margin: 14px * 0.7 18px * 0.7 30px * 0.7 18px * 0.7;
          padding: 28px * 0.7 0;
          font-size: 32px * 0.7;
          line-height: 36px * 0.7;
          text-align: center;
          color: #2d3632;
          user-select: none;
        }

        .choose-item {
          position: absolute;
          top: 14px * 0.7;
          left: 18px * 0.7;
          width: 231px * 0.7;
          height: 92px * 0.7;
          border: 3px * 0.7 solid #007cfb;
          border-radius: 32px * 0.7;
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 35px * 0.7;
      display: flex;
      justify-content: space-between;
      width: 1528px * 0.7;
      padding-right: 30px * 0.7;
      z-index: 4;

      .left-group {
        width: 580px * 0.7;
        display: flex;
        justify-content: space-between;
      }

      .img1 {
        width: 270px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }

      .img3 {
        width: 268px * 0.7;
        height: 115px * 0.7;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px * 0.7;
      margin-bottom: 40px * 0.7;
      z-index: 99;
    }
  }
}
</style>