<template>
  <div class="game186-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPageLib title="认识钱币" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">通过观察与学习，提高训练者对钱币的认识。</p>
      </div>
    </settingPageLib>

    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="content-top">
          <span v-for="item in 7" :key="item + 'item'">
            <img :class="['top-item', 'item-left', (number + 1 === item && isPlay) && 'flash-item']" :src="`/static/game_assets/game186/item_${item}_1.png`" />
            <img :class="['top-item', 'item-right', (number + 1 === item && isPlay) && 'flash-item']" :src="`/static/game_assets/game186/item_${item}_2.png`" />
          </span>
        </div>

        <div class="content-bottom">
          <span v-for="item in 3" :key="item + 'item-1'">
            <img :class="['bottom-item', (number - 6 === item && isPlay) && 'flash-item']" :src="`/static/game_assets/game186/item_${item + 7}_1.png`" />
            <img :class="['bottom-item', (number - 6 === item && isPlay) && 'flash-item']" :src="`/static/game_assets/game186/item_${item + 7}_2.png`" />
          </span>
        </div>
      </div>
    </div>

    <resultPageLib v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPageLib>
  </div>
</template>

<script>
import settingPage from '../component/settingPageCmpLib.vue'
import resultPage from '../component/resultPageCmpLib.vue'
import data from './data/data.json'

export default {
  name: 'game186',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    'settingPageLib': settingPage,
    'resultPageLib': resultPage
  },

  data () {
    return {
      musicUrl: '',
      number: 0,
      second: 0,
      store: 0,
      level: 1,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted () {
    this.timing()
  },

  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play (url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    pause () {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnd () {
      this.isPlay = false
      setTimeout(() => {
        this.number++
        if (this.number < 10) {
          this.startProcess()
        } else {
          this.number = 0
        }
      }, 1500)
    },

    start () {
      this.level = Number(this.info.level) || 1
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess () {
      this.$nextTick(() => {
        this.play(`/static/game_assets/audio/game186/audio_${this.number + 1}.mp3`)
      })
    },

    submit () {
      this.isStop = true
      this.store = this.succesNum * 10
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again () {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game186-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game186/bg.png');
  }

  .game-synopsis {
    width: 860px * 0.7;
    height: 500px * 0.7;
    margin: 34px * 0.7;
    padding: 33px * 0.7 30px * 0.7;
    background: #fff;
    border-radius: 36px * 0.7;
    border: 2px * 0.7 solid #75bffc;

    .synopsis-title {
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 600;
      color: #5e381f;
      padding-bottom: 18px * 0.7;
    }

    .synopsis-content {
      padding-bottom: 18px * 0.7;
      margin: 0;
      font-size: 36px * 0.7;
      line-height: 50px * 0.7;
      font-weight: 400;
      color: #5e381f;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1316px * 0.7;
      height: 764px * 0.7;

      .content-top {
        position: relative;
        width: 100%;
        display: flex;
        flex-wrap: wrap;

        .top-item {
          height: 132px * 0.7;
          margin-bottom: 30px * 0.7;
        }

        .item-right {
          margin: 0 65px * 0.7 0 20px * 0.7;
        }
      }

      .content-bottom {
        position: relative;
        width: 100%;
        padding-top: 10px * 0.7;
        display: flex;

        .bottom-item {
          position: relative;
          width: 108px * 0.7;
          margin-right: 33px * 0.7;
        }
      }

      .flash-item {
        animation: flash 1.5s linear infinite;
      }
    }
  }
}

@keyframes flash {
  0% {
    transform: scale(1);
  }
  60% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style>