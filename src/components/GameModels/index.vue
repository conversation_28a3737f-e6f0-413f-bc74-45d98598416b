<template>
  <div class="games-container">
    <component :is="getComponentName" :info="info" />
  </div>
</template>
<script setup>
import { getGameInfo } from '/@/api/index';
import { ElMessage } from 'element-plus';
import game1 from "/@/components/GameModels/lib/game1/index.vue";
import game4 from "/@/components/GameModels/lib/game4/index.vue";
import game6 from "/@/components/GameModels/lib/game6/index.vue";
import game8 from "/@/components/GameModels/lib/game8/index.vue";
import game9 from "/@/components/GameModels/lib/game9/index.vue";
import game10 from "/@/components/GameModels/lib/game10/index.vue";
import game11 from "/@/components/GameModels/lib/game11/index.vue";
import game12 from "/@/components/GameModels/lib/game12/index.vue";
import game14 from "/@/components/GameModels/lib/game14/index.vue";
import game15 from "/@/components/GameModels/lib/game15/index.vue";
import game16 from "/@/components/GameModels/lib/game16/index.vue";
import game17 from "/@/components/GameModels/lib/game17/index.vue";
import game18 from "/@/components/GameModels/lib/game18/index.vue";
import game19 from "/@/components/GameModels/lib/game19/index.vue";
import game20 from "/@/components/GameModels/lib/game20/index.vue";
import game21 from "/@/components/GameModels/lib/game21/index.vue";
import game22 from "/@/components/GameModels/lib/game22/index.vue";
import game23 from "/@/components/GameModels/lib/game23/index.vue";
import game24 from "/@/components/GameModels/lib/game24/index.vue";
import game25 from "/@/components/GameModels/lib/game25/index.vue";
import game26 from "/@/components/GameModels/lib/game26/index.vue";
import game27 from "/@/components/GameModels/lib/game27/index.vue";
import game28 from "/@/components/GameModels/lib/game28/index.vue";
import game29 from "/@/components/GameModels/lib/game29/index.vue";
import game30 from "/@/components/GameModels/lib/game30/index.vue";
import game31 from "/@/components/GameModels/lib/game31/index.vue";
import game32 from "/@/components/GameModels/lib/game32/index.vue";
import game33 from "/@/components/GameModels/lib/game33/index.vue";
import game34 from "/@/components/GameModels/lib/game34/index.vue";
import game35 from "/@/components/GameModels/lib/game35/index.vue";
import game36 from "/@/components/GameModels/lib/game36/index.vue";
import game37 from "/@/components/GameModels/lib/game37/index.vue";
import game38 from "/@/components/GameModels/lib/game38/index.vue";
import game39 from "/@/components/GameModels/lib/game39/index.vue";
import game40 from "/@/components/GameModels/lib/game40/index.vue";
import game41 from "/@/components/GameModels/lib/game41/index.vue";
import game42 from "/@/components/GameModels/lib/game42/index.vue";
import game43 from "/@/components/GameModels/lib/game43/index.vue";
import game44 from "/@/components/GameModels/lib/game44/index.vue";
import game45 from "/@/components/GameModels/lib/game45/index.vue";
import game46 from "/@/components/GameModels/lib/game46/index.vue";
import game47 from "/@/components/GameModels/lib/game47/index.vue";
import game48 from "/@/components/GameModels/lib/game48/index.vue";
import game50 from "/@/components/GameModels/lib/game50/index.vue";
import game51 from "/@/components/GameModels/lib/game51/index.vue";
import game52 from "/@/components/GameModels/lib/game52/index.vue";
import game55 from "/@/components/GameModels/lib/game55/index.vue";
import game56 from "/@/components/GameModels/lib/game56/index.vue";
import game57 from "/@/components/GameModels/lib/game57/index.vue";
import game59 from "/@/components/GameModels/lib/game59/index.vue";
import game60 from "/@/components/GameModels/lib/game60/index.vue";
import game61 from "/@/components/GameModels/lib/game61/index.vue";
import game64 from "/@/components/GameModels/lib/game64/index.vue";
import game65 from "/@/components/GameModels/lib/game65/index.vue";
import game66 from "/@/components/GameModels/lib/game66/index.vue";
import game67 from "/@/components/GameModels/lib/game67/index.vue";
import game68 from "/@/components/GameModels/lib/game68/index.vue";
import game69 from "/@/components/GameModels/lib/game69/index.vue";
import game70 from "/@/components/GameModels/lib/game70/index.vue";
import game71 from "/@/components/GameModels/lib/game71/index.vue";
import game72 from "/@/components/GameModels/lib/game72/index.vue";
import game73 from "/@/components/GameModels/lib/game73/index.vue";
import game74 from "/@/components/GameModels/lib/game74/index.vue";
import game76 from "/@/components/GameModels/lib/game76/index.vue";
import game77 from "/@/components/GameModels/lib/game77/index.vue";
import game78 from "/@/components/GameModels/lib/game78/index.vue";
import game79 from "/@/components/GameModels/lib/game79/index.vue";
import game80 from "/@/components/GameModels/lib/game80/index.vue";
import game81 from "/@/components/GameModels/lib/game81/index.vue";
import game82 from "/@/components/GameModels/lib/game82/index.vue";
import game83 from "/@/components/GameModels/lib/game83/index.vue";
import game84 from "/@/components/GameModels/lib/game84/index.vue";
import game85 from "/@/components/GameModels/lib/game85/index.vue";
import game92 from "/@/components/GameModels/lib/game92/index.vue";
import game95 from "/@/components/GameModels/lib/game95/index.vue";
import game96 from "/@/components/GameModels/lib/game96/index.vue";
import game97 from "/@/components/GameModels/lib/game97/index.vue";
import game99 from "/@/components/GameModels/lib/game99/index.vue";
import game100 from "/@/components/GameModels/lib/game100/index.vue";
import game101 from "/@/components/GameModels/lib/game101/index.vue";
import game102 from "/@/components/GameModels/lib/game102/index.vue";
import game103 from "/@/components/GameModels/lib/game103/index.vue";
import game104 from "/@/components/GameModels/lib/game104/index.vue";
import game105 from "/@/components/GameModels/lib/game105/index.vue";
import game107 from "/@/components/GameModels/lib/game107/index.vue";
import game108 from "/@/components/GameModels/lib/game108/index.vue";
import game109 from "/@/components/GameModels/lib/game109/index.vue";
import game110 from "/@/components/GameModels/lib/game110/index.vue";
import game111 from "/@/components/GameModels/lib/game111/index.vue";
import game112 from "/@/components/GameModels/lib/game112/index.vue";
import game113 from "/@/components/GameModels/lib/game113/index.vue";
import game114 from "/@/components/GameModels/lib/game114/index.vue";
import game115 from "/@/components/GameModels/lib/game115/index.vue";
import game116 from "/@/components/GameModels/lib/game116/index.vue";
import game117 from "/@/components/GameModels/lib/game117/index.vue";
import game118 from "/@/components/GameModels/lib/game118/index.vue";
import game119 from "/@/components/GameModels/lib/game119/index.vue";
import game120 from "/@/components/GameModels/lib/game120/index.vue";
import game121 from "/@/components/GameModels/lib/game121/index.vue";
import game122 from "/@/components/GameModels/lib/game122/index.vue";
import game123 from "/@/components/GameModels/lib/game123/index.vue";
import game124 from "/@/components/GameModels/lib/game124/index.vue";
import game125 from "/@/components/GameModels/lib/game125/index.vue";
import game126 from "/@/components/GameModels/lib/game126/index.vue";
import game127 from "/@/components/GameModels/lib/game127/index.vue";
import game128 from "/@/components/GameModels/lib/game128/index.vue";
import game129 from "/@/components/GameModels/lib/game129/index.vue";
import game130 from "/@/components/GameModels/lib/game130/index.vue";
import game131 from "/@/components/GameModels/lib/game131/index.vue";
import game132 from "/@/components/GameModels/lib/game132/index.vue";
import game133 from "/@/components/GameModels/lib/game133/index.vue";
import game134 from "/@/components/GameModels/lib/game134/index.vue";
import game135 from "/@/components/GameModels/lib/game135/index.vue";
import game136 from "/@/components/GameModels/lib/game136/index.vue";
import game137 from "/@/components/GameModels/lib/game137/index.vue";
import game138 from "/@/components/GameModels/lib/game138/index.vue";
import game139 from "/@/components/GameModels/lib/game139/index.vue";
import game140 from "/@/components/GameModels/lib/game140/index.vue";
import game141 from "/@/components/GameModels/lib/game141/index.vue";
import game142 from "/@/components/GameModels/lib/game142/index.vue";
import game143 from "/@/components/GameModels/lib/game143/index.vue";
import game144 from "/@/components/GameModels/lib/game144/index.vue";
import game145 from "/@/components/GameModels/lib/game145/index.vue";
import game146 from "/@/components/GameModels/lib/game146/index.vue";
import game147 from "/@/components/GameModels/lib/game147/index.vue";
import game148 from "/@/components/GameModels/lib/game148/index.vue";
import game149 from "/@/components/GameModels/lib/game149/index.vue";
import game150 from "/@/components/GameModels/lib/game150/index.vue";
import game151 from "/@/components/GameModels/lib/game151/index.vue";
import game152 from "/@/components/GameModels/lib/game152/index.vue";
import game153 from "/@/components/GameModels/lib/game153/index.vue";
import game154 from "/@/components/GameModels/lib/game154/index.vue";
import game155 from "/@/components/GameModels/lib/game155/index.vue";
import game156 from "/@/components/GameModels/lib/game156/index.vue";
import game157 from "/@/components/GameModels/lib/game157/index.vue";
import game158 from "/@/components/GameModels/lib/game158/index.vue";
import game159 from "/@/components/GameModels/lib/game159/index.vue";
import game160 from "/@/components/GameModels/lib/game160/index.vue";
import game161 from "/@/components/GameModels/lib/game161/index.vue";
import game162 from "/@/components/GameModels/lib/game162/index.vue";
import game163 from "/@/components/GameModels/lib/game163/index.vue";
import game164 from "/@/components/GameModels/lib/game164/index.vue";
import game165 from "/@/components/GameModels/lib/game165/index.vue";
import game166 from "/@/components/GameModels/lib/game166/index.vue";
import game167 from "/@/components/GameModels/lib/game167/index.vue";
import game168 from "/@/components/GameModels/lib/game168/index.vue";
import game169 from "/@/components/GameModels/lib/game169/index.vue";
import game170 from "/@/components/GameModels/lib/game170/index.vue";
import game171 from "/@/components/GameModels/lib/game171/index.vue";
import game172 from "/@/components/GameModels/lib/game172/index.vue";
import game173 from "/@/components/GameModels/lib/game173/index.vue";
import game174 from "/@/components/GameModels/lib/game174/index.vue";
import game175 from "/@/components/GameModels/lib/game175/index.vue";
import game176 from "/@/components/GameModels/lib/game176/index.vue";
import game177 from "/@/components/GameModels/lib/game177/index.vue";
import game178 from "/@/components/GameModels/lib/game178/index.vue";
import game179 from "/@/components/GameModels/lib/game179/index.vue";
import game180 from "/@/components/GameModels/lib/game180/index.vue";
import game181 from "/@/components/GameModels/lib/game181/index.vue";
import game182 from "/@/components/GameModels/lib/game182/index.vue";
import game183 from "/@/components/GameModels/lib/game183/index.vue";
import game184 from "/@/components/GameModels/lib/game184/index.vue";
import game185 from "/@/components/GameModels/lib/game185/index.vue";
import game186 from "/@/components/GameModels/lib/game186/index.vue";
import game187 from "/@/components/GameModels/lib/game187/index.vue";
import game188 from "/@/components/GameModels/lib/game188/index.vue";
import game189 from "/@/components/GameModels/lib/game189/index.vue";
import game190 from "/@/components/GameModels/lib/game190/index.vue";
import game191 from "/@/components/GameModels/lib/game191/index.vue";
import game192 from "/@/components/GameModels/lib/game192/index.vue";
import game193 from "/@/components/GameModels/lib/game193/index.vue";
import game194 from "/@/components/GameModels/lib/game194/index.vue";
import game195 from "/@/components/GameModels/lib/game195/index.vue";
import game196 from "/@/components/GameModels/lib/game196/index.vue";
import game197 from "/@/components/GameModels/lib/game197/index.vue";
import game198 from "/@/components/GameModels/lib/game198/index.vue";
import game199 from "/@/components/GameModels/lib/game199/index.vue";
import game200 from "/@/components/GameModels/lib/game200/index.vue";
import game201 from "/@/components/GameModels/lib/game201/index.vue";
import game202 from "/@/components/GameModels/lib/game202/index.vue";
import game203 from "/@/components/GameModels/lib/game203/index.vue";
import game204 from "/@/components/GameModels/lib/game204/index.vue";
import game205 from "/@/components/GameModels/lib/game205/index.vue";
import game206 from "/@/components/GameModels/lib/game206/index.vue";
import game207 from "/@/components/GameModels/lib/game207/index.vue";
import game208 from "/@/components/GameModels/lib/game208/index.vue";
import game209 from "/@/components/GameModels/lib/game209/index.vue";
import game210 from "/@/components/GameModels/lib/game210/index.vue";
import game211 from "/@/components/GameModels/lib/game211/index.vue";
import game212 from "/@/components/GameModels/lib/game212/index.vue";
import game213 from "/@/components/GameModels/lib/game213/index.vue";
import game214 from "/@/components/GameModels/lib/game214/index.vue";
import game215 from "/@/components/GameModels/lib/game215/index.vue";
import game216 from "/@/components/GameModels/lib/game216/index.vue";
import game217 from "/@/components/GameModels/lib/game217/index.vue";
import game218 from "/@/components/GameModels/lib/game218/index.vue";/**/
const props = defineProps(['componentName', 'id']);
defineOptions({
  components: { game1, game4, game6, game8, game9, game10, game11, game12, game14, game15, game16, game17, game18, game19, game20, game21, game22, game23, game24, game25, game26, game27, game28, game29, game30, game31, game32, game33, game34, game35, game36, game37, game38, game39, game40, game41, game42, game43, game44, game45, game46, game47, game48, game50, game51, game52, game55, game56, game57, game59, game60, game61, game64, game65, game66, game67, game68, game69, game70, game71, game72, game73, game74, game76, game77, game78, game79, game80, game81, game82, game83, game84, game85, game92, game95, game96, game97, game99, game100, game101, game102, game103, game104, game105, game107, game108, game109, game110, game111, game112, game113, game114, game115, game116, game117, game118, game119, game120, game121, game122, game123, game124, game125, game126, game127, game128, game129, game130, game131, game132, game133, game134, game135, game136, game137, game138, game139, game140, game141, game142, game143, game144, game145, game146, game147, game148, game149, game150, game151, game152, game153, game154, game155, game156, game157, game158, game159, game160, game161, game162, game163, game164, game165, game166, game167, game168, game169, game170, game171, game172, game173, game174, game175, game176, game177, game178, game179, game180, game181, game182, game183, game184, game185, game186, game187, game188, game189, game190, game191, game192, game193, game194, game195, game196, game197, game198, game199, game200, game201, game202, game203, game204, game205, game206, game207, game208, game209, game210, game211, game212, game213, game214, game215, game216, game217, game218 }
})
const info = ref({
  level: null
})
watch(() => props.id, (n, o) => {
  getInfo(props.id)
})
watch(() => props.componentName, (n, o) => {
  console.log(111, props.componentName);
})
const getComponentName = computed(() => {
  return props.componentName
})
onMounted(() => {
  console.log(props)
  if (props.id) {
    getInfo(props.id)
  }
});
function getInfo (id) {
  getGameInfo({ id: id }).then((res) => {
    if (res.success) {
      info.value = res.result
      info.value.id = id
    } else {
      ElMessage({
        message: res.message,
        type: 'warning',
      });
    }
  })
}
</script>

<style lang="scss">
.games-container {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;

  .page-content4 {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .content {
      padding-bottom: 200px * 0.7;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .number-bg {
        width: 1148px * 0.7;
        position: relative;
        z-index: 1;
        background-image: url('/static/game_assets/game1/images/number-bg.png');
        background-size: 100% auto;
        background-repeat: no-repeat;
      }

      .number {
        position: absolute;
        z-index: 2;
        top: 100px * 0.7;
        display: flex;
        align-items: flex-end;

        .value {
          font-size: 170px * 0.7;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          line-height: 150px * 0.7;
          color: #ffffff;
          background-image: linear-gradient(124deg, #ff7b00 0%, #e82f2f 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .unit {
          font-size: 48px * 0.7;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 67px * 0.7;
          background-image: linear-gradient(121deg, #ffc500 0%, #ff5353 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .info {
        width: 950px * 0.7;
        position: absolute;
        z-index: 2;
        top: 400px * 0.7;
      }
    }
  }
}
</style>