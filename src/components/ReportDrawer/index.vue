<template>
  <el-drawer v-model="showDrawer" size="40%" title='查看详情'>
    <div class='no-print' style='text-align: right;margin-right: 35px;'>
      <el-button v-print="'#reportCommonContent'" ghost type='primary' style='margin-right: 10px;'>打印</el-button>
    </div>
    <div id='reportCommonContent'>
      <p v-html='datas'></p>
    </div>
  </el-drawer>

</template>
<script setup>
  import { generatePrintHtml } from '/@/api/index';
  
  const showDrawer = ref(false)
  const resultId = ref('')
  const datas = ref('')
  function show(record) {
    showDrawer.value = true
    resultId.value = record.id
    loadDetail()
  }

  function loadDetail() {
    let params = {
      ids: resultId.value
    };
    generatePrintHtml(params).then((result) => {
      datas.value = result.result;
    });
  }

    // 暴露方法
  defineExpose({
    show,
  });
</script>