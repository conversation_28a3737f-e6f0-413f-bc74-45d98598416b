// 获取assets静态资源
export const getAssetsFile = (path: string, url: string) => {
  // console.log(url);
  if (url && url.indexOf('http') == -1) {
    return new URL(`../assets/images/${path}/${url}`, import.meta.url).href;
  } else {
    return url;
  }
};

// 手机号检验
export const validatePhone = (phone: string) => {
  const regex = /^1[3-9]\d{9}$/;
  return regex.test(phone);
};

// 当前时间
export const getNowDate = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() >= 9 ? now.getMonth() + 1 : `0${now.getMonth() + 1}`;
  const date = now.getDate() >= 10 ? now.getDate() : `0${now.getDate()}`;
  // const hour = now.getHours() >= 10 ? now.getHours() : `0${now.getHours()}`
  // const minutes = now.getMinutes() >= 10 ? now.getMinutes() : `0${now.getMinutes()}`
  // const seconds = now.getSeconds() >= 10 ? now.getSeconds() : `0${now.getSeconds()}`
  return `${year}-${month}-${date}`;
};
// 当前时间
export const getNowTime = () => {
  const now = new Date();
  // const year = now.getFullYear();
  // const month = now.getMonth() >= 9 ? now.getMonth() + 1 : `0${now.getMonth() + 1}`;
  // const date = now.getDate() >= 10 ? now.getDate() : `0${now.getDate()}`;
  const hour = now.getHours() >= 10 ? now.getHours() : `0${now.getHours()}`;
  const minutes = now.getMinutes() >= 10 ? now.getMinutes() : `0${now.getMinutes()}`;
  // const seconds = now.getSeconds() >= 10 ? now.getSeconds() : `0${now.getSeconds()}`;
  return `${hour}:${minutes}`;
};
// 根据size分割数组
export const chunkArray = (chunkSize: number, array: any) => {
  let chunks: any = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    let chunk = array.slice(i, i + chunkSize);
    chunks.push(chunk);
  }
  return chunks;
};
// 根据秒数转换时分秒
export const transitionTime = (duration) => {
  let timeStr = '';
  let minutesStr = '00';
  let secondsStr = '00';
  if (duration < 60) {
    if (duration < 10) {
      secondsStr = '0' + duration;
    } else {
      secondsStr = '' + duration;
    }
  } else {
    let poor = Math.floor(duration / 60);
    let seconds = duration - poor * 60;
    if (seconds < 10) {
      secondsStr = '0' + seconds;
    } else {
      secondsStr = seconds + '';
    }
    if (poor < 10) {
      minutesStr = '0' + poor;
    } else {
      minutesStr = poor + '';
    }
  }
  timeStr = minutesStr + ':' + secondsStr;
  return timeStr;
};
