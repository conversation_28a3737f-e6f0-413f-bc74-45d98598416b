import { resolve } from 'path';

const fs = require('fs');

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

export const getFolder = (path: any) => {
  const components: Array<string> = [];
  const files = fs.readdirSync(path);
  files.forEach(function (item: string) {
    const stat = fs.lstatSync(path + '/' + item);
    if (stat.isDirectory() === true && item != 'components') {
      components.push(path + '/' + item);
      components.push(pathResolve(path + '/' + item));
    }
  });
  return components;
};

// 截取Url里面的参数
export const GetRequest = () => {
  const url = decodeURIComponent(location.search); // 获取url中"?"符后的字串
  const theRequest = {};
  if (url.indexOf('?') !== -1) {
    const str = url.substr(1);
    // alert(str);
    const strs = str.split('&');
    for (let i = 0; i < strs.length; i++) {
      theRequest[strs[i].split('=')[0]] = decodeURI(strs[i].split('=')[1]); // 获取中文参数转码<span style="font-family: Arial, Helvetica, sans-serif;">decodeURI</span>，（unescape只针对数字，中文乱码)
    }
  }
  return theRequest;
};
/**
 * 数据写入
 * @param {object} data  待写入数据
 * @param {string} filename 文件名
 */
export const writeF = (data, filename) => {
  const options = {
    data: data,
    filename: filename,
  };
  return window.electronApi.writeFile(options);
};
/**
 * 数据读取
 * @param {string} filename 文件名
 */
export const readF = (filename) => {
  return window.electronApi.readFile(filename);
};
/**
 * 数据删除
 * @param {string} filename 文件名
 */
export const clearF = (foldername) => {
  return window.electronApi.clearFolder(foldername);
};
