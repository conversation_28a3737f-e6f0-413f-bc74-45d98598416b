import { setToken } from '/@/utils/auth';
import { userVisitLogin, orgCode } from '/@/api/index';

const UserInfo = 'userInfo';
// const TokenPrefix = 'Bearer ';
const UserInfoPrefix = '';

const getUserInfo = () => {
  return localStorage.getItem(UserInfo);
};
const setUserInfo = (userInfo: Object) => {
  let _userInfo = Object.assign({ realname: '' }, userInfo);
  if (_userInfo.realname.indexOf(orgCode) != -1) {
    _userInfo.realname = _userInfo.realname.replace(orgCode, '');
  }
  localStorage.setItem(UserInfo, JSON.stringify(_userInfo));
};
const clearUserInfo = () => {
  return userVisitLogin(orgCode).then((result) => {
    const _result = Object.assign({ result: { token: '', userInfo: {} } }, result);
    setToken(_result.result.token);
    setUserInfo(_result.result.userInfo);
  });
};
export { UserInfoPrefix, getUserInfo, setUserInfo, clearUserInfo };
