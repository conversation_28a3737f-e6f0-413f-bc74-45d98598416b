export default (_this) => {
  ((_this) => {
    // vue 对象
    // 动态获取vue组件实例
    const appRef = _this

    // * 默认缩放值
    const scale = {
      width: '1',
      height: '1'
    }

    // * 设计稿尺寸（px）
    const baseWidth = 1920
    const baseHeight = 1080

    // * 需保持的比例（默认1.77778）
    const baseProportion = parseFloat((baseWidth / baseHeight).toFixed(5))

    // 重新设置缩放比例值
    const calcRate = () => {
      // 当前视口的宽高比例
      const currentRate = parseFloat((window.innerWidth / window.innerHeight).toFixed(5))
      if (appRef) {
        // 获取到的当前实例
        if (currentRate > baseProportion) {
          // 表示更宽
          scale.width = ((window.innerHeight * baseProportion) / baseWidth).toFixed(5)
          scale.height = (window.innerHeight / baseHeight).toFixed(5)
          appRef.style.transform = `scale(${scale.width}, ${scale.height}) translate(-50%, -50%) translate3d(0,0,0)`
        } else {
          // 表示更高
          scale.height = ((window.innerWidth / baseProportion) / baseHeight).toFixed(5)
          scale.width = (window.innerWidth / baseWidth).toFixed(5)
          appRef.style.transform = `scale(${scale.width}, ${scale.height}) translate(-50%, -50%) translate3d(0,0,0)`
        }

      }

    }

    const resize = () => {
      setTimeout(() => {
        calcRate()
      }, 200)
    }

    // 改变窗口大小重新绘制
    const windowDraw = () => {
      window.addEventListener('resize', resize)
    }
    windowDraw()
    calcRate()
  })(_this)
  /**
   * @参数说明
   * @_this 当前vue实例对象，用于获取当前的显示页面
   * @scrrenRef  vue2 中获取操作页面的根页面。页面的ref节点
   * **/
}