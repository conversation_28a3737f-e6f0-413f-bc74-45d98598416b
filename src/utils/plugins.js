
//调用各个浏览器提供的全屏方法
export function enterFullScreen () {  //进入全屏
  let de = document.documentElement;
  if (de.requestFullscreen) {
    de.requestFullscreen();
  } else if (de.mozRequestFullScreen) {
    de.mozRequestFullScreen();
  } else if (de.webkitRequestFullScreen) {
    de.webkitRequestFullScreen();
  } else if (de.msRequestFullscreen) {
    de.msRequestFullscreen();
  }
  console.log('已全屏')
}
//调用各个浏览器提供的退出全屏方法
export function exitFullscreen () {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen();
  } else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen();
  } else if (document.msExitFullscreen) {
    document.msExitFullscreen();
  }
  console.log('已还原')
}