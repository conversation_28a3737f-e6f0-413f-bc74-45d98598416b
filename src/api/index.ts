import { POST, GET, DELETE } from '/@/utils/http/axios';
import { AxiosResponse } from 'axios';
export const orgCode = window.org_code;
// export const orgCode = 'A05A05A01';

/**start 认知游戏 start**/
//获取游戏列表
export const getGameList = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/psychology/psCognize/list',
    params,
  });
};
//获取游戏详情
export const getGameIdInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/psychology/psCognize/queryById',
    params,
  });
};
//获取游戏信息
export const getGameInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/train/cogTrainResult/getTrainInfo',
    params,
  });
};
/**end 认知游戏 end**/
//用户注册
export const userRegister = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return POST({
    url: 'https://music.zhisongkeji.com/zhisong-music/sys/user/register',
    params,
  });
};
//用户登录
export const userlogin = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return POST({
    url: 'https://music.zhisongkeji.com/zhisong-music/sys/login',
    params,
  });
};
//校验登录账号
export const userRegisterCheck = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return POST({
    url: 'https://music.zhisongkeji.com/zhisong-music/sys/user/register/check',
    params,
  });
};
//用户登出
export const userLogout = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/sys/logout',
    params,
  });
};
//用户登出
export const userVisitLogin = (params?: string): Promise<AxiosResponse<any, any>> => {
  return POST({
    url: 'https://music.zhisongkeji.com/zhisong-music/sys/visitLogin?orgCode=' + params,
  });
};
//机构介绍
export const sysDepartGetByOrgCode = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/sysdepart/sysDepart/getByOrgCode',
    params,
  });
};

/**start 心理阅读  start**/
//分类
export const getklPsyReadCategory = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyReadCategory/list',
    params: params,
  });
};
//分类详情
export const getklPsyReadCategoryInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyReadCategory/queryById',
    params: params,
  });
};

//列表
export const getlistread = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyRead/list',
    params: params,
  });
};
//详情
export const getlistreadInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyRead/queryById',
    params: params,
  });
};
/**end 心理阅读  end**/
/**start 咨询辅导  start**/
//分类
export const getklPsyCounselCategoryCategory = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyCounselCategory/list',
    params: params,
  });
};
//分类详情
export const getklPsyCounselCategoryInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyCounselCategory/queryById',
    params: params,
  });
};
//列表
export const getklPsyCounsel = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyCounsel/list',
    params: params,
  });
};
//详情
export const getklPsyCounselInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyCounsel/queryById',
    params: params,
  });
};
/**end 咨询辅导  end**/
/**start 心理科普  start**/
//分类
export const getklPsyPopularCategory = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyPopularCategory/list',
    params: params,
  });
};
//分类详情
export const getklPsyPopularCategoryInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyPopularCategory/queryById',
    params: params,
  });
};

//列表
export const getklPsyPopular = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyPopular/list',
    params: params,
  });
};
//详情
export const getklPsyPopularInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyPopular/queryById',
    params: params,
  });
};
/**end 心理科普  end**/
/**start 心理影视  start**/
//分类
export const getklPsyFilmCategory = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyFilmCategory/list',
    params: params,
  });
};
//分类详情
export const getklPsyFilmCategoryInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyFilmCategory/queryById',
    params: params,
  });
};
//列表
export const getklPsyFilm = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyFilm/list',
    params: params,
  });
};
//详情
export const getklPsyFilmInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyFilm/queryById',
    params: params,
  });
};
/**end 心理影视  end**/
/**start 心理图库  start**/
//分类
export const getklPsyLibraryCategory = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyLibraryCategory/list',
    params: params,
  });
};
//列表
export const getklPsyLibrary = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyLibrary/list',
    params: params,
  });
};
//详情
export const getklPsyLibraryInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyLibrary/queryById',
    params: params,
  });
};
/**end 心理图库  end**/
/**start 心理FM  start**/
//分类
export const getklPsyFmCategory = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyFmCategory/list',
    params: params,
  });
};
//分类详情
export const getklPsyFmCategoryInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyFmCategory/queryById',
    params: params,
  });
};
//列表
export const getklPsyFm = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyFm/list',
    params: params,
  });
};
//详情
export const getklPsyFmInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyFm/queryById',
    params: params,
  });
};
/**end 心理FM  end**/
/**start 放松减压  start**/
//分类
export const getklPsyRelaxCategory = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyRelaxCategory/list',
    params: params,
  });
};
//分类详情
export const getklPsyRelaxCategoryInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyRelaxCategory/queryById',
    params: params,
  });
};

//列表
export const getklPsyRelaxFm = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyRelax/list',
    params: params,
  });
};
//详情
export const getklPsyRelaxInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyRelax/queryById',
    params: params,
  });
};
/**end 放松减压  end**/
/**start 心灵康养  start**/
//分类
export const getklPsyMentalCategory = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyMentalCategory/list',
    params: params,
  });
};
//分类详情
export const getklPsyMentalCategoryInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyMentalCategory/queryById',
    params: params,
  });
};

//列表
export const getklPsyMentalList = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyMental/list',
    params: params,
  });
};
//详情
export const getklPsyMentalInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/knowledge/klPsyMental/queryById',
    params: params,
  });
};
/**end 心灵康养  end**/
/**start 访问记录  start**/
// /knowledge/klVisitLog/start
// /knowledge/klVisitLog/end
// /knowledge/klVisitLog/list
/**end 访问记录  end**/
/**start 心理测评  start**/
//根据编号查询用户列表
export const getfindUserNumberList = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/psychology/psUser/findUserNumberList',
    params: params,
  });
};
//根据id查询用户
export const getpsUserById = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/psychology/psUser/queryById',
    params: params,
  });
};
//根据id查询用户
export const getMentalityList = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/psychology/psCategory/queryTreeList',
    params: params,
  });
};

//开始答题
export const postPsUserAddForHome = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return POST({
    url: 'https://music.zhisongkeji.com/zhisong-music/psychology/psUser/addForHome',
    params: params,
  });
};
//检查是否有未完成的测量
export const getpsUserCheckUnfinishedMeasure = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return POST({
    url: 'https://music.zhisongkeji.com/zhisong-music/psychology/psUser/checkUnfinishedMeasure',
    params: params,
  });
};
// 获取题目配置
export const getLoadAnswerPageData = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/diagnosis/dxAnswer/loadAnswerPageData',
    params: params,
  });
};
// 保存答案
export const postAddOption = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return POST({
    url: 'https://music.zhisongkeji.com/zhisong-music/diagnosis/dxResult/addOption',
    params: params,
  });
};
// 获取没答题的题目
export const getNotAnswer = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/diagnosis/dxAnswer/getNotAnswer',
    params: params,
  });
};
// Gesell量表提交
export const getUploadResult = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/diagnosis/dxResult/uploadResult',
    params: params,
  });
};
// 进入题目推送
export const postWsMessagePush = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return POST({
    url: 'https://music.zhisongkeji.com/zhisong-music/message/wsMessage/push',
    params: params,
  });
};
//PPVT量表结束答题
export const postUploadResultForPPVT = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return POST({
    url: 'https://music.zhisongkeji.com/zhisong-music/diagnosis/dxResult/uploadResultForPPVT',
    params: params,
  });
};
//注意力测验结束答题
export const postUploadResultForZYLCY = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return POST({
    url: 'https://music.zhisongkeji.com/zhisong-music/diagnosis/dxResult/uploadResultForZYLCY',
    params: params,
  });
};
//威斯康星结束答题
export const postUploadResultForWCST = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return POST({
    url: 'https://music.zhisongkeji.com/zhisong-music/diagnosis/dxResult/uploadResultForWCST',
    params: params,
  });
};

//测评记录
export const getdxResultList = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/diagnosis/dxResult/list',
    params: params,
  });
};
//删除记录
export const deldxResult = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return DELETE({
    url: 'https://music.zhisongkeji.com/zhisong-music/diagnosis/dxResult/deleteBatch?ids=' + params,
    // params: params,
  });
};

//测评查看报告
export const generatePrintHtml = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/diagnosis/dxResult/generatePrintHtml',
    params: params,
  });
};

/**end 心理测评  end**/
/**start 训练游戏  start**/
//开始训练
export const getpsUserStartTrain = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return POST({
    url: 'https://music.zhisongkeji.com/zhisong-music/psychology/psUser/startTrain',
    params: params,
  });
};
//训练详情
export const getTrainInfo = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return GET({
    url: 'https://music.zhisongkeji.com/zhisong-music/train/cogTrainResult/getTrainInfo',
    params: params,
  });
};
export const postTrainResultComplete = (params?: Object): Promise<AxiosResponse<any, any>> => {
  return POST({
    url: 'https://music.zhisongkeji.com/zhisong-music/train/cogTrainResult/complete',
    params: params,
  });
};

/**end 训练游戏  end**/
