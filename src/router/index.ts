import { createRouter, createWebHashHistory } from 'vue-router';
import routes from 'virtual:generated-pages';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';

console.log(routes);
const _routes = routes.map((item) => {
  let keepAlive = true;
  if (item.name == 'Sh-Cognitive-info') {
    keepAlive = false;
  }
  return Object.assign({}, item, {
    meta: {
      keepAlive: keepAlive,
    },
  });
});
//导入生成的路由数据
const router = createRouter({
  history: createWebHashHistory(),
  routes: _routes,
});

router.beforeEach(async (_to, _from, next) => {
  NProgress.start();
  next();
});

router.afterEach((_to) => {
  NProgress.done();
});

export default router;
