<template>
  <ScaleBox :width="1920" :height="1080" bgc="transparent" :delay="100" :isFlat="false">
    <!-- <KeepAlive>
      <router-view />
    </KeepAlive> -->
    <router-view v-slot="{ Component }">
      <keep-alive v-if="route.meta.keepAlive">
        <component :is="Component" />
      </keep-alive>
      <component :is="Component" v-if="!route.meta.keepAlive" />
    </router-view>
  </ScaleBox>
</template>
<script setup>
import ScaleBox from 'vue3-scale-box';

const route = useRoute();
const router = useRouter();
onMounted(() => {
  // router.push('/music');
});
</script>
<style>
html,
body {
  height: 100vh;
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #3e3a39;
  background-image: url('/@/assets/images/app_bg.png');
  background-size: cover;
  box-sizing: border-box;
}
body.colorWeak {
  filter: invert(80%);
}
* {
  user-select: none;
}
.el-carousel__indicators {
  display: none !important;
}
</style>
