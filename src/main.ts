window.addEventListener('message', function (evn) {
  let data = evn.data;
  setOrgCode(data);
});
if (window.android && window.android.getCode) {
  var data = window.android.getCode();
  setOrgCode(data);
}
function setOrgCode(data) {
  if (data) {
  } else {
    // data = 'A06A02A02A01';
    data = 'A05A05A01';
  }
  setOrgCode(data);
}
window.org_code = 'A05A05A01';
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import piniaStore from './store';

import '/@/styles/index.less';
import '/@/styles/reset.less';
import 'uno.css';

import ElementPlus from 'element-plus'; // 引入Element Plus 所需
import Print from 'vue3-print-nb';
import 'element-plus/dist/index.css';
import * as Icons from '@element-plus/icons-vue';
import 'virtual:svg-icons-register';

// 引入Echarts
import echarts from '/@/utils/echarts';

// 支持SVG

if (import.meta.hot) {
  if (import.meta.hot) {
    // @ts-ignore
    const originalCreateRecord = __VUE_HMR_RUNTIME__.createRecord;
    // @ts-ignore
    __VUE_HMR_RUNTIME__.createRecord = function (id, component) {
      return originalCreateRecord.call(this, id.replace(/\?.+$/, ''), component);
    };
  }
  // Other HMR related code
}

const app = createApp(App);
// Element Plus  注册所有图标
Object.keys(Icons).forEach((key) => {
  app.component(key, Icons[key]);
});

// 挂载Echarts 采用provide, inject方式使用
app.provide('$echarts', echarts);

app.use(ElementPlus);
app.use(router);
app.use(piniaStore);
app.use(Print);
app.mount('#app');
