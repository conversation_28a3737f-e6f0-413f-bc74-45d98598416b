<template>
  <Header :title="title" status="back" />
  <Container>
    <div class="info h-full flex" id="contentMian">
      <div class="info-content flex-1" v-html="info" v-loading="loading">

      </div>
    </div>
  </Container>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { getlistreadInfo } from '/@/api/index';
import { ElLoading } from 'element-plus'

const props = defineProps(['id']);
const title = ref('');
const info = ref('');

onActivated(() => {
  getList();
});
onActivated(() => {
  getList();
})

function getList () {
  const loadingInstance = ElLoading.service({ lock: true, target: '#contentMian' })
  let params = {
    id: props.id,
  };
  getlistreadInfo(params).then((result) => {
    loadingInstance.close();
    title.value = result.result.categoryName;
    info.value = result.result.content;
  });
}
</script>

<style lang="scss" scoped>
@import './style.scss';
</style>
