.bookshelf {
  padding: 0 68px;
  overflow: hidden;
  height: 660px;

  &-content {
    .bookshelf-page {
      padding: 0 200px;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: repeat(3, 1fr);
      grid-gap: 43px 93px;

      .book {
        width: 220px;
        background-image: url('../../../../assets/images/bookname.jpg');
        position: relative;
        padding-bottom: 53px;
        background-size: 220px 57px;
        background-position: left bottom;
        background-repeat: no-repeat;

        &-cover {
          width: 139px;
          height: 193px;
          position: relative;

          &-img{
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
          }

          &-text{
            position: relative;
            width: 100%;
            height: 100%;
            z-index: 2;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 15px 20px;
            color: #fff;
            font-size: 15px;
          }
        }

        &-name {
          position: absolute;
          bottom: -5px;
          width: 220px;
          height: 57px;
          padding: 0 5px;
          font-weight: 600;
          font-size: 24px;
          color: #1e79ae;
          line-height: 57px;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        // &-name {
        //   width: 220px;
        //   height: 57px;
        // }
      }
    }
  }
}
