<template>
  <Header title="心理阅读" status="home" />
  <Container>
    <Tab :tabs="tabs" :current="currentTab" @change="changeTab" />
    <div class="bookshelf w-full mt-30px" id="listMain">
      <el-carousel class="bookshelf-content" height="600px" :autoplay="false" indicator-position="outside" arrow="always" :loop="false">
        <el-carousel-item v-for="(item, index) of list" :key="index">
          <div class="bookshelf-page">
            <div class="book flex flex-col items-center" :key="_item.id" v-for="_item of item" @click="goInfo(_item.id)">
              <!-- <img v-if="_item.coverImage" class="book-cover" :src="_item.coverImage" alt="" /> -->
              <div class="book-cover">
                <img class="book-cover-img" src="/@/assets/images/bookcover.jpg" alt="">
                <span class="book-cover-text">{{ _item.title }}</span>
              </div>
              <!--  -->
              <div class="book-name">{{ _item.title }}</div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </Container>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { getlistread, getklPsyReadCategory } from '/@/api/index';
import { chunkArray } from '/@/utils/tool';

const router = useRouter();
function goInfo (id) {
  router.push('../read/info/' + id);
}
const tabs = ref([]);
const tabsIds = ref([]);
const currentTab = ref(0);
function changeTab (index) {
  currentTab.value = index;
  getList(tabsIds.value[index]);
}
const list = ref([]);
onMounted(() => {
  getType();
});
onActivated(() => {
  getType();
})

function getType () {
  let params = {
    pageNo: 1,
    pageSize: 50,
  };
  getklPsyReadCategory(params).then((result) => {
    let _list = result.result.records;
    tabs.value = _list.map((item) => item.name);
    tabsIds.value = _list.map((item) => item.id);
    getList(_list[0].id);
  });
}
function getList (id) {
  const loadingInstance = ElLoading.service({ lock: true, target:'#listMain' })
  let params = {
    pageNo: 1,
    pageSize: 50,
    categoryId: id,
  };
  getlistread(params).then((result) => {
    loadingInstance.close();
    list.value = chunkArray(8, result.result.records);
    console.log(list.value)
  });
}
</script>

<style lang="scss">
@import './style.scss';

/* 自定义Carousel右箭头 */
.bookshelf-content .el-carousel__arrow--right {
  color: #1e79ae;
  font-size: 72px;
  font-weight: 700;
  background-color: transparent; /* 箭头颜色 */
}

/* 自定义Carousel左箭头 */
.bookshelf-content .el-carousel__arrow--left {
  color: #1e79ae;
  font-size: 72px;
  font-weight: 700;
  background-color: transparent; /* 箭头颜色 */
}
</style>
