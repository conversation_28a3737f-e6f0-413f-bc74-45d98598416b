.boxpadding {
  padding: 0 68px;
}

.mask{
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: rgba($color: #000, $alpha: 30%);
  display: flex;
  align-items: center;
  justify-content: center;

  .back {
    background-image: url('/@/assets/images/back.png');
    position: absolute;
    left: 78px;
    top: 49px;
  }

  .back:active {
    background-image: url('/@/assets/images/back-active.png');
  }

  .big-pic{
    height: 90%;
    max-width: 90%;
  }

  .el-textarea__inner {
    align-items: center;
    background-color: rgb(30 121 174 / 13%);
    box-shadow: none;
    border-radius: 20px;
    padding: 32px 45px;
  }

  .el-upload--picture-card {
    background-color: transparent;
  }

  .el-table {
    font-size: 22px;
    background-color: transparent;
  }

  .el-table tr {
    background-color: transparent;
  }

  .el-table .cell,
  .el-table th div {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .el-table tbody tr:hover > td {
    background: #91c8e7 !important;
  }

  // .el-textarea__inner::-webkit-input-placeholder {
  //   color: #1e79ae;
  // }
  .el-input__wrapper {
    align-items: center;
    background-color: transparent;
    box-shadow: none;
  }

  // input输入框focus颜色样式
  .el-input {
    --el-input-focus-border: transparent;
    --el-input-focus-border-color: transparent;
  }

  .el-pagination.is-background .el-pager li.is-active {
    background-color: #1e79ae;
  }
}
