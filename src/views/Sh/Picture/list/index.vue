<template>
  <Header title="心理图库" status="home" />
  <Container>
    <div id="listMain" class="boxpadding w-full flex-1">
      <Carousel :list="list" :single="false" @info="goInfo" />
    </div>
    <div class="mask" v-if="popSwitch" @click="back()">
      <div class="header-icon-item back" @click="back()"></div>
      <img class="big-pic" :src="currentSrc" alt="">
    </div>
  </Container>
</template>

<script setup>
import { getAssetsFile, chunkArray } from '/@/utils/tool';
import { ElMessage } from 'element-plus';
import { getklPsyLibrary } from '/@/api/index';

const popSwitch = ref(false);
const currentSrc = ref("");
const list = ref([
  // [
  //   { id: 'https://localhost:1020/src/assets/images/science/zjjs.png', cover: 'zjjs.png', label: '不可能图', value: '' },
  //   { id: 2, cover: 'zymc.png', label: '似动图片', value: '' },
  //   { id: 3, cover: 'xlxsy.png', label: '其他类', value: '' },
  //   { id: 4, cover: 'xlxy.png', label: '多视图片', value: '' },
  //   { id: 5, cover: 'zjjs.png', label: '家庭教育类', value: '' },
  //   { id: 6, cover: 'zymc.png', label: '形状结合', value: '' },
  //   { id: 7, cover: 'xlxsy.png', label: '社会公德类', value: '' },
  //   { id: 8, cover: 'xlxy.png', label: '心理效应', value: '' },
  // ],
  // [
  //   { id: 9, cover: 'zjjs.png', label: '专家介绍', value: '' },
  //   { id: 10, cover: 'zymc.png', label: '专有名词', value: '' },
  //   { id: 11, cover: 'xlxsy.png', label: '心理学实验', value: '' },
  //   { id: 12, cover: 'xlxy.png', label: '心理效应', value: '' },
  // ],
]);
onMounted(() => {
  getList(1);
});
onActivated(() => {
  getList(1);
})
function getList (id) {
  const loadingInstance = ElLoading.service({ lock: true, target:'#listMain' })
  let params = {
    pageNo: 1,
    pageSize: 99,
    categoryId: id,
  };
  getklPsyLibrary(params).then((result) => {
    list.value = chunkArray(
      8,
      result.result.records.map((item) => {
        let url = item.content
        // if (item.content.indexOf('temp/') == 0) {
        //   url = imgserver + 'zhisong-music/' + item.content
        // }
        console.log(url);
        return { id: url, cover: url, label: item.title, value: '' };
      }),
    );
    loadingInstance.close()
  });
}
function goInfo (params) {
  currentSrc.value = params;
  popSwitch.value = true;
}
function back () {
  popSwitch.value = false;
  currentSrc.value = "";
}
</script>

<style lang="scss" setup>
@import './style.scss';
</style>
