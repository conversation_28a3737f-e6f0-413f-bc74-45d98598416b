<template>
  <Header :title="title" status="back" />
  <Container>
    <div id="contentMain" class="info h-full flex">
      <div class="info-content flex-1" v-html="info">

      </div>
    </div>
  </Container>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { getklPsyCounselInfo } from '/@/api/index';

const props = defineProps(['id']);
const title = ref('');
const info = ref('');

onActivated(() => {
  getList();
});
onActivated(() => {
  getList();
})

function getList () {
  const loadingInstance = ElLoading.service({ lock: true, target: '#contentMain' })
  let params = {
    id: props.id,
  };
  // getklPsyCounselCategoryInfo(params).then((result) => {
  //   title.value = result.result.name;
  // });
  getklPsyCounselInfo(params).then((result) => {
    loadingInstance.close()
    title.value = result.result.categoryName;
    info.value = result.result.content;
  });
}
</script>

<style lang="scss" scoped>
@import './style.scss';
</style>
