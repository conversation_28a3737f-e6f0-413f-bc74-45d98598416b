.counselings {
  width: calc(100% - 100px);
  overflow-y: scroll;
  padding: 0 36px;

  &-item {
    min-height: 109px;
    background: #e1f4ff;
    border-radius: 13px;
    font-family: PingFangSC, 'PingFang SC';
    font-weight: 400;
    font-size: 24px;
    color: #000;
    line-height: 33px;
    text-align: left;
  }

.el-textarea__inner {
  align-items: center;
  background-color: rgb(30 121 174 / 13%);
  box-shadow: none;
  border-radius: 20px;
  padding: 32px 45px;
}

.el-upload--picture-card {
  background-color: transparent;
}

.el-table {
  font-size: 22px;
  background-color: transparent;
}

.el-table tr {
  background-color: transparent;
}

.el-table .cell,
.el-table th div {
  overflow: hidden;
  text-overflow: ellipsis;
}

.el-table tbody tr:hover > td {
  background: #91c8e7 !important;
}

// .el-textarea__inner::-webkit-input-placeholder {
//   color: #1e79ae;
// }
.el-input__wrapper {
  align-items: center;
  background-color: transparent;
  box-shadow: none;
}

// input输入框focus颜色样式
.el-input {
  --el-input-focus-border: transparent;
  --el-input-focus-border-color: transparent;
}

.el-pagination.is-background .el-pager li.is-active {
  background-color: #1e79ae;
}
}
