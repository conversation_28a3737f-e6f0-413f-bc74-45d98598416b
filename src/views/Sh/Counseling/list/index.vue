<template>
  <Header title="咨询辅导" status="back" />
  <Container>
    <div class="counseling w-full h-full flex flex-col items-center">
      <Tab :tabs="tabs" :current="currentTab" @change="changeTab" />
      <div id="listMain" class="counselings w-full flex-1 flex flex-col mt-30px">
        <div class="counselings-item flex mb-29px px-48px items-center" :key="index" v-for="(item, index) of list" @click="goInfo(item.id)">
          <div class="counselings-item-label flex-1">{{ item.title }}</div>
          <el-icon>
            <ArrowRightBold />
          </el-icon>
        </div>
      </div>
    </div>
  </Container>
</template>

<script setup>
import { getAssetsFile } from '/@/utils/tool';
import { getklPsyCounsel, getklPsyCounselCategoryCategory } from '/@/api/index';

const router = useRouter();
const tabs = ref([]);
const tabsIds = ref([]);
const currentTab = ref(0);
function changeTab (index) {
  currentTab.value = index;
  getList(tabsIds.value[index]);
}
function goInfo (id) {
  router.push('../counseling/info/' + id);
}

// const list = ref([
//   { id: 1, label: '经典案例01:厌学症问题' },
// ]);
const list = ref([]);
onMounted(() => {
  getType();
});
onActivated(() => {
  getType();
})

function getType () {
  let params = {
    pageNo: 1,
    pageSize: 50,
  };
  getklPsyCounselCategoryCategory(params).then((result) => {
    let _list = result.result.records;
    tabs.value = _list.map((item) => item.name);
    tabsIds.value = _list.map((item) => item.id);
    getList(_list[0].id);
  });
}
function getList (id) {
  const loadingInstance = ElLoading.service({ lock: true, target:'#listMain' })
  let params = {
    pageNo: 1,
    pageSize: 99,
    categoryId: id,
  };
  getklPsyCounsel(params).then((result) => {
    loadingInstance.close()
    list.value = result.result.records;
  });
}
</script>

<style lang="scss">
@import './style.scss';
</style>
