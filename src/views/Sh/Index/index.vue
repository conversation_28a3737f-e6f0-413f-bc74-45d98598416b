<template>
  <div class="sh-index w-full h-100vh flex flex-col px-63px py-50px">
    <div class="sh-index-header max-w-1792px mb-67px w-full flex items-center justify-between">
      <div class="company"> {{companyName}} </div>
      <div class="title">AI心理问答系统</div>
      <div class="info flex items-center">
        <div class="header-actions">
          <div class="header-actions-item">
            <el-switch size="big" v-model="colorWeak" @change="onColorWeak" />
            <div class="header-actions-item-title">色弱模式</div>
          </div>
        </div>
        <span class="info-realname-box">
          <span class="info-realname">{{userInfo.realname}}</span>
          <span class="info-login cursor-pointer" @click="login()">切换账号</span>
        </span>
        <img class="info-wifi" src="/@/assets/images/wifi.png" alt="" />
        <span class="info-date">{{ time }}</span>
      </div>
    </div>
    <div class="sh-index-content flex-1 flex flex-col items-center">
      <div class="box mb-60px">
        <div @click="goPage(item.path)" :class="'box-item flex flex-col ' + `grid${index + 1}`" :key="index" v-for="(item, index) of list" :style="{ backgroundImage: `url(${getAssetsFile('list', item.bg)})`, color: item.fontColor }">
          <span class="box-item-label">
            {{ item.label }}
          </span>
          <span class="box-item-desc">
            {{ item.desc }}
          </span>
        </div>
      </div>
      <div class="menu flex items-center justify-around">
        <div class="menu-item flex flex-col items-center" @click="goPage(item.path)" :key="index" v-for="(item, index) of menus">
          <img class="menu-item-icon mb-18px" :src="getAssetsFile('menu', item.icon)" alt="" />
          <span class="menu-item-label">{{ item.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { getAssetsFile, getNowTime } from '/@/utils/tool';
import { getUserInfo, clearUserInfo } from '/@/utils/user';
import { setEquipmentType } from '/@/utils/equipment';

import { userLogout } from '/@/api/index';


const router = useRouter();
function login () {
  router.push('/login/login');
}
function loginout (params) {
  userLogout().then((result) => {
    userInfo.value = {}
    clearUserInfo().then(_ => {
      getUserInfoFun()
      ElMessage({
        message: '切换游客模式成功',
        type: 'success',
      });
    })
  })
}
function goPage (path) {
  if (path == "/user/info") {
    let _userInfo = JSON.parse(getUserInfo())
    if (_userInfo) {
      router.push(path);
    } else {
      ElMessage({
        message: '个人中心需在登录后进入',
        type: 'warning',
      });
      login();
      return
    }
  }
  router.push(path);
}
function getUserInfoFun (params) {
  let _userInfo = JSON.parse(getUserInfo())
  if (_userInfo) {
    userInfo.value = _userInfo
  }
}
function setTime () {
  time.value = getNowTime()
  setTimeout(() => {
    setTime()
  }, 60000)
}
const time = ref('00:00');
const userInfo = ref({})
const companyName = ref('')
const list = ref([
  { label: '心理阅读', path: '/sh/read/list', desc: 'Psychological readin', bg: 'icon1.png', fontColor: '#0D8471' },
  { label: '心理科普', path: '/sh/science/list', desc: 'Psychological Scienc', bg: 'icon2.png', fontColor: '#1C6BAB' },
  { label: '心理图库', path: '/sh/picture/list', desc: 'Psychological Librar', bg: 'icon3.png', fontColor: '#925111' },
  { label: '心理FM', path: '/sh/fm/list', desc: 'Psychological FM', bg: 'icon4.png', fontColor: '#3E4F91' },
  { label: '心理影视', path: '/sh/movies/list', desc: 'Psychological film a', bg: 'icon5.png', fontColor: '#3E4F91' },
  { label: '咨询辅导', path: '/sh/counseling/list', desc: 'Counseling and couns', bg: 'icon6.png', fontColor: '#925111' },
  { label: '放松减压', path: '/sh/relax/list', desc: 'Relaxation and decom', bg: 'icon7.png', fontColor: '#925111' },
  { label: '心理测评', path: '/sh/evaluating/list', desc: 'psychometrics', bg: 'icon8.png', fontColor: '#0D8471' },
  { label: '训练游戏', path: '/sh/cognitive/list', desc: 'Cognitive training', bg: 'icon9.png', fontColor: '#1C6BAB' },
]);
const colorWeak = ref(false);
function onColorWeak (val) {
  console.log(val)
  colorWeak.value = val;
  val ? document.body.classList.add('colorWeak') : document.body.classList.remove('colorWeak')
}
const menus = ref([
  { icon: 'intro.png', label: '机构介绍', path: '/user/intro' },
  // { icon: 'config.png', label: '设置', path: '/user/config' },
  { icon: 'user.png', label: '个人中心', path: '/user/info' },
]);
onMounted(() => {
  if (document.body.className.indexOf('colorWeak') == -1) {
    colorWeak.value = false;
  } else {
    colorWeak.value = true;
  }
  setEquipmentType('sh')
  getUserInfoFun()
  setTime()
})
watch(() => router.currentRoute.value.path, (n, o) => {
  if (document.body.className.indexOf('colorWeak') == -1) {
    colorWeak.value = false;
  } else {
    colorWeak.value = true;
  }
  getUserInfoFun()
})
</script>

<style lang="scss">
@import './style.scss';
</style>
