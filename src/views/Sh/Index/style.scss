.sh-index {
  &-header {
    .info-realname-box{
      display: flex;
      align-items: center;
    }

    .info-realname{
      max-width: 7em;
      display: inline-block;
      overflow: hidden; 
      white-space: nowrap; 
      text-overflow: ellipsis; 
    }

    .company {
      font-weight: 400;
      font-size: 24px;
      color: #fff;
      line-height: 33px;
    }

    .title {
      font-family: PingFangSC, 'PingFang SC';
      font-weight: 600;
      font-size: 40px;
      color: #fff;
      line-height: 56px;
      position: absolute;
      width: 100%;
      display: flex;
      justify-content: center;
      z-index: -1;
    }

    .header-actions{
      margin-right: 30px;

      .header-actions-item{
        display: flex;
        align-items: center;

        .header-actions-item-title{
          margin-left: 10px;
          font-family: PingFangSC, 'PingFang SC';
          font-weight: 400;
          font-size: 24px;
          color: #fff;
        }
      }
    }

    .info {
      font-family: PingFangSC, 'PingFang SC';
      font-weight: 400;
      font-size: 24px;
      color: #fff;
      line-height: 33px;

      .cursor-pointer{
        cursor: pointer;
      }

      &-login {
        height: 47px;
        background: #00a4ff;
        border-radius: 31px;
        padding: 0 29px;
        text-align: center;
        line-height: 47px;
      }

      &-wifi {
        width: 24px;
        height: 24px;
        margin: 0 17px 0 58px;
      }
    }
  }

  &-content {
    .box {
      display: grid;
      grid-template-columns: repeat(6, 276px);
      grid-template-rows: repeat(2, 290px);
      grid-gap: 38px;

      &-item {
        background-size: 100% 100%;
        padding: 47px 53px;

        &-label {
          font-family: PingFangSC, 'PingFang SC';
          font-weight: 600;
          font-size: 38px;
          line-height: 53px;
        }

        &-value {
          font-family: PingFangSC, 'PingFang SC';
          font-weight: 400;
          font-size: 18px;
          color: #0d8471;
          line-height: 25px;
        }
      }

      .grid1 {
        grid-column: 1 / 3;
      }

      .grid2 {
        grid-column: 3 / 5;
      }

      .grid7 {
        grid-row: 2;
        grid-column: 3 / 5;
      }
    }

    .menu {
      width: 651px;
      height: 189px;
      background: rgb(255 255 255 / 10%);
      border-radius: 30px;

      &-item {
        &-icon {
          width: 100px;
          height: 100px;
        }

        &-label {
          font-family: PingFangSC, 'PingFang SC';
          font-weight: 400;
          font-size: 24px;
          color: #fff;
          line-height: 33px;
        }
      }
    }
  }
}
