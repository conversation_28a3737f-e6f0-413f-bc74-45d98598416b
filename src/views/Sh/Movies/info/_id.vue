<template>
  <Header :title="title" status="back" />
  <Container>
    <div id="contentMain" class="moviebox w-full h-full flex flex-col">
      <div class="movie flex-1 flex">
        <el-carousel ref="carousel" @change="changeItem" class="movie-content w-full" height="507px" :autoplay="false" arrow="always" :initial-index="current">
          <el-carousel-item :key="item.id" v-for="item of list">
            <div class="movie-page h-full flex items-center justify-center">
              <div class="movie-item">
                <video :id="item.id" class="movie-item-cover" ref="videoPlayer" controls @play="play" @pause="pause" @ended="ended" :src="item.url"></video>
                <!-- <img class="movie-item-cover" src="/@/assets/images/moviestest.png" alt="" /> -->
                <div class="movie-item-title">{{ item.title }}</div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="movieMenus w-full py-13px">
        <div class="movieMenus-scroll flex">
          <div :class="'movieMenus-item ' + (current == index ? 'current' : '')" :key="item.id" v-for="(item, index) of list" @click="changeItem(index)">
            <img class="movieMenus-item-cover" :src="item.cover" alt="" />
            <div class="movieMenus-item-title">{{ item.title }}</div>
          </div>
        </div>
      </div>
    </div>
  </Container>
</template>

<script setup>
import { getklPsyFilm, getklPsyFilmInfo, getklPsyFilmCategoryInfo } from '/@/api/index';

const props = defineProps(['id']);
const list = ref([]);
const title = ref('');
const current = ref();
const videoPlayer = ref();
const carousel = ref();
function changeItem (e) {
  function fun () {
    const old_videoPlayer = videoPlayer.value[current.value];
    old_videoPlayer && old_videoPlayer.pause();
    const _videoPlayer = videoPlayer.value[e];
    _videoPlayer.pause();
    _videoPlayer.load();
  }
  if (videoPlayer.value) {
    fun()
  } else {
    setTimeout(() => {
      fun()
    }, 10000)
  }
  current.value = e;
  carousel.value.setActiveItem(e)
}

onActivated(() => {
  getInfo();
});
onActivated(() => {
  getInfo();
})
function getInfo () {
  let params = {
    id: props.id,
  };
  getklPsyFilmInfo(params).then((result) => {
    let _list = result.result;
    getklPsyFilmCategoryInfo({ id: _list.categoryId }).then((result) => {
      title.value = result.result.name;
    });
    getList(_list.categoryId);
  });
}
function getList (id) {
  const loadingInstance = ElLoading.service({ lock: true, target: '#contentMain' })
  let params = {
    pageNo: 1,
    pageSize: 50,
    categoryId: id,
  };
  getklPsyFilm(params).then((result) => {
    let _index = 0;
    list.value = result.result.records.map((item, index) => {
      if (item.id == props.id) {
        _index = index
      }
      return {
        id: item.id,
        title: item.title,
        cover: item.coverImage ? item.coverImage : '/@/assets/images/moviestest.png',
        url: item.content,
      };
    });
    loadingInstance.close()
    changeItem(_index)
  });
}
</script>

<style lang="scss">
@import './style.scss';

/* 自定义Carousel右箭头 */
.movie-content .el-carousel__arrow--right {
  color: #1e79ae;
  font-size: 72px;
  font-weight: 700;
  background-color: transparent; /* 箭头颜色 */
}

/* 自定义Carousel左箭头 */
.movie-content .el-carousel__arrow--left {
  color: #1e79ae;
  font-size: 72px;
  font-weight: 700;
  background-color: transparent; /* 箭头颜色 */
}
</style>
