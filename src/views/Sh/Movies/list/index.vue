<template>
  <Header title="心理影视" status="home" />
  <Container>
    <Tab :tabs="tabs" :current="currentTab" @change="changeTab" />
    <div id="listMain" class="boxpadding w-full flex-1">
      <Carousel :list="list" :single="true" @info="goInfo" />
    </div>
  </Container>
</template>

<script setup>
import { getAssetsFile, chunkArray } from '/@/utils/tool';
import { getklPsyFilm, getklPsyFilmCategory } from '/@/api/index';

const router = useRouter();
function goInfo (id) {
  router.push('../movies/info/' + id);
}

const tabs = ref([]);
const tabsIds = ref([]);
const currentTab = ref(0);
function changeTab (index) {
  currentTab.value = index;
  getList(tabsIds.value[index]);
}

// const list = ref([
//   [
//     { id: 1, cover: 'zjjs.png', label: '不可能图', value: '' },
//     { id: 2, cover: 'zymc.png', label: '似动图片', value: '' },
//     { id: 3, cover: 'xlxsy.png', label: '其他类', value: '' },
//     { id: 4, cover: 'xlxy.png', label: '多视图片', value: '' },
//   ],
//   [
//     { id: 9, cover: 'zjjs.png', label: '专家介绍', value: '' },
//     { id: 10, cover: 'zymc.png', label: '专有名词', value: '' },
//   ],
// ]);

const list = ref([]);
onMounted(() => {
  getType();
});

function getType () {
  let params = {
    pageNo: 1,
    pageSize: 50,
  };
  getklPsyFilmCategory(params).then((result) => {
    let _list = result.result.records;
    tabs.value = _list.map((item) => item.name);
    tabsIds.value = _list.map((item) => item.id);
    getList(_list[0].id);
  });
}
function getList (id) {
  let params = {
    pageNo: 1,
    pageSize: 50,
    categoryId: id,
  };
  getklPsyFilm(params).then((result) => {
    list.value = chunkArray(
      4,
      result.result.records.map((item) => {
        return { id: item.id, cover: item.coverImage, label: item.title, value: '', categoryId: item.categoryId };
      }),
    );
  });
}
</script>

<style lang="scss">
@import './style.scss';
</style>
