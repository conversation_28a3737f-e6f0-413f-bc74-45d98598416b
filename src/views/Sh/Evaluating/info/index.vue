<template>
  <Header title="心理测评" status="back" />
  <Container>
    <div class="evaluatinginfo w-full h-full flex flex-col">
      <div class="evaluatinginfo-title mb-42px">
        {{ testQuestions.title }}
      </div>
      <div class="evaluatinginfo-content flex-1 flex">
        <div class="keybox flex-1 flex flex-col justify-between">
          <div class="questions flex flex-col">
            <div class="question mb-59px flex items-center">{{ current.question }}
              <img class="voice ml-11px" src="/@/assets/images/bf.png" alt="" />
            </div>
            <div class="option flex-1">
              <div class="option-item flex items-center mb-35px" :key="index" v-for="(item, index) of current.option" @click="answer[item.id] = item.key">
                <span :class="'icon mr-25px ' + (answer[item.id] == item.key ? 'selected' : '')"></span>
                {{ item.key }}. {{ item.value }}
              </div>
            </div>
          </div>
          <div class="btn">提交</div>
        </div>
        <div class="configs flex flex-col justify-between">
          <div class="configs-msg flex items-center">
            <span class="mr-90px flex items-center">
              <img class="mr-7px" src="/@/assets/images/unit-icon.png" alt="" /> 题目数量：{{ testQuestions.questions.length }}
            </span>
            <span class="flex items-center"> <img class="mr-7px" src="/@/assets/images/time-icon.png" alt="" /> 答题计时：{{ time }}</span>
          </div>
          <div class="configs-btn flex justify-end">
            <div class="btn mr-57px">上一题</div>
            <div class="btn">下一题</div>
          </div>
        </div>
      </div>
    </div>
  </Container>
</template>

<script setup>
import { getAssetsFile } from '/@/utils/tool';

const current = ref({});
const time = ref('08:08:00');
const testQuestions = reactive({
  title: '症状自评量表（SCL-90成人版）',
  total: 3,
  questions: [
    {
      id: 1,
      question: '1. 近1个月，晚上上床睡觉通常（ ）点',
      option: [
        { key: 'A', value: '9-10点之间' },
        { key: 'B', value: '10-11点之间' },
        { key: 'C', value: '11-12点之间' },
        { key: 'D', value: '12-13点之间' },
      ],
    },
  ],
});
const answer = ref({
  1: 'A',
});
onMounted(() => {
  current.value = testQuestions.questions[0];
});
onActivated(() => {
  current.value = testQuestions.questions[0];
})
</script>

<style lang="scss">
@import './style.scss';
</style>
