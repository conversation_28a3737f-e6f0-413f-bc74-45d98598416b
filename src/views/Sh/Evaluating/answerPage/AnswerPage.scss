/* 禁止文字选中 */
* {
  -webkit-touch-callout: none;

  /* 系统默认菜单被禁用 */

  /* webkit浏览器 */

  /* 早期浏览器 */

  /* 火狐 */

  /* IE10 */
  user-select: none;
}

.answer-page {
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;

  .page-title {
    height: 110px;
    padding: 50px 300px 0 350px;
    background: #4495D1;
    font-size: 24px;
    line-height: 70px;
    text-align: right;
    font-weight: 600;
    color: #fff;
  }

  .page-content {
    padding: 10px 50px;

    .ant-select-selection {
      width: 180px;
      margin-left: 8px;

      .anticon-down {
        font-size: 18px !important;
      }
    }

    .content-top {
      height: 60px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding-bottom: 20px;

      .top-item {
        display: inline-flex;
        align-items: center;
        padding-left: 30px;
        font-size: 24px;
        line-height: 40px;

        .anticon {
          font-size: 24px;
          margin-right: 10px;
        }

        .ant-switch {
          width: 50px;
          height: 24px;
          margin-right: 10px;
        }
      }
    }

    .content-info {
      padding: 70px 300px 100px 380px;
      text-align: center;

      .info-sub-title {
        font-weight: bold;
        text-align: left;
      }

      .info-description {
        font-size: 30px;
        line-height: 55px;
        text-indent: 32px;
        text-align: left;
      }

      .ant-btn {
        margin: 20px auto 0;
        height: 40px;
      }
    }

    .cotent-main {
      padding: 10px 150px 0;

      .main-title {
        padding: 10px 200px 5px 380px;
        font-weight: bold;
      }

      .main-sub-title {
        padding: 0 200px 10px 350px;
        font-weight: bold;
        text-indent: 80px;
      }

      .main-content {
        display: flex;
        padding: 20px 50px;

        .content-left {
          height: 450px;

          .ant-slider-handle {
            margin-left: -0.219rem;
          }
        }

        .content-right {
          flex: 1;
          padding-left: 50px;

          .right-btn {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 70px 280px 0 0;

            // text-align: right;

            .voice-btn {
              width: 45px;
              height: 45px;
              margin: 0 10px;
              background: #1890ff;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;

              .anticon {
                font-size: 24px;
                color: #fff;
              }

              span {
                font-size: 24px;
                color: #fff;
              }
            }

            .voice-input-button {
              width: 45px;
              height: 45px;
              margin: 0 10px;
              background: #1890ff;
              border-radius: 50%;

              .recording {
                width: 45px;
                height: 45px;
                display: flex;
                justify-content: center;
                align-items: center;

                svg {
                  width: 35px;
                  height: 35px;

                  // margin: 5px;
                }
              }

              .microphone {
                width: 45px;
                height: 45px;

                svg {
                  width: 45px;
                  height: 45px;
                }
              }
            }

            .ant-btn {
              height: 40px;
              margin: 0 10px;
            }

            .no-choose {
              cursor: not-allowed;
            }
          }
        }
      }
    }

    .ant-alert {
      margin: 0 240px 0 300px;
    }
  }
}