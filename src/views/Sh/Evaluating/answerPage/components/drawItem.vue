<template>
  <div class="connect-item">
    <p class="connect-title" v-html="question.title"></p>
    <p v-if="question.titleExplain && isControl && isSynchronize" class="connect-title">题目解释：{{ question.titleExplain }}</p>

    <div class="connect-main">
      <div class="left-btn" @click="setType(1)">疼痛部位</div>
      <div class="right-btn" @click="setType(2)">最剧烈部位</div>
      <!-- v-if="isTerminal && isSynchronize" -->
      <div class="confirm-btn" @click="getImg">确认</div>

      <div class="connect-img" ref="connectImg" @click="setChoose">
        <img class="img" v-if="isControl && isSynchronize && imgUrl" :src="getUrl(imgUrl)" />
        <template v-else>
          <img class="img" :src="getUrl(question.answer || question.imgPath)" />
          <div v-for="item in leftChoose" :key="item.index" :class="['main-item-1']" :style="item.style"></div>
          <div v-for="item in rightChoose" :key="item.index" :class="['main-item-2']" :style="item.style">X</div>
        </template>

      </div>
    </div>

    <div class="connect-item-answer" v-if="!(isTerminal && isSynchronize)">
      <div class="answer-item" v-for="(item, index) in question.psOptions" :key="index" :id="item.id" @click="chooseItem(item.id)">
        <a-button type="primary" size="large">{{ item.title }}</a-button>
        <span v-if="item.type == 1" class="item-text">{{ item.content }}</span>
        <img class="item-img" v-if="item.type == 2" :src="item.content" />
      </div>
    </div>
  </div>
</template>

<script>
import html2canvas from 'html2canvas'
import { getToken } from '/@/utils/auth';

export default {
  name: 'DrawItem',

  props: {
    question: {
      type: Object,
      default: () => {
      }
    },

    isTerminal: {
      type: Boolean,
      default: false
    },

    isControl: {
      type: Boolean,
      default: false
    },

    isSynchronize: {
      type: Boolean,
      default: false
    },

    imgUrl: {
      type: String,
      default: ''
    }
  },

  data () {
    return {
      leftChoose: [],
      rightChoose: [],
      path: [],
      imgserver: window._CONFIG['domianURL'],
      type: 1,
    }
  },

  mounted () {
  },

  methods: {
    setChoose (e) {
      if (this.type === 1) {
        this.leftChoose.push({
          style: {
            left: (e.layerX - 20) + 'px',
            top: (e.layerY - 20) + 'px'
          }
        })
      } else {
        this.rightChoose.push({
          style: {
            left: (e.layerX - 20) + 'px',
            top: (e.layerY - 20) + 'px'
          }
        })
      }
    },

    setType (type) {
      this.type = type
    },

    getUrl (url) {
      if (url.includes('temp')) {
        return this.imgserver + '/' + url
      } else {
        return url
      }
    },

    async getImg () {
      let flieParam = new FormData()
      const img = await this.toImg()
      flieParam.append('file', new File([this.convertBase64UrlToBlob(img)], 'connectLine.png'))
      flieParam.append('biz', 'temp/image/connectLine')
      return fetch(window._CONFIG['domianURL'] + '/sys/common/upload', {
        method: 'post',
        body: flieParam,
        headers: { 'X-Access-Token': getToken() },
      })
        .then((response) => response.text())
        .then((result) => {
          const canvasImgUrl = JSON.parse(result).message
          // this.$emit('submitImg', canvasImgUrl)
          this.$emit('submit', { answer: canvasImgUrl, type: '10' })
        })
    },

    async chooseItem (id) {
      if (this.isControl && this.isSynchronize) {
        this.$emit('submit', { optionId: id, answer: this.imgUrl, type: '9' })
        return
      }
      let flieParam = new FormData()
      const img = await this.toImg()
      flieParam.append('file', new File([this.convertBase64UrlToBlob(img)], 'connectLine.png'))
      flieParam.append('biz', 'temp/image/connectLine')
      fetch(window._CONFIG['domianURL'] + '/sys/common/upload', {
        method: 'post',
        body: flieParam,
        headers: { 'X-Access-Token': getToken() },
      })
        .then((response) => response.text())
        .then((result) => {
          const canvasImgUrl = JSON.parse(result).message
          this.$emit('submit', { optionId: id, answer: canvasImgUrl, type: '9' })
        })
    },

    // base64转formData
    convertBase64UrlToBlob (urlData) {
      //去掉url的头，并转换为byte
      var bytes = window.atob(urlData.split(',')[1]);
      //处理异常,将ascii码小于0的转换为大于0
      var ab = new ArrayBuffer(bytes.length);
      var ia = new Uint8Array(ab);
      for (var i = 0; i < bytes.length; i++) {
        ia[i] = bytes.charCodeAt(i);
      }
      return new Blob([ia], { type: 'image/png' });
    },

    // 绘制图片
    toImg () {
      let canvas2 = document.createElement('canvas')
      const ref = this.$refs['connectImg']
      let w = ref.offsetWidth
      let h = ref.offsetHeight
      canvas2.width = w * 2
      canvas2.height = h * 2
      const context = canvas2.getContext('2d')
      context.scale(2, 2)
      let scale = 2
      let opts = {
        scale,
        canvas2: context,
        w,
        h,
        scrollX: 0,
        scrollY: 0,
        // 【重要】开启跨域配置
        useCORS: true,
        allowTaint: true
      }
      return new Promise((resolve, reject) => {
        html2canvas(ref, opts).then(function (canvas) {
          const imgUrl = canvas.toDataURL('image/png')
          resolve(imgUrl)
        })
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.connect-item {
  padding: 0 100px;

  #mycanvas {
    position: absolute;
    top: 0;
    left: 0;
  }

  .connect-title {
    font-size: 28px;
    padding-bottom: 20px;
  }

  .connect-main {
    position: relative;
    width: 800px;
    height: 784px;

    .left-btn {
      position: absolute;
      padding: 5px;
      top: 10px;
      left: 10px;
      font-size: 18px;
      z-index: 1;
      color: red;
      cursor: pointer;
    }

    .right-btn {
      position: absolute;
      padding: 5px !important;
      top: 10px;
      right: 10px;
      font-size: 18px;
      z-index: 1;
      color: red;
      cursor: pointer;
    }

    .connect-img {
      position: relative;
      width: 800px;
      height: 784px;
      overflow: hidden;
    }

    .img {
      width: 100%;
      height: 100%;
    }

    .clear-btn {
      position: absolute;
      top: 0;
      left: 0;
      padding: 10px;
      background-color: #448ef7;
      color: #fff;
      cursor: pointer;
      z-index: 99;
    }

    .confirm-btn {
      position: absolute;
      top: 0;
      right: -80px;
      padding: 10px;
      background-color: #448ef7;
      color: #fff;
      cursor: pointer;
      z-index: 99;
    }

    .main-item-1 {
      position: absolute;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 1px solid red;
      z-index: 1;
    }

    .main-item-2 {
      position: absolute;
      width: 40px;
      height: 40px;
      font-size: 30px;
      line-height: 50px;
      text-align: center;
      color: red;
      z-index: 1;
    }
  }

  .connect-item-answer {
    width: 1200px;
    padding-top: 30px;
    display: flex;
    flex-wrap: wrap;

    .answer-item {
      display: flex;
      width: 300px;
      padding: 10px;

      .ant-btn {
        height: 40px;
        margin-right: 10px;
      }

      .item-text {
        display: inline-block;
        font-size: 24px;
        line-height: 40px;
        cursor: pointer;
      }

      .item-img {
        width: 150px;
        cursor: pointer;
      }
    }
  }
}
</style>