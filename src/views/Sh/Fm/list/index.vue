<template>
  <Header title="心理FM" status="home" />
  <Container>
    <div class="fmlist" id="listMain">
      <div class="fmlist-item flex flex-col" @click="goInfo(item.id)" :key="index" v-for="(item, index) of list">
        <img class="fmlist-item-cover" :src="getAssetsFile('fm', item.cover)" alt="" />
        <span class="fmlist-item-label w-full">{{ item.label }}</span>
      </div>
    </div>
  </Container>
</template>

<script setup>
import { getAssetsFile, chunkArray } from '/@/utils/tool';
import { getklPsyFmCategory } from '/@/api/index';

const router = useRouter();
function goInfo (id) {
  router.push('../fm/info/' + id);
}
const list = ref([]);
onMounted(() => {
  getType();
});
onActivated(() => {
  getType();
})

function getType () {
  const loadingInstance = ElLoading.service({ lock: true, target:'#listMain' })
  let params = {
    pageNo: 1,
    pageSize: 50,
  };
  getklPsyFmCategory(params).then((result) => {
    loadingInstance.close()
    let _list = result.result.records;
    list.value = _list.map((item) => {
      return { cover: item.coverImage, label: item.name, id: item.id };
    });
  });
}
</script>

<style lang="scss">
@import './style.scss';
</style>
