<template>
  <Header :title="title" status="back" />
  <Container>
    <el-scrollbar ref="scrollFun" class="experts w-full h-full flex flex-col" v-loading="loading" element-loading-text="加载中..." @scroll="scroll">
      <div class="experts-item flex" :key="index" v-for="(item, index) of list">
        <div class="experts-item-avatar mr-26px">
          <img :src="item.avatar ? item.avatar : `/@/assets/images/avatar.png`" alt="" />
        </div>
        <div class="experts-item-content flex-1 flex flex-col">
          <span class="label mb-25px">{{ item.label }}</span>
          <span class="value" v-html="item.value"></span>
        </div>
      </div>
    </el-scrollbar>
  </Container>
</template>

<script setup>
import { getAssetsFile } from '/@/utils/tool';
import { getklPsyPopular, getklPsyPopularCategoryInfo } from '/@/api/index';

const props = defineProps(['id']);
const scrollFun = ref(null);
const title = ref('');
const list = ref([]);
const loading = ref(true);
const currentPage = ref(1) // 当前页
const total = ref(0) // 当前页
const size = ref(3) // 当前页
onMounted(() => {
  currentPage.value = 1;
  getInfo()
  getList();
});
onActivated(() => {
  list.value = [];
  currentPage.value = 1;
  getInfo()
  getList();
})
function getInfo (params) {
  getklPsyPopularCategoryInfo({ id: props.id }).then((result) => {
    title.value = result.result.name;
  });
}
function scroll (e) {
  if (scrollFun.value.wrapRef.scrollHeight - scrollFun.value.wrapRef.clientHeight < e.scrollTop + 50) {
    changePage()
  }
}
function changePage () {

  if (currentPage.value * size.value < total.value) {
    currentPage.value++;
    getList();
  }
}
function getList () {
  loading.value = true;
  let params = {
    pageNo: currentPage.value,
    pageSize: size.value,
    categoryId: props.id,
    delFlag: 0
  };
  getklPsyPopular(params).then((result) => {
    let _list = result.result.records.map((item) => {
      return { avatar: item.coverImage ? item.coverImage : '', label: item.title, value: item.content };
    });
    if (currentPage.value == 1) {
      total.value = result.result.total
      list.value = _list
    } else {
      list.value = list.value.concat(_list)
    }
    loading.value = false;
  });
}
</script>

<style lang="scss">
@import './style.scss';
</style>
