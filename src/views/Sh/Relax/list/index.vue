<template>
  <Header title="放松减压" status="home" />
  <Container>
    <Tab :tabs="tabs" :current="currentTab" @change="changeTab" />
    <div class="boxpadding w-full flex-1">
      <Carousel :list="list" :single="false" @info="goInfo" />
    </div>
  </Container>
</template>

<script setup>
import { getAssetsFile, chunkArray } from '/@/utils/tool';
import { ElMessage } from 'element-plus';
import { getklPsyRelaxFm, getklPsyRelaxCategory } from '/@/api/index';

const tabs = ref([]);
const tabsIds = ref([]);
const currentTab = ref(0);
function changeTab (index) {
  currentTab.value = index;
  getList(tabsIds.value[index]);
}

const router = useRouter();
function goInfo (id) {
  router.push('../relax/info/' + id);
}

const list = ref([]);
onMounted(() => {
  getType();
});
onActivated(() => {
  getType();
})

function getType () {
  let params = {
    pageNo: 1,
    pageSize: 8,
  };
  getklPsyRelaxCategory(params).then((result) => {
    let _list = result.result.records;
    tabs.value = _list.map((item) => item.name);
    tabsIds.value = _list.map((item) => item.id);
    getList(_list[0].id);
  });
}
function getList (id) {
  let params = {
    pageNo: 1,
    pageSize: 50,
    categoryId: id,
  };
  getklPsyRelaxFm(params).then((result) => {
    list.value = chunkArray(
      8,
      result.result.records.map((item) => {
        return { id: item.id, cover: item.coverImage ? item.coverImage : '', label: item.title, value: '' };
      }),
    );
  });
}
</script>

<style lang="scss">
@import './style.scss';
</style>
