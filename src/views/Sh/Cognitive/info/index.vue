<template>
  <Header title="训练游戏" status="back" />
  <Container>
    <div class="info h-full">
      <!-- {{ info }} -->
      <GameModels :componentName="componentName" :id="id" />
    </div>
  </Container>
</template>
<script setup>
import { ElMessage } from 'element-plus';

const route = useRoute();
console.log(route.query.id);
const info = ref('');
const componentName = ref('');
const id = ref('');
onMounted(() => {
  componentName.value = route.query.gameName;
  id.value = route.query.id;
  // componentName.value = "Game11"
});
</script>

<style lang="scss" scoped>
@import './style.scss';
</style>
