<template>
  <Header title="训练游戏" status="home" />
  <Container>
    <!-- todo 第一张背景图更换 -->
    <Tab :tabs="tabs" :current="currentTab" @change="changeTab" />
    <div class="boxpadding w-full flex-1">
      <Carousel :list="list" :single="false" @info="goInfo" />
    </div>
    <userForm ref="UserForm" @ok="modalFormOk"></userForm>
    <WebsocketMixin ref="socket" />
  </Container>
</template>

<script setup>
import { WebsocketMixin } from '/@/mixins/WebsocketMixin'
import { getAssetsFile } from '/@/utils/tool';
import { getGameList } from '/@/api/index';
import userForm from '../userForm/userForm.vue';

const router = useRouter();
const tabs = ref([]);
const currentTab = ref(0);
const UserForm = ref(null);
function changeTab (index) {
  list.value = dataList.value[index]
  currentTab.value = index;
}

const list = ref([]);
const dataList = ref([])
function goInfo (id) {
  // router.push('../cognitive/info/' + id);
  UserForm.value.loadMeasureIds(id)
  UserForm.value.title = '添加测试者信息'
}
const socket = ref()
onMounted(() => {
  getList();
});
onActivated(() => {
  getList();
})
onBeforeUnmount(() => {
  socket.value.websocketOnclose()
})

function getList () {
  let params = {
    pageNo: 1,
    pageSize: 400,
  };
  getGameList(params).then((result) => {
    let _list = result.result.records;
    let _dataList = []
    let _tabs = []
    for (const item of _list) {
      let isTabsIndex = _tabs.indexOf(item.categoryName)
      let newItem = { cover: window.location.origin + "/static/game_bg/" + item.htmlPath + ".png", label: item.name, id: item.id, cmpName: item.htmlPath }
      if (isTabsIndex == -1) {
        _tabs.push(item.categoryName)
        _dataList.push([newItem])
      } else {
        _dataList[isTabsIndex].push(newItem)
      }
    }
    _dataList = _dataList.map(item => {
      let _item = []
      let index = 0;
      for (const ite of item) {
        if (index == 0) {
          _item.push([ite])
        } else {
          _item[_item.length - 1].push(ite)
        }
        if (index == 7) {
          index = 0;
        } else {
          index++
        }
      }
      return _item
    })
    console.log(_dataList);
    dataList.value = _dataList;
    tabs.value = _tabs
    list.value = _dataList[0]
  });
}
</script>

<style lang="scss">
@import './style.scss';
</style>
