<template>
  <Header title="设置" status="home" />
  <Container>
    <div class="list w-full h-full flex flex-col">
      <div class="list-item py-31px w-full flex justify-center" :key="index" v-for="(item, index) of list">
        <div class="list-item-label flex-1">{{ item.label }}</div>
        <el-icon>
          <ArrowRightBold />
        </el-icon>
      </div>
    </div>
  </Container>
</template>

<script setup>
import { ElMessage } from 'element-plus';

const list = ref([
  {
    label: '修改登录密码',
    path: '',
  },
]);
</script>

<style lang="scss">
@import './style.scss';
</style>
