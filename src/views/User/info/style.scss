.user {
  &-avatar {
    width: 146px;
    height: 146px;

    // border-radius: 50%;
    overflow: hidden;

    > img {
      width: 100%;
    }
  }

  > span {
    font-family: PingFangSC, 'PingFang SC';
    font-weight: 400;
    font-size: 32px;
    color: #000;
  }

.el-textarea__inner {
  align-items: center;
  background-color: rgb(30 121 174 / 13%);
  box-shadow: none;
  border-radius: 20px;
  padding: 32px 45px;
}

.el-upload--picture-card {
  background-color: transparent;
}

// .el-textarea__inner::-webkit-input-placeholder {
//   color: #1e79ae;
// }
.el-input__wrapper {
  align-items: center;
  background-color: transparent;
  box-shadow: none;
}

// input输入框focus颜色样式
.el-input {
  --el-input-focus-border: transparent;
  --el-input-focus-border-color: transparent;
}

.el-pagination.is-background .el-pager li.is-active {
  background-color: #1e79ae;
}
}
