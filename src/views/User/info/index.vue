<template>
  <Header title="个人中心" status="back" />
  <Container>
    <div class="w-full h-full flex flex-col items-center justify-center">
      <div class="user flex-1 flex flex-col items-center justify-center">
        <div class="user-avatar mb-25px">
          <img src="/@/assets/images/avatar.png" alt="" />
        </div>
        <span>{{ user.realname }}</span>
      </div>
      <div class="user user flex-1 flex flex-col items-center justify-center">
        <div class="user-avatar mb-25px" @click="goReport()">
          <img src="/@/assets/images/report.png" alt="" />
        </div>
        <span>报告列表</span>
      </div>
    </div>
  </Container>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { getUserInfo, clearUserInfo } from '/@/utils/user';

const router = useRouter();
function goReport () {
  router.push('/user/report');
}
const user = ref({
  nickName: '',
  avater: '',
});

onMounted(() => {
  let _userInfo = JSON.parse(getUserInfo())
  if (_userInfo) {
    user.value = _userInfo
  }
})
</script>

<style lang="scss">
@import './style.scss';
</style>
