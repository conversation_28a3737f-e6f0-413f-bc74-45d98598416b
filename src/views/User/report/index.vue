<template>
  <Header title="数据中心" status="home" />
  <Container>
    <div class="tableContent w-full h-full flex flex-col">
      <div class="tool w-full flex flex-col justify-between items-end">
        <div class="tool-search w-full flex items-center">
          用户名 <el-input v-model="keyword" class="ml-23px mr-17px" style="width: 292px; height: 52px;" />
          <div class="btn confirm mr-12px" @click="searchFun">查询</div>
          <div class="btn reset" @click="resetFun">重置</div>
        </div>
        <!-- <div class="tool-delect mb-25px">批量删除</div> -->
      </div>
      <el-table ref="multipleTable" :data="tableData" style="
          width: 99.97%;
          height: 100%;
          border-right: 1px #98cae7 solid;
          border-left: 1px #98cae7 solid;
        " :highlight-current-row="false" :header-cell-style="{
          backgroundColor: '#91C8E7',
          color: '#fff',
          fontSize: '22px',
        }" :cell-style="{
          color: '#5DA5CE',
          fontSize: '22px',
        }" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="measureName" label="量表" />
        <el-table-column prop="updateTime" label="时间" />
        <el-table-column prop="userName" label="用户名" />
        <el-table-column prop="operate" label="操作">
          <template #default="scoped">
            <el-button link type="primary" @click="handleClickReport(scoped.row)">查看报告</el-button>
            <el-button link type="primary" @click="handleClickDel(scoped.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination layout="prev, pager, next" :total="total" :page-size="12" @current-change="handleChangePage" />
    </div>
    <ReportDrawer ref="reportDetail"></ReportDrawer>
  </Container>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus';
import { getdxResultList, deldxResult } from '/@/api/index';

const reportDetail = ref(null); // 获取子组件实例(需要跟ref处同名)
const keyword = ref('');
const currentPage = ref(1);
const total = ref(0);
const tableData = ref([]);
const multipleTable = ref(null);
const multipleSelection = ref([]);
const toggleSelection = (rows) => {
  if (rows) {
    rows.forEach((row) => {
      multipleTable.value.toggleRowSelection(row, undefined);
    });
  } else {
    multipleTable.value.clearSelection();
  }
};
const handleSelectionChange = (val) => {
  multipleSelection.value = val;
};

onMounted(() => {
  getDataList();
});
function getDataList (userName = "") {
  let params = {
    column: 'updateTime',
    order: 'desc',
    pageNo: currentPage.value,
    pageSize: 9999,
    userName: userName
  };
  getdxResultList(params).then((result) => {
    let _list = result.result.records;
    tableData.value = _list
    total.value = result.result.total
  });
}
function handleChangePage (page) {
  currentPage.value = page;
  getDataList()
}
function handleClickReport (record) {
  reportDetail.value.show(record)
}
function searchFun (params) {
  getDataList(keyword.value);
}
function resetFun (params) {
  keyword.value = '';
  getDataList();
}
function handleClickDel (record) {
  ElMessageBox.confirm('确定要删除所选中的数据?', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deldxResult(record.id).then((res) => {
      if (res.success) {
        ElMessage.success(res.message)
        getDataList();
      } else {
        ElMessage.warning(res.message)
      }
    })
  }).catch(() => {
  });
}
const router = useRouter();
watch(() => router.currentRoute.value.path, (n, o) => {
  getDataList();
})
</script>

<style lang="scss">
@import './style.scss';
</style>
