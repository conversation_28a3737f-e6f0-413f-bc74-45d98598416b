<template>
  <Header title="编辑" status="back" />
  <Container>
    <div class="editIntro px-68px w-full h-full flex flex-col items-end">
      <div class="editIntro-form w-full flex flex-col">
        <div class="editIntro-form-intro">
          <el-input v-model="dataForm.intro" :autosize="{ minRows: 15, maxRows: 30 }" type="textarea" placeholder="请输入单位简介…" />
        </div>
        <div class="editIntro-form-images">
          <span class="label"> 选择图片： </span>
          <div class="upload">
            <el-upload v-model:file-list="dataForm.fileList" action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15" list-type="picture-card" :multiple="true" limit="3" :on-remove="handleRemove">
              <el-icon>
                <Plus />
              </el-icon> </el-upload>
          </div>
        </div>
      </div>
      <div class="editIntro-confirm"> 确认 </div>
    </div>
  </Container>
</template>

<script setup>
import { ElMessage } from 'element-plus';

onMounted(() => { });
const dataForm = ref({
  intro: '',
  fileList: [],
});
const handleRemove = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles);
};
</script>

<style lang="scss">
@import './style.scss';
</style>
