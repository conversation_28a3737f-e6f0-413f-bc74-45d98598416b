<template>
  <Header title="机构介绍" status="back" />
  <Container>
    <div class="intro w-full h-full px-70px flex flex-col items-end justify-between">
      <div class="intro-text" v-html="dataForm.intro"></div>
      <!-- <div class="intro-banner w-full flex justify-between">
        <img src="/@/assets/images/avatar.png" alt="" />
        <img src="/@/assets/images/avatar.png" alt="" />
        <img src="/@/assets/images/avatar.png" alt="" />
      </div> -->
      <!-- <div class="intro-edit" @click="goEdit()">
        <el-icon>
          <EditPen />
        </el-icon>编辑
      </div> -->
    </div>
  </Container>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { sysDepartGetByOrgCode, orgCode } from '/@/api/index';

const dataForm = ref({
  intro:
    '',
  fileList: [],
});
const router = useRouter();
// function goEdit () {
//   router.push('/user/intro/edit');
// }
// 提交
onMounted(() => {
  getContent()
});
function getContent (params) {
  sysDepartGetByOrgCode({ orgCode }).then(res => {
    dataForm.value.intro = res.result.memo
  })
}
</script>

<style lang="scss">
@import './style.scss';
</style>
