.intro {
  width: calc(100% - 136px);
  padding: 49px 36px;

  // background: #fff;
  border-radius: 30px;

  .el-textarea__inner {
    align-items: center;
    background-color: rgb(30 121 174 / 13%);
    box-shadow: none;
    border-radius: 20px;
    padding: 32px 45px;
  }
  
  .el-upload--picture-card {
    background-color: transparent;
  }
  
  // .el-textarea__inner::-webkit-input-placeholder {
  //   color: #1e79ae;
  // }
  .el-input__wrapper {
    align-items: center;
    background-color: transparent;
    box-shadow: none;
  }

  // input输入框focus颜色样式
  .el-input {
    --el-input-focus-border: transparent;
    --el-input-focus-border-color: transparent;
  }
  
  .el-pagination.is-background .el-pager li.is-active {
    background-color: #1e79ae;
  }

  &-text {
    font-family: PingFangSC, 'PingFang SC';
    font-weight: 400;
    font-size: 24px;
    color: #666;
    line-height: 50px;
    overflow-y: auto;
    padding: 0 36px;
    width: 100%;
    min-height: 100%;
  }

  &-banner {
    > img {
      width: 497px;
      height: 301px;
      object-fit: cover;
    }
  }

  &-edit {
    font-family: PingFangSC, 'PingFang SC';
    font-weight: 600;
    font-size: 20px;
    color: #1e79ae;
    line-height: 28px;
  }
}

::-webkit-scrollbar {
  width: 14px;
  background: #fff;
  border-radius: 7px;
}

::-webkit-scrollbar-thumb {
  width: 8px;
  height: 89px;
  background: #85ccf5;
  border-radius: 10px;
}

::-webkit-scrollbar-track {
  background: #fff;
  border-radius: 10px;
}

.editIntro {
  position: relative;

  &-form {
    &-images {
      .label {
        font-family: PingFangSC, 'PingFang SC';
        font-weight: 400;
        font-size: 20px;
        color: #1e79ae;
        line-height: 56px;
      }
    }
  }

  &-confirm {
    background-image: url('../.././../assets/images/title_bg.png');
    width: 200px;
    height: 76px;
    background-size: 100% 100%;
    font-family: PingFangSC, 'PingFang SC';
    font-weight: 600;
    font-size: 24px;
    color: #000;
    line-height: 76px;
    text-align: center;
    position: absolute;
    bottom: 0;
    right: 68px;
  }
}
