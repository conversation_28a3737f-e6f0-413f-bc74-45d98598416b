.parts {
  height: 100%;
  padding-bottom: 120px;
  overflow-y: scroll;

  .part {
    padding: 0 36px;

    &-item {
      padding: 28px;
      border-bottom: 1px solid #85ccf5;
      font-family: PingFangSC, 'PingFang SC';
      font-weight: 400;
      font-size: 28px;
      color: #000;
      line-height: 40px;
    }

    .current {
      color: #85ccf5;
    }
  }

  .player1 {
    background-color: red;
  }
}

.fm {
  margin: 0 auto 50px;
  background-image: url('/@/assets/images/container_bg.png');
  background-size: 100% 100%;
  position: relative;
}

.player {
  width: 100%;
  height: 156px;
  position: absolute;
  bottom: 20px;
  overflow: hidden;
  z-index: 1;

  &-progress {
    background-color: #fff;
  }

  &-content {
    padding: 0 72px;
    background-color: #fff;

    .cover {
      width: 114px;
      height: 114px;
      border-radius: 10px;
      overflow: hidden;

      > img {
        width: 100%;
      }
    }

    .info {
      &-label {
        font-family: PingFangSC, 'PingFang SC';
        font-weight: 400;
        font-size: 32px;
        color: #000;
        line-height: 45px;
      }

      &-time {
        font-family: PingFangSC, 'PingFang SC';
        font-weight: 400;
        font-size: 24px;
        color: #000;
        line-height: 33px;
      }
    }

    .player-content-btn {
      &-item {
        width: 38px;
        height: 38px;

        > img {
          width: 100%;
        }
      }

      .play {
        width: 66px;
        height: 66px;

        > img {
          width: 100%;
        }
      }
    }
  }
}
