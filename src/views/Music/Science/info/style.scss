.experts {
  width: calc(100% - 100px);
  overflow-y: scroll;
  padding: 0 36px;

  &-item {
    padding: 44px 0 26px;
    border-bottom: 1px solid #85ccf5;

    &-avatar {
      width: 110px;
      height: 110px;
      border-radius: 10px;
      overflow: hidden;

      > img {
        width: 100%;
      }
    }

    &-content {
      .label {
        font-family: PingFangSC, 'PingFang SC';
        font-weight: 600;
        font-size: 32px;
        color: #000;
        line-height: 45px;
      }

      .value {
        font-family: PingFangSC, 'PingFang SC';
        font-weight: 400;
        font-size: 28px;
        color: #000;
        line-height: 40px;
      }
    }
  }

  .el-textarea__inner {
    align-items: center;
    background-color: rgb(30 121 174 / 13%);
    box-shadow: none;
    border-radius: 20px;
    padding: 32px 45px;
  }

  .el-upload--picture-card {
    background-color: transparent;
  }

  .el-table {
    font-size: 22px;
    background-color: transparent;
  }

  .el-table tr {
    background-color: transparent;
  }

  .el-table .cell,
  .el-table th div {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .el-table tbody tr:hover > td {
    background: #91c8e7 !important;
  }

  // .el-textarea__inner::-webkit-input-placeholder {
  //   color: #1e79ae;
  // }
  .el-input__wrapper {
    align-items: center;
    background-color: transparent;
    box-shadow: none;
  }

  // input输入框focus颜色样式
  .el-input {
    --el-input-focus-border: transparent;
    --el-input-focus-border-color: transparent;
  }

  .el-pagination.is-background .el-pager li.is-active {
    background-color: #1e79ae;
  }
}
