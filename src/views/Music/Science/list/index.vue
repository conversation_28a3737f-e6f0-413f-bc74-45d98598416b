<template>
  <Header title="科普宣教" status="home" />
  <Container>
    <div class="sciencelist w-full flex-1">
      <!-- <div class="sciencelist-item flex flex-col" @click="goInfo(item.id)" :key="index" v-for="(item, index) of list">
        <img class="sciencelist-item-cover" :src="getAssetsFile('science', item.coverImage)" alt="" />
        <span class="sciencelist-item-label w-full">{{ item.name }}</span>
      </div> -->
      <Carousel :list="list" :single="true" @info="goInfo" />
    </div>
  </Container>
</template>

<script setup>
import { getAssetsFile, chunkArray } from '/@/utils/tool';
import { getklPsyPopularCategory } from '/@/api/index';

const router = useRouter();
function goInfo (id) {
  router.push('../science/info/' + id);
}
const list = ref([
]);

onMounted(() => {
  getType();
});
onActivated(() => {
  getType();
})

function getType () {
  let params = {
    pageNo: 1,
    pageSize: 50,
  };
  getklPsyPopularCategory(params).then((result) => {
    let _list = result.result.records;
    list.value = chunkArray(
      4,
      result.result.records.map((item) => {
        return { id: item.id, cover: item.coverImage, label: item.name, value: '' };
      }),
    );
  });
}
</script>

<style lang="scss">
@import './style.scss';
</style>
