<template>
  <Header title="成长" status="back" />
  <div class="fm content w-1758px h-839px flex flex-col items-center justify-center py-50px">
    <div class="parts w-full h-full">
      <div class="part flex-1 flex flex-col">
        <div :class="'part-item flex  justify-between ' + (playIndex == index ? 'current' : '')" :key="index" v-for="(item, index) of list">
          <div class="part-item-label mr-26px">
            {{ item.label }}
          </div>
          <div class="part-item-time mr-26px">
            {{ item.time }}
          </div>
        </div>
      </div>
    </div>
    <div class="player flex flex-col">
      <audio @timeupdate="updateProgress" controls ref="audioRef" style="display: none" src="/@/assets/images/testAudio.mp3"></audio>
      <div class="player-progress w-99.9% h-12px" ref="currentProgress">
        <el-slider class="slider_box" v-model="currentProgress" :show-tooltip="false" @input="handleProgressChange" />
      </div>
      <div class="player-content flex-1 flex items-center">
        <div class="cover mr-30px">
          <img src="/@/assets/images/avatar.png" alt="" />
        </div>
        <div class="info flex-1 flex flex-col">
          <span class="info-label mb-20px">{{ audioDom.label }}</span>
          <span class="info-time">{{ durationTime }}</span>
        </div>
        <div class="btn flex items-center">
          <div class="btn-item pre" @click="changePlay('pre')">
            <img src="/@/assets/images/ht.png" alt="" />
          </div>
          <div class="play mx-46px" @click="playAudio">
            <img v-if="isPlay" src="/@/assets/images/play.jpg" alt="" />
            <img v-else src="/@/assets/images/stop.png" alt="" />
          </div>
          <div class="btn-item next" @click="changePlay('next')">
            <img src="/@/assets/images/kj.png" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getAssetsFile } from '/@/utils/tool';

const list = ref([
  {
    id: 1,
    cover: '',
    audioUrl: 'https://img.zhisongkeji.com/musicTherapy/testAudio.mp3',
    label: 'test1',
    time: '08:11',
    durationTime: '69.381224',
  },
  {
    id: 2,
    cover: '',
    audioUrl: 'https://img.zhisongkeji.com/musicTherapy/testAudio.mp3',
    label: 'test2',
    time: '04:02',
    durationTime: '69.381224',
  },
  {
    id: 3,
    cover: '',
    audioUrl: 'https://img.zhisongkeji.com/musicTherapy/testAudio.mp3',
    label: 'test3',
    time: '05:11',
    durationTime: '69.381224',
  },
]);
const audioDom = ref({}); //当前
const playIndex = ref(0);
const audioRef = ref(null);
const isPlay = ref(false); //音频是否在播放
const audioStart = ref('0:00');
const durationTime = ref('0:00'); //音频的总时长，显示的时间格式
const duration = ref(0); //音频的总时长
const currentProgress = ref(0);

onMounted(() => {
  audioDom.value = list.value[0];
  calculateDuration();
});
onActivated(() => {
  audioDom.value = list.value[0];
  calculateDuration();
})
// 获取音频时长
function calculateDuration () {
  let _audioRef = audioRef.value;
  _audioRef.loop = false;
  _audioRef.src = audioDom.value.audioUrl;
  // 监听音频播放完毕
  _audioRef.addEventListener(
    'ended',
    function () {
      isPlay.value = false;
      currentProgress.value = 0;
    },
    false,
  );
  if (_audioRef != null) {
    // 开始播放前执行
    _audioRef.oncanplay = function () {
      duration.value = _audioRef.duration; // 计算音频时长
      durationTime.value = transTime(_audioRef.duration); //换算成时间格式
    };
    // 进入页面默认开始暂停
    // _audioRef.value.pause();
    // isPlay.value = false;
  }
}
//切换音频
function changePlay (type) {
  let index = playIndex.value;
  if (type == 'pre' && index == 0) {
    return;
  } else {
    play.value = list.value[index - 1];
    playIndex.value = index - 1;
  }

  if (type == 'next' && index == list.value.length - 1) {
    return;
  } else {
    play.value = list.value[index + 1];
    playIndex.value = index + 1;
  }

  isPlay.value = true;
  currentProgress.value = 0;

  calculateDuration();
}

// function handleCloseMusic() {
//   audioRef.value.pause();
//   isPlay.value = true;
// }
// 音频播放时间换算
function transTime (duration) {
  const minutes = Math.floor(duration / 60);
  const seconds = Math.floor(duration % 60);
  const formattedMinutes = String(minutes).padStart(2, '0'); //padStart(2,"0") 使用0填充使字符串长度达到2
  const formattedSeconds = String(seconds).padStart(2, '0');
  return `${formattedMinutes}:${formattedSeconds}`;
}
// 播放暂停控制
function playAudio () {
  if (audioRef.value.paused) {
    audioRef.value.play();
    isPlay.value = false;
  } else {
    audioRef.value.pause();
    isPlay.value = true;
  }
}
// 根据当前播放时间，实时更新进度条
function updateProgress (e) {
  var value = e.target.currentTime / e.target.duration;
  if (audioRef.value.play) {
    currentProgress.value = value * 100;
    audioStart.value = transTime(audioRef.value.currentTime);
  }
}
//调整播放进度
const handleProgressChange = (val) => {
  console.log(val);
  if (!val) {
    return;
  }
  let currentTime = duration.value * (val / 100);
  // 更新音频的当前播放时间
  audioRef.value.currentTime = currentTime;
};
</script>

<style lang="scss">
@import './style.scss';
</style>
