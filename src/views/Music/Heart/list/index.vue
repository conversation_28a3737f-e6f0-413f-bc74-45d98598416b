<template>
  <Header title="心灵康养" status="home" />
  <Container>
    <div class="sciencelist">
      <div @click="goInfo(item.id)" class="sciencelist-item flex flex-col" :key="index" v-for="(item, index) of list">
        <img class="sciencelist-item-cover" :src="getAssetsFile('science', item.cover)" alt="" />
        <span class="sciencelist-item-label w-full">{{ item.label }}</span>
      </div>
    </div>
  </Container>
</template>

<script setup>
import { getAssetsFile } from '/@/utils/tool';

const router = useRouter();
function goInfo (id) {
  router.push('/heart/info/' + id);
}
const list = ref([
  { id: 1, cover: 'zjjs.png', label: '专家介绍', value: '' },
  { id: 2, cover: 'zymc.png', label: '专有名词', value: '' },
  { id: 3, cover: 'xlxsy.png', label: '心理学实验', value: '' },
  { id: 4, cover: 'xlxy.png', label: '心理效应', value: '' },
]);
</script>

<style lang="scss">
@import './style.scss';
</style>
