<template>
  <div class="music_index w-full h-100vh flex flex-col px-63px py-50px">
    <div class="music_index-header max-w-1792px mb-67px w-full flex items-center justify-between">
      <div class="company"> {{companyName}} </div>
      <div class="title">AI心理问答系统</div>
      <div class="info flex items-center">
        <div class="header-actions">
          <div class="header-actions-item">
            <el-switch size="big" v-model="colorWeak" @change="onColorWeak" />
            <div class="header-actions-item-title">色弱模式</div>
          </div>
        </div>
        <span class="info-realname-box">
          <span class="info-realname">{{userInfo.realname}}</span>
          <span class="info-login cursor-pointer" @click="login()">切换账号</span>
        </span>
        <img class="info-wifi" src="/@/assets/images/wifi.png" alt="" />
        <span class="info-date">{{ time }}</span>
      </div>
    </div>
    <div class="music_index-content flex-1 flex items-center justify-center mt-164px">
      <div class="box">
        <div @click="goPage(item.path)" :class="'box-item flex flex-col ' + `grid${index + 1}`" :key="index" v-for="(item, index) of list" :style="{ backgroundImage: `url(${getAssetsFile('m_list', item.bg)})`, color: item.fontColor }">
          <span class="box-item-label">
            {{ item.label }}
          </span>
          <span class="box-item-desc">
            {{ item.desc }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getAssetsFile, getNowTime } from '/@/utils/tool';
import { getUserInfo, clearUserInfo } from '/@/utils/user';
import { setEquipmentType } from '/@/utils/equipment';

import { userLogout } from '/@/api/index';

const router = useRouter();
function login () {
  router.push('/login/login');
}
const companyName = ref('')
const time = ref('00:00');
const list = ref([
  { label: '数据中心', path: '/user/report', desc: 'Data center', bg: 'icon1.png', fontColor: '#0D8471' },
  { label: 'AI问答', path: '/AI/question', desc: 'AI question and answers', bg: 'icon7.png', fontColor: '#2867CF' },
  { label: '心理FM', path: '/music/fm/list', desc: 'Psychological FM', bg: 'icon4.png', fontColor: '#925111' },
  { label: '心理阅读', path: '/music/read/list', desc: 'Psychological readin', bg: 'icon8.png', fontColor: '#925111' },
  { label: '科普宣教', path: '/music/science/list', desc: 'Psychological Scienc', bg: 'icon2.png', fontColor: '#925111' },
  { label: '心理评估', path: '/music/evaluating/list', desc: 'Psychological Science Popularization', bg: 'icon6.png', fontColor: '#0D8471' },
]);
const userInfo = ref({})

function loginout (params) {
  userLogout().then((result) => {
    userInfo.value = {}
    clearUserInfo().then(_ => {
      getUserInfoFun()
      ElMessage({
        message: '切换游客模式成功',
        type: 'success',
      });
    })
  })
}
function goPage (path) {
  if (path == "/user/info") {
    let _userInfo = JSON.parse(getUserInfo())
    if (_userInfo) {
      router.push(path);
    } else {
      ElMessage({
        message: '个人中心需在登录后进入',
        type: 'warning',
      });
      login();
      return
    }
  }
  router.push(path);
}
function getUserInfoFun (params) {
  let _userInfo = JSON.parse(getUserInfo())
  if (_userInfo) {
    userInfo.value = _userInfo
  }
}
const colorWeak = ref(false);
function onColorWeak (val) {
  console.log(val)
  colorWeak.value = val;
  val ? document.body.classList.add('colorWeak') : document.body.classList.remove('colorWeak')
}
function setTime () {
  time.value = getNowTime()
  setTimeout(() => {
    setTime()
  }, 60000)
}
onMounted(() => {
  if (document.body.className.indexOf('colorWeak') == -1) {
    colorWeak.value = false;
  } else {
    colorWeak.value = true;
  }
  setEquipmentType('music')
  getUserInfoFun()
  setTime()
})
watch(() => router.currentRoute.value.path, (n, o) => {
  if (document.body.className.indexOf('colorWeak') == -1) {
    colorWeak.value = false;
  } else {
    colorWeak.value = true;
  }
  getUserInfoFun()
})
</script>

<style lang="scss">
@import './style.scss';
</style>
