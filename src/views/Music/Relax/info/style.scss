.relax-page{
  .fm{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .tab{
    width: 600px;
    flex-direction: column;
    height: 100%;
    overflow: hidden auto;
    margin-right: 10px;
    border-top: solid #1E79AE 1px;
    padding: 0;

    .tab-item{
      min-height: 80px;
      width: 100%;
      overflow: hidden;
      background-color: rgba($color: #fff, $alpha: 50%);
      box-shadow: none !important;
      border-radius: 0;
      border-left: solid #1E79AE 1px;
      border-bottom: solid #1E79AE 1px;
      padding: 0 20px;
      display: flex;
      justify-content: center;
      align-items: center;

      &.current{
        color: #fff;
        background-color: #1E79AE;
      }
    }
  }

  .ilabel {
    font-family: PingFangSC, 'PingFang SC';
    font-weight: 600;
    font-size: 32px;
    color: #1393dd;
    line-height: 45px;
  }

  .audioContent {
    &+.tab{
      overflow-x: auto;
      width: 100%;
      padding-bottom: 20px;

      .tab-item{
        min-width: 6em;
      }
    }

    .player {
      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }

        to {
          transform: rotate(360deg);
        }
      }

      &-record {
        width: 230px;
        height: 230px;
      }

      .rotate {
        animation: rotate 5s linear infinite;
      }

      &-content {
        .label {
          font-family: PingFangSC, 'PingFang SC';
          font-size: 28px;
          color: #000;
          line-height: 40px;
        }

        .durationTime {
          font-family: PingFangSC, 'PingFang SC';
          font-weight: 400;
          font-size: 24px;
          color: #000;
          line-height: 33px;

          .durationTime-btn {
            > span {
              display: flex;
              align-items: center;

              > img {
                width: 31px;
              }
            }
          }
        }
      }
    }

    .playBtn {
      width: 92px;
      height: 92px;
      margin: 0 auto 122px;

      > img {
        width: 100%;
      }
    }
  }

  .time-content {
    .el-radio.el-radio--large .el-radio__label {
      font-family: PingFangSC, 'PingFang SC';
      font-weight: 400;
      font-size: 32px;
      color: #000;
      line-height: 45px;
    }
  }

  .time-btn {
    .btn {
      width: 200px;
      height: 85px;
      font-family: PingFangSC, 'PingFang SC';
      font-weight: 600;
      font-size: 24px;
      color: #000;
      background-image: url('/static/icon/btn-bg.jpg');
      background-size: 100%;
      text-align: center;
      line-height: 85px;
    }
  }

}