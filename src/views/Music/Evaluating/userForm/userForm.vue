<template>
  <el-dialog v-model="visible" title="添加测试者信息" width="1020">
    <el-form ref="form" :model="model" :rules="rules" :label-width="formLabelWidth" style="width: 100%; display: flex; flex-direction: row; flex-wrap: wrap;">
      <el-col :span="8">
        <el-form-item label="编号" style="width: 100%;" prop="userNumber">
          <el-autocomplete v-model="model.userNumber" hide-loading :fetch-suggestions="searchUserNumAsync" placeholder="请输入编号" @select="handleSelect">
            <template #default="{ item }">
              <p>{{ item.text }} {{ item.name }}</p>
            </template>
          </el-autocomplete>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="姓名" style="width: 100%;" prop="name">
          <el-input v-model="model.name" placeholder="请输入姓名" autocomplete="off" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="性别" style="width: 100%;" prop="sex">
          <el-select v-model="model.sex" placeholder="请选择性别" style="width: 100%;">
            <el-option v-for="item in sexOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="年龄" style="width: 100%;" prop="age">
          <el-input v-model="model.age" placeholder="请输入年龄" autocomplete="off" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="生日" style="width: 100%;" prop="birthday">
          <el-date-picker v-model="model.birthday" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD" placeholder="请选择生日" />
        </el-form-item>
      </el-col>
      <!-- <el-col :span="8">
                <el-form-item label="文化程度" style="width: 100%;" prop="cultural">
                    <el-select v-model="model.cultural" placeholder="请选择文化程度" style="width: 100%;">
                        <el-option v-for="item in sexOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-col> -->
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleOk" :loading="confirmLoading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { getfindUserNumberList, postPsUserAddForHome, getpsUserById } from '/@/api/index';
export default {
  name: 'UserForm',
  data () {
    return {
      visible: false,
      formLabelWidth: '80px',
      confirmLoading: false,
      measureId: '',
      answerType: 0,
      model: {
        userNumber: '',
        cultural: '3'
      },
      sexOptions: [{
        label: '男',
        value: '1'
      }, {
        label: '女',
        value: '2'
      }],
      rules: {
        userNumber: [{ required: true, message: '请输入编号', trigger: 'blur' },],
        name: [{ required: true, message: '请输入测试者姓名', trigger: 'blur' },],
        sex: [{ required: true, message: '请选择性别', trigger: 'blur' },],
        age: [{ required: true, message: '请输入年龄', trigger: 'blur' },],
        birthday: [{ required: true, message: '请选择生日', trigger: 'blur' },],
      }
    }
  },
  methods: {
    loadMeasureIds (currSelectedIds) {
      this.measureId = currSelectedIds
      this.visible = true
    },
    searchUserNumAsync (value, cb) {
      getfindUserNumberList({ userNumber: value, type: 1 }).then(res => {
        if (res.success) {
          cb(res.result)
        }
      })
    },
    handleSelect (item) {
      this.model.userNumber = item.text
      getpsUserById({ id: item.value }).then((res) => {
        if (res.success) {
          this.$refs.form.resetFields()
          this.model = res.result
          console.log(res.result)
        }
      })
    },
    handleOk () {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          this.confirmLoading = true
          this.pushAnswerPage(this.model)
        } else {
          return false
        }
      })
    },
    handleCancel() {
      this.visible = false
    },
    pushAnswerPage (formData) {
      let that = this
      formData.measureIds = [this.measureId]
      formData.answerType = that.answerType
      postPsUserAddForHome(formData).then((res) => {
        if (res.success) {
        } else {
          that.$message.error(res.message)
        }
        that.confirmLoading = false
      })
    },
  }
}
</script>