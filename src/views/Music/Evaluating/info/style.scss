.evaluatinginfo {
  padding: 0 68px;

  &-title {
    font-size: 36px;
    color: #000;
    line-height: 56px;
    text-align: center;
  }

  &-content {
    .btn {
      width: 200px;
      height: 76px;
      background-image: url('/static/icon/btn-bg.jpg');
      background-size: 100% 100%;
      font-family: PingFangSC, 'PingFang SC';
      font-weight: 600;
      font-size: 24px;
      color: #000;
      line-height: 76px;
      text-align: center;
      cursor: pointer;
    }

    .keybox {
      .questions {
        .question {
          font-family: PingFangSC, 'PingFang SC';
          font-weight: 600;
          font-size: 24px;
          color: #000;
          line-height: 33px;

          .voice {
            width: 20px;
            height: 20px;
            cursor: pointer;
          }
        }

        .option {
          &-item {
            font-family: PingFangSC, 'PingFang SC';
            font-weight: 400;
            font-size: 24px;
            color: #000;
            line-height: 33px;
            cursor: pointer;

            .icon {
              width: 20px;
              min-width: 20px;
              height: 20px;
              border-radius: 50%;
              border: 1px solid #1e79ae;
            }

            .selected {
              border: 5px solid #1e79ae;
            }
          }
        }
      }
    }

    .configs {
      &-msg {
        > span {
          font-family: PingFangSC, 'PingFang SC';
          font-weight: 400;
          font-size: 24px;
          color: #000;
          line-height: 33px;

          > img {
            width: 25px;
            height: 25px;
          }
        }
      }
    }
  }
}
