.evaluating {
  padding: 0 68px;

  &-content {
    .evaluating-page {
      height: 100%;

      .page {
        display: grid;
        grid-template-columns: repeat(2, 730px);
        grid-template-rows: repeat(2, 256px);
        grid-gap: 43px;

        .evaluating-item {
          background: #fff;
          border-radius: 15px;
          padding: 29px 34px;

          &-cover {
            width: 197px;
            height: 197px;
          }

          &-content {
            .label {
              font-size: 20px;
              color: #333;
              line-height: 31px;
            }

            .value {
              width: 449px;
              height: 150px;
              font-family: PingFangSC, 'PingFang SC';
              font-weight: 400;
              font-size: 16px;
              color: #666;
              line-height: 30px;
              display: -webkit-box;
              -webkit-line-clamp: 5;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: normal;
            }
          }
        }
      }
    }
  }
}
