<template>
  <Header title="心理评估" status="home" />
  <Container>
    <Tab :tabs="tabs" :current="currentTab" @change="changeTab" />
    <div class="evaluating w-full flex-1">
      <el-carousel class="evaluating-content w-full h-full" height="100%" :autoplay="false" indicator-position="outside" arrow="always" :loop="false">
        <el-carousel-item v-for="(item, index) of list" :key="index">
          <div class="evaluating-page flex items-center justify-center">
            <div class="page card">
              <div class="evaluating-item flex" :key="_item.id" @click="goInfo(_item.key)" v-for="_item of item">
                <img class="evaluating-item-cover mr-16px" src="/@/assets/images/evaluatingtest.png" alt="" />
                <div class="evaluating-item-content flex-1 flex flex-col">
                  <span class="label mb-18px">{{ _item.title }}</span>
                  <span class="value flex-1">{{ _item.title }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
    <userForm ref="UserForm" @ok="modalFormOk"></userForm>
    <WebsocketMixin ref="socket" />
  </Container>
</template>

<script setup>
import { getAssetsFile } from '/@/utils/tool';
import { ElMessage } from 'element-plus';
import userForm from '../userForm/userForm.vue';
import { getMentalityList } from '/@/api/index';
import { WebsocketMixin } from '/@/mixins/WebsocketMixin'

const router = useRouter();
const list = ref([]);
const dataList = ref([]);
const tabs = ref([]);
const currentTab = ref(0);
const UserForm = ref(null);
const socket = ref()
onMounted(() => {
  getList();
});
onActivated(() => {
  UserForm.value.handleCancel()
  getList();
})
onBeforeUnmount(() => {
  socket.value.websocketOnclose()
})
function goInfo (id) {
  UserForm.value.loadMeasureIds(id)
  UserForm.value.title = '添加测试者信息'
}
function getList () {
  getMentalityList().then((result) => {
    let _list = result.result;
    let _dataList = []
    let _tabs = []
    for (const item of _list) {
      _tabs.push(item.title)
      _dataList.push(item.children)
    }
    _dataList = _dataList.map(item => {
      let _item = []
      let index = 0;
      for (const ite of item) {
        if (index == 0) {
          _item.push([ite])
        } else {
          _item[_item.length - 1].push(ite)
        }
        if (index == 3) {
          index = 0;
        } else {
          index++
        }
      }
      return _item
    })
    dataList.value = _dataList;
    console.log(dataList)
    tabs.value = _tabs
    list.value = _dataList[0]
    console.log(list.value)
  });
}
function changeTab (index) {
  list.value = dataList.value[index]
  currentTab.value = index;
}
function modalFormOk () {

}
</script>

<style lang="scss">
@import './style.scss';

/* 自定义Carousel右箭头 */
.evaluating-content .el-carousel__arrow--right {
  color: #1e79ae;
  font-size: 72px;
  font-weight: 700;
  background-color: transparent; /* 箭头颜色 */
}

/* 自定义Carousel左箭头 */
.evaluating-content .el-carousel__arrow--left {
  color: #1e79ae;
  font-size: 72px;
  font-weight: 700;
  background-color: transparent; /* 箭头颜色 */
}
</style>
