<template>
  <Header title="心理测评" status="back" />
  <Container>
    <div class="answer-page">
      <audio ref="music" controls="controls" style="display:none" @ended="handleEnded">
        <source :src="audioUrlPrefix + audioUrl" type="audio/mpeg" />
      </audio>
      <div class="page-title" v-if="!isShowQuestion || !isShowWskx">{{ measure.name }}</div>

      <div class="page-content">
        <div class="content-top">
          <div class="top-item">
            <el-icon type="ordered-list" />
            <span v-if="isShowAttention">{{ attentionOrderPage }}</span>
            <span v-else>{{ (isShowQuestion ? questionNum : '--') + '/' + questionList.length }}</span>
          </div>

          <div class="top-item">
            <el-icon type="clock-circle" theme="twoTone" />
            <span>{{ timer }}</span>
          </div>

          <!-- v-if="!isShowGesell && !isShowQuestion" -->
          <div class="top-item" v-if="!isShowQuestion || (!isShowWskx && !isShowAttention)">
            <el-switch v-model="audioChange" :disabled="shouldDisabledAudio" @change="handleAudioChange" />
            <span>语音读题</span>
            <el-select v-model="readType" :disabled="!readType" style="width: 120px" @change="handleChange">
              <el-option :value="0" label="文本阅读" />
              <el-option :value="1" label="普通话" />
              <el-option :value="2" label="粤语" />
            </el-select>
          </div>
        </div>

        <!-- 量表确认信息 -->
        <div class="content-info" v-if="!isShowQuestion">
          <!-- <h3 class="info-sub-title" v-if="subsectionTitle">{{ subsectionTitle }}</h3> -->

          <p class="info-description" v-html="measure.description"></p>

          <el-button type="primary" @click="quesionStart">开始</el-button>
        </div>

        <div class="cotent-main" v-if="isShowQuestion && !isShowAttention && !isShowGesell && !isShowWskx">
          <el-alert v-if="notAnswerArr.length > 0" title="有未答完的题目，请点击左侧滑动条中未完成的题目信息进行补充答题。" type="error" />

          <h3 class="main-title" v-if="subsectionTitle">{{ subsectionTitle }}</h3>
          <h3 class="main-sub-title" v-if="subsectionSubTitle">{{ subsectionSubTitle }}</h3>

          <div class="main-content">
            <!-- 同步情况下终端不显示 -->
            <div class="content-left" v-if="!(isTerminal && isSynchronize) && !isPPVT">
              <el-slider :marks="marks" vertical :max="questionList.length" v-model="questionNum" @change="onChangeSlider" />
            </div>

            <div class="content-right">
              <ChooseItem v-if="questionList[questionIndex].titleType === '1' || questionList[questionIndex].titleType === '2'" :isTerminal="isTerminal" :isControl="isControl" :isSynchronize="isSynchronize" :isPPVT="isPPVT" :questionIndex="questionIndex" :question="questionList[questionIndex]" @submit="submit" @playAudioAgain="playAudioAgain" @ppvtBegin="handlePPVTBegin" @ppvtEnd="handlePPVTEnd">
              </ChooseItem>
              <ManualEntryItem v-if="['3', '4', '5', '6', '11', '12', '13', '14'].includes(questionList[questionIndex].titleType)" :question="questionList[questionIndex]" @submit="submit"></ManualEntryItem>
              <CanvasItem v-if="questionList[questionIndex].titleType === '7'" :question="questionList[questionIndex]" :isTerminal="isTerminal" :isControl="isControl" :isSynchronize="isSynchronize" :imgUrl="imgUrl" @submit="submit" @submitImg="submitImg"></CanvasItem>
              <DigitalBreadthItem v-if="questionList[questionIndex].titleType === '8'" :question="questionList[questionIndex]" :audioChange="audioChange" :topicEnd="topicEnd" :readType="readType" @submit="submit" @videoPlay="videoPlay"></DigitalBreadthItem>
              <ConnectItem v-if="questionList[questionIndex].titleType === '9'" :question="questionList[questionIndex]" :isTerminal="isTerminal" :isControl="isControl" :isSynchronize="isSynchronize" :imgUrl="imgUrl" @submit="submit" @submitImg="submitImg"></ConnectItem>
              <DrawItem v-if="questionList[questionIndex].titleType === '10'" :question="questionList[questionIndex]" :isTerminal="isTerminal" :isControl="isControl" :isSynchronize="isSynchronize" :imgUrl="imgUrl" @submit="submit" @submitImg="submitImg"></DrawItem>
              <div class="right-btn" v-if="!(isTerminal && isSynchronize) && !isPPVT">
                <el-button :class="[questionNum <= 1 && 'no-choose']" @click="previousPage" type="primary">
                  <el-icon type="left" />
                  上一题
                </el-button>
                <el-button :class="[questionNum >= questionList.length && 'no-choose']" @click="nextPage" type="primary">
                  下一题
                  <el-icon type="right" />
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 威斯康星 -->
        <WskxItem v-if="isShowQuestion && !isShowGesell && !isShowAttention && isShowWskx" :timer="timer" :time="time" :userId="userId" :measure="measure" :resultId="dxResult.id" @ok="finishWskxItem"></WskxItem>
        <!-- 注意力测验量表 -->
        <AttentionItem v-if="isShowQuestion && !isShowGesell && isShowAttention" :time="totalTime" :userId="userId" :measure="measure" :resultId="dxResult.id" @pageChange="attentionChangePage" @ok="finishAttentionItem" />
        <Gesell v-if="isShowQuestion && isShowGesell && !isShowAttention" :questionList="questionList" @submit="submit" @commit="commit"></Gesell>
      </div>
    </div>
  </Container>
</template>

<script>
import ChooseItem from './components/chooseItem.vue'
import ManualEntryItem from './components/manualEntryItem.vue'
import DigitalBreadthItem from './components/digitalBreadthItem.vue'
import CanvasItem from './components/canvasItem.vue'
import ConnectItem from './components/connectItem.vue'
import WskxItem from './components/wskxItem.vue'
import AttentionItem from './components/attentionItem.vue'
import DrawItem from './components/drawItem.vue'
import Gesell from './GesellPage.vue'
import { getCurrentInstance } from "vue";
import { getLoadAnswerPageData, postAddOption, getNotAnswer, getUploadResult, postWsMessagePush, postUploadResultForPPVT, postPsUserAddForHome, getpsUserById } from '/@/api/index';
// 子组件需要传回的参数: score、answer、type(都不是必填，视情况而定)
// 题目类型通过titleType区分，目前存在的类型：
// 1 - 题目、选项均为文字
// 2 - 题目、选项均为图片
// 3 - 自输入分值
// 4 - 自输入内容
// 5 - 选择时间
// 6 - 自输入数字
// 7 - 画图
// 8 - 数字广度
// 9 - 顺序连线
// 11 - 选择日期
// 12 - 选择月份
// 13 - 选择星期几
// 14 - 选择城市
// 15 - 威斯康星卡片 颜色-形状-数量

export default {
  name: 'AnswerForm',
  components: {
    [ChooseItem.name]: ChooseItem,
    [ManualEntryItem.name]: ManualEntryItem,
    [CanvasItem.name]: CanvasItem,
    [DigitalBreadthItem.name]: DigitalBreadthItem,
    [ConnectItem.name]: ConnectItem,
    [WskxItem.name]: WskxItem,
    [Gesell.name]: Gesell,
    [DrawItem.name]: DrawItem,
    [AttentionItem.name]: AttentionItem,
  },
  data () {
    return {
      timer: '00:00:00',
      time: 0,
      totalTime: 0,

      audioChange: false,
      questionNum: 1,
      userId: '',
      type: '',
      subsectionTitle: '',
      subsectionSubTitle: '',
      subsectionContent: '',
      measure: {},
      //未答完题目标准指针
      notAnswerArr: [],
      isShowQuestion: false,
      questionList: [],
      questionIndex: 0,
      dxResult: {},
      marks: {},
      isShowGesell: false,
      isShowAttention: false,
      isShowWskx: false,
      isPPVT: false,  //是否是PPVT量表
      shouldDisabledAudio: false, //是否禁用语音播放开关
      readType: 0,
      audioUrlPrefix: import.meta.env.VITE_APP_MEASURE_AUDIO_URL, //音频前缀，默认为空，由于本地没有音频文件，本地调试的时候此处配置线上绝对路径  https://zcpm.zhisongkeji.com
      audioUrl: '',
      palyStatus: '',
      topicEnd: true,
      isTerminal: false,  //判断是否是终端
      isSynchronize: false, //同步显示模式，医生端和患者端同时显示量表（0_非同步模式，1_同步模式）
      isControl: false,
      imgUrl: '',
      hasNext: false,
      attentionOrderPage: '1/5',
      ppvtAnswerContent: [], //记录ppvt答题对错详情 1对，0错

      searchData: '',
      voiceShow: false,
      voiceNum: 10,
      voiceInterval: null,
      result: null
    }
  },
  watch: {
    audioChange (val) {
      if (val) {
        this.readType = 1
        this.playAudio()
      } else {
        this.readType = 0
        this.pauseAudio()
        if (this.questionList[this.questionIndex].titleType === '8') this.topicEnd = true
      }
    }
  },
  created () {
    //初始化答题页数据
    this.init()
  },
  activated () {
    //初始化答题页数据
    this.init()
  },
  methods: {
    async init () {
      console.log("参数")
      // 终端第一次做
      if (this.$route.query.isTerminal && !this.$route.query.resultId) {
        this.isTerminal = true
        this.isControl = false
        this.loadData()
      } else if (this.$route.query.isTerminal && this.$route.query.resultId) {
        // 终端继续做
        this.isTerminal = true
        this.isControl = false
        await this.loadData()
        this.quesionStart()
      } else if (this.$route.query.isControl) {
        // 主控
        this.isTerminal = false
        this.isControl = true
        await this.loadData()
        this.quesionStart()
      } else {
        this.isTerminal = false
        this.isControl = false
        this.loadData()
      }
    },

    setNextMeasure () {
      this.loadData()
      // 主控端传给终端，多量表
      if (this.isControl && this.isSynchronize) {
        let params = {}
        if (this.type == '1' || this.type == '0') {
          params = { userId: this.$route.query.userId, type: this.$route.query.type, doctorId: this.$route.query.doctorId, terminalDoctorId: this.$route.query.terminalDoctorId, hasNext: true, packageMark: this.$route.query.packageMark }
        } else if (this.type == '2') {
          params = { resultId: this.$route.query.resultId, type: this.$route.query.type, doctorId: this.$route.query.doctorId, terminalDoctorId: this.$route.query.terminalDoctorId }
        }
        const data = {
          sysUserId: this.$route.query.terminalDoctorId,
          code: '401',
          messageJson: JSON.stringify(params)
        }
        this.messagePush(data)
      }
    },
    handleSearch () {
      return getpsUserById({ id: this.$route.query.userId }).then((res) => {
        if (res.success) {
          return res.result
        } else {
          return false
        }
      })
    },
    handleChange (value) {
      this.readType = Number(value)
      if (this.readType) {
        this.playAudio()
      } else {
        this.audioChange = false
        this.pauseAudio()
      }
    },

    // 播放语音
    playAudio () {
      if (this.audioChange) {
        //题目信息
        if (!this.isShowQuestion) {
          this.playTopic(1)
        } else {
          this.playQuestion(1)
          if (this.questionList[this.questionIndex].titleType === '8') this.topicEnd = false
        }
      } else {
        if (this.questionList.length > 0 && this.questionList[this.questionIndex].titleType === '8') {
          this.topicEnd = true
        }
      }
    },

    playTopic (step) {
      if (step === 1) {
        this.audioUrl = this.readType === 1 ? '/static/measure/common/pt/measure_prefix.mp3' : '/static/measure/common/gd/measure_prefix.mp3'
        this.palyStatus = 'topicPrefix'
      }
      if (step === 2) {
        this.audioUrl = this.readType === 1 ? this.measure.audioPath : this.measure.audioPathGd
        this.palyStatus = 'topic'
      }
      if (step === 3) {
        this.audioUrl = this.readType === 1 ? '/static/measure/common/pt/measure_suffix.mp3' : '/static/measure/common/gd/measure_suffix.mp3'
        this.palyStatus = 'topicSuffix'
      }
      this.$refs.music.load()
      this.$nextTick(() => {
        this.$refs.music.play()
      })
    },

    playQuestion (step) {
      if (step === 1) {
        this.audioUrl = this.readType === 1 ? this.questionList[this.questionIndex].audioPath : this.questionList[this.questionIndex].audioPathGd
        this.palyStatus = 'question'
      }
      if (step === 2) {
        this.audioUrl = this.readType === 1 ? '/static/measure/common/pt/question_suffix.mp3' : '/static/measure/common/gd/question_suffix.mp3'
        this.palyStatus = 'questionSuffix'
      }
      this.$refs.music.load()
      this.$nextTick(() => {
        this.$refs.music.play()
      })
    },

    pauseAudio () {
      this.$refs.music.pause()
      this.palyStatus = ''
    },

    handleEnded () {
      if (this.palyStatus === 'topicPrefix') {
        this.playTopic(2)
      } else if (this.palyStatus === 'topic') {
        this.playTopic(3)
      } else if (this.measure.id != 'ff511081d69248f0b27f1d329d280675' && this.measure.id != 'd803a9716d4b415cbc9c0c6396580b85' && this.palyStatus === 'question' && this.questionList[this.questionIndex].titleType == 1) {
        this.playQuestion(2)
      } else if (this.palyStatus === 'question' && this.questionList[this.questionIndex].titleType == 8) {
        this.topicEnd = true
      } else {
        this.palyStatus = ''
      }
    },

    handleAudioChange (fixed) {
      this.audioChange = fixed
    },
    /**
     * 加载量表信息数据
     */
    loadData () {
      // this.$forceUpdate()
      this.type = this.$route.query.type
      let params = {}
      if (this.type == '1' || this.type == '0') {
        this.userId = this.$route.query.userId
        params = { userId: this.userId, type: this.$route.query.type, resultId: this.$route.query.resultId, packageMark: this.$route.query.packageMark }
      } else if (this.type == '2') {
        params = { resultId: this.$route.query.resultId, type: this.$route.query.type }
      }
      return getLoadAnswerPageData(params).then((res) => {
        if (res.success) {
          if (res.result) {
            this.isShowQuestion = false
            this.notAnswerArr = []
            if (res.result.measure) {
              this.measure = res.result.measure
              // console.log(this.measure.syncMode)
              this.isSynchronize = !!this.measure.syncMode
            }
            if (res.result.quesionList) this.questionList = res.result.quesionList
            this.isShowGesell = (this.measure.id === 'c178fc3050c1d8f7e287f1230e1881e7')
            this.isShowAttention = (this.measure.id === '14c4fcfd1a8ee6b9f2e8089b658ebc4e')
            this.isShowWskx = (this.measure.id === 'd12d1f23e45c413c8179da35f7d9f7b0')
            this.isPPVT = (this.measure.id === '915ef9bfacf23247195d12c9f620f49a')

            if (res.result.quesionIndex != null) {
              this.questionIndex = res.result.quesionIndex
              this.questionNum = res.result.quesionIndex + 1

              // PPVT第三道测试题答完
              if (this.isPPVT && this.questionIndex == 3) {
                this.questionIndex = 2
                this.questionNum = 3
              }

              if (this.isPPVT && res.result.quesionEndIndex > 0) {
                this.questionIndex = res.result.quesionEndIndex
                this.questionNum = res.result.quesionEndIndex + 1
              }

              this.subsectionContent = res.result.quesionList.length && res.result.quesionList[res.result.quesionIndex].subsectionContent
              this.subsectionTitle = res.result.quesionList.length && res.result.quesionList[res.result.quesionIndex].subsectionTitle
              this.subsectionSubTitle = res.result.quesionList.length && res.result.quesionList[res.result.quesionIndex].subsectionSubTitle
              this.controlToTerminal()
            }
            if (res.result.dxResult) this.dxResult = res.result.dxResult
          } else {
            // 结束时主控端推给终端
            if (this.isControl && this.isSynchronize) {
              let params = {
                isFinish: true
              }
              const data = {
                sysUserId: this.$route.query.terminalDoctorId, // 终端id
                code: '401',
                messageJson: JSON.stringify(params)
              }
              this.messagePush(data)
            }
            this.toDashboard()
          }
        } else {
          // this.$message.warning('数据异常，请返回首页后重新开始答题。（我们保存了答题进度，点击继续答题开始续答）', 5)
          ElMessage({
            message: '数据异常，请返回首页后重新开始答题。（我们保存了答题进度，点击继续答题开始续答）',
            type: 'warning',
          });
          setTimeout(function () {
            this.$router.back()
          }, 5000)
        }
      })
    },

    toDashboard () {
      this.$router.back()
      this.$notification['success']({
        message: '测试完成',
        description: this.measure.name + '量表测试已完成',
      })
    },
    /**
     * 答题开始
     */
    quesionStart () {
      console.log(this.isTerminal + "```" + this.isSynchronize)
      this.isShowQuestion = true
      let date = new Date()
      this.time = date.getTime()
      this.totalTime = date.getTime()
      setInterval(() => {
        this.startTimer()
      }, 10)
      if (this.isPPVT) {
        this.audioChange = true
        this.shouldDisabledAudio = true
        this.readType = 0
      }
      this.playAudio()

      // 终端传给主控端 开始进入
      if (this.isTerminal && this.isSynchronize) {
        let params = {}
        console.log("tongbu")
        console.log(this.$route.query)
        if (this.type == '1' || this.type == '0') {
          params = { userId: this.$route.query.userId, type: this.$route.query.type, doctorId: this.dxResult.doctorId, terminalDoctorId: this.dxResult.terminalDoctorId, hasNext: this.hasNext, packageMark: this.$route.query.packageMark }
        } else if (this.type == '2') {
          params = { resultId: this.$route.query.resultId, type: this.$route.query.type, doctorId: this.dxResult.doctorId, terminalDoctorId: this.dxResult.terminalDoctorId, packageMark: this.$route.query.packageMark }
        }
        const data = {
          sysUserId: this.dxResult.doctorId,
          code: '402',
          messageJson: JSON.stringify(params)
        }
        this.messagePush(data)
      }

      // // 终端传给主控端 继续答题
      // if (this.isTerminal && this.isSynchronize) {
      //   let params = {}
      //   if (this.type == '1' || this.type == '0') {
      //     params = {userId: this.$route.query.userId, type: this.$route.query.type, doctorId: this.$route.query.doctorId, terminalDoctorId: this.$route.query.terminalDoctorId, packageMark: this.$route.query.packageMark}
      //   } else if (this.type == '2') {
      //     params = {resultId: this.$route.query.resultId, type: this.$route.query.type, doctorId: this.$route.query.doctorId, terminalDoctorId: this.$route.query.terminalDoctorId, packageMark: this.$route.query.packageMark}
      //   }
      //   const data = {
      //     sysUserId: this.$route.query.terminalDoctorId,
      //     code: '402',
      //     messageJson: JSON.stringify(params)
      //   }
      //   this.messagePush(data)
      // }
    },

    // 时间格式化
    startTimer () {
      const intervals = new Date().getTime() - this.time
      const b = (intervals % 60000) / 1000
      const c = (intervals % 3600000) / 60000
      const d = intervals / 3600000
      const timerSecond = b < 10 ? '0' + Math.floor(b) : Math.floor(b)
      const timerMinute = c < 10 ? '0' + Math.floor(c) : Math.floor(c)
      const timerHour = d < 10 ? '0' + Math.floor(d) : Math.floor(d)
      this.timer = timerHour + ':' + timerMinute + ':' + timerSecond
    },
    messagePush (data) {
      postWsMessagePush(data)
    },
    /**
     * 上一页
     * */
    previousPage () {
      if (this.questionIndex <= 0) return
      if (this.questionIndex >= 1) {
        this.questionIndex--
        if (this.questionList[this.questionIndex].subsectionContent) {
          this.isShowQuestion = false
        }
        this.subsectionContent = this.questionList[this.questionIndex].subsectionContent
        this.subsectionTitle = this.questionList[this.questionIndex].subsectionTitle
        this.subsectionSubTitle = this.questionList[this.questionIndex].subsectionSubTitle
      }
      this.questionNum--
      this.imgUrl = ''
      this.playAudio()
      this.refreshNotAnswer(this.questionNum)
      this.controlToTerminal()
    },
    /**
     * 下一页
     */
    nextPage () {
      if (this.questionIndex >= this.questionList.length - 1) return
      if (this.questionIndex + 1 < this.questionList.length) {
        this.questionIndex++
        if (this.questionList[this.questionIndex].subsectionContent) {
          this.isShowQuestion = false
        }
        this.subsectionContent = this.questionList[this.questionIndex].subsectionContent
        this.subsectionTitle = this.questionList[this.questionIndex].subsectionTitle
        this.subsectionSubTitle = this.questionList[this.questionIndex].subsectionSubTitle
      }
      this.questionNum++
      this.imgUrl = ''
      this.playAudio()
      this.refreshNotAnswer(this.questionNum)
      this.controlToTerminal()
    },
    // 注意力量表下一题
    attentionChangePage (orderPage) {
      this.attentionOrderPage = orderPage
      let date = new Date()
      this.time = date.getTime()
    },

    // 主控端传给终端
    controlToTerminal () {
      if (this.isControl && this.isSynchronize) {
        let params = {
          questionNum: this.questionNum
        }
        const data = {
          sysUserId: this.$route.query.terminalDoctorId, // 终端id
          code: '401',
          messageJson: JSON.stringify(params)
        }
        this.messagePush(data)
      }
    },

    submitImg (url) {
      // 终端传给主控端 画画连线点击确定时调用
      if (this.isTerminal && this.isSynchronize) {
        let params = {
          imgUrl: url,
          questionNum: this.questionNum
        }
        const data = {
          sysUserId: this.dxResult.doctorId,
          code: '402',
          messageJson: JSON.stringify(params)
        }
        this.messagePush(data)
      }
    },

    finishAttentionItem () {
      this.isShowAttention = false
      this.setNextMeasure()
    },

    finishWskxItem () {
      this.isShowWskx = false
      this.setNextMeasure()
    },

    finishGeselltem () {
      this.isShowGesell = false
      this.setNextMeasure()
    },

    finishPPVTtem () {
      this.isPPVT = false
      this.setNextMeasure()
    },

    setAnswer (questionNum, imgUrl) {
      if (questionNum !== this.questionNum) {
        this.onChangeSlider(questionNum)
        this.imgUrl = ''
      } else {
        this.imgUrl = imgUrl
      }
    },
    videoPlay () {
      this.topicEnd = false
    },
    playAudioAgain () {
      this.playAudio()
    },
    // 提交答案
    submit (params, flag = false) {
      const optionInfo = {
        ...params,
        userId: this.userId,
        measureId: this.measure.id,
        resultId: this.dxResult.id,
        time: new Date() - this.totalTime,
        totalTime: new Date() - this.time
      }
      if (this.measure.id === 'a9833e4ae12f723159763ec1b86c19be' && params.type === '4' && params.answer === '月') optionInfo.score = 1
      if (!flag) optionInfo.questionId = this.questionList[this.questionIndex].id
      if (optionInfo.type === '1') this.questionList[this.questionIndex].optionId = optionInfo.optionId
      if (optionInfo.type === '10') this.questionList[this.questionIndex].answer = optionInfo.answer
      this.totalTime = new Date()

      postAddOption(optionInfo).then((res) => {
        if (res.success) {
          if (res.code === 10001) {
            if (!this.isSynchronize && !this.isControl) {
              this.setNextMeasure()
            } else {
              let params = {
                isFinish: true
              }
              const data = {
                sysUserId: this.$route.query.terminalDoctorId, // 终端id
                code: '401',
                messageJson: JSON.stringify(params)
              }
              this.messagePush(data)
              this.toDashboard()
            }
          } else if (res.code === 10010) {
            if (res.result.questionSort) {
              // PPVT跳转答题
              this.onChangeSlider(res.result.questionSort)
            }
          } else {
            if (this.questionNum == this.questionList.length) {
              if (this.isPPVT) {
                this.handlePPVTEnd()
              } else {
                ElMessage({
                  message: '还有题目未答完',
                  type: 'warning',
                });
              }
            } else {
              this.questionNum++
              this.imgUrl = ''
              this.controlToTerminal()
            }
          }
          this.refreshNotAnswer(this.questionNum)
        } else {
          ElMessage({
            message: res.message,
            type: 'warning',
          });
        }
      })
        .catch((err) => {
          if (this.notAnswerArr.indexOf(this.questionNum) === -1) {
            this.notAnswerArr.push(this.questionNum)
          }
          this.questionNum++
          this.imgUrl = ''
          this.refreshNotAnswerArr()
          this.controlToTerminal()
        })
      // ppvt第三题提交答案不进入下一题，需要点击开始进入下一题
      if (!(this.isPPVT && this.questionIndex == 2)) {
        this.submitNextPage()
      }
    },

    // 提交答题下一题
    submitNextPage () {
      //下一题
      if (this.questionIndex + 1 < this.questionList.length) {
        this.questionIndex++
        if (this.questionList[this.questionIndex].subsectionContent) {
          this.isShowQuestion = false
        }
        this.subsectionContent = this.questionList[this.questionIndex].subsectionContent
        this.subsectionTitle = this.questionList[this.questionIndex].subsectionTitle
        this.subsectionSubTitle = this.questionList[this.questionIndex].subsectionSubTitle
        let that = this
        setTimeout(function () {
          that.playAudio()
        }, 1500)
      }
    },

    handlePPVTBegin () {
      this.submitNextPage()
    },
    handlePPVTEnd () {
      let params = {
        measureId: this.measure.id,
        resultId: this.dxResult.id,
      }
      postUploadResultForPPVT(params).then(res => {
        if (res.success) {
          this.finishPPVTtem()
        } else {
          // this.$message.warning('数据异常，请返回首页后重新开始答题。（我们保存了答题进度，点击继续答题开始续答）', 5)
          ElMessage({
            message: '数据异常，请返回首页后重新开始答题。（我们保存了答题进度，点击继续答题开始续答）',
            type: 'warning',
          });
          setTimeout(function () {
            this.$router.back()
          }, 5000)
        }
      })
    },

    // Gesell量表最后提交
    commit () {
      getUploadResult({ resultId: this.dxResult.id }).then((res) => {
        if (res.success) {
          this.finishGeselltem()
        } else {
          // this.$message.warning('数据异常，请返回首页后重新开始答题。（我们保存了答题进度，点击继续答题开始续答）', 5)
          ElMessage({
            message: '数据异常，请返回首页后重新开始答题。（我们保存了答题进度，点击继续答题开始续答）',
            type: 'warning',
          });
          setTimeout(function () {
            this.$router.back()
          }, 5000)
        }
      })
    },

    // 选择题号
    onChangeSlider (value) {
      this.imgUrl = ''
      this.questionNum = value
      this.questionIndex = value - 1
      this.subsectionContent = this.questionList[this.questionIndex].subsectionContent
      this.subsectionTitle = this.questionList[this.questionIndex].subsectionTitle
      this.subsectionSubTitle = this.questionList[this.questionIndex].subsectionSubTitle
      this.refreshNotAnswer(this.questionNum)
      this.controlToTerminal()
      this.playAudio()
    },

    // 获取未回答的题目index
    refreshNotAnswer (val) {
      if (this.isPPVT) return
      getNotAnswer({
        measureId: this.measure.id,
        resultId: this.dxResult.id,
        questionNum: val,
      }).then((res) => {
        if (res.success && res.result) {
          this.notAnswerArr = res.result.questionArr
          this.refreshNotAnswerArr()
        }
      })
    },

    // 渲染未答题样式
    refreshNotAnswerArr () {
      let marks = {}
      for (let key in this.notAnswerArr) {
        marks[this.notAnswerArr[key]] = {
          style: {
            color: '#f50',
          },
          label: '第' + this.notAnswerArr[key] + '题未答完',
        }
      }
      this.marks = marks
    },
  },
  destroyed () {
    clearInterval(this.timer)
  }
}
</script>

<style lang="scss">
@import './AnswerPage.scss';
</style>
<style scoped>
.py-50px {
  padding-top: 0;
}
</style>