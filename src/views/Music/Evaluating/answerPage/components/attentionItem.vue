<template>
  <div class="attention-item-view">
    <a-back-top />
    <p class="digital-breadth-title" v-html="answerTitle[(questionNum - 1)]"></p>

    <div class="attention-random-view">
      <div :class="['random-num', item.choose && 'has-choose']" v-for="(item, index) of randomNumbers" :key="index" @click="chooseItem(item, index)">{{ item.number }}</div>
    </div>
    <!-- <div class="footer-btn" v-if="isShowNextPage">
      <a-button v-if="questionNum == 5" style="height: 30px" @click="nextPage" type="primary">提交</a-button>
      <a-button v-else style="height: 30px" @click="nextPage" type="primary">
        下一题
        <a-icon type="right"/>
      </a-button>
    </div> -->
  </div>
</template>

<script>
import { postUploadResultForZYLCY } from '/@/api/index';

export default {
  name: 'AttentionItem',

  props: {
    userId: {
      type: String,
      default: ''
    },

    measure: {
      type: Object,
      default: () => { }
    },

    resultId: {
      type: String,
      default: ''
    },

    time: {
      type: String | Number,
      default: ''
    },
  },

  data () {
    return {
      answerTitle: [
        '第一部分: 划去3. 在屏幕上只要出现3, 就将这个3划去',
        '第二部分: 划去3前面的数. 在屏幕上只要出现3, 就将这个3前面的一个数字划去,如: 43就将数字4划去',
        '第三部分: 划去3前面的7. 在屏幕上只要连续出现73,就将7划去,如: 573 就将7划去',
        '第四部分: 划去3,7之间的数. 在屏幕上只要出现3*7或7*3, 就将*划去,如: 397 就将9划去,723就将2划去, 773就将第2个7划去',
        '第五部分: 划去3,7之间的偶数. 在屏幕上只要出现3*7或7*3, 且*为偶数,就将*划去,如: 367将6划去, 783将8划去'
      ],
      timer: null,

      questionNum: 1,
      randomNumbers: [], // 初始化空数组
      answerIndexs: [], // 正确答案数字位置
      correctIndexs: [], // 选择正确数组位置
      wrongIndexs: [], // 选择错误数组位置
      heightIndex: 0, //选择最高的位置

      loading: false,
      // isShowNextPage: false,

      url: '/diagnosis/dxResult/uploadResultForZYLCY',
      resultArray: [],
    }
  },
  mounted () {
    this.generateRandomNumbers()
  },
  methods: {
    generateRandomNumbers1 () {
      this.randomNumbers = []
      this.answerIndexs = []
      this.correctIndexs = []
      this.wrongIndexs = []
      let randomArr = []
      let correctArr = []

      // 3
      for (let part = 0; part < 64; part++) {
        let randomArrPart = []
        // 取0-24随机数，作为位置
        let randomPosition = Math.floor(Math.random() * 25)
        // 随机24个非3数字
        for (let i = 0; i < 24; i++) {
          let randomNum = this.getRandomNone3()
          randomArrPart.push({
            number: randomNum,
            choose: false,
          })
        }
        let num1 = { number: 3, choose: false }
        randomArrPart.splice(randomPosition, 0, num1)
        randomArr.push(...randomArrPart)
        correctArr.push(randomPosition + 25 * part)
      }

      this.$nextTick(() => {
        this.randomNumbers = randomArr
        this.answerIndexs = correctArr
        this.refreshTimer()
      })
    },
    generateRandomNumbers2 () {
      this.randomNumbers = []
      this.answerIndexs = []
      this.correctIndexs = []
      this.wrongIndexs = []
      let randomArr = []
      let correctArr = []

      // 3之前
      for (let part = 0; part < 64; part++) {
        let randomArrPart = []
        // 取0-23随机数，作为位置
        let randomPosition = Math.floor(Math.random() * 24)
        // 随机24个非3数字
        for (let i = 0; i < 24; i++) {
          let randomNum = this.getRandomNone3()
          randomArrPart.push({
            number: randomNum,
            choose: false,
          })
        }
        let num1 = { number: 3, choose: false }
        randomArrPart.splice(randomPosition + 1, 0, num1)
        randomArr.push(...randomArrPart)
        correctArr.push(randomPosition + 25 * part)
      }

      this.$nextTick(() => {
        this.randomNumbers = randomArr
        this.answerIndexs = correctArr
        this.refreshTimer()
      })
    },
    generateRandomNumbers3 () {
      this.randomNumbers = []
      this.answerIndexs = []
      this.correctIndexs = []
      this.wrongIndexs = []
      let randomArr = []
      let correctArr = []

      // 73
      for (let part = 0; part < 32; part++) {
        let randomArrPart = []
        // 取0-47随机数，作为位置
        let randomPosition = Math.floor(Math.random() * 48)
        // 随机48个 73不在一起数字
        for (let i = 0; i < 48; i++) {
          let randomNum = Math.floor(Math.random() * 10)
          let index = i - 1
          if (i > 0) {
            // 上一个数字的坐标
            if (randomArrPart[index].number == 7) {
              randomNum = this.getRandomNone3()
            }
          } else if (i == 0 && part > 0) {
            if (randomArr[part * 50 + index].number == 7) {
              randomNum = this.getRandomNone3()
            }
          }
          randomArrPart.push({
            number: randomNum,
            choose: false,
          })
        }
        let num1 = { number: 7, choose: false }
        let num2 = { number: 3, choose: false }
        randomArrPart.splice(randomPosition, 0, ...[num1, num2])
        randomArr.push(...randomArrPart)
        correctArr.push(randomPosition + 50 * part)
      }

      this.$nextTick(() => {
        this.randomNumbers = randomArr
        this.answerIndexs = correctArr
        this.refreshTimer()
      })
    },
    generateRandomNumbers4 () {
      this.randomNumbers = []
      this.answerIndexs = []
      this.correctIndexs = []
      this.wrongIndexs = []
      let randomArr = []
      let correctArr = []
      // 3*7 or 7*3
      for (let part = 0; part < 64; part++) {
        let randomArrPart = []
        // 取0-21随机数，作为位置
        let randomPosition = Math.floor(Math.random() * 22)
        // 随机22个 非3*7 or 7*3数字
        for (let i = 0; i < 22; i++) {
          let randomNum = Math.floor(Math.random() * 10)
          let index = i - 2
          if (i > 1) {
            if (randomArrPart[index].number === 7) {
              randomNum = this.getRandomNone3()
            } else if (randomArrPart[index].number === 3) {
              randomNum = this.getRandomNone7()
            }
          } else if ((i == 0 || i == 1) && part > 0) {
            if (randomArr[part * 25 + index].number == 7) {
              randomNum = this.getRandomNone3()
            } else if (randomArr[part * 25 + index].number == 3) {
              randomNum = this.getRandomNone7()
            }
          }
          randomArrPart.push({
            number: randomNum,
            choose: false,
          })
        }
        let random37 = this.getRandom37()
        let random73 = this.getRandom73(random37)
        let randomNone37 = this.getRandomNone37()
        let num1 = { number: random37, choose: false }
        let num2 = { number: randomNone37, choose: false }
        let num3 = { number: random73, choose: false }

        randomArrPart.splice(randomPosition, 0, ...[num1, num2, num3])
        // 修正新增 3*7 or 7*3 前后2位数字
        if (randomPosition < 2) {
          if (part > 0) {
            randomArr[part * 25 + randomPosition - 2].number = this.getRandomNone37()
            randomArrPart[randomPosition + 4].number = this.getRandomNone37()
          }
        } else if (randomPosition >= 2 && randomPosition < 20) {
          randomArrPart[randomPosition - 2].number = this.getRandomNone37()
          randomArrPart[randomPosition + 4].number = this.getRandomNone37()
        }

        randomArr.push(...randomArrPart)
        correctArr.push(randomPosition + 1 + 25 * part)
      }

      this.$nextTick(() => {
        this.randomNumbers = randomArr
        this.answerIndexs = correctArr
        this.refreshTimer()
      })
    },
    generateRandomNumbers5 () {
      this.randomNumbers = []
      this.answerIndexs = []
      this.correctIndexs = []
      this.wrongIndexs = []
      let randomArr = []
      let correctArr = []

      // 3*7 or 7*3 *为偶数
      for (let part = 0; part < 32; part++) {
        let randomArrPart = []
        // 取0-46随机数，作为位置
        let randomPosition = Math.floor(Math.random() * 47)
        // 随机47个 不符合条件数字
        for (let i = 0; i < 47; i++) {
          let randomNum = Math.floor(Math.random() * 10)
          let index2 = i - 2
          let index1 = i - 1
          if (i > 1) {
            if (randomArrPart[index2].number == 7 && this.isEvenNum(randomArrPart[index1].number)) {
              randomNum = this.getRandomNone3()
            } else if (randomArrPart[index2].number == 3 && this.isEvenNum(randomArrPart[index1].number)) {
              randomNum = this.getRandomNone7()
            }
          } else if (i == 0 && part > 0) {
            if (randomArr[part * 50 + index2].number == 7 && this.isEvenNum(randomArr[part * 50 + index1].number)) {
              randomNum = this.getRandomNone3()
            } else if (randomArr[part * 50 + index2].number == 3 && this.isEvenNum(randomArr[part * 50 + index1].number)) {
              randomNum = this.getRandomNone7()
            }
          } else if (i == 1 && part > 0) {
            if (randomArr[part * 50 + index2].number == 7 && this.isEvenNum(randomArrPart[index1].number)) {
              randomNum = this.getRandomNone3()
            } else if (randomArr[part * 50 + index2].number == 3 && this.isEvenNum(randomArrPart[index1].number)) {
              randomNum = this.getRandomNone7()
            }
          }
          randomArrPart.push({
            number: randomNum,
            choose: false,
          })
        }
        let random37 = this.getRandom37()
        let random73 = this.getRandom73(random37)
        let randomEven = this.getRandomEven()
        let num1 = { number: random37, choose: false }
        let num2 = { number: randomEven, choose: false }
        let num3 = { number: random73, choose: false }

        randomArrPart.splice(randomPosition, 0, ...[num1, num2, num3])
        // 修正新增 3*7 or 7*3 前后2位数字
        if (randomPosition < 2) {
          if (part > 0) {
            randomArr[part * 50 + randomPosition - 2].number = this.getRandomNone37()
            randomArrPart[randomPosition + 4].number = this.getRandomNone37()
          }
        } else if (randomPosition >= 2 && randomPosition < 45) {
          randomArrPart[randomPosition - 2].number = this.getRandomNone37()
          randomArrPart[randomPosition + 4].number = this.getRandomNone37()
        }

        randomArr.push(...randomArrPart)
        correctArr.push(randomPosition + 1 + 50 * part)
      }

      this.$nextTick(() => {
        this.randomNumbers = randomArr
        this.answerIndexs = correctArr
        this.refreshTimer()
      })
    },
    generateRandomNumbers () {
      if (this.questionNum == 1) {
        this.generateRandomNumbers1()
      } else if (this.questionNum == 2) {
        this.generateRandomNumbers2()
      } else if (this.questionNum == 3) {
        this.generateRandomNumbers3()
      } else if (this.questionNum == 4) {
        this.generateRandomNumbers4()
      } else if (this.questionNum == 5) {
        this.generateRandomNumbers5()
      }
    },
    refreshTimer () {
      let that = this
      this.timer = setTimeout(() => {
        // console.log('answerIndexs=====', that.answerIndexs)
        // console.log('correctIndexs=====', that.correctIndexs)
        // console.log('wrongIndexs=====', that.wrongIndexs)
        if (this.questionNum < 5) {
          this.$info({
            title: '提示',
            content: '该部分已结束，点击进行下一部分',
            okText: '确定',
            keyboard: false,
            onOk () {
              that.nextPage()
            }
          })
        } else {
          this.$info({
            title: '提示',
            content: '第五部分已结束，点击完成进行提交',
            okText: '完成',
            keyboard: false,
            onOk () {
              that.nextPage()
            }
          })
        }
      }, 180000)
    },
    chooseItem (item, index) {
      item.choose = !item.choose
      if (item.choose == false) {
        // 取消选中，从数组移除
        let correctI = this.correctIndexs.indexOf(index)
        let wrongI = this.wrongIndexs.indexOf(index)
        if (correctI != -1) {
          this.correctIndexs.splice(correctI, 1)
        }
        if (wrongI != -1) {
          this.wrongIndexs.splice(wrongI, 1)
        }
        return
      }
      // 赋值选择最高位置
      if (index > this.heightIndex) {
        this.heightIndex = index
      }
      if (this.answerIndexs.indexOf(index) === -1) {
        this.wrongIndexs.push(index)
      } else {
        this.correctIndexs.push(index)
      }
    },

    /**
     * 下一页
     */
    nextPage () {

      let omitNum = this.calculateOmitNum()
      let params = {
        questionNum: this.questionNum,
        totalNum: 1600,
        correctNum: this.correctIndexs.length,
        wrongNum: this.wrongIndexs.length,
        omitNum: omitNum,
        // omitNum: this.answerIndexs.length - this.correctIndexs.length
      }
      this.resultArray.push(params)
      // console.log('resultArray=====', this.resultArray)
      clearTimeout(this.timer)
      if (this.questionNum >= 5) {
        this.commit()
        return
      }

      this.questionNum++
      this.heightIndex = 0
      this.$emit('pageChange', this.questionNum + '/5')

      // this.isShowNextPage = false
      this.generateRandomNumbers()
    },
    /**
     * 计算漏答 (计算位置为开始到最高选择处即0-heightIndex)
     */
    calculateOmitNum () {
      // 获取最高选择处之前的所有正确答案
      console.log('heightIndex=====', this.heightIndex)

      let heightAnswerIndexs = []
      this.answerIndexs.forEach((element, index) => {
        if (element < this.heightIndex) {
          heightAnswerIndexs.push(element)
        }
      });
      let omitNum = heightAnswerIndexs.length - this.correctIndexs.length
      if (this.correctIndexs.indexOf(this.heightIndex) === -1) {
        return omitNum
      } else {
        return omitNum + 1
      }

    },
    /**
     * 提交
     */
    commit () {
      if (this.loading) return

      this.loading = true
      const params = {
        userId: this.userId,
        measureId: this.measure.id,
        resultId: this.resultId,
        time: new Date() - this.time,
        result: this.resultArray
      }

      postUploadResultForZYLCY(params).then(res => {
        if (res.success) {
          this.$emit('ok')
          // this.$router.push({
          //   name: 'dashboard',
          // })
          // this.$notification['success']({
          //   message: '测试完成',
          //   description: this.measure.name + '量表测试已完成',
          // })
        } else {
          ElMessage({
            message: res.message,
            type: 'error',
          });
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 随机3或7
    getRandom37 () {
      let arr = [3, 7]
      let randomNum = Math.floor(Math.random() * 2) // 生成范围在0到1之间的随机数
      return arr[randomNum]
    },
    // 反向取3,7 即num=3输出7；num=7输出3
    getRandom73 (num) {
      if (num == 3) {
        return 7
      }
      return 3
    },
    // 非3随机数
    getRandomNone3 () {
      let randomNum = Math.floor(Math.random() * 10) // 生成范围在0到9之间的随机数
      if (randomNum == 3) {
        return this.getRandomNone3()
      }
      return randomNum
    },
    // 非7随机数
    getRandomNone7 () {
      let randomNum = Math.floor(Math.random() * 10) // 生成范围在0到9之间的随机数
      if (randomNum == 7) {
        return this.getRandomNone7()
      }
      return randomNum
    },
    // 非3非7随机数
    getRandomNone37 () {
      let randomNum = Math.floor(Math.random() * 10) // 生成范围在0到9之间的随机数
      if (randomNum == 3 || randomNum == 7) {
        return this.getRandomNone37()
      }
      return randomNum
    },
    // 偶数随机数
    getRandomEven () {
      let randomNum = Math.floor(Math.random() * 5)
      let evenArr = [0, 2, 4, 6, 8]
      return evenArr[randomNum]
    },
    // 奇数随机数
    getRandomOdd () {
      let randomNum = Math.floor(Math.random() * 5)
      let oddArr = [1, 3, 5, 7, 9]
      return oddArr[randomNum]
    },
    isEvenNum (num) {
      let evenArr = [0, 2, 4, 6, 8]
      if (evenArr.indexOf(num) === -1) {
        return false
      }
      return true
    }
  },
  destroyed () {
    clearInterval(this.timer)
  }
}
</script>

<style lang="scss">
.attention-item-view {
  padding-left: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .digital-breadth-title {
    width: 100%;
    font-size: 28px;
    padding-bottom: 20px;
    margin: 0;
  }

  .attention-random-view {
    display: flex;
    flex-wrap: wrap;
    padding: 0 50px 50px 50px;
    overflow: auto;

    .random-num {
      font-size: 36px;
      padding: 0 2px;
    }
    .has-choose {
      color: red;
      text-decoration: line-through;
    }
  }
  .footer-btn {
    padding: 20px 0;
    .no-choose {
      cursor: not-allowed;
    }
  }
}
</style>