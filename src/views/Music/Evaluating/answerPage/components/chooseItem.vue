<template>
  <div class="choose-item">
    <div class="choose-item-top">
      <div v-if="isPPVT" style="display: flex; align-items: center; justify-content: space-between">
        <div @click="handlePlayAudio()" style="display: flex; align-items: center">
          <p v-if="question.titleType" class="choose-item-title-ppvt" v-html="question.title"></p>
          <!-- <img v-if="questionIndex < 123" src="../../../assets/img/audio_play.png" class="ppvt-play-audio" style="width: 70px; height: 70px"> -->
        </div>
        <div v-if="questionIndex == 2">
          <a-button type="primary" size="large" style="margin-right: 40px" @click="handlePPVTBegin()">开始</a-button>
          <a-button type="primary" size="large" @click="handlePPVTEnd()">结束</a-button>
        </div>
      </div>
      <p v-else-if="question.titleType" class="choose-item-title" v-html="question.title"></p>
      <img class="choose-item-img" v-if="question.imgPath" :src="question.imgPath" />
      <p class="choose-item-title" v-if="question.titleExplain && isControl && isSynchronize">题目解释：{{ question.titleExplain }}</p>
    </div>

    <div class="choose-item-answer" v-if="!(isTerminal && isSynchronize)">
      <div class="answer-item" v-for="(item, index) in question.psOptions" :key="index" :id="item.id" @click="chooseItem(item)">
        <a-button :class="[question.optionId === item.id && 'choose-btn']" type="primary" size="large">{{ item.title }}</a-button>
        <span v-if="item.type == 1" class="item-text">{{ item.content }}</span>
        <img class="item-img" v-if="item.type == 2" :src="item.content" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChooseItem',

  props: {
    question: {
      type: Object,
      default: () => {
      }
    },

    isTerminal: {
      type: Boolean,
      default: false
    },

    isControl: {
      type: Boolean,
      default: false
    },

    isSynchronize: {
      type: Boolean,
      default: false
    },

    isPPVT: {
      type: Boolean,
      default: false
    },

    questionIndex: {
      type: Number,
      default: 0
    }
  },

  data () {
    return {
      imgserver: import.meta.env.VITE_APP_URL + '/zhisong-music',
    }
  },

  mounted () {
    this.getText()
  },

  methods: {
    chooseItem (item) {
      this.$emit('submit', { optionId: item.id, type: item.type })
    },

    getText () {
      const list = ['1', '2', '3', '4', '5', '6', '7']
      let that = this
      window.onkeypress = function (e) {
        if (that.isStop) return
        const key = e.key.toUpperCase()
        const index = list.indexOf(key)
        if (index !== -1) {
          that.$emit('submit', { optionId: that.question.psOptions[index].id, type: that.question.psOptions[index].type })
        }
      }
    },
    // 重复播放题目
    handlePlayAudio () {
      this.$emit('playAudioAgain')
    },

    handlePPVTBegin () {
      this.$emit('ppvtBegin')
    },
    handlePPVTEnd () {
      let that = this
      this.$info({
        title: '提示',
        content: '该确定结束该测验？',
        okText: '确定',
        keyboard: false,
        onOk () {
          that.$emit('ppvtEnd')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-item {
  padding: 0 100px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .choose-item-top {
    width: 100%;
    min-height: 200px;
    text-align: center;

    .choose-item-title {
      width: 100%;
      margin: 0;
      padding: 0 150px 20px 150px;
      font-size: 28px;
      text-align: left;
    }
    .choose-item-title-ppvt {
      margin: 0;
      font-size: 28px;
      text-align: left;
    }

    .choose-item-img {
      width: 600px;
    }
  }

  .choose-item-answer {
    width: 100%;
    padding: 50px 130px 0 130px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .answer-item {
      display: flex;
      padding: 10px 20px 20px 20px;

      .ant-btn {
        height: 40px;
        margin-right: 10px;
      }

      .choose-btn {
        background: green;
      }

      .item-text {
        display: inline-block;
        font-size: 24px;
        line-height: 40px;
        cursor: pointer;
      }

      .item-img {
        width: 150px;
        cursor: pointer;
        background: #eee;
        border: 1px solid #eee;
      }
    }
  }
}
</style>