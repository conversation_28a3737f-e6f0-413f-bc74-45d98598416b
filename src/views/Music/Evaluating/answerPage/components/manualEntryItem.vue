<template>
  <div class="manual-entry">
    <div class="manual-entry-top">
      <p v-if="question.title" class="manual-entry-title" v-html="question.title"></p>
      <img class="manual-entry-img" v-if="question.imgPath" :src="question.imgPath" />
    </div>

    <div :class="['manual-entry-footer', question.titleType === '4' && 'manual-entry-textarea']">
      <span class="footer-text">
        {{ (question.titleType === '3' && '输入分值：') ||
            (question.titleType === '4' && '输入内容：') ||
            (question.titleType === '5' && '选择时间：') ||
            (question.titleType === '6' && '输入数值：') ||
            (question.titleType === '11' && '选择日期：') ||
            (question.titleType === '12' && '选择月份：') ||
            (question.titleType === '13' && '选择星期几：') ||
            (question.titleType === '14' && '选择城市：')
        }}
      </span>

      <!-- 输入数字 -->
      <a-input-number v-if="question.titleType === '3' || question.titleType === '6'" v-model="entryValue" :placeholder="question.titleType === '3' ? '请输入分值' : '请输入数值'" :min="0" :max="10000" :precision="0" />

      <!-- 输入内容 -->
      <a-textarea v-if="question.titleType === '4'" v-model="entryValue" placeholder="请输入内容" :auto-size="{ minRows: 4, maxRows: 6 }" />

      <!-- 选择时间 -->
      <a-time-picker v-if="question.titleType === '5'" v-model="entryValue" format="HH:mm" />

      <!-- 选择日期 -->
      <a-date-picker v-if="question.titleType === '11'" size="large" v-model="entryValue" />

      <!-- 选择月份 -->
      <a-select v-if="question.titleType === '12'" placeholder="选择月份" size="large" @change="onChange">
        <a-select-option v-for="item in 12" :key="item" :value="item">{{item + '月'}}</a-select-option>
      </a-select>

      <!-- 选择星期几 -->
      <a-select v-if="question.titleType === '13'" placeholder="选择星期几" size="large" @change="onChange">
        <a-select-option v-for="item in weekDay" :key="item">{{item}}</a-select-option>
      </a-select>

      <!-- 选择城市 -->
      <a-cascader v-if="question.titleType === '14'" :options="provinceList" placeholder="选择城市" size="large" @change="onChange" />

      <a-button @click="confirm" type="primary">
        确定
        <a-icon type="arrow-right" />
      </a-button>
    </div>
  </div>
</template>

<script>
import cityData from "../data/city.json"

export default {
  name: 'ManualEntryItem',

  props: {
    question: {
      type: Object,
      default: () => { }
    }
  },

  data () {
    return {
      entryValue: '',
      weekDay: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'],
      provinceList: cityData
    }
  },


  methods: {
    confirm () {
      if (this.question.titleType === '3' && !this.entryValue && this.entryValue !== 0) {
        ElMessage({
          message: '请填写当前项目的分值！',
          type: 'warning',
        });
        return
      }

      if (this.question.titleType === '4' && !this.entryValue) {
        ElMessage({
          message: '请填写当前项目的分值！',
          type: 'warning',
        });
        return
      }

      if (this.question.titleType === '5' && !this.entryValue) {
        ElMessage({
          message: '请填写当前项目的分值！',
          type: 'warning',
        });
        return
      }

      if (this.question.titleType === '6' && !this.entryValue && this.entryValue !== 0) {
        ElMessage({
          message: '请填写当前项目的分值！',
          type: 'warning',
        });
        return
      }

      const params = {}
      if (this.question.titleType === '3') {
        params.score = this.entryValue
        params.type = '3'
      }
      if (['4', '5', '6', '11', '12', '13', '14'].includes(this.question.titleType)) {
        params.score = 0
        params.answer = this.question.titleType === '5' ? this.entryValue.format('HH:mm') : this.question.titleType === '11' ? this.entryValue.format("yyyy-MM-DD") : this.entryValue
        params.type = this.question.titleType
      }
      this.$emit('submit', params)
    },

    onChange (value) {
      if (this.question.titleType === '12') this.entryValue = value
      if (this.question.titleType === '13') this.entryValue = value
      if (this.question.titleType === '14') this.entryValue = value.join('')
    }
  },

  watch: {
    question: {
      handler () {
        this.entryValue = ''
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="scss">
.manual-entry {
  padding: 0 100px;

  .manual-entry-top {
    height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .manual-entry-title {
      width: 100%;
      font-size: 28px;
      padding: 20 120px 10px 120px;
    }

    .manual-entry-img {
      width: 450px;
    }
  }

  .manual-entry-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 30px;

    .footer-text {
      display: inline-block;
      font-size: 24px;
      line-height: 40px;
    }

    .ant-input-number {
      width: 400px;
      height: 40px;

      .ant-input-number-input {
        height: 40px;
      }
    }

    .ant-input {
      width: 400px;
    }

    .ant-time-picker {
      width: 400px;
      height: 40px;

      .ant-time-picker-input {
        height: 40px;
      }
    }

    .ant-cascader-picker,
    .ant-calendar-picker {
      width: 400px;
      height: 40px;

      .ant-input {
        height: 40px;
      }
    }

    .ant-select {
      width: 400px;
      height: 40px;

      .ant-select-selection {
        height: 40px;
      }
    }

    .ant-btn {
      height: 40px;
      margin-left: 10px;
      border-radius: 5px;
    }
  }

  .manual-entry-textarea {
    align-items: flex-start;
  }
}
</style>