<template>
  <div class="wskx-item">
    <div class="page-bg"></div>

    <div class="game-content">
      <div class="title">
        <div class="text">威斯康星卡片测验量表</div>
        <div class="right">
          <div class="top-item">
            <a-icon type="ordered-list" />
            <span>{{ (ra == 128 ? 128 : (ra + 1)) + '/' + 128 }}</span>
          </div>

          <div class="top-item">
            <a-icon type="clock-circle" theme="twoTone" />
            <span>{{ timer }}</span>
          </div>
        </div>

      </div>

      <div class="content">
        <div class="top">
          <div class="title">卡片区（反应卡片）</div>
          <img class="img" :src="`${imageUrl}/static/wskx/${imgName}.png`" />
        </div>

        <div class="bottom">
          <div class="title">选择区（刺激卡片）</div>
          <div class="bottom-content">
            <div :class="['item', answerList[ra] === '1_1_1' && 'choose-item']">
              <img class="img" :src="`${imageUrl}/static/wskx/1_1_1.png`" @click="chooseItem('1_1_1')" />
            </div>
            <div :class="['item', answerList[ra] === '2_3_2' && 'choose-item']">
              <img class="img" :src="`${imageUrl}/static/wskx/2_3_2.png`" @click="chooseItem('2_3_2')" />
            </div>
            <div :class="['item', answerList[ra] === '4_2_3' && 'choose-item']">
              <img class="img" :src="`${imageUrl}/static/wskx/4_2_3.png`" @click="chooseItem('4_2_3')" />
            </div>
            <div :class="['item', answerList[ra] === '3_4_4' && 'choose-item']">
              <img class="img" :src="`${imageUrl}/static/wskx/3_4_4.png`" @click="chooseItem('3_4_4')" />
            </div>
          </div>
        </div>
      </div>

      <!-- <div class="footer">
        <div class="footer-right">
          <img class="img" src="/static/wskx/previous.png" @click="toPrevious" />
          <img class="img" src="/static/wskx/next.png" @click="toNext" />
        </div>
      </div> -->
    </div>

    <div class="mask" v-if="isShowCorrect || isShowError">
      <div class="center">
        <div class="center-content">
          <img v-show="isShowCorrect" class="icon" src="/static/wskx/sucess.png" />
          <img v-show="isShowError" class="icon" src="/static/wskx/error.png" />

          <p class="text">{{ isShowCorrect ? '正确' : isShowError ? '错误' : '' }}</p>
        </div>

        <!-- <div class="btn" @click="handleClose">OK</div> -->
      </div>
    </div>
  </div>
</template>

<script>
import { postUploadResultForWCST } from '/@/api/index';
// 128道题
// 顺序：颜色-形状-数量
// 完成正确分类6次结束游戏（每个规则完成10个算是完成一个正确分类）

export default {
  name: 'WskxItem',

  props: {
    userId: {
      type: String,
      default: ''
    },

    measure: {
      type: Object,
      default: () => { }
    },

    resultId: {
      type: String,
      default: ''
    },

    time: {
      type: String | Number,
      default: ''
    },

    timer: {
      type: String,
      default: ''
    }
  },

  data () {
    return {
      type: 0, // 训练模式 0-颜色 1-形状 2-数量
      questionList: [], // 题目列表
      answerList: [],
      imgName: '',
      correctNum: 0, // 连续答对数量
      rfs: [],

      ra: 0, // 题目序号 总应答数
      cc: 0, // 完成分类数
      rc: 0, // 正确应答数
      re: 0, // 错误应答数
      rcpRatio: 0, // 正确应答百分比
      rf: 0, // 完成第一个分类所需应答数
      rfRatio: 0, // 概念化水平百分数 连续完成3-10个正确应答的总数，占总应答数的百分比
      pe: 0, // 持续性应答数 不需要看前边的分类原则，12题按照颜色分类是错误的，13题依然按照颜色分类来选择
      rpe: 0, // 持续性错误数 需要看前边的分类原则，12题逻辑上是按照形状分类的，分类原创发生了变化，但是12题按照颜色分类，13题依然按照颜色分类来选择
      nrpe: 0, // 非持续性错误 总错误数-持续性错误数
      rpeRatio: 0, // 持续性错误的百分数 总错误数与持续性错误数之差
      fm: 0, // 不能维持完整分类数 连续完成5－9个正确应答的次数，即已发现分类规则但不能坚持完成分类的次数

      isShowCorrect: false,
      isShowError: false,
      showNext: false,
      loading: false,

      imgserver: import.meta.env.VITE_APP_URL + '/zhisong-music',
      imageUrl: '',
    }
  },

  mounted () {
    this.setData()
    if (this.imgserver.indexOf('https') !== -1) {
      this.imageUrl = 'https://img.zhisongkeji.com'
    }
  },

  methods: {
    setData () {
      for (let i = 1; i <= 4; i++) {
        for (let j = 1; j <= 4; j++) {
          for (let k = 1; k <= 4; k++) {
            const item = i + '_' + j + '_' + k
            this.questionList.push(item)
          }
        }
      }

      this.questionList = this.questionList.concat(this.questionList)
      this.questionList = this.shuffle(this.questionList)
      this.startProcess()
    },

    startProcess () {
      this.imgName = this.questionList[this.ra]
      if (this.correctNum >= 10) {
        this.type = (this.type + 1) % 3
        this.correctNum = 0
      }
    },

    chooseItem (item) {
      this.answerList.splice(this.ra, 0, item)
      const answer = item.split('_')
      const question = this.questionList[this.ra].split('_')

      if (answer[this.type] === question[this.type]) {
        this.rc++
        this.correctNum++
        if (this.correctNum >= 3) this.rfRatio++
        this.isShowCorrect = true
      } else {
        this.re++
        if (this.correctNum >= 5) this.fm++
        this.isShowError = true

        let type0 = false
        let type1 = false
        let type2 = false
        if (answer[0] === question[0]) {
          type0 = true
        } else if (answer[1] === question[1]) {
          type1 = true
        } else if (answer[2] === question[2]) {
          type2 = true
        }

        if (this.ra) {
          const perAnswer = this.answerList[this.ra - 1].split('_')
          const perQuestion = this.questionList[this.ra - 1].split('_')
          let perType0 = false
          let perType1 = false
          let perType2 = false
          if (perAnswer[0] === perQuestion[0]) {
            perType0 = true
          } else if (perAnswer[1] === perQuestion[1]) {
            perType1 = true
          } else if (perAnswer[2] === perQuestion[2]) {
            perType2 = true
          }

          let lastType0 = false
          let lastType1 = false
          let lastType2 = false
          if (this.ra > 1) {
            const lastAnswer = this.answerList[this.ra - 2].split('_')
            const lastQuestion = this.questionList[this.ra - 2].split('_')
            if (lastAnswer[0] === lastQuestion[0]) {
              lastType0 = true
            } else if (lastAnswer[1] === lastQuestion[1]) {
              lastType1 = true
            } else if (lastAnswer[2] === lastQuestion[2]) {
              lastType2 = true
            }
          }

          if ((type0 && perType0 || type1 && perType1 || type2 && perType2) && !this.correctNum) {
            this.pe++
            if (this.ra > 1 && !(type0 && lastType0 || type1 && lastType1 || type2 && lastType2)) this.pe++
            if (((this.type - 1) % 3 === 0 && type0) || ((this.type - 1) % 3 === 1 && type1) || ((this.type - 1) % 3 === 2 && type2)) {
              this.rpe++
              if (this.ra > 1 && !(type0 && lastType0 || type1 && lastType1 || type2 && lastType2)) this.rpe++
            }
          }

          this.correctNum = 0
        }
      }

      if (this.correctNum >= 10) {
        this.cc++
        if (!this.rfs.length) {
          this.rfs.push(this.ra + 1)
        } else {
          this.rfs.push(this.ra + 1 - this.rfs[this.rfs.length - 1])
        }
      }
      let that = this
      setTimeout(function () {
        that.handleClose()
      }, 1000)
      //调试用 不弹出正确错误提示框
      // this.isShowCorrect = false
      // this.isShowError = false
      // this.handleClose()
    },

    toPrevious () {
      this.ra--
      this.startProcess()
    },

    toNext () {
      this.ra++
      this.startProcess()
    },

    handleClose () {
      this.isShowCorrect = false
      this.isShowError = false
      this.ra++
      if (this.cc >= 6 || this.ra >= 128) {
        this.commit()
        return
      }
      this.startProcess()
      console.log('1--------1111', this.correctNum, this.type, this.ra, this.cc, this.rc, this.re, this.rcpRatio,
        this.rf, this.rfRatio, this.pe, this.rpe, this.nrpe, this.rpeRatio, this.fm)
    },

    commit () {
      if (this.loading) return

      let ll = 0
      this.rfs.forEach(item => {
        if (!ll) ll = (item - 10) / item
        ll = ll - (item - 10) / item
      })
      ll = ll / this.rfs.length
      if (!ll && ll != 0) {
        ll = -11
      }
      this.loading = true
      const params = {
        userId: this.userId,
        measureId: this.measure.id,
        resultId: this.resultId,
        time: new Date() - this.time,
        result: {
          ra: this.ra,			// 总应答数（Response Administered ，Ra）
          cc: this.cc,			 // 完成分类数（Categories Completed Cc）
          rc: this.rc,			 // 正确应答数（RC）
          re: this.re,			 //错误应答数（Re）
          rcpRatio: (this.rc / this.ra).toFixed(2) * 100,	// 正确应答百分比（RCP）
          rf: this.rfs.length ? this.rfs[0] : 0,			 //完成第一个分类所需应答数（Rf，TCFC）
          rfRatio: (this.rfRatio / this.ra).toFixed(2) * 100,	 //概念化水平百分数（Rf%）
          pe: this.pe,			 //持续性应答数（Perseverative Errors， PE）
          rpe: this.rpe,			 // 持续性错误数（Rpe）
          nrpe: this.re - this.rpe,			 // 非持续性错误（nRpe）
          rpeRatio: (this.rpe / this.ra).toFixed(2) * 100,	// 持续性错误的百分数（Rpe%）
          fm: this.fm,			 // 不能维持完整分类数（Fm）
          ll                 // 学习到学会
        }
      }

      postUploadResultForWCST(params).then(res => {
        if (res.success) {
          this.$emit('ok')
          // this.$router.push({
          //   name: 'dashboard',
          // })
          // this.$notification['success']({
          //   message: '测试完成',
          //   description: this.measure.name + '量表测试已完成',
          // })
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 数组乱序
    shuffle (arr) {
      for (let i = arr.length; i; i--) {
        let j = Math.floor(Math.random() * i)
        const tab = arr[i - 1]
        arr[i - 1] = arr[j]
        arr[j] = tab
      }
      return arr;
    }
  }
}
</script>

<style lang="scss" scoped>
.wskx-item {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/wskx/bg.png');
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
      width: 100%;
      position: relative;
      display: flex;
      justify-content: space-between;

      .text {
        font-size: 28px;
        line-height: 44px;
        color: #0055a6;
      }

      .right {
        display: inline-flex;
        align-items: center;

        .top-item {
          display: inline-flex;
          align-items: center;
          padding-left: 30px;
          font-size: 28px;
          line-height: 44px;

          .anticon {
            font-size: 28px;
            margin-right: 10px;
          }

          .ant-switch {
            width: 58px;
            height: 28px;
            margin-right: 10px;
          }
        }
      }
    }

    .content {
      position: relative;
      padding: 25px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .top {
        width: 330px;
        padding: 30px 0 50px 0;
        text-align: center;

        .title {
          width: 330px;
          padding: 0 15px 15px 25px;
          text-align: center;
          font-size: 32px;
          line-height: 50px;
          color: #0055a6;
        }

        .img {
          width: 330px;
          border-radius: 12px;
        }
      }

      .bottom {
        display: flex;
        flex-direction: column;
        text-align: center;
        align-items: center;
        padding: 20px 25px 30px 25px;
        background: #a5c8eb;
        border-radius: 16px;
        border: 1px solid #ffffff;

        .title {
          text-align: center;
          padding-left: 500px;
          padding-bottom: 20px;
          font-size: 32px;
          line-height: 50px;
          color: #0055a6;
        }

        .bottom-content {
          min-width: 1200px;
          display: flex;
          justify-content: space-between;
          padding-bottom: 20px;

          .item {
            margin: 0 20px;
            width: 260px;
            padding: 5px;
            border-radius: 12px;
            background: #fff;

            .img {
              width: 250px;
              border-radius: 12px;
              cursor: pointer;
            }
          }

          .choose-item {
            background: #0055a6;
          }
        }
      }
    }

    .footer {
      width: 1600px;
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;

      .footer-right {
        width: 620px;
        display: inline-flex;
        justify-content: space-between;
      }

      .img {
        height: 115px;
        cursor: pointer;
      }
    }
  }

  .mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;

    .center {
      position: absolute;
      top: 400px;
      background: #fff;
      border-radius: 12px;
      padding: 60px 80px 40px 80px;

      .center-content {
        display: flex;
        align-items: center;
        height: 50px;
        padding-bottom: 20px;

        .icon {
          height: 40px;
        }

        .text {
          margin: 0;
          font-size: 36px;
          padding-left: 15px;
        }
      }

      .btn {
        margin: 0;
        padding: 0 50px;
        font-size: 20px;
        line-height: 44px;
        background: #0055a6;
        color: #fff;
        border-radius: 20px;
        cursor: pointer;
      }
    }
  }
}
</style>
