.ilabel {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 600;
  font-size: 32px;
  color: #1393dd;
  line-height: 45px;
}

.audioContent {
  .player {
    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }

    &-record {
      width: 230px;
      height: 230px;
    }

    .rotate {
      animation: rotate 5s linear infinite;
    }

    &-content {
      .label {
        font-family: PingFangSC, 'PingFang SC';
        font-size: 28px;
        color: #000;
        line-height: 40px;
      }

      .durationTime {
        font-family: PingFangSC, 'PingFang SC';
        font-weight: 400;
        font-size: 24px;
        color: #000;
        line-height: 33px;

        .durationTime-btn {
          > span {
            display: flex;
            align-items: center;

            > img {
              width: 31px;
            }
          }
        }
      }
    }
  }

  .playBtn {
    width: 92px;
    height: 92px;
    margin: 0 auto 122px;

    > img {
      width: 100%;
    }
  }
}

.time-content {
  .el-radio.el-radio--large .el-radio__label {
    font-family: PingFangSC, '<PERSON>Fang SC';
    font-weight: 400;
    font-size: 32px;
    color: #000;
    line-height: 45px;
  }
}

.time-btn {
  .time-btn-btn {
    width: 200px;
    height: 85px;
    font-family: PingFangSC, 'PingFang SC';
    font-weight: 600;
    font-size: 24px;
    color: #000;
    background-image: url('/static/icon/btn-bg.jpg');
    background-size: 100%;
    text-align: center;
    line-height: 85px;
  }
}
