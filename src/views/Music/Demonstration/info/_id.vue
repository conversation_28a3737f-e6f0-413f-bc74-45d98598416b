<template>
  <Header title="呼吸训练" status="home" />
  <Container>
    <div class="w-full h-full flex flex-col justify-between items-center">
      <div class="ilabel">{{ tabs[currentTab] }}</div>
      <audio @timeupdate="updateProgress" controls ref="audioRef" style="display: none" src="/@/assets/images/testAudio.mp3"></audio>
      <div class="audioContent w-full flex flex-col">
        <div class="player w-full flex px-123px">
          <img :class="'player-record mr-46px ' + (isPlay ? 'rotate' : '')" src="/@/assets/images/record.png" alt="" />
          <div class="player-content flex flex-col flex-1 justify-center">
            <span class="label">{{ audioDom.label }}</span>
            <div class="progress">
              <el-slider class="slider_box" v-model="currentProgress" :show-tooltip="false" @input="handleProgressChange" />
            </div>
            <div class="durationTime flex justify-between">
              <span>{{ durationTime }}</span>demonstration-info
              <div class="durationTime-btn flex">
                <span class="mr-51px" @click="changeloop()">
                  <img src="/@/assets/images/circulate.png" alt="" />
                  循环
                </span>
                <span @click="showTime = true">
                  <img src="/@/assets/images/fixedTime.png" alt="" />
                  定时
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="playBtn" @click="playAudio">
          <img v-if="isPlay" src="/@/assets/images/play.jpg" alt="" />
          <img v-else src="/@/assets/images/stop.png" alt="" />
        </div>
      </div>
      <Tab :tabs="tabs" :current="currentTab" @change="changeTab" />
    </div>
  </Container>
  <el-dialog v-model="showTime" title="请选择" width="739" top="40vh" :modal="false" :center="true" @close="showTime = false">
    <div class="time-content flex justify-center my-112px">
      <el-radio-group v-model="cycleTime" fill="#85CCF5" @change="changeCyctime">
        <el-radio value="900000" size="large">15分钟</el-radio>
        <el-radio value="1800000" size="large">30分钟</el-radio>
        <el-radio value="3600000" size="large">60分钟</el-radio>
      </el-radio-group>
    </div>
    <div class="time-btn flex items-center justify-center">
      <div class="time-btn-btn mr-57px" @click="showTime = false">取消</div>
      <div class="time-btn-btn" @click="setCycTime()">确认</div>
    </div>
  </el-dialog>
</template>

<script setup>
import { getAssetsFile } from '/@/utils/tool';

const showTime = ref(false);
const cycleTime = ref('900000');
const tabs = ref([]);
const currentTab = ref(0);
function changeTab (index) {
  currentTab.value = index;
}
//定时
function setCycTime () {
  const time = cycleTime.value;
  showTime.value = false;
  audioRef.value.loop = true;
  setTimeout(() => {
    audioRef.value.pause();
    isPlay.value = false;
  }, time);
}
const list = ref([
  {
    id: 1,
    cover: '',
    audioUrl: 'https://img.zhisongkeji.com/musicTherapy/testAudio.mp3',
    label: 'test1',
    time: '08:11',
    durationTime: '69.381224',
  },
  {
    id: 2,
    cover: '',
    audioUrl: 'https://img.zhisongkeji.com/musicTherapy/testAudio.mp3',
    label: 'test2',
    time: '04:02',
    durationTime: '69.381224',
  },
  {
    id: 3,
    cover: '',
    audioUrl: 'https://img.zhisongkeji.com/musicTherapy/testAudio.mp3',
    label: 'test3',
    time: '05:11',
    durationTime: '69.381224',
  },
]);
const audioDom = ref({}); //当前
const playIndex = ref(0);
const audioRef = ref(null);
const isPlay = ref(false); //音频是否在播放
const audioStart = ref('0:00');
const durationTime = ref('0:00'); //音频的总时长，显示的时间格式
const duration = ref(0); //音频的总时长
const currentProgress = ref(0);

onMounted(() => {
  audioDom.value = list.value[playIndex.value];
  calculateDuration();
});
// onActivated(() => {
//   audioDom.value = list.value[playIndex.value];
//   calculateDuration();
// })

function changeloop () {
  let _audioRef = audioRef.value;
  if (_audioRef.loop) {
    _audioRef.loop = false;
    _audioRef.pause();
    isPlay.value = false;
  } else {
    _audioRef.loop = true;
    _audioRef.play();
    isPlay.value = false;
  }
  calculateDuration();
}
// 获取音频时长
function calculateDuration () {
  let _audioRef = audioRef.value;
  // _audioRef.loop = false;
  _audioRef.src = audioDom.value.audioUrl;
  // 监听音频播放完毕
  _audioRef.addEventListener(
    'ended',
    function () {
      isPlay.value = false;
      currentProgress.value = 0;
    },
    false,
  );
  if (_audioRef != null) {
    // 开始播放前执行
    _audioRef.oncanplay = function () {
      duration.value = _audioRef.duration; // 计算音频时长
      durationTime.value = transTime(_audioRef.duration); //换算成时间格式
    };
    // 进入页面默认开始暂停
    // _audioRef.value.pause();
    // isPlay.value = false;
  }
}
//切换音频
function changePlay (type) {
  isPlay.value = false;
  currentProgress.value = 0;

  let index = playIndex.value;
  if (type == 'pre' && index == 0) {
    return;
  } else {
    audioDom.value = list.value[index - 1];
    playIndex.value = index - 1;
  }

  if (type == 'next' && index == list.value.length - 1) {
    return;
  } else {
    audioDom.value = list.value[index + 1];
    playIndex.value = index + 1;
  }

  calculateDuration();
}

// function handleCloseMusic() {
//   audioRef.value.pause();
//   isPlay.value = true;
// }
// 音频播放时间换算
function transTime (duration) {
  const minutes = Math.floor(duration / 60);
  const seconds = Math.floor(duration % 60);
  const formattedMinutes = String(minutes).padStart(2, '0'); //padStart(2,"0") 使用0填充使字符串长度达到2
  const formattedSeconds = String(seconds).padStart(2, '0');
  return `${formattedMinutes}:${formattedSeconds}`;
}
// 播放暂停控制
function playAudio () {
  if (audioRef.value.paused) {
    audioRef.value.play().catch((err) => {
      console.log(err);
    });
    isPlay.value = true;
  } else {
    audioRef.value.pause();
    isPlay.value = false;
  }
}
// 根据当前播放时间，实时更新进度条
function updateProgress (e) {
  var value = e.target.currentTime / e.target.duration;
  if (audioRef.value.play) {
    currentProgress.value = value * 100;
    audioStart.value = transTime(audioRef.value.currentTime);
  }
}
//调整播放进度
const handleProgressChange = (val) => {
  console.log(val);
  if (!val) {
    return;
  }
  let currentTime = duration.value * (val / 100);
  // 更新音频的当前播放时间
  audioRef.value.currentTime = currentTime;
};
</script>

<style lang="scss">
@import './style.scss';

.el-dialog__title {
  font-weight: 400;
  font-size: 32px;
  color: #000;
  line-height: 45px;
}

.el-dialog__headerbtn .el-dialog__close {
  font-size: 32px;
}
</style>
