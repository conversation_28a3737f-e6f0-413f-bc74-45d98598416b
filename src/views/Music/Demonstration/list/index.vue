<template>
  <Header title="教学示范" status="home" />
  <Container>
    <Tab :tabs="tabs" :current="currentTab" @change="changeTab" />
    <div class="boxpadding w-full flex-1">
      <Carousel :list="list" :single="true" @info="goInfo" />
    </div>
  </Container>
</template>

<script setup>
  import { getAssetsFile } from '/@/utils/tool';

  const router = useRouter();
  function goInfo(id) {
    router.push('/demonstration/info/' + id);
  }
  //tabs
  const tabs = ref(['呼吸训练', '正念冥想', '睡眠调整', '减压音频']);
  const currentTab = ref(0);
  function changeTab(index) {
    currentTab.value = index;
  }

  const list = ref([
    [
      { id: 1, cover: 'zjjs.png', label: '478呼吸训练', value: '' },
      { id: 2, cover: 'zymc.png', label: '似动图片', value: '' },
      { id: 3, cover: 'xlxsy.png', label: '其他类', value: '' },
      { id: 4, cover: 'xlxy.png', label: '多视图片', value: '' },
    ],
    [
      { id: 9, cover: 'zjjs.png', label: '专家介绍', value: '' },
      { id: 10, cover: 'zymc.png', label: '专有名词', value: '' },
    ],
  ]);
</script>

<style lang="scss">
  @import './style.scss';
</style>
