<template>
  <Header title="数据中心" status="home" />
  <Container>
    <div class="tableContent w-full h-full flex flex-col">
      <div class="tool w-full flex flex-col justify-between items-end">
        <div class="tool-search w-full flex items-center">
          用户名 <el-input v-model="keyword" class="ml-23px mr-17px" style="width: 292px; height: 52px" />
          <div class="btn confirm mr-12px">查询</div>
          <div class="btn reset">重置</div>
        </div>
        <div class="tool-delect mb-25px">批量删除</div>
      </div>
      <el-table ref="multipleTable" :data="tableData" style="
          width: 99.97%;
          height: 100%;
          border-right: 1px #98cae7 solid;
          border-left: 1px #98cae7 solid;
          border-bottom: 1px #98cae7 solid;
        " :highlight-current-row="false" :header-cell-style="{
          backgroundColor: '#91C8E7',
          color: '#fff',
          fontSize: '22px',
        }" :cell-style="{
          color: '#5DA5CE',
          fontSize: '22px',
          borderBottom: '1px #98cae7 solid',
        }" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="tableName" label="名称" />
        <el-table-column prop="date" label="时间" />
        <el-table-column prop="account" label="用户名" />
        <el-table-column prop="operate" label="操作">
          <span>查看报告</span>
          <span>删除</span>
        </el-table-column>
      </el-table>
    </div>
  </Container>
</template>

<script setup>
import { ElMessage } from 'element-plus';

const keyword = ref('');
const tableData = ref([]);
const multipleTable = ref(null);
const multipleSelection = ref([]);
const toggleSelection = (rows) => {
  if (rows) {
    rows.forEach((row) => {
      multipleTable.value.toggleRowSelection(row, undefined);
    });
  } else {
    multipleTable.value.clearSelection();
  }
};
const handleSelectionChange = (val) => {
  multipleSelection.value = val;
};
</script>

<style lang="scss">
@import './style.scss';
</style>
