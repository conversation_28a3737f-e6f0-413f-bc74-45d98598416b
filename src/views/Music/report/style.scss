.tableContent {
  padding: 0 68px;

  .el-input__inner {
    height: 52px;
    align-items: center;
    background-color: rgb(30 121 174 / 13%);
    box-shadow: none;
    border-radius: 5px;
  }

  .el-table {
    font-size: 22px;
    background-color: transparent;
  }

  .el-table tr {
    background-color: transparent;
  }

  .el-table .cell,
  .el-table th div {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .el-table tbody tr:hover > td {
    background: #91c8e7 !important;
  }

  // .el-textarea__inner::-webkit-input-placeholder {
  //   color: #1e79ae;
  // }
  .el-input__wrapper {
    align-items: center;
    background-color: transparent;
    box-shadow: none;
  }

  // input输入框focus颜色样式
  .el-input {
    --el-input-focus-border: transparent;
    --el-input-focus-border-color: transparent;
  }

  .el-pagination.is-background .el-pager li.is-active {
    background-color: #1e79ae;
  }

  .tool {
    &-search {
      font-family: PingFangSC, 'PingFang SC';
      font-weight: 400;
      font-size: 28px;
      color: #1e79ae;
      line-height: 40px;

      .btn {
        width: 100px;
        height: 52px;
        border-radius: 4px;
        font-family: PingFangSC, 'PingFang SC';
        font-weight: 400;
        font-size: 28px;
        line-height: 52px;
        text-align: center;
      }

      .confirm {
        color: #fff;
        background: #1e79ae;
      }

      .reset {
        color: #1e79ae;
        background: #fff;
      }
    }

    &-delect {
      width: 130px;
      height: 52px;
      background: #1e79ae;
      border-radius: 4px;
      font-family: PingFangSC, 'PingFang SC';
      font-weight: 400;
      font-size: 28px;
      color: #fff;
      line-height: 52px;
      text-align: center;
    }
  }
}
