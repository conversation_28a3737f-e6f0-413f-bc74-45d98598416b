.moviebox {
  padding: 0 68px;

  .movie-content {
    .movie-page {
      .movie-item {
        width: 832px;
        height: 507px;
        position: relative;
        overflow: hidden;
        border-radius: 13px;

        &-cover {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        &-title {
          position: absolute;
          top: 0;
          width: 832px;
          height: 54px;
          background: linear-gradient(180deg, rgb(0 0 0 / 58%) 0%, rgb(0 0 0 / 0%) 100%);
          font-family: PingFangSC, 'PingFang SC';
          font-weight: 600;
          font-size: 22px;
          color: #fff;
          line-height: 31px;
          text-align: left;
          padding: 5px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .movieMenus {
    overflow-x: scroll;

    &-item {
      width: 248px;
      height: 152px;
      margin-right: 35px;
      position: relative;
      overflow: hidden;
      border-radius: 13px;

      &-cover {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      &-title {
        position: absolute;
        bottom: 0;
        width: 248px;
        height: 32px;
        background: linear-gradient(180deg, rgb(0 0 0 / 0%) 0%, rgb(0 0 0 / 58%) 100%);
        font-family: PingFangSC, 'PingFang SC';
        font-weight: 400;
        font-size: 19px;
        color: #fff;
        line-height: 32px;
        text-align: center;
        padding: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .current {
    border: 5px solid #1e79ae;
  }
}
