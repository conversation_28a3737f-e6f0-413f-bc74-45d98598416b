<template>
  <Header title="呼吸训练" status="back" />
  <Container>
    <div class="moviebox w-full h-full flex flex-col">
      <div class="movie flex-1 flex">
        <el-carousel @change="changeItem" class="movie-content w-full" height="507px" :autoplay="false" arrow="always">
          <el-carousel-item :key="item.id" v-for="item of list">
            <div class="movie-page h-full flex items-center justify-center">
              <div class="movie-item">
                <video :id="item.id" class="movie-item-cover" ref="videoPlayer" controls @play="play" @pause="pause" @ended="ended" :src="item.url"></video>
                <!-- <img class="movie-item-cover" src="/@/assets/images/moviestest.png" alt="" /> -->
                <div class="movie-item-title">{{ item.title }}</div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="movieMenus w-full py-13px flex justify-center">
        <div :class="'movieMenus-item ' + (current == index ? 'current' : '')" :key="item.id" v-for="(item, index) of list">
          <img class="movieMenus-item-cover" src="/@/assets/images/moviestest.png" alt="" />
          <div class="movieMenus-item-title">{{ item.title }}</div>
        </div>
      </div>
    </div>
  </Container>
</template>

<script setup>
const props = defineProps(['id']);
const list = ref([]);
const current = ref(0);
const videoPlayer = ref(null);

function changeItem (e) {
  const _videoPlayer = videoPlayer.value[e];
  _videoPlayer.pause();
  _videoPlayer.load();
  current.value = e;
}
</script>

<style lang="scss">
@import './style.scss';

/* 自定义Carousel右箭头 */
.movie-content .el-carousel__arrow--right {
  color: #1e79ae;
  font-size: 72px;
  font-weight: 700;
  background-color: transparent; /* 箭头颜色 */
}

/* 自定义Carousel左箭头 */
.movie-content .el-carousel__arrow--left {
  color: #1e79ae;
  font-size: 72px;
  font-weight: 700;
  background-color: transparent; /* 箭头颜色 */
}
</style>
