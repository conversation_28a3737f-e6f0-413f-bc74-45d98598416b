.moviebox {
  padding: 0 68px;
  overflow: hidden;

  .movie-content {
    .movie-page {
      .movie-item {
        width: 832px;
        height: 507px;
        position: relative;
        overflow: hidden;
        border-radius: 13px;

        &-cover {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 13px;
        }

        &-title {
          position: absolute;
          top: 0;
          width: 832px;
          height: 54px;
          background: linear-gradient(180deg, rgb(0 0 0 / 58%) 0%, rgb(0 0 0 / 0%) 100%);
          font-family: PingFangSC, 'PingFang SC';
          font-weight: 600;
          font-size: 22px;
          color: #fff;
          line-height: 31px;
          text-align: left;
          padding: 5px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .movieMenus {
    overflow: hidden;
    width: 100%;

    .movieMenus-scroll{
      width: 100%;
      overflow-x: scroll;
    }

    ::-webkit-scrollbar {
      width: 14px;
      background: #fff;
      border-radius: 7px;
    }
    
    ::-webkit-scrollbar-thumb {
      width: 8px;
      height: 89px;
      background: #85ccf5;
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-track {
      background: #fff;
      border-radius: 10px;
    }

    &-item {
      width: 248px;
      min-width: 248px;
      height: 152px;
      margin-right: 35px;
      position: relative;
      overflow: hidden;
      border-radius: 13px;

      &.current {
        border: 10px solid #1e79ae;
      }

      &:last-child{
        margin-right: 0;
      }

      &-cover {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      &-title {
        position: absolute;
        bottom: 0;
        width: 248px;
        height: 32px;
        background: linear-gradient(180deg, rgb(0 0 0 / 0%) 0%, rgb(0 0 0 / 58%) 100%);
        font-family: PingFangSC, 'PingFang SC';
        font-weight: 400;
        font-size: 19px;
        color: #fff;
        line-height: 32px;
        text-align: center;
        padding: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
