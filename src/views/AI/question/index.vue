<template>
  <Header title="AI问答" status="back" />
  <Container>
    <div id="chatbot" style="width: 100%; height: 100%"></div>
  </Container>
</template>

<script setup>
  import { ElMessage } from 'element-plus';
  import { getUserInfo, clearUserInfo } from '/@/utils/user';

  const router = useRouter();
  function goReport() {
    router.push('/user/report');
  }
  const user = ref({
    nickName: '',
    avater: '',
  });

  // 动态加载扣子AI SDK
  const loadCozeSDK = () => {
    return new Promise((resolve, reject) => {
      if (window.CozeWebSDK) {
        resolve(window.CozeWebSDK);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.10/libs/cn/index.js';
      script.onload = () => {
        resolve(window.CozeWebSDK);
      };
      script.onerror = reject;
      document.head.appendChild(script);
    });
  };

  onMounted(async () => {
    let _userInfo = JSON.parse(getUserInfo());
    if (_userInfo) {
      user.value = _userInfo;
    }
    await init();
  });

  onUnmounted

  const chatClient = ref(null);
  const init = async () => {
    try {
      const CozeWebSDK = await loadCozeSDK();

      console.log('CozeWebSDK loaded:', CozeWebSDK);

      // 创建扣子AI聊天客户端
      chatClient.value = new CozeWebSDK.WebChatClient({
        config: {
          bot_id: '7538709349795921971',
        },
        componentProps: {
          title: '心理自助',
        },
        auth: {
          type: 'token',
          token: 'pat_L1vU2Y2vHlyifIHLMyyOdvQe5T1UK9uuSQw0dbhWhp7Lrce2CF4ooJspoOyUxgS1',
          onRefreshToken: function () {
            return 'pat_L1vU2Y2vHlyifIHLMyyOdvQe5T1UK9uuSQw0dbhWhp7Lrce2CF4ooJspoOyUxgS1';
          },
        },
        ui: {
          asstBtn: {
            isNeed: false, // 不显示悬浮球
          },
          header: {
            isShow: false, // 隐藏头部
            isNeedClose: false,
          },
          footer: {
            isShow: true, // 显示输入框
            expressionText: ' ',
          },
          chatBot: {
            width: '100%',
            uploadable: true,
            isNeedAddNewConversation: true, // 允许新建对话
            isNeedQuote: true,
            el: document.getElementById('chatbot'),
          },
        },
      });

      console.log('扣子AI聊天客户端创建成功:', chatClient.value);

      // 显示聊天机器人
      chatClient.value.showChatBot();

      // 保存客户端实例以便后续操作
      window.cozeChatClient = chatClient.value;
    } catch (error) {
      console.error('加载扣子AI SDK失败:', error);
      ElMessage.error('AI聊天功能加载失败，请刷新页面重试');
    }
  };
</script>

<style lang="scss">
  @import './style.scss';
</style>
