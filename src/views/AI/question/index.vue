<template>
  <Header title="AI问答" status="back" />
  <Container>
    <div id="chatbot" style="width: 100%; height: 100%"></div>
  </Container>
</template>

<script setup>
  import { ElMessage } from 'element-plus';
  import { getUserInfo, clearUserInfo } from '/@/utils/user';

  const router = useRouter();
  function goReport() {
    router.push('/user/report');
  }
  const user = ref({
    nickName: '',
    avater: '',
  });

  // 动态加载扣子AI SDK
  const loadCozeSDK = () => {
    return new Promise((resolve, reject) => {
      if (window.CozeWebSDK) {
        resolve(window.CozeWebSDK);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.10/libs/cn/index.js';
      script.onload = () => {
        resolve(window.CozeWebSDK);
      };
      script.onerror = reject;
      document.head.appendChild(script);
    });
  };

  onMounted(async () => {
    let _userInfo = JSON.parse(getUserInfo());
    if (_userInfo) {
      user.value = _userInfo;
    }
    await init();
  });

  const init = async () => {
    try {
      const CozeWebSDK = await loadCozeSDK();

      console.log('CozeWebSDK loaded:', CozeWebSDK);

      // 创建扣子AI聊天客户端
      const chatClient = new CozeWebSDK.WebChatClient({
        config: {
          bot_id: '7538709349795921971',
        },
        componentProps: {
          title: '心理自助',
        },
        auth: {
          type: 'token',
          token: 'pat_L1vU2Y2vHlyifIHLMyyOdvQe5T1UK9uuSQw0dbhWhp7Lrce2CF4ooJspoOyUxgS1',
          onRefreshToken: function () {
            return 'pat_L1vU2Y2vHlyifIHLMyyOdvQe5T1UK9uuSQw0dbhWhp7Lrce2CF4ooJspoOyUxgS1';
          },
        },
        ui: {
          asstBtn: {
            isNeed: false, // 不显示悬浮球
          },
          header: {
            isShow: false, // 隐藏头部
            isNeedClose: false,
          },
          footer: {
            isShow: true, // 显示输入框
            expressionText: '请输入您的问题...',
          },
          chatBot: {
            uploadable: true,
            isNeedAddNewConversation: false,
            isNeedQuote: true,
          },
        },
      });

      console.log('扣子AI聊天客户端创建成功:', chatClient);

      // 显示聊天机器人
      chatClient.showChatBot();

      // 等待DOM渲染完成后调整样式
      setTimeout(() => {
        adjustChatBotPosition();
      }, 0);
    } catch (error) {
      console.error('加载扣子AI SDK失败:', error);
      ElMessage.error('AI聊天功能加载失败，请刷新页面重试');
    }
  };

  // 调整聊天框位置和样式的函数
  const adjustChatBotPosition = () => {
    try {
      // 查找扣子AI创建的聊天框元素
      const chatBotElements = [
        document.querySelector('.coze-chat-container'),
        document.querySelector('.coze-webchat-container'),
        document.querySelector('.webchat-container'),
        document.querySelector('[class*="coze"]'),
        document.querySelector('[class*="chat"]'),
      ];

      let chatBotElement = null;
      for (const element of chatBotElements) {
        if (element && element.style) {
          chatBotElement = element;
          break;
        }
      }

      if (chatBotElement) {
        console.log('找到聊天框元素:', chatBotElement);

        // 获取目标容器
        const targetContainer = document.querySelector('#chatbot');

        if (targetContainer) {
          // 将聊天框移动到目标容器内
          targetContainer.appendChild(chatBotElement);

          // 调整样式
          chatBotElement.style.position = 'static';
          chatBotElement.style.width = '100%';
          chatBotElement.style.height = '100%';
          chatBotElement.style.border = 'none';
          chatBotElement.style.borderRadius = '0';
          chatBotElement.style.boxShadow = 'none';

          console.log('聊天框已移动到Container内');
        }
      } else {
        console.log('未找到聊天框元素，尝试查找所有可能的元素...');
        const allElements = document.querySelectorAll('*');
        allElements.forEach((el, index) => {
          if (el.className && (el.className.includes('coze') || el.className.includes('chat'))) {
            console.log(`找到可能的聊天框元素 ${index}:`, el);
          }
        });
      }
    } catch (error) {
      console.error('调整聊天框位置时出错:', error);
    }
  };
</script>

<style lang="scss">
  @import './style.scss';
</style>
