<template>
  <Header title="AI问答" status="back" />
  <Container>
    <div id="chatbot" style="width: 100%; height: 100%"></div>
  </Container>
</template>

<script setup>
  import { ElMessage } from 'element-plus';
  import { getUserInfo, clearUserInfo } from '/@/utils/user';

  const router = useRouter();
  function goReport() {
    router.push('/user/report');
  }
  const user = ref({
    nickName: '',
    avater: '',
  });

  // 动态加载扣子AI SDK
  const loadCozeSDK = () => {
    return new Promise((resolve, reject) => {
      if (window.CozeWebSDK) {
        resolve(window.CozeWebSDK);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.10/libs/cn/index.js';
      script.onload = () => {
        resolve(window.CozeWebSDK);
      };
      script.onerror = reject;
      document.head.appendChild(script);
    });
  };

  onMounted(async () => {
    let _userInfo = JSON.parse(getUserInfo());
    if (_userInfo) {
      user.value = _userInfo;
    }
    await init();
  });

  const init = async () => {
    try {
      const CozeWebSDK = await loadCozeSDK();

      new CozeWebSDK.WebChatClient({
        config: {
          bot_id: '7538709349795921971',
        },
        componentProps: {
          title: 'Coze',
        },
        auth: {
          type: 'token',
          token: 'cztei_hyco2QlQLa8M1M8EFdjQKdWYP4GaaQrL00LHqR3QXOjJzyFYQ7FygJOEkAE73b7Pk',
          onRefreshToken: function () {
            return 'cztei_hyco2QlQLa8M1M8EFdjQKdWYP4GaaQrL00LHqR3QXOjJzyFYQ7FygJOEkAE73b7Pk';
          },
        },
        ui: {
          asstBtn: false, // 不显示悬浮球
        },
      });
    } catch (error) {
      console.error('加载扣子AI SDK失败:', error);
      ElMessage.error('AI聊天功能加载失败，请刷新页面重试');
    }
  };
</script>

<style lang="scss">
  @import './style.scss';
</style>
