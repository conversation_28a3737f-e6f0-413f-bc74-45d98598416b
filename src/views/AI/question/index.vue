<template>
  <Header title="AI问答" status="back" />
  <Container>
    <div style="width: 100%; height: 100%">
      <iframe style="border: none" width="100%" height="100%" ref="iframeRef" src="/static/chat/aiQuestion.html"></iframe>
    </div>
  </Container>
</template>

<script setup>
  import { ElMessage } from 'element-plus';
  import { getUserInfo, clearUserInfo } from '/@/utils/user';

  const router = useRouter();
  function goReport() {
    router.push('/user/report');
  }
  const user = ref({
    nickName: '',
    avater: '',
  });

  onMounted(() => {
    let _userInfo = JSON.parse(getUserInfo());
    if (_userInfo) {
      user.value = _userInfo;
    }
    init();
  });

  const init = () => {
    // new CozeWebSDK.WebChatClient({
    //   config: {
    //     bot_id: '7538709349795921971',
    //   },
    //   componentProps: {
    //     title: 'Coze',
    //   },
    //   auth: {
    //     type: 'token',
    //     token: 'cztei_hyco2QlQLa8M1M8EFdjQKdWYP4GaaQrL00LHqR3QXOjJzyFYQ7FygJOEkAE73b7Pk',
    //     onRefreshToken: function () {
    //       return 'cztei_hyco2QlQLa8M1M8EFdjQKdWYP4GaaQrL00LHqR3QXOjJzyFYQ7FygJOEkAE73b7Pk';
    //     },
    //   },
    //   // ui: {
    //   //   asstBtn: false,
    //   // },
    // });
  };
</script>

<style lang="scss">
  @import './style.scss';
</style>
