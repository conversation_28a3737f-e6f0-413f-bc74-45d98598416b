<template>
  <Header title="AI问答" status="back" />
  <Container>
    <div id="chatbot" style="width: 100%; height: 100%"></div>
  </Container>
</template>

<script setup>
  import { ElMessage } from 'element-plus';
  import { getUserInfo, clearUserInfo } from '/@/utils/user';

  const router = useRouter();
  function goReport() {
    router.push('/user/report');
  }
  const user = ref({
    nickName: '',
    avater: '',
  });

  // 动态加载聊天SDK
  const loadChatSDK = () => {
    return new Promise((resolve, reject) => {
      if (window.ChatSDK && window.ChatSSE) {
        resolve({ ChatSDK: window.ChatSDK, ChatSSE: window.ChatSSE });
        return;
      }

      // 需要按顺序加载的脚本文件
      const scripts = ['/static/chat/ChatSDK.js', '/static/chat/ChatSSE.js', '/static/chat/index.js'];

      let loadedCount = 0;

      const loadScript = (src) => {
        return new Promise((resolve, reject) => {
          const script = document.createElement('script');
          script.src = src;
          script.onload = resolve;
          script.onerror = reject;
          document.head.appendChild(script);
        });
      };

      // 按顺序加载所有脚本
      const loadAllScripts = async () => {
        try {
          for (const src of scripts) {
            await loadScript(src);
          }
          resolve({ ChatSDK: window.ChatSDK, ChatSSE: window.ChatSSE });
        } catch (error) {
          reject(error);
        }
      };

      loadAllScripts();
    });
  };

  onMounted(async () => {
    let _userInfo = JSON.parse(getUserInfo());
    if (_userInfo) {
      user.value = _userInfo;
    }
    await init();
  });

  // 生成UUID的函数
  const generateUUID = () => {
    const temp_url = URL.createObjectURL(new Blob());
    const uuid = temp_url.toString();
    URL.revokeObjectURL(temp_url);
    return uuid.substring(uuid.lastIndexOf('/') + 1);
  };

  const init = async () => {
    try {
      const { ChatSDK, ChatSSE } = await loadChatSDK();

      console.log('ChatSDK loaded:', ChatSDK);
      console.log('ChatSSE loaded:', ChatSSE);

      // SessionId，保持上下文使用
      const SessionId = generateUUID();

      // 渲染对话页面
      window.bot = new ChatSDK({
        root: document.querySelector('#chatbot'),
        config: {
          navbar: {
            title: '心理自助',
          },
          robot: {
            avatar: '/static/chat/avatar_ai.png',
          },
          user: {
            avatar: '/static/chat/avatar_user.png',
          },
          messages: [
            {
              type: 'text',
              content: {
                text: '请问有什么可以帮您？',
              },
            },
          ],
        },
        requests: {
          send(msg) {
            sendMessage(msg.content.text, SessionId, ChatSSE);
          },
        },
      });

      // 进行聊天窗的渲染
      window.bot.run();
    } catch (error) {
      console.error('加载聊天SDK失败:', error);
      ElMessage.error('AI聊天功能加载失败，请刷新页面重试');
    }
  };

  // 发送消息的函数
  const sendMessage = async (text, sessionId, ChatSSE) => {
    try {
      ChatSSE.fetchBeebotSSE({
        url: `https://music.zhisongkeji.com/zhisong-music/sse/chat?sessionId=${sessionId}&query=${text}`,
        json: {},
        onJsonText(data) {
          // 接收到消息的回调
          console.log('receiver:', data);
          // 响应消息解析和渲染
          window.bot.updateOrAppendMessage(ChatSDK.isvParser({ data }));
        },
        onerror(err) {
          // 接收消息出错时处理
          console.log('error', err);
          ElMessage.error('发送消息失败，请重试');
        },
      });
    } catch (error) {
      console.error('发送消息失败:', error);
      ElMessage.error('发送消息失败，请重试');
    }
  };
</script>

<style lang="scss">
  @import './style.scss';
  @import '../../../../public/static/chat/ChatSDK.css';
</style>

<style></style>
