// 确保扣子AI聊天界面适应Container的尺寸
#chatbot {
  // 确保聊天界面填满整个容器
  .coze-chat-container {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
    border: none !important;
    box-shadow: none !important;
  }

  // 如果需要调整聊天界面的内部样式
  .coze-chat-wrapper {
    width: 100% !important;
    height: 100% !important;
  }

  // 确保消息容器适应高度
  .coze-message-container {
    height: 100% !important;
  }
}

.Message-meta {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
  text-align: center;

  time {
    font-size: 12px;
    color: #999;
    font-weight: bold;
  }
}

.Message-main {
  align-items: flex-start;
  display: flex;

  .Avatar img {
    display: block;
    height: 56px;
    object-fit: cover;
    width: 56px
  }
}