// 确保扣子AI聊天界面适应Container的尺寸
#chatbot {
  width: 100% !important;
  height: 100% !important;

  // 扣子AI的主容器样式调整
  .coze-chat-container,
  .coze-webchat-container,
  .webchat-container {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
  }

  // 聊天界面包装器
  .coze-chat-wrapper,
  .webchat-wrapper {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }

  // 消息容器
  .coze-message-container,
  .webchat-message-container {
    height: 100% !important;
  }

  // 聊天窗口主体
  .coze-chat-window,
  .webchat-window {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
    border: none !important;
  }

  // 头部标题栏
  .coze-chat-header,
  .webchat-header {
    background: transparent !important;
    border-bottom: 1px solid #e5e5e5 !important;
  }

  // 输入框区域
  .coze-chat-input,
  .webchat-input {
    border-top: 1px solid #e5e5e5 !important;
  }
}