// 确保扣子AI聊天界面适应Container的尺寸
#chatbot {
  width: 100% !important;
  height: 100% !important;
  position: relative;

  // 扣子AI的主容器样式调整
  .coze-chat-container,
  .coze-webchat-container,
  .webchat-container,
  .coze-chat-bot {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    position: static !important;
  }

  // 聊天界面包装器
  .coze-chat-wrapper,
  .webchat-wrapper,
  .coze-chat-bot-wrapper {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
    position: static !important;
  }

  // 消息容器
  .coze-message-container,
  .webchat-message-container,
  .coze-chat-messages {
    height: calc(100% - 120px) !important; // 减去头部和输入框的高度
    overflow-y: auto !important;
  }

  // 聊天窗口主体
  .coze-chat-window,
  .webchat-window,
  .coze-chat-main {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
    border: none !important;
    display: flex !important;
    flex-direction: column !important;
  }

  // 头部标题栏 - 已通过配置隐藏
  .coze-chat-header,
  .webchat-header,
  .coze-chat-title {
    display: none !important; // 因为我们设置了 header.isShow: false
  }

  // 输入框区域
  .coze-chat-input,
  .webchat-input,
  .coze-chat-footer {
    border-top: 1px solid #e5e5e5 !important;
    background: #fff !important;
    padding: 12px !important;
    min-height: 60px !important;
  }

  // 发送按钮样式
  .coze-chat-send-btn,
  .webchat-send-btn {
    background: #1890ff !important;
    border-color: #1890ff !important;

    &:hover {
      background: #40a9ff !important;
      border-color: #40a9ff !important;
    }
  }

  // 消息气泡样式
  .coze-message-bubble,
  .webchat-message-bubble {
    max-width: 80% !important;

    // 用户消息
    &.user {
      background: #1890ff !important;
      color: white !important;
    }

    // AI消息
    &.assistant {
      background: #f5f5f5 !important;
      color: #333 !important;
    }
  }
}