// 确保扣子AI聊天界面适应Container的尺寸
#chatbot {
  width: 100% !important;
  height: 100% !important;
  position: relative;
  border-radius: 42px;
  overflow: hidden;

  // 全局字体设置
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
  font-size: 20px !important;
  line-height: 1.5 !important;
  color: #333 !important;

  // 扣子AI的主容器样式调整
  .coze-chat-container,
  .coze-webchat-container,
  .webchat-container,
  .coze-chat-bot,
  .coze-chat-bot-wrapper,
  .coze-chat-wrapper {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    position: static !important;
    font-size: 20px !important;
    font-family: inherit !important;
  }

  // 消息容器
  .coze-message-container,
  .webchat-message-container,
  .coze-chat-messages,
  .coze-message-list {
    height: calc(100% - 80px) !important;
    overflow-y: auto !important;
    padding: 16px !important;
    font-size: 16px !important;
  }

  // 聊天窗口主体
  .coze-chat-window,
  .webchat-window,
  .coze-chat-main {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
    border: none !important;
    display: flex !important;
    flex-direction: column !important;
    font-size: 16px !important;
  }

  // 消息项样式
  .coze-message-item,
  .webchat-message-item,
  .coze-chat-message {
    margin-bottom: 16px !important;
    font-size: 16px !important;
    line-height: 1.6 !important;

    // 消息内容
    .coze-message-content,
    .webchat-message-content,
    .message-content {
      font-size: 16px !important;
      line-height: 1.6 !important;
      color: #333 !important;
      padding: 12px 16px !important;
      border-radius: 12px !important;
      max-width: 80% !important;
      word-wrap: break-word !important;
      white-space: pre-wrap !important;
    }

    // 用户消息
    &.user,
    &.human {
      .coze-message-content,
      .webchat-message-content,
      .message-content {
        background: #1890ff !important;
        color: white !important;
        margin-left: auto !important;
      }
    }

    // AI消息
    &.assistant,
    &.bot {
      .coze-message-content,
      .webchat-message-content,
      .message-content {
        background: #f8f9fa !important;
        color: #333 !important;
        border: 1px solid #e9ecef !important;
      }
    }
  }

  // 输入框区域
  .coze-chat-input,
  .webchat-input,
  .coze-chat-footer,
  .coze-input-container,
  .coze-input-wrapper {
    border-top: 1px solid #e5e5e5 !important;
    background: #fff !important;
    padding: 16px !important;
    min-height: 80px !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;

    // 输入框
    input,
    textarea {
      flex: 1 !important;
      font-size: 18px !important;
      line-height: 1.5 !important;
      color: #333 !important;
      border: 1px solid #d9d9d9 !important;
      border-radius: 8px !important;
      padding: 12px 16px !important;
      min-height: 44px !important;

      &::placeholder {
        color: #999 !important;
        font-size: 18px !important;
      }

      &:focus {
        border-color: #1890ff !important;
        box-shadow: 0 0 0 2px rgb(24 144 255 / 20%) !important;
        outline: none !important;
      }
    }
  }

  // 发送按钮样式
  .coze-chat-send-btn,
  .webchat-send-btn,
  .coze-send-button,
  button[class*="send"],
  button[title*="发送"],
  button[aria-label*="send"] {
    background: #1890ff !important;
    border: 1px solid #1890ff !important;
    color: white !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    padding: 12px 24px !important;
    border-radius: 8px !important;
    min-height: 44px !important;
    min-width: 80px !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;

    &:hover {
      background: #40a9ff !important;
      border-color: #40a9ff !important;
    }

    &:active {
      background: #096dd9 !important;
      border-color: #096dd9 !important;
    }

    &:disabled {
      background: #d9d9d9 !important;
      border-color: #d9d9d9 !important;
      color: #999 !important;
      cursor: not-allowed !important;
    }

    // 发送图标
    svg,
    i {
      width: 16px !important;
      height: 16px !important;
      fill: currentcolor !important;
    }
  }

  // 确保按钮容器正确显示
  .coze-input-actions,
  .coze-chat-actions,
  .webchat-actions {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
  }

  // 头部标题栏 - 已通过配置隐藏
  .coze-chat-header,
  .webchat-header,
  .coze-chat-title {
    display: none !important;
  }

  // 通用文本样式增强
  * {
    font-size: inherit !important;
    font-family: inherit !important;

    // 确保所有文本都清晰可见
    &:not(input):not(textarea) {
      -webkit-font-smoothing: antialiased !important;
      -moz-osx-font-smoothing: grayscale !important;
      text-rendering: optimizelegibility !important;
    }
  }

  // 滚动条样式
  ::-webkit-scrollbar {
    width: 6px !important;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 3px !important;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 3px !important;

    &:hover {
      background: #a8a8a8 !important;
    }
  }

  .semi-typography {
    font-size: 16px !important;
    color: #666 !important;
  }
}