<template>
  <Header :title="title[type]" status="home" />
  <Container>
    <div class="form flex-1 flex flex-col items-end mt-60px">
      <div class="form-item flex justify-center items-end mb-48px" :key="index" v-for="(item, index) in formNode[type]">
        <div class="form-item-label">
          {{ item.label }}
        </div>
        <div class="form-item-value">
          <el-input class="input" v-model="dataForm[item.modelName]" :type="item.type" />
        </div>
      </div>
      <div class="form-forget flex justify-between" v-if="type == 'login'">
        <span @click="goregister()">注册</span>
        <span @click="setVisitLogin()">游客模式</span>
        <!-- <span>忘记密码？</span> -->
      </div>
    </div>
    <div class="login-submit" @click="submit()">
      {{type == 'register' ? '注册' : '登录'}}
    </div>
  </Container>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { setToken } from '/@/utils/auth';
import { setUserInfo } from '/@/utils/user';
import { getEquipmentType } from '/@/utils/equipment';
import { userRegister, userlogin, userRegisterCheck, userVisitLogin, orgCode } from '/@/api/index';

const props = defineProps(['type']);
//type: login登录 register注册

const router = useRouter();
function goregister () {
  router.replace('/login/register');
}
// 提交
onMounted(() => {
  dataForm.value = form[props.type];
});
const dataForm = ref({});
const form = reactive({
  login: {
    account: '',
    password: '',
  },
  register: {
    account: '',
    password: '',
    passwordConfirm: '',
  },
});
const formNode = {
  login: [
    {
      label: '账号：',
      modelName: 'account',
      type: '',
    },
    {
      label: '密码：',
      modelName: 'password',
      type: 'password',
    },
  ],
  register: [
    {
      label: '账号：',
      modelName: 'account',
      type: '',
    },
    {
      label: '姓名：',
      modelName: 'name',
      type: '',
    },
    {
      label: '密码：',
      modelName: 'password',
      type: 'password',
    },
    {
      label: '确认密码：',
      modelName: 'passwordConfirm',
      type: 'password',
    },
  ],
};
const rule = {
  login: {
    account: '请输入账号',
    password: '请输入密码',
  },
  register: {
    account: '请输入账号',
    password: '请输入密码',
    passwordConfirm: '请确认密码',
  },
};
function setVisitLogin (params) {
  userVisitLogin(orgCode).then((result) => {
    setToken(result.result.token);
    setUserInfo(result.result.userInfo);
    ElMessage({
      message: '游客登录成功',
      type: 'success',
    });
    router.back();

    // setTimeout(() => {
    //   router.push('/' + getEquipmentType() + '/')
    // }, 300)
  })
}
function submit () {
  //检验
  const _rule = rule[props.type];
  for (let item in _rule) {
    if (!dataForm.value[item]) {
      ElMessage({
        message: _rule[item],
        type: 'error',
      });
      return;
    }
  }
  if (props.type == 'register') {
    const { password, passwordConfirm, name, account } = dataForm.value;
    if (password != passwordConfirm) {
      ElMessage({
        message: '两次密码不一致',
        type: 'error',
      });
      return;
    }
    // {"password":"123456","realname":"陈测试","username":"chentest"}
    userRegisterCheck({
      password: password,
      realname: name,
      username: account,
    }).then((res) => {
      if (res.code == 200) {
        userRegister({
          password: password,
          realname: name,
          username: account,
          orgCode
        }).then((result) => {
          if (result.code == 500) {
            ElMessage({
              message: result.message,
              type: 'error',
            });
          } else {
            ElMessage({
              message: '注册成功',
              type: 'success',
            });
            router.back();
          }
        });
      } else {
        ElMessage({
          message: res.message,
          type: 'error',
        });
      }

    })

  } else {
    const { password, account } = dataForm.value;
    userlogin({
      password: password,
      username: account,
    }).then((result) => {
      if (result.code == 500) {
        ElMessage({
          message: '账号或密码错误',
          type: 'error',
        });
      } else {
        setToken(result.result.token);
        setUserInfo(result.result.userInfo);
        ElMessage({
          message: '登录成功',
          type: 'success',
        });
        router.back();
        // router.push('/' + getEquipmentType() + '/')
      }
    });
  }
}
const title = {
  login: '登录',
  register: '注册',
};
</script>

<style lang="scss">
@import './style.scss';

.el-input__wrapper {
  align-items: center;
  background-color: transparent;
  box-shadow: none;
}
// input输入框focus颜色样式
.el-input {
  --el-input-focus-border: transparent;
  --el-input-focus-border-color: transparent;
}

.el-pagination.is-background .el-pager li.is-active {
  background-color: #1e79ae;
}
</style>
